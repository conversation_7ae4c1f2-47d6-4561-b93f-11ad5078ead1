#!/usr/bin/env python3
"""
JSON to CSV Converter
Converts JSON files to CSV format with support for nested objects and arrays.
"""

import json
import csv
import sys
import argparse
from pathlib import Path
from typing import Dict, List, Any, Union


def flatten_json(obj: Any, parent_key: str = '', separator: str = '.', keep_arrays: List[str] = None) -> Dict[str, Any]:
    """
    Flatten a nested JSON object.
    
    Args:
        obj: The JSON object to flatten
        parent_key: The parent key for nested objects
        separator: The separator to use between nested keys
        keep_arrays: List of field names to keep as arrays (comma-separated strings)
        
    Returns:
        A flattened dictionary
    """
    if keep_arrays is None:
        keep_arrays = []
    
    items = []
    
    if isinstance(obj, dict):
        for key, value in obj.items():
            new_key = f"{parent_key}{separator}{key}" if parent_key else key
            
            if isinstance(value, list) and key in keep_arrays:
                # Keep array as comma-separated string
                items.append((new_key, ', '.join(str(item) for item in value)))
            elif isinstance(value, (dict, list)):
                items.extend(flatten_json(value, new_key, separator, keep_arrays).items())
            else:
                items.append((new_key, value))
                
    elif isinstance(obj, list):
        for i, item in enumerate(obj):
            new_key = f"{parent_key}{separator}{i}" if parent_key else str(i)
            
            if isinstance(item, (dict, list)):
                items.extend(flatten_json(item, new_key, separator, keep_arrays).items())
            else:
                items.append((new_key, item))
    else:
        items.append((parent_key, obj))
    
    return dict(items)


def json_to_csv(json_file_path: str, csv_file_path: str = None, flatten: bool = True, keep_arrays: List[str] = None) -> str:
    """
    Convert a JSON file to CSV format.
    
    Args:
        json_file_path: Path to the input JSON file
        csv_file_path: Path to the output CSV file (optional)
        flatten: Whether to flatten nested objects
        keep_arrays: List of field names to keep as arrays (comma-separated strings)
        
    Returns:
        Path to the created CSV file
    """
    if keep_arrays is None:
        keep_arrays = []
    # Generate output filename if not provided
    if csv_file_path is None:
        json_path = Path(json_file_path)
        csv_file_path = json_path.with_suffix('.csv')
    
    # Read JSON file
    try:
        with open(json_file_path, 'r', encoding='utf-8') as json_file:
            content = json_file.read().strip()
            
        # Try to parse as regular JSON first
        try:
            data = json.loads(content)
        except json.JSONDecodeError:
            # If that fails, try parsing as JSONL (newline-delimited JSON)
            lines = content.split('\n')
            data = []
            for i, line in enumerate(lines):
                line = line.strip()
                if line:  # Skip empty lines
                    try:
                        data.append(json.loads(line))
                    except json.JSONDecodeError as e:
                        raise ValueError(f"Invalid JSON format on line {i+1}: {e}")
            
            if not data:
                raise ValueError("No valid JSON objects found")
                
    except FileNotFoundError:
        raise FileNotFoundError(f"JSON file not found: {json_file_path}")
    
    # Handle different JSON structures
    if isinstance(data, list):
        # Array of objects
        if not data:
            raise ValueError("Empty JSON array")
        
        if flatten:
            # Flatten each object in the array
            flattened_data = [flatten_json(item, keep_arrays=keep_arrays) for item in data]
            
            # Get all unique keys from all objects
            all_keys = set()
            for item in flattened_data:
                all_keys.update(item.keys())
            all_keys = sorted(list(all_keys))
            
            data_to_write = flattened_data
        else:
            # Use objects as-is (only works if all objects have the same structure)
            all_keys = set()
            for item in data:
                if isinstance(item, dict):
                    all_keys.update(item.keys())
            all_keys = sorted(list(all_keys))
            data_to_write = data
            
    elif isinstance(data, dict):
        # Single object
        if flatten:
            flattened_data = flatten_json(data, keep_arrays=keep_arrays)
            all_keys = list(flattened_data.keys())
            data_to_write = [flattened_data]
        else:
            all_keys = list(data.keys())
            data_to_write = [data]
    else:
        raise ValueError("JSON must be either an object or an array of objects")
    
    # Write to CSV
    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csv_file:
        writer = csv.DictWriter(csv_file, fieldnames=all_keys)
        writer.writeheader()
        
        for row in data_to_write:
            # Fill missing keys with empty strings
            complete_row = {key: row.get(key, '') for key in all_keys}
            writer.writerow(complete_row)
    
    return str(csv_file_path)


def main():
    parser = argparse.ArgumentParser(description='Convert JSON files to CSV format')
    parser.add_argument('input_file', help='Path to the input JSON file')
    parser.add_argument('-o', '--output', help='Path to the output CSV file (optional)')
    parser.add_argument('--no-flatten', action='store_true', 
                       help='Do not flatten nested objects (may cause issues with complex structures)')
    parser.add_argument('--keep-arrays', nargs='*', default=[], 
                       help='Field names to keep as arrays (comma-separated values) instead of flattening')
    
    args = parser.parse_args()
    
    try:
        output_path = json_to_csv(
            args.input_file, 
            args.output, 
            flatten=not args.no_flatten,
            keep_arrays=args.keep_arrays
        )
        print(f"Successfully converted JSON to CSV: {output_path}")
        
        # Show some basic info about the conversion
        with open(output_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)
            row_count = sum(1 for _ in reader)
            
        print(f"Columns: {len(headers)}")
        print(f"Rows: {row_count}")
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main() 