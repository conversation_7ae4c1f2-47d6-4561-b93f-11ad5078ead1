package main

import (
	"bufio"
	"compress/gzip"
	"crypto/tls"
	"encoding/xml"
	"flag"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"
	"unicode"
)

// -- Structures --
type SitemapIndex struct {
	Sitemaps []struct {
		Loc string `xml:"loc"`
	} `xml:"sitemap"`
}

type URLSet struct {
	URLs []struct {
		Loc string `xml:"loc"`
	} `xml:"url"`
}

// -- Globals --
var (
	wg          sync.WaitGroup
	mu          sync.Mutex
	visited     = make(map[string]bool)
	results     []PageResult
	rootHost    string
	client      *http.Client
	robotsRules []RobotsRule
)

// PageResult holds a URL and its raw HTML size in bytes
type PageResult struct {
	URL      string
	HTMLSize int
}

// RobotsRule represents a robots.txt rule
type RobotsRule struct {
	UserAgent string
	Disallow  []string
	Allow     []string
}

// WebsiteInfo holds information about the website
type WebsiteInfo struct {
	Name        string
	Title       string
	Description string
	Domain      string
	IsCrawlable bool
	RobotsRules []RobotsRule
}

// -- URL Validation and Normalization --
func normalizeURL(input string) (string, error) {
	input = strings.TrimSpace(input)
	if input == "" {
		return "", fmt.Errorf("empty URL provided")
	}

	// Remove common prefixes that users might include
	input = strings.TrimPrefix(input, "www.")

	// Check if it already has a protocol
	if !strings.HasPrefix(input, "http://") && !strings.HasPrefix(input, "https://") {
		// Default to HTTPS
		input = "https://" + input
	}

	// Parse and validate the URL
	parsedURL, err := url.Parse(input)
	if err != nil {
		return "", fmt.Errorf("invalid URL format: %v", err)
	}

	if parsedURL.Host == "" {
		return "", fmt.Errorf("no host found in URL")
	}

	// Validate domain format
	if !isValidDomain(parsedURL.Host) {
		return "", fmt.Errorf("invalid domain format: %s", parsedURL.Host)
	}

	// Handle international domains
	if hasUnicodeChars(parsedURL.Host) {
		asciiHost, err := idnaToASCII(parsedURL.Host)
		if err != nil {
			return "", fmt.Errorf("failed to convert international domain: %v", err)
		}
		parsedURL.Host = asciiHost
	}

	// Ensure we have a clean base URL
	parsedURL.Path = ""
	parsedURL.RawQuery = ""
	parsedURL.Fragment = ""

	return parsedURL.String(), nil
}

func isValidDomain(domain string) bool {
	// Basic domain validation regex
	domainRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)
	return domainRegex.MatchString(domain)
}

func hasUnicodeChars(s string) bool {
	for _, r := range s {
		if r > unicode.MaxASCII {
			return true
		}
	}
	return false
}

func idnaToASCII(domain string) (string, error) {
	// Simple ASCII conversion for international domains
	// In a real implementation, you'd use golang.org/x/net/idna
	return domain, nil // Placeholder - would need proper IDNA implementation
}

// -- Enhanced HTTP Client --
func createHTTPClient() *http.Client {
	return &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: false, // Set to true only for testing
			},
			MaxIdleConns:       10,
			IdleConnTimeout:    30 * time.Second,
			DisableCompression: false, // Enable compression support
		},
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// Allow up to 10 redirects
			if len(via) >= 10 {
				return fmt.Errorf("too many redirects")
			}
			return nil
		},
	}
}

// -- Enhanced Fetch Functions --
func fetchWithRetry(url string, maxRetries int) ([]byte, error) {
	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			fmt.Printf("🔄 Retry attempt %d for %s\n", attempt, url)
			time.Sleep(time.Duration(attempt) * time.Second) // Exponential backoff
		}

		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			lastErr = fmt.Errorf("failed to create request: %v", err)
			continue
		}

		// Set user agent to identify as a legitimate crawler
		req.Header.Set("User-Agent", "Universal-Sitemap-Crawler/1.0 (+https://github.com/sitemap-crawler)")
		req.Header.Set("Accept", "application/xml,text/xml,*/*")
		req.Header.Set("Accept-Encoding", "gzip, deflate")

		resp, err := client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("request failed: %v", err)
			continue
		}
		defer resp.Body.Close()

		// Check for HTTP error status codes
		if resp.StatusCode >= 400 {
			lastErr = fmt.Errorf("HTTP %d error", resp.StatusCode)
			continue
		}

		// Handle compressed responses
		var reader io.Reader = resp.Body
		if resp.Header.Get("Content-Encoding") == "gzip" {
			gzipReader, err := gzip.NewReader(resp.Body)
			if err != nil {
				lastErr = fmt.Errorf("failed to decompress gzip: %v", err)
				continue
			}
			defer gzipReader.Close()
			reader = gzipReader
		}

		body, err := io.ReadAll(reader)
		if err != nil {
			lastErr = fmt.Errorf("failed to read response body: %v", err)
			continue
		}

		return body, nil
	}

	return nil, fmt.Errorf("failed after %d attempts: %v", maxRetries+1, lastErr)
}

func fetchRobotsTxt(domain string) ([]string, error) {
	robotsURL := domain + "/robots.txt"
	fmt.Printf("🤖 Fetching robots.txt from %s\n", robotsURL)

	body, err := fetchWithRetry(robotsURL, 2)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch robots.txt: %v", err)
	}

	fmt.Printf("📄 robots.txt content:\n%s\n", string(body))

	// Parse lines for sitemap URLs (case-insensitive)
	var sitemaps []string
	scanner := bufio.NewScanner(strings.NewReader(string(body)))
	for scanner.Scan() {
		line := scanner.Text()
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if strings.HasPrefix(lineLower, "sitemap:") {
			sitemap := strings.TrimSpace(line[8:]) // Remove "sitemap:" prefix
			if sitemap != "" {
				sitemaps = append(sitemaps, sitemap)
			}
		}
	}

	fmt.Printf("🗺️  Found %d sitemap(s) in robots.txt\n", len(sitemaps))
	return sitemaps, nil
}

// parseRobotsTxt parses robots.txt content and returns rules
func parseRobotsTxt(domain string) ([]RobotsRule, error) {
	robotsURL := domain + "/robots.txt"
	fmt.Printf("🤖 Parsing robots.txt from %s\n", robotsURL)

	body, err := fetchWithRetry(robotsURL, 2)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch robots.txt: %v", err)
	}

	var rules []RobotsRule
	var currentRule *RobotsRule

	scanner := bufio.NewScanner(strings.NewReader(string(body)))
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		lineLower := strings.ToLower(line)

		if strings.HasPrefix(lineLower, "user-agent:") {
			// Save previous rule if exists
			if currentRule != nil {
				rules = append(rules, *currentRule)
			}
			// Start new rule
			userAgent := strings.TrimSpace(line[11:]) // Remove "user-agent:" prefix
			currentRule = &RobotsRule{
				UserAgent: userAgent,
				Disallow:  []string{},
				Allow:     []string{},
			}
		} else if currentRule != nil {
			if strings.HasPrefix(lineLower, "disallow:") {
				path := strings.TrimSpace(line[9:]) // Remove "disallow:" prefix
				if path != "" {
					currentRule.Disallow = append(currentRule.Disallow, path)
				}
			} else if strings.HasPrefix(lineLower, "allow:") {
				path := strings.TrimSpace(line[6:]) // Remove "allow:" prefix
				if path != "" {
					currentRule.Allow = append(currentRule.Allow, path)
				}
			}
		}
	}

	// Add the last rule
	if currentRule != nil {
		rules = append(rules, *currentRule)
	}

	return rules, nil
}

// isCrawlableByRobots checks if a path is crawlable based on robots.txt rules
func isCrawlableByRobots(path string, rules []RobotsRule, userAgent string) bool {
	if len(rules) == 0 {
		return true // No robots.txt means everything is allowed
	}

	// Find applicable rules for our user agent or wildcard
	var applicableRules []RobotsRule
	for _, rule := range rules {
		if rule.UserAgent == "*" || strings.Contains(strings.ToLower(rule.UserAgent), strings.ToLower(userAgent)) {
			applicableRules = append(applicableRules, rule)
		}
	}

	if len(applicableRules) == 0 {
		return true // No applicable rules means allowed
	}

	// Check rules in order - more specific rules should come first
	for _, rule := range applicableRules {
		// Check Allow rules first (they take precedence)
		for _, allowPath := range rule.Allow {
			if matchesRobotsPath(path, allowPath) {
				return true
			}
		}

		// Check Disallow rules
		for _, disallowPath := range rule.Disallow {
			if matchesRobotsPath(path, disallowPath) {
				return false
			}
		}
	}

	return true // Default to allowed if no rules match
}

// matchesRobotsPath checks if a path matches a robots.txt pattern
func matchesRobotsPath(path, pattern string) bool {
	if pattern == "" {
		return false
	}

	if pattern == "/" {
		return true // Disallow all
	}

	// Simple prefix matching (robots.txt uses prefix matching)
	return strings.HasPrefix(path, pattern)
}

// validateWebsiteCrawlability checks if the website is crawlable
func validateWebsiteCrawlability(domain string) (bool, []RobotsRule, error) {
	rules, err := parseRobotsTxt(domain)
	if err != nil {
		// If robots.txt doesn't exist or can't be fetched, assume crawlable
		fmt.Printf("⚠️  Could not fetch robots.txt: %v (assuming crawlable)\n", err)
		return true, nil, nil
	}

	// Check if our crawler is allowed to crawl the root path
	userAgent := "Universal-Sitemap-Crawler"
	isCrawlable := isCrawlableByRobots("/", rules, userAgent)

	if isCrawlable {
		fmt.Printf("✅ Website is crawlable by our bot\n")
	} else {
		fmt.Printf("❌ Website disallows crawling by our bot\n")
	}

	return isCrawlable, rules, nil
}

// getWebsiteInfo extracts website information including name and title
func getWebsiteInfo(domain string) (*WebsiteInfo, error) {
	fmt.Printf("🌐 Extracting website information from %s\n", domain)

	// Fetch the homepage
	body, err := fetchWithRetry(domain, 2)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch homepage: %v", err)
	}

	htmlContent := string(body)

	// Extract title
	title := extractTitle(htmlContent)

	// Extract meta description
	description := extractMetaDescription(htmlContent)

	// Generate website name from domain or title
	name := generateWebsiteName(domain, title)

	// Check crawlability
	isCrawlable, robotsRules, _ := validateWebsiteCrawlability(domain)

	parsedURL, _ := url.Parse(domain)

	return &WebsiteInfo{
		Name:        name,
		Title:       title,
		Description: description,
		Domain:      parsedURL.Host,
		IsCrawlable: isCrawlable,
		RobotsRules: robotsRules,
	}, nil
}

// extractTitle extracts the title from HTML content
func extractTitle(html string) string {
	// Look for <title> tag (case-insensitive)
	titleRegex := regexp.MustCompile(`(?i)<title[^>]*>(.*?)</title>`)
	matches := titleRegex.FindStringSubmatch(html)
	if len(matches) > 1 {
		title := strings.TrimSpace(matches[1])
		// Decode HTML entities (basic ones)
		title = strings.ReplaceAll(title, "&amp;", "&")
		title = strings.ReplaceAll(title, "&lt;", "<")
		title = strings.ReplaceAll(title, "&gt;", ">")
		title = strings.ReplaceAll(title, "&quot;", "\"")
		title = strings.ReplaceAll(title, "&#39;", "'")
		return title
	}
	return ""
}

// extractMetaDescription extracts meta description from HTML content
func extractMetaDescription(html string) string {
	// Look for meta description tag (case-insensitive)
	descRegex := regexp.MustCompile(`(?i)<meta[^>]*name\s*=\s*["']description["'][^>]*content\s*=\s*["']([^"']*?)["']`)
	matches := descRegex.FindStringSubmatch(html)
	if len(matches) > 1 {
		desc := strings.TrimSpace(matches[1])
		// Decode HTML entities (basic ones)
		desc = strings.ReplaceAll(desc, "&amp;", "&")
		desc = strings.ReplaceAll(desc, "&lt;", "<")
		desc = strings.ReplaceAll(desc, "&gt;", ">")
		desc = strings.ReplaceAll(desc, "&quot;", "\"")
		desc = strings.ReplaceAll(desc, "&#39;", "'")
		return desc
	}

	// Try alternative format
	descRegex2 := regexp.MustCompile(`(?i)<meta[^>]*content\s*=\s*["']([^"']*?)["'][^>]*name\s*=\s*["']description["']`)
	matches2 := descRegex2.FindStringSubmatch(html)
	if len(matches2) > 1 {
		return strings.TrimSpace(matches2[1])
	}

	return ""
}

// generateWebsiteName generates a website name from domain and title
func generateWebsiteName(domain, title string) string {
	parsedURL, err := url.Parse(domain)
	if err != nil {
		return domain
	}

	host := parsedURL.Host

	// Remove www. prefix
	if strings.HasPrefix(host, "www.") {
		host = host[4:]
	}

	// If we have a title, use it (but clean it up)
	if title != "" {
		// Remove common suffixes from title that might include domain
		cleanTitle := title
		domainParts := strings.Split(host, ".")
		if len(domainParts) > 0 {
			mainDomain := domainParts[0]
			// Remove domain name from title if present
			titleLower := strings.ToLower(cleanTitle)
			domainLower := strings.ToLower(mainDomain)

			// Remove patterns like "- Domain.com", "| Domain", etc.
			patterns := []string{
				" - " + domainLower,
				" | " + domainLower,
				" :: " + domainLower,
				" | " + strings.Title(domainLower),
				" - " + strings.Title(domainLower),
			}

			for _, pattern := range patterns {
				if strings.Contains(titleLower, strings.ToLower(pattern)) {
					cleanTitle = strings.TrimSpace(strings.Split(cleanTitle, pattern)[0])
					break
				}
			}
		}

		if cleanTitle != "" && len(cleanTitle) < 100 { // Reasonable length
			return cleanTitle
		}
	}

	// Fallback to domain-based name
	domainParts := strings.Split(host, ".")
	if len(domainParts) > 0 {
		mainDomain := domainParts[0]
		// Capitalize first letter
		if len(mainDomain) > 0 {
			return strings.ToUpper(string(mainDomain[0])) + mainDomain[1:]
		}
	}

	return host
}

// -- Comprehensive Sitemap Discovery --
func getComprehensiveSitemapURLs(domain string) []string {
	return []string{
		// Standard locations
		domain + "/sitemap.xml",
		domain + "/sitemaps.xml",
		domain + "/site-map.xml",
		domain + "/sitemap_index.xml",
		domain + "/sitemap-index.xml",

		// WordPress
		domain + "/wp-sitemap.xml",
		domain + "/wp-content/sitemap.xml",

		// Drupal
		domain + "/xmlsitemap.xml",
		domain + "/sitemap/sitemap.xml",

		// Joomla
		domain + "/index.php?option=com_xmap&sitemap=1",
		domain + "/component/xmap/xml/1",

		// E-commerce platforms
		domain + "/product-sitemap.xml",
		domain + "/category-sitemap.xml",
		domain + "/products.xml",
		domain + "/catalog.xml",

		// Other common patterns
		domain + "/feeds/sitemap.xml",
		domain + "/xml/sitemap.xml",
		domain + "/sitemap/sitemap.xml",
		domain + "/sitemaps/sitemap.xml",
	}
}

func tryComprehensiveSitemapDiscovery(domain string) []string {
	fmt.Printf("🔍 Starting comprehensive sitemap discovery for %s\n", domain)

	fallbackURLs := getComprehensiveSitemapURLs(domain)
	var validSitemaps []string

	for i, sitemapURL := range fallbackURLs {
		fmt.Printf("🔍 [%d/%d] Trying: %s\n", i+1, len(fallbackURLs), sitemapURL)

		body, err := fetchWithRetry(sitemapURL, 1) // Only 1 retry for discovery
		if err != nil {
			fmt.Printf("❌ Failed: %v\n", err)
			continue
		}

		if isValidXMLSitemap(body) {
			fmt.Printf("✅ Valid sitemap found: %s\n", sitemapURL)
			validSitemaps = append(validSitemaps, sitemapURL)

			// For efficiency, stop after finding the first valid sitemap
			// unless it's a sitemap index, then we might want to continue
			if strings.Contains(string(body), "<sitemapindex") {
				fmt.Printf("📋 Found sitemap index, continuing search for additional sitemaps...\n")
			} else {
				break
			}
		} else {
			fmt.Printf("❌ Not a valid XML sitemap\n")
		}
	}

	fmt.Printf("🎯 Discovery complete: found %d valid sitemap(s)\n", len(validSitemaps))
	return validSitemaps
}

func isValidXMLSitemap(body []byte) bool {
	bodyStr := strings.TrimSpace(string(body))

	// Check for XML declaration or sitemap root elements
	validPrefixes := []string{
		"<?xml",
		"<urlset",
		"<sitemapindex",
		"<sitemap", // Some sitemaps start directly with sitemap tag
	}

	bodyLower := strings.ToLower(bodyStr)
	for _, prefix := range validPrefixes {
		if strings.HasPrefix(bodyLower, prefix) {
			return true
		}
	}

	// Additional check for XML content within the first 200 characters
	if len(bodyStr) > 200 {
		bodyStr = bodyStr[:200]
	}

	return strings.Contains(strings.ToLower(bodyStr), "<loc>") ||
		strings.Contains(strings.ToLower(bodyStr), "<sitemap>") ||
		strings.Contains(strings.ToLower(bodyStr), "<url>")
}

// -- Enhanced Sitemap Parsing --
func parseSitemap(sitemapURL string) {
	defer wg.Done()

	mu.Lock()
	if visited[sitemapURL] {
		mu.Unlock()
		return
	}
	visited[sitemapURL] = true
	mu.Unlock()

	fmt.Printf("🔍 Parsing sitemap: %s\n", sitemapURL)

	data, err := fetchWithRetry(sitemapURL, 2)
	if err != nil {
		fmt.Printf("❌ Failed to fetch sitemap: %v\n", err)
		return
	}

	// Try sitemap index first
	var index SitemapIndex
	if err := xml.Unmarshal(data, &index); err == nil && len(index.Sitemaps) > 0 {
		fmt.Printf("📋 Found sitemap index with %d sitemaps\n", len(index.Sitemaps))
		for _, s := range index.Sitemaps {
			sitemapLoc := strings.TrimSpace(s.Loc)
			if sitemapLoc != "" {
				wg.Add(1)
				go parseSitemap(sitemapLoc)
			}
		}
		return
	}

	// Try flat URL set
	var urlSet URLSet
	if err := xml.Unmarshal(data, &urlSet); err == nil && len(urlSet.URLs) > 0 {
		fmt.Printf("📄 Found URL set with %d URLs\n", len(urlSet.URLs))
		allowedCount := 0
		blockedCount := 0
		for _, u := range urlSet.URLs {
			link := strings.TrimSpace(u.Loc)
			if isValidPageURL(link) {
				if isURLAllowedByRobots(link, robotsRules) {
					wg.Add(1)
					go fetchPageSize(link)
					allowedCount++
				} else {
					fmt.Printf("🚫 Skipping %s (blocked by robots.txt)\n", link)
					blockedCount++
				}
			}
		}
		if blockedCount > 0 {
			fmt.Printf("⚠️  Skipped %d URLs due to robots.txt restrictions\n", blockedCount)
		}
		fmt.Printf("✅ Processing %d allowed URLs\n", allowedCount)
		return
	}

	// Try to extract URLs from any XML format
	fmt.Printf("🔧 Attempting manual URL extraction from XML\n")
	urls := extractURLsFromXML(string(data))
	fmt.Printf("🔗 Extracted %d URLs manually\n", len(urls))
	allowedCount := 0
	blockedCount := 0
	for _, link := range urls {
		if isValidPageURL(link) {
			if isURLAllowedByRobots(link, robotsRules) {
				wg.Add(1)
				go fetchPageSize(link)
				allowedCount++
			} else {
				fmt.Printf("🚫 Skipping %s (blocked by robots.txt)\n", link)
				blockedCount++
			}
		}
	}
	if blockedCount > 0 {
		fmt.Printf("⚠️  Skipped %d URLs due to robots.txt restrictions\n", blockedCount)
	}
	fmt.Printf("✅ Processing %d allowed URLs\n", allowedCount)
}

func isValidPageURL(pageURL string) bool {
	if pageURL == "" {
		return false
	}

	// Parse URL to validate
	parsedURL, err := url.Parse(pageURL)
	if err != nil {
		return false
	}

	// Check if it's from the same domain or subdomain
	if !strings.Contains(parsedURL.Host, rootHost) && !strings.Contains(rootHost, parsedURL.Host) {
		return false
	}

	// Skip common non-page URLs
	path := strings.ToLower(parsedURL.Path)
	skipExtensions := []string{".jpg", ".jpeg", ".png", ".gif", ".pdf", ".zip", ".css", ".js", ".ico"}
	for _, ext := range skipExtensions {
		if strings.HasSuffix(path, ext) {
			return false
		}
	}

	return true
}

// isURLAllowedByRobots checks if a specific URL is allowed by robots.txt
func isURLAllowedByRobots(pageURL string, robotsRules []RobotsRule) bool {
	if len(robotsRules) == 0 {
		return true // No robots.txt means everything is allowed
	}

	parsedURL, err := url.Parse(pageURL)
	if err != nil {
		return false
	}

	path := parsedURL.Path
	if path == "" {
		path = "/"
	}

	userAgent := "Universal-Sitemap-Crawler"
	return isCrawlableByRobots(path, robotsRules, userAgent)
}

func fetchPageSize(pageURL string) {
	defer wg.Done()

	fmt.Printf("📥 Fetching: %s\n", pageURL)

	body, err := fetchWithRetry(pageURL, 1)
	if err != nil {
		fmt.Printf("❌ Error fetching %s: %v\n", pageURL, err)
		return
	}

	mu.Lock()
	results = append(results, PageResult{URL: pageURL, HTMLSize: len(body)})
	fmt.Printf("✅ Fetched %s (%d bytes)\n", pageURL, len(body))
	mu.Unlock()
}

func extractURLsFromXML(xmlContent string) []string {
	var urls []string
	lines := strings.Split(xmlContent, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "<loc>") && strings.HasSuffix(line, "</loc>") {
			url := strings.TrimPrefix(line, "<loc>")
			url = strings.TrimSuffix(url, "</loc>")
			url = strings.TrimSpace(url)
			if url != "" {
				urls = append(urls, url)
			}
		}
	}
	return urls
}

// -- Enhanced Main Function --
func main() {
	input := flag.String("url", "", "Website URL (e.g., example.com or https://example.com)")
	verbose := flag.Bool("v", false, "Verbose output")
	maxPages := flag.Int("max", 100, "Maximum number of pages to crawl (0 = unlimited)")
	flag.Parse()

	if *input == "" {
		fmt.Println("❌ Please provide a website URL using the -url flag.")
		fmt.Println("Examples:")
		fmt.Println("  go run universal_sitemap_crawler.go -url example.com")
		fmt.Println("  go run universal_sitemap_crawler.go -url https://www.example.com")
		return
	}

	// Initialize HTTP client
	client = createHTTPClient()

	// Normalize and validate the input URL
	normalizedURL, err := normalizeURL(*input)
	if err != nil {
		fmt.Printf("❌ URL validation failed: %v\n", err)
		return
	}

	parsed, err := url.Parse(normalizedURL)
	if err != nil {
		fmt.Printf("❌ Failed to parse normalized URL: %v\n", err)
		return
	}

	rootHost = parsed.Host
	domain := parsed.Scheme + "://" + rootHost

	fmt.Printf("🚀 Starting universal sitemap crawl for: %s\n", domain)
	fmt.Printf("🎯 Root host: %s\n", rootHost)
	if *maxPages > 0 {
		fmt.Printf("📊 Max pages limit: %d\n", *maxPages)
	}

	// Get website information
	websiteInfo, err := getWebsiteInfo(domain)
	if err != nil {
		fmt.Printf("⚠️  Could not extract website info: %v\n", err)
	} else {
		// Store robots rules globally for URL validation
		robotsRules = websiteInfo.RobotsRules
		fmt.Printf("\n🌐 Website Information:\n")
		fmt.Printf("  📛 Name: %s\n", websiteInfo.Name)
		if websiteInfo.Title != "" {
			fmt.Printf("  📄 Title: %s\n", websiteInfo.Title)
		}
		if websiteInfo.Description != "" {
			fmt.Printf("  📝 Description: %s\n", websiteInfo.Description)
		}
		fmt.Printf("  🌍 Domain: %s\n", websiteInfo.Domain)
		if websiteInfo.IsCrawlable {
			fmt.Printf("  🤖 Crawlable: ✅ Yes\n")
		} else {
			fmt.Printf("  🤖 Crawlable: ❌ No (blocked by robots.txt)\n")
			fmt.Println("\n⚠️  Warning: This website's robots.txt disallows crawling.")
			fmt.Println("   Proceeding anyway for sitemap discovery, but respect the robots.txt rules.")
		}

		if len(websiteInfo.RobotsRules) > 0 {
			fmt.Printf("  📋 Robots.txt rules found: %d\n", len(websiteInfo.RobotsRules))
			if *verbose {
				for i, rule := range websiteInfo.RobotsRules {
					fmt.Printf("    Rule %d - User-Agent: %s\n", i+1, rule.UserAgent)
					if len(rule.Disallow) > 0 {
						fmt.Printf("      Disallow: %v\n", rule.Disallow)
					}
					if len(rule.Allow) > 0 {
						fmt.Printf("      Allow: %v\n", rule.Allow)
					}
				}
			}
		}
		fmt.Println()
	}

	// Step 1: Try to get sitemaps from robots.txt
	var sitemaps []string
	robotsSitemaps, err := fetchRobotsTxt(domain)
	if err != nil {
		fmt.Printf("⚠️  Failed to fetch robots.txt: %v\n", err)
	} else if len(robotsSitemaps) > 0 {
		// Validate robots.txt sitemaps
		fmt.Printf("🔍 Validating %d sitemap(s) from robots.txt\n", len(robotsSitemaps))
		for _, sitemap := range robotsSitemaps {
			body, err := fetchWithRetry(sitemap, 1)
			if err != nil {
				fmt.Printf("❌ Failed to validate %s: %v\n", sitemap, err)
				continue
			}

			if isValidXMLSitemap(body) {
				fmt.Printf("✅ Validated sitemap: %s\n", sitemap)
				sitemaps = append(sitemaps, sitemap)
			} else {
				fmt.Printf("❌ Invalid XML sitemap: %s\n", sitemap)
			}
		}
	}

	// Step 2: If no valid sitemaps from robots.txt, try comprehensive discovery
	if len(sitemaps) == 0 {
		fmt.Printf("⚠️  No valid sitemaps found in robots.txt. Starting comprehensive discovery...\n")
		sitemaps = tryComprehensiveSitemapDiscovery(domain)
	}

	// Step 3: Process found sitemaps
	if len(sitemaps) == 0 {
		fmt.Printf("❌ No valid sitemaps found for %s\n", domain)
		fmt.Println("\n💡 Suggestions:")
		fmt.Println("  • Check if the website has a sitemap manually")
		fmt.Println("  • Try different URL variations (with/without www)")
		fmt.Println("  • Verify the website is accessible")
		fmt.Println("  • Some websites may not have XML sitemaps")
		return
	}

	fmt.Printf("📍 Found %d valid sitemap(s) to process\n", len(sitemaps))
	for i, sitemap := range sitemaps {
		fmt.Printf("  %d. %s\n", i+1, sitemap)
	}

	// Start crawling
	fmt.Printf("\n🕷️  Starting sitemap crawl...\n")
	for _, sitemap := range sitemaps {
		wg.Add(1)
		go parseSitemap(sitemap)
	}

	wg.Wait()

	// Display results
	fmt.Printf("\n🎉 Crawl completed!\n")
	fmt.Printf("✅ Total Pages Found: %d\n", len(results))

	if len(results) == 0 {
		fmt.Println("⚠️  No pages were extracted from the sitemaps")
		return
	}

	totalSize := 0
	if *verbose {
		fmt.Println("\n📋 Detailed Results:")
		for i, r := range results {
			if *maxPages > 0 && i >= *maxPages {
				fmt.Printf("... (showing first %d pages, use -max=0 for all)\n", *maxPages)
				break
			}
			fmt.Printf("  %d. %s (%d bytes)\n", i+1, r.URL, r.HTMLSize)
			totalSize += r.HTMLSize
		}
	} else {
		// Calculate total size for all results
		for _, r := range results {
			totalSize += r.HTMLSize
		}
		fmt.Printf("📄 Use -v flag for detailed page list\n")
	}

	fmt.Printf("\n📦 Total HTML size: %.2f KB (%.2f MB)\n",
		float64(totalSize)/1024, float64(totalSize)/(1024*1024))

	if len(results) > 10 {
		fmt.Printf("🏆 Largest pages:\n")
		// Simple sorting to show largest pages
		for i := 0; i < len(results) && i < 5; i++ {
			largest := results[i]
			largestIdx := i
			for j := i + 1; j < len(results); j++ {
				if results[j].HTMLSize > largest.HTMLSize {
					largest = results[j]
					largestIdx = j
				}
			}
			if largestIdx != i {
				results[i], results[largestIdx] = results[largestIdx], results[i]
			}
			fmt.Printf("  %d. %s (%.1f KB)\n", i+1, results[i].URL, float64(results[i].HTMLSize)/1024)
		}
	}
}
