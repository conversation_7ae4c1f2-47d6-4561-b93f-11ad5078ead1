package main

import (
	"bufio"
	"compress/gzip"
	"crypto/tls"
	"encoding/xml"
	"flag"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"
	"unicode"
)

// -- Structures --
type SitemapIndex struct {
	Sitemaps []struct {
		Loc string `xml:"loc"`
	} `xml:"sitemap"`
}

type URLSet struct {
	URLs []struct {
		Loc string `xml:"loc"`
	} `xml:"url"`
}

// -- Globals --
var (
	wg       sync.WaitGroup
	mu       sync.Mutex
	visited  = make(map[string]bool)
	results  []PageResult
	rootHost string
	client   *http.Client
)

// PageResult holds a URL and its raw HTML size in bytes
type PageResult struct {
	URL      string
	HTMLSize int
}

// -- URL Validation and Normalization --
func normalizeURL(input string) (string, error) {
	input = strings.TrimSpace(input)
	if input == "" {
		return "", fmt.Errorf("empty URL provided")
	}

	// Remove common prefixes that users might include
	input = strings.TrimPrefix(input, "www.")

	// Check if it already has a protocol
	if !strings.HasPrefix(input, "http://") && !strings.HasPrefix(input, "https://") {
		// Default to HTTPS
		input = "https://" + input
	}

	// Parse and validate the URL
	parsedURL, err := url.Parse(input)
	if err != nil {
		return "", fmt.Errorf("invalid URL format: %v", err)
	}

	if parsedURL.Host == "" {
		return "", fmt.Errorf("no host found in URL")
	}

	// Validate domain format
	if !isValidDomain(parsedURL.Host) {
		return "", fmt.Errorf("invalid domain format: %s", parsedURL.Host)
	}

	// Handle international domains
	if hasUnicodeChars(parsedURL.Host) {
		asciiHost, err := idnaToASCII(parsedURL.Host)
		if err != nil {
			return "", fmt.Errorf("failed to convert international domain: %v", err)
		}
		parsedURL.Host = asciiHost
	}

	// Ensure we have a clean base URL
	parsedURL.Path = ""
	parsedURL.RawQuery = ""
	parsedURL.Fragment = ""

	return parsedURL.String(), nil
}

func isValidDomain(domain string) bool {
	// Basic domain validation regex
	domainRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)
	return domainRegex.MatchString(domain)
}

func hasUnicodeChars(s string) bool {
	for _, r := range s {
		if r > unicode.MaxASCII {
			return true
		}
	}
	return false
}

func idnaToASCII(domain string) (string, error) {
	// Simple ASCII conversion for international domains
	// In a real implementation, you'd use golang.org/x/net/idna
	return domain, nil // Placeholder - would need proper IDNA implementation
}

// -- Enhanced HTTP Client --
func createHTTPClient() *http.Client {
	return &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: false, // Set to true only for testing
			},
			MaxIdleConns:       10,
			IdleConnTimeout:    30 * time.Second,
			DisableCompression: false, // Enable compression support
		},
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// Allow up to 10 redirects
			if len(via) >= 10 {
				return fmt.Errorf("too many redirects")
			}
			return nil
		},
	}
}

// -- Enhanced Fetch Functions --
func fetchWithRetry(url string, maxRetries int) ([]byte, error) {
	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			fmt.Printf("🔄 Retry attempt %d for %s\n", attempt, url)
			time.Sleep(time.Duration(attempt) * time.Second) // Exponential backoff
		}

		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			lastErr = fmt.Errorf("failed to create request: %v", err)
			continue
		}

		// Set user agent to identify as a legitimate crawler
		req.Header.Set("User-Agent", "Universal-Sitemap-Crawler/1.0 (+https://github.com/sitemap-crawler)")
		req.Header.Set("Accept", "application/xml,text/xml,*/*")
		req.Header.Set("Accept-Encoding", "gzip, deflate")

		resp, err := client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("request failed: %v", err)
			continue
		}
		defer resp.Body.Close()

		// Check for HTTP error status codes
		if resp.StatusCode >= 400 {
			lastErr = fmt.Errorf("HTTP %d error", resp.StatusCode)
			continue
		}

		// Handle compressed responses
		var reader io.Reader = resp.Body
		if resp.Header.Get("Content-Encoding") == "gzip" {
			gzipReader, err := gzip.NewReader(resp.Body)
			if err != nil {
				lastErr = fmt.Errorf("failed to decompress gzip: %v", err)
				continue
			}
			defer gzipReader.Close()
			reader = gzipReader
		}

		body, err := io.ReadAll(reader)
		if err != nil {
			lastErr = fmt.Errorf("failed to read response body: %v", err)
			continue
		}

		return body, nil
	}

	return nil, fmt.Errorf("failed after %d attempts: %v", maxRetries+1, lastErr)
}

func fetchRobotsTxt(domain string) ([]string, error) {
	robotsURL := domain + "/robots.txt"
	fmt.Printf("🤖 Fetching robots.txt from %s\n", robotsURL)

	body, err := fetchWithRetry(robotsURL, 2)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch robots.txt: %v", err)
	}

	fmt.Printf("📄 robots.txt content:\n%s\n", string(body))

	// Parse lines for sitemap URLs (case-insensitive)
	var sitemaps []string
	scanner := bufio.NewScanner(strings.NewReader(string(body)))
	for scanner.Scan() {
		line := scanner.Text()
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if strings.HasPrefix(lineLower, "sitemap:") {
			sitemap := strings.TrimSpace(line[8:]) // Remove "sitemap:" prefix
			if sitemap != "" {
				sitemaps = append(sitemaps, sitemap)
			}
		}
	}

	fmt.Printf("🗺️  Found %d sitemap(s) in robots.txt\n", len(sitemaps))
	return sitemaps, nil
}

// -- Comprehensive Sitemap Discovery --
func getComprehensiveSitemapURLs(domain string) []string {
	return []string{
		// Standard locations
		domain + "/sitemap.xml",
		domain + "/sitemaps.xml",
		domain + "/site-map.xml",
		domain + "/sitemap_index.xml",
		domain + "/sitemap-index.xml",

		// WordPress
		domain + "/wp-sitemap.xml",
		domain + "/wp-content/sitemap.xml",

		// Drupal
		domain + "/xmlsitemap.xml",
		domain + "/sitemap/sitemap.xml",

		// Joomla
		domain + "/index.php?option=com_xmap&sitemap=1",
		domain + "/component/xmap/xml/1",

		// E-commerce platforms
		domain + "/product-sitemap.xml",
		domain + "/category-sitemap.xml",
		domain + "/products.xml",
		domain + "/catalog.xml",

		// Other common patterns
		domain + "/feeds/sitemap.xml",
		domain + "/xml/sitemap.xml",
		domain + "/sitemap/sitemap.xml",
		domain + "/sitemaps/sitemap.xml",
	}
}

func tryComprehensiveSitemapDiscovery(domain string) []string {
	fmt.Printf("🔍 Starting comprehensive sitemap discovery for %s\n", domain)

	fallbackURLs := getComprehensiveSitemapURLs(domain)
	var validSitemaps []string

	for i, sitemapURL := range fallbackURLs {
		fmt.Printf("🔍 [%d/%d] Trying: %s\n", i+1, len(fallbackURLs), sitemapURL)

		body, err := fetchWithRetry(sitemapURL, 1) // Only 1 retry for discovery
		if err != nil {
			fmt.Printf("❌ Failed: %v\n", err)
			continue
		}

		if isValidXMLSitemap(body) {
			fmt.Printf("✅ Valid sitemap found: %s\n", sitemapURL)
			validSitemaps = append(validSitemaps, sitemapURL)

			// For efficiency, stop after finding the first valid sitemap
			// unless it's a sitemap index, then we might want to continue
			if strings.Contains(string(body), "<sitemapindex") {
				fmt.Printf("📋 Found sitemap index, continuing search for additional sitemaps...\n")
			} else {
				break
			}
		} else {
			fmt.Printf("❌ Not a valid XML sitemap\n")
		}
	}

	fmt.Printf("🎯 Discovery complete: found %d valid sitemap(s)\n", len(validSitemaps))
	return validSitemaps
}

func isValidXMLSitemap(body []byte) bool {
	bodyStr := strings.TrimSpace(string(body))

	// Check for XML declaration or sitemap root elements
	validPrefixes := []string{
		"<?xml",
		"<urlset",
		"<sitemapindex",
		"<sitemap", // Some sitemaps start directly with sitemap tag
	}

	bodyLower := strings.ToLower(bodyStr)
	for _, prefix := range validPrefixes {
		if strings.HasPrefix(bodyLower, prefix) {
			return true
		}
	}

	// Additional check for XML content within the first 200 characters
	if len(bodyStr) > 200 {
		bodyStr = bodyStr[:200]
	}

	return strings.Contains(strings.ToLower(bodyStr), "<loc>") ||
		strings.Contains(strings.ToLower(bodyStr), "<sitemap>") ||
		strings.Contains(strings.ToLower(bodyStr), "<url>")
}

// -- Enhanced Sitemap Parsing --
func parseSitemap(sitemapURL string) {
	defer wg.Done()

	mu.Lock()
	if visited[sitemapURL] {
		mu.Unlock()
		return
	}
	visited[sitemapURL] = true
	mu.Unlock()

	fmt.Printf("🔍 Parsing sitemap: %s\n", sitemapURL)

	data, err := fetchWithRetry(sitemapURL, 2)
	if err != nil {
		fmt.Printf("❌ Failed to fetch sitemap: %v\n", err)
		return
	}

	// Try sitemap index first
	var index SitemapIndex
	if err := xml.Unmarshal(data, &index); err == nil && len(index.Sitemaps) > 0 {
		fmt.Printf("📋 Found sitemap index with %d sitemaps\n", len(index.Sitemaps))
		for _, s := range index.Sitemaps {
			sitemapLoc := strings.TrimSpace(s.Loc)
			if sitemapLoc != "" {
				wg.Add(1)
				go parseSitemap(sitemapLoc)
			}
		}
		return
	}

	// Try flat URL set
	var urlSet URLSet
	if err := xml.Unmarshal(data, &urlSet); err == nil && len(urlSet.URLs) > 0 {
		fmt.Printf("📄 Found URL set with %d URLs\n", len(urlSet.URLs))
		for _, u := range urlSet.URLs {
			link := strings.TrimSpace(u.Loc)
			if isValidPageURL(link) {
				wg.Add(1)
				go fetchPageSize(link)
			}
		}
		return
	}

	// Try to extract URLs from any XML format
	fmt.Printf("🔧 Attempting manual URL extraction from XML\n")
	urls := extractURLsFromXML(string(data))
	fmt.Printf("🔗 Extracted %d URLs manually\n", len(urls))
	for _, link := range urls {
		if isValidPageURL(link) {
			wg.Add(1)
			go fetchPageSize(link)
		}
	}
}

func isValidPageURL(pageURL string) bool {
	if pageURL == "" {
		return false
	}

	// Parse URL to validate
	parsedURL, err := url.Parse(pageURL)
	if err != nil {
		return false
	}

	// Check if it's from the same domain or subdomain
	if !strings.Contains(parsedURL.Host, rootHost) && !strings.Contains(rootHost, parsedURL.Host) {
		return false
	}

	// Skip common non-page URLs
	path := strings.ToLower(parsedURL.Path)
	skipExtensions := []string{".jpg", ".jpeg", ".png", ".gif", ".pdf", ".zip", ".css", ".js", ".ico"}
	for _, ext := range skipExtensions {
		if strings.HasSuffix(path, ext) {
			return false
		}
	}

	return true
}

func fetchPageSize(pageURL string) {
	defer wg.Done()

	fmt.Printf("📥 Fetching: %s\n", pageURL)

	body, err := fetchWithRetry(pageURL, 1)
	if err != nil {
		fmt.Printf("❌ Error fetching %s: %v\n", pageURL, err)
		return
	}

	mu.Lock()
	results = append(results, PageResult{URL: pageURL, HTMLSize: len(body)})
	fmt.Printf("✅ Fetched %s (%d bytes)\n", pageURL, len(body))
	mu.Unlock()
}

func extractURLsFromXML(xmlContent string) []string {
	var urls []string
	lines := strings.Split(xmlContent, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "<loc>") && strings.HasSuffix(line, "</loc>") {
			url := strings.TrimPrefix(line, "<loc>")
			url = strings.TrimSuffix(url, "</loc>")
			url = strings.TrimSpace(url)
			if url != "" {
				urls = append(urls, url)
			}
		}
	}
	return urls
}

// -- Enhanced Main Function --
func main() {
	input := flag.String("url", "", "Website URL (e.g., example.com or https://example.com)")
	verbose := flag.Bool("v", false, "Verbose output")
	maxPages := flag.Int("max", 100, "Maximum number of pages to crawl (0 = unlimited)")
	flag.Parse()

	if *input == "" {
		fmt.Println("❌ Please provide a website URL using the -url flag.")
		fmt.Println("Examples:")
		fmt.Println("  go run universal_sitemap_crawler.go -url example.com")
		fmt.Println("  go run universal_sitemap_crawler.go -url https://www.example.com")
		return
	}

	// Initialize HTTP client
	client = createHTTPClient()

	// Normalize and validate the input URL
	normalizedURL, err := normalizeURL(*input)
	if err != nil {
		fmt.Printf("❌ URL validation failed: %v\n", err)
		return
	}

	parsed, err := url.Parse(normalizedURL)
	if err != nil {
		fmt.Printf("❌ Failed to parse normalized URL: %v\n", err)
		return
	}

	rootHost = parsed.Host
	domain := parsed.Scheme + "://" + rootHost

	fmt.Printf("🚀 Starting universal sitemap crawl for: %s\n", domain)
	fmt.Printf("🎯 Root host: %s\n", rootHost)
	if *maxPages > 0 {
		fmt.Printf("📊 Max pages limit: %d\n", *maxPages)
	}

	// Step 1: Try to get sitemaps from robots.txt
	var sitemaps []string
	robotsSitemaps, err := fetchRobotsTxt(domain)
	if err != nil {
		fmt.Printf("⚠️  Failed to fetch robots.txt: %v\n", err)
	} else if len(robotsSitemaps) > 0 {
		// Validate robots.txt sitemaps
		fmt.Printf("🔍 Validating %d sitemap(s) from robots.txt\n", len(robotsSitemaps))
		for _, sitemap := range robotsSitemaps {
			body, err := fetchWithRetry(sitemap, 1)
			if err != nil {
				fmt.Printf("❌ Failed to validate %s: %v\n", sitemap, err)
				continue
			}

			if isValidXMLSitemap(body) {
				fmt.Printf("✅ Validated sitemap: %s\n", sitemap)
				sitemaps = append(sitemaps, sitemap)
			} else {
				fmt.Printf("❌ Invalid XML sitemap: %s\n", sitemap)
			}
		}
	}

	// Step 2: If no valid sitemaps from robots.txt, try comprehensive discovery
	if len(sitemaps) == 0 {
		fmt.Printf("⚠️  No valid sitemaps found in robots.txt. Starting comprehensive discovery...\n")
		sitemaps = tryComprehensiveSitemapDiscovery(domain)
	}

	// Step 3: Process found sitemaps
	if len(sitemaps) == 0 {
		fmt.Printf("❌ No valid sitemaps found for %s\n", domain)
		fmt.Println("\n💡 Suggestions:")
		fmt.Println("  • Check if the website has a sitemap manually")
		fmt.Println("  • Try different URL variations (with/without www)")
		fmt.Println("  • Verify the website is accessible")
		fmt.Println("  • Some websites may not have XML sitemaps")
		return
	}

	fmt.Printf("📍 Found %d valid sitemap(s) to process\n", len(sitemaps))
	for i, sitemap := range sitemaps {
		fmt.Printf("  %d. %s\n", i+1, sitemap)
	}

	// Start crawling
	fmt.Printf("\n🕷️  Starting sitemap crawl...\n")
	for _, sitemap := range sitemaps {
		wg.Add(1)
		go parseSitemap(sitemap)
	}

	wg.Wait()

	// Display results
	fmt.Printf("\n🎉 Crawl completed!\n")
	fmt.Printf("✅ Total Pages Found: %d\n", len(results))

	if len(results) == 0 {
		fmt.Println("⚠️  No pages were extracted from the sitemaps")
		return
	}

	totalSize := 0
	if *verbose {
		fmt.Println("\n📋 Detailed Results:")
		for i, r := range results {
			if *maxPages > 0 && i >= *maxPages {
				fmt.Printf("... (showing first %d pages, use -max=0 for all)\n", *maxPages)
				break
			}
			fmt.Printf("  %d. %s (%d bytes)\n", i+1, r.URL, r.HTMLSize)
			totalSize += r.HTMLSize
		}
	} else {
		// Calculate total size for all results
		for _, r := range results {
			totalSize += r.HTMLSize
		}
		fmt.Printf("📄 Use -v flag for detailed page list\n")
	}

	fmt.Printf("\n📦 Total HTML size: %.2f KB (%.2f MB)\n",
		float64(totalSize)/1024, float64(totalSize)/(1024*1024))

	if len(results) > 10 {
		fmt.Printf("🏆 Largest pages:\n")
		// Simple sorting to show largest pages
		for i := 0; i < len(results) && i < 5; i++ {
			largest := results[i]
			largestIdx := i
			for j := i + 1; j < len(results); j++ {
				if results[j].HTMLSize > largest.HTMLSize {
					largest = results[j]
					largestIdx = j
				}
			}
			if largestIdx != i {
				results[i], results[largestIdx] = results[largestIdx], results[i]
			}
			fmt.Printf("  %d. %s (%.1f KB)\n", i+1, results[i].URL, float64(results[i].HTMLSize)/1024)
		}
	}
}
