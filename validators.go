package aioauditresults

import (
	"github.com/vendasta/gosdks/validation"
	"github.com/vendasta/gosdks/verrors"
)

// Validate ensures that the AuditScores it is run on is in a valid state for saving to storage and returning to consumers of the Service interface.
func (a *AuditScores) Validate() error {
	return validation.NewValidator().
		Rule(validation.BoolTrue(false, verrors.Unimplemented, "You should be writing your own validators")).
		Validate()
}

// Validate ensures that the AuditScoreResults it is run on is in a valid state for saving to storage and returning to consumers of the Service interface.
func (a *AuditScoreResults) Validate() error {
	for _, t := range a.AuditScores {
		err := validation.NewValidator().Rule(t).Validate()
		if err != nil {
			return err
		}
	}
	return validation.NewValidator().
		Rule(validation.BoolTrue(false, verrors.Unimplemented, "You should be writing your own validators")).
		Validate()
}

// Validate ensures that the AuditPages it is run on is in a valid state for saving to storage and returning to consumers of the Service interface.
func (a *AuditPageData) Validate() error {
	return validation.NewValidator().
		Rule(validation.BoolTrue(false, verrors.Unimplemented, "You should be writing your own validators")).
		Validate()
}

// Validate ensures that the AIOAuditResults it is run on is in a valid state for saving to storage and returning to consumers of the Service interface.
func (a *AIOAuditResults) Validate() error {
	for _, t := range a.AuditPages {
		err := validation.NewValidator().Rule(t).Validate()
		if err != nil {
			return err
		}
	}
	for _, t := range a.AuditScoreResults {
		err := validation.NewValidator().Rule(t).Validate()
		if err != nil {
			return err
		}
	}
	return validation.NewValidator().Rule(validation.StringNotEmpty(a.AuditID, verrors.InvalidArgument, "AuditID is required")).
		Rule(validation.StringNotEmpty(a.BusinessID, verrors.InvalidArgument, "BusinessID is required")).
		Validate()
}
