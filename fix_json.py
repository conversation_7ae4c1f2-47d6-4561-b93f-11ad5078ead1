#!/usr/bin/env python3
import json
import re

def fix_json_file(input_file, output_file):
    """Fix malformed JSON file by extracting JSON portion and properly formatting it."""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the JSON portion (from opening brace to closing brace)
    # Look for the first { and try to find the matching closing }
    start_idx = content.find('{')
    if start_idx == -1:
        print("No JSON object found in file")
        return False
    
    # Find the matching closing brace
    brace_count = 0
    end_idx = -1
    
    for i in range(start_idx, len(content)):
        if content[i] == '{':
            brace_count += 1
        elif content[i] == '}':
            brace_count -= 1
            if brace_count == 0:
                end_idx = i
                break
    
    if end_idx == -1:
        print("Could not find matching closing brace")
        return False
    
    # Extract the JSON portion
    json_content = content[start_idx:end_idx + 1]
    
    # Fix common JSON issues
    # 1. Fix missing commas between objects in arrays
    json_content = re.sub(r'}\s*{', '},{', json_content)
    
    # 2. Fix missing quotes around property names
    json_content = re.sub(r'([{,])\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1 "\2":', json_content)
    
    # 3. Fix missing quotes around string values
    json_content = re.sub(r':\s*([a-zA-Z][a-zA-Z0-9_]*)\s*([,}])', r': "\1"\2', json_content)
    
    try:
        # Try to parse the JSON
        parsed_json = json.loads(json_content)
        
        # Write the formatted JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(parsed_json, f, indent=2, ensure_ascii=False)
        
        print(f"Successfully fixed JSON and saved to {output_file}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"JSON parsing error: {e}")
        print("Attempting to fix more issues...")
        
        # Additional fixes
        # Remove any HTML content that might be mixed in
        json_content = re.sub(r'<[^>]*>', '', json_content)
        
        # Fix escaped quotes
        json_content = json_content.replace('\\"', '"')
        
        # Try parsing again
        try:
            parsed_json = json.loads(json_content)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(parsed_json, f, indent=2, ensure_ascii=False)
            print(f"Successfully fixed JSON after additional cleanup and saved to {output_file}")
            return True
        except json.JSONDecodeError as e2:
            print(f"Still cannot parse JSON: {e2}")
            # Save the cleaned content for manual inspection
            with open(output_file + '.debug', 'w', encoding='utf-8') as f:
                f.write(json_content)
            print(f"Saved debug version to {output_file}.debug")
            return False

if __name__ == "__main__":
    fix_json_file("INPUT.JSON", "INPUT_fixed.json") 