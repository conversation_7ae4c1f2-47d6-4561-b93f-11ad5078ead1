package aioauditresultsservice

import (
	"context"

	"time"

	"github.com/vendasta/gosdks/logging"
	model "github.com/vendasta/listing-products/internal/seo/aioauditresults"
	repository "github.com/vendasta/listing-products/internal/seo/aioauditresults/repository"
)

// ServiceImpl implements Service
type ServiceImpl struct {
	repo repository.Repository
}

// New returns a new ServiceImpl
func New(repo repository.Repository) *ServiceImpl {
	return &ServiceImpl{
		repo: repo,
	}
}

// Create will create a new AIOAuditResults if it does not exist yet.
// If the AIOAuditResults already exists, a verrors.AlreadyExists error will be returned.
// TODO: Add more parameters to this method - it is likely that the primary key alone may not be enough to create a valid AIOAuditResults
func (s *ServiceImpl) Create(ctx context.Context, businessID string, auditID string) error {
	in := &model.AIOAuditResults{
		BusinessID: businessID, AuditID: auditID,
	}

	err := s.repo.Create(ctx, in)
	if err != nil {
		logging.Debugf(ctx, "Error trying to create AIOAuditResults: %s", err.Error())
		return err
	}
	return nil
}

// Get will return the existing AIOAuditResults specified by the given identifiers, or an verrors.NotFound error
// if the targeted AIOAuditResults does not exist.
func (s *ServiceImpl) Get(ctx context.Context, businessID string, auditID string) (*model.AIOAuditResults, error) {
	out, err := s.repo.Get(ctx, model.NewKey(businessID, auditID))
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AIOAuditResults from repo: %s", err.Error())
		return nil, err
	}
	return out, nil
}

// List see Service.List
// TODO you need to add parameters that make sense in your business domain,
//
//	and use them to add the appropriate filters.
//	(Either filtering by key columns, or columns in a composite index.)
func (s *ServiceImpl) List(ctx context.Context /* TODO add filterable parameters here */, cursorIn string, pageSize int64) (results []*model.AIOAuditResults, cursor string, hasMore bool, err error) {
	keyFilter := model.Key{
		// TODO filter by key columns here
	}
	compositeFilters := repository.FilterByNothing()
	// compositeFilters.SomeField("Some Value")
	results, cursor, hasMore, err = s.repo.List(ctx, keyFilter, compositeFilters, cursorIn, pageSize)
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AIOAuditResults from repo: %s", err.Error())
		return nil, "", false, err
	}
	return
}

// Delete will delete the specified AIOAuditResults.
// If the AIOAuditResults does not exist or has already been deleted, a verrors.NotFound error will be returned.
func (s *ServiceImpl) Delete(ctx context.Context, businessID string, auditID string) error {
	err := s.repo.Mutate(ctx, model.NewKey(businessID, auditID), repository.SetDeleted(time.Now().UTC()))
	if err != nil {
		logging.Infof(ctx, "Error trying to delete AIOAuditResults from repo: %s", err.Error())
		return err
	}
	return nil
}

func (s *ServiceImpl) UpsertAIOAuditResults(ctx context.Context, businessID string, auditID string, aioAuditResults *model.AIOAuditResults) error {
	err := s.repo.Upsert(ctx, model.NewKey(businessID, auditID), repository.SetAIOAuditResults(aioAuditResults))
	if err != nil {
		logging.Infof(ctx, "Error trying to upsert AIOAuditResults from repo: %s", err.Error())
		return err
	}
	return nil
}

func (s *ServiceImpl) GetAIOAuditResults(ctx context.Context, businessID string, auditID string) (*model.AIOAuditResults, error) {
	out, err := s.repo.Get(ctx, model.NewKey(businessID, auditID))
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AIOAuditResults from repo: %s", err.Error())
		return nil, err
	}
	return out, nil
}

func (s *ServiceImpl) GetAuditStatus(ctx context.Context, businessID string, auditID string) (string, error) {
	out, err := s.repo.Get(ctx, model.NewKey(businessID, auditID))
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AIOAuditResults from repo: %s", err.Error())
		return "", err
	}
	return out.AuditStatus, nil
}

func (s *ServiceImpl) UpdateAuditStatus(ctx context.Context, businessID string, auditID string, auditStatus string) error {
	err := s.repo.Mutate(ctx, model.NewKey(businessID, auditID), repository.SetAuditStatus(auditStatus))
	if err != nil {
		logging.Infof(ctx, "Error trying to update AIOAuditResults from repo: %s", err.Error())
		return err
	}
	return nil
}

func (s *ServiceImpl) GetAllAIOAuditResultsByBusinessID(ctx context.Context, businessID string) ([]*model.AIOAuditResults, error) {
	out, _, _, err := s.repo.List(ctx, model.NewKey(businessID, ""), repository.FilterByBusinessIDDeletedIndex().BusinessID(businessID), "", 0)
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AIOAuditResults from repo: %s", err.Error())
		return nil, err
	}
	return out, nil
}
func (s *ServiceImpl) GetAllAIOAuditResultsByModel(ctx context.Context, businessID, modelName string) ([]*model.AIOAuditResults, error) {
	out, _, _, err := s.repo.List(ctx, model.NewKey(businessID, ""), repository.FilterByBusinessIDDeletedIndex().BusinessID(businessID), "", 0)
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AIOAuditResults from repo: %s", err)
		return nil, err
	}

	return out, nil
}

func (s *ServiceImpl) GetAllAIOAuditResultsbyDateRange(ctx context.Context, businessID string, startDate, endDate time.Time) ([]*model.AIOAuditResults, error) {
	out, err := s.repo.ListByDate(ctx, businessID, startDate, endDate)
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AIOAuditResults from repo: %s", err.Error())
		return nil, err
	}
	return out, nil
}
