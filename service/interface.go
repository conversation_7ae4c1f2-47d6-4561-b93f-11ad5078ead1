package aioauditresultsservice

import (
	"context"
	"time"

	model "github.com/vendasta/listing-products/internal/seo/aioauditresults"
)

// Service can create and get AIOAuditResultss.
// This should be the interface that the API layer uses to interact with the AIOAuditResults model.
//
//go:generate mockgen -destination mock.go -source=interface.go -package=aioauditresultsservice
type Service interface {
	// Create will create a new AIOAuditResults if it does not exist yet.
	// If the AIOAuditResults already exists, a verrors.AlreadyExists error will be returned.
	// TODO: Add more parameters to this method - it is likely that the primary key alone may not be enough to create a valid AIOAuditResults
	Create(ctx context.Context, businessID string, auditID string) error

	// Get will return the existing AIOAuditResults specified by the given identifiers, or an verrors.NotFound error
	// if the targeted AIOAuditResults does not exist.
	Get(ctx context.Context, businessID string, auditID string) (*model.AIOAuditResults, error)

	// List will return a page of AIOAuditResultss with the given keys and filters
	List(ctx context.Context /* TODO add filterable parameters here */, cursorIn string, pageSize int64) (results []*model.AIOAuditResults, cursor string, hasMore bool, err error)

	// Delete will delete the specified AIOAuditResults.
	// If the AIOAuditResults does not exist or has already been deleted, a verrors.NotFound error will be returned.
	Delete(ctx context.Context, businessID string, auditID string) error

	// UpsertAIOAuditResults will upsert the AIOAuditResults for the given businessID and auditID.
	UpsertAIOAuditResults(ctx context.Context, businessID string, auditID string, aioAuditResults *model.AIOAuditResults) error

	// GetAuditStatus will get the audit status for the given businessID and auditID.
	GetAuditStatus(ctx context.Context, businessID string, auditID string) (string, error)

	// UpdateAuditStatus will update the audit status for the given businessID and auditID.
	UpdateAuditStatus(ctx context.Context, businessID string, auditID string, auditStatus string) error

	// GetAIOAuditResults will get the AIOAuditResults for the given businessID and auditID.
	GetAIOAuditResults(ctx context.Context, businessID string, auditID string) (*model.AIOAuditResults, error)

	// GetAIOAuditResultsByBusinessID will get the AIOAuditResults for the given businessID.
	GetAllAIOAuditResultsByBusinessID(ctx context.Context, businessID string) ([]*model.AIOAuditResults, error)

	// GetAllAIOAuditResults will get all the AIOAuditResults for the given businessID.
	GetAllAIOAuditResultsByModel(ctx context.Context, businessID string, model string) ([]*model.AIOAuditResults, error)

	// GetAllAIOAuditResultsbyDateRange will get all the AIOAuditResults for the given businessID and date range.
	GetAllAIOAuditResultsbyDateRange(ctx context.Context, businessID string, startDate time.Time, endDate time.Time) ([]*model.AIOAuditResults, error)
}
