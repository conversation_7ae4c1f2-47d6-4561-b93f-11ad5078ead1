// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go
//
// Generated by this command:
//
//	mockgen -destination mock.go -source=interface.go -package=aioauditresultsservice
//

// Package aioauditresultsservice is a generated GoMock package.
package aioauditresultsservice

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	aioauditresults "github.com/vendasta/listing-products/internal/seo/aioauditresults"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
	isgomock struct{}
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockService) Create(ctx context.Context, businessID, auditID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, businessID, auditID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockServiceMockRecorder) Create(ctx, businessID, auditID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockService)(nil).Create), ctx, businessID, auditID)
}

// Delete mocks base method.
func (m *MockService) Delete(ctx context.Context, businessID, auditID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, businessID, auditID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockServiceMockRecorder) Delete(ctx, businessID, auditID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockService)(nil).Delete), ctx, businessID, auditID)
}

// Get mocks base method.
func (m *MockService) Get(ctx context.Context, businessID, auditID string) (*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, businessID, auditID)
	ret0, _ := ret[0].(*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockServiceMockRecorder) Get(ctx, businessID, auditID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockService)(nil).Get), ctx, businessID, auditID)
}

// GetAIOAuditResults mocks base method.
func (m *MockService) GetAIOAuditResults(ctx context.Context, businessID, auditID string) (*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIOAuditResults", ctx, businessID, auditID)
	ret0, _ := ret[0].(*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIOAuditResults indicates an expected call of GetAIOAuditResults.
func (mr *MockServiceMockRecorder) GetAIOAuditResults(ctx, businessID, auditID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIOAuditResults", reflect.TypeOf((*MockService)(nil).GetAIOAuditResults), ctx, businessID, auditID)
}

// GetAllAIOAuditResultsByBusinessID mocks base method.
func (m *MockService) GetAllAIOAuditResultsByBusinessID(ctx context.Context, businessID string) ([]*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllAIOAuditResultsByBusinessID", ctx, businessID)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllAIOAuditResultsByBusinessID indicates an expected call of GetAllAIOAuditResultsByBusinessID.
func (mr *MockServiceMockRecorder) GetAllAIOAuditResultsByBusinessID(ctx, businessID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllAIOAuditResultsByBusinessID", reflect.TypeOf((*MockService)(nil).GetAllAIOAuditResultsByBusinessID), ctx, businessID)
}

// GetAllAIOAuditResultsByModel mocks base method.
func (m *MockService) GetAllAIOAuditResultsByModel(ctx context.Context, businessID, model string) ([]*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllAIOAuditResultsByModel", ctx, businessID, model)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllAIOAuditResultsByModel indicates an expected call of GetAllAIOAuditResultsByModel.
func (mr *MockServiceMockRecorder) GetAllAIOAuditResultsByModel(ctx, businessID, model any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllAIOAuditResultsByModel", reflect.TypeOf((*MockService)(nil).GetAllAIOAuditResultsByModel), ctx, businessID, model)
}

// GetAllAIOAuditResultsbyDateRange mocks base method.
func (m *MockService) GetAllAIOAuditResultsbyDateRange(ctx context.Context, businessID, startDate, endDate string) ([]*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllAIOAuditResultsbyDateRange", ctx, businessID, startDate, endDate)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllAIOAuditResultsbyDateRange indicates an expected call of GetAllAIOAuditResultsbyDateRange.
func (mr *MockServiceMockRecorder) GetAllAIOAuditResultsbyDateRange(ctx, businessID, startDate, endDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllAIOAuditResultsbyDateRange", reflect.TypeOf((*MockService)(nil).GetAllAIOAuditResultsbyDateRange), ctx, businessID, startDate, endDate)
}

// GetAuditStatus mocks base method.
func (m *MockService) GetAuditStatus(ctx context.Context, businessID, auditID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuditStatus", ctx, businessID, auditID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuditStatus indicates an expected call of GetAuditStatus.
func (mr *MockServiceMockRecorder) GetAuditStatus(ctx, businessID, auditID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuditStatus", reflect.TypeOf((*MockService)(nil).GetAuditStatus), ctx, businessID, auditID)
}

// List mocks base method.
func (m *MockService) List(ctx context.Context, cursorIn string, pageSize int64) ([]*aioauditresults.AIOAuditResults, string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, cursorIn, pageSize)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// List indicates an expected call of List.
func (mr *MockServiceMockRecorder) List(ctx, cursorIn, pageSize any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockService)(nil).List), ctx, cursorIn, pageSize)
}

// UpdateAuditStatus mocks base method.
func (m *MockService) UpdateAuditStatus(ctx context.Context, businessID, auditID, auditStatus string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAuditStatus", ctx, businessID, auditID, auditStatus)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAuditStatus indicates an expected call of UpdateAuditStatus.
func (mr *MockServiceMockRecorder) UpdateAuditStatus(ctx, businessID, auditID, auditStatus any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAuditStatus", reflect.TypeOf((*MockService)(nil).UpdateAuditStatus), ctx, businessID, auditID, auditStatus)
}

// UpsertAIOAuditResults mocks base method.
func (m *MockService) UpsertAIOAuditResults(ctx context.Context, businessID, auditID string, aioAuditResults *aioauditresults.AIOAuditResults) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertAIOAuditResults", ctx, businessID, auditID, aioAuditResults)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertAIOAuditResults indicates an expected call of UpsertAIOAuditResults.
func (mr *MockServiceMockRecorder) UpsertAIOAuditResults(ctx, businessID, auditID, aioAuditResults any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertAIOAuditResults", reflect.TypeOf((*MockService)(nil).UpsertAIOAuditResults), ctx, businessID, auditID, aioAuditResults)
}
