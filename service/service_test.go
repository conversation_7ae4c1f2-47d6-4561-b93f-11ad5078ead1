package aioauditresultsservice

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	model "github.com/vendasta/listing-products/internal/seo/aioauditresults"
	repository "github.com/vendasta/listing-products/internal/seo/aioauditresults/repository"
)

func Test_Create(t *testing.T) {
	type testCase struct {
		name       string
		BusinessID string
		AuditID    string

		expectedError error
		repoError     error
	}
	cases := []*testCase{
		{
			name:       "This would be hard so you have to write it yourself",
			BusinessID: "business_id", AuditID: "audit_id",
			expectedError: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			repoMock := repository.NewMockRepository(ctrl)
			repoMock.EXPECT().Create(gomock.Any(), gomock.Any()).Return(c.repoError).AnyTimes()

			service := New(repoMock)

			err := service.Create(ctx, c.BusinessID, c.AuditID)

			assert.Equal(t, c.expectedError, err)
		})
	}
}

func Test_Get(t *testing.T) {
	type testCase struct {
		name       string
		BusinessID string
		AuditID    string

		expectedError           error
		repoError               error
		expectedAIOAuditResults *model.AIOAuditResults
	}
	cases := []testCase{
		{
			name:                    "should return error when error with repo",
			repoError:               errors.New("repo error"),
			expectedAIOAuditResults: nil,
			expectedError:           errors.New("repo error"),
		},
		{
			name:       "should return AIOAuditResults for given identifier",
			BusinessID: "business_id", AuditID: "audit_id",
			repoError: nil,
			expectedAIOAuditResults: &model.AIOAuditResults{
				BusinessID: "business_id", AuditID: "audit_id",
			},
			expectedError: nil,
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			repoMock := repository.NewMockRepository(ctrl)
			repoMock.EXPECT().Get(gomock.Any(), model.NewKey(c.BusinessID, c.AuditID)).Return(c.expectedAIOAuditResults, c.repoError).AnyTimes()

			service := New(repoMock)

			out, err := service.Get(ctx, c.BusinessID, c.AuditID)

			assert.Equal(t, c.expectedError, err)
			assert.Equal(t, c.expectedAIOAuditResults, out)
		})
	}
}

func Test_Delete(t *testing.T) {
	type testCase struct {
		name       string
		BusinessID string
		AuditID    string

		expectedError error
		repoError     error
	}
	cases := []*testCase{
		{
			name:       "returns error from repository mutate",
			BusinessID: "business_id", AuditID: "audit_id",
			expectedError: errors.New("repo error"),
			repoError:     errors.New("repo error"),
		},
		{
			name:       "returns nil on success",
			BusinessID: "business_id", AuditID: "audit_id",
			expectedError: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			repoMock := repository.NewMockRepository(ctrl)
			repoMock.EXPECT().Mutate(gomock.Any(), gomock.Any(), gomock.Any()).Return(c.repoError).AnyTimes()

			service := New(repoMock)

			err := service.Delete(ctx, c.BusinessID, c.AuditID)

			assert.Equal(t, c.expectedError, err)
		})
	}
}

func Test_UpsertAIOAuditResults(t *testing.T) {
	type testCase struct {
		name            string
		businessID      string
		auditID         string
		aioAuditResults *model.AIOAuditResults
		expectedError   error
		repoError       error
	}

	cases := []testCase{
		{
			name:       "should successfully upsert AIOAuditResults",
			businessID: "business1",
			auditID:    "audit1",
			aioAuditResults: &model.AIOAuditResults{
				BusinessID: "business1",
				AuditID:    "audit1",
				AIModel:    "gpt-4",
				AuditDate:  "2024-01-15",
			},
			expectedError: nil,
			repoError:     nil,
		},
		{
			name:       "should return error when repository upsert fails",
			businessID: "business1",
			auditID:    "audit1",
			aioAuditResults: &model.AIOAuditResults{
				BusinessID: "business1",
				AuditID:    "audit1",
			},
			expectedError: errors.New("repository error"),
			repoError:     errors.New("repository error"),
		},
		{
			name:       "should handle empty business ID",
			businessID: "",
			auditID:    "audit1",
			aioAuditResults: &model.AIOAuditResults{
				BusinessID: "",
				AuditID:    "audit1",
			},
			expectedError: nil,
			repoError:     nil,
		},
		{
			name:       "should handle empty audit ID",
			businessID: "business1",
			auditID:    "",
			aioAuditResults: &model.AIOAuditResults{
				BusinessID: "business1",
				AuditID:    "",
			},
			expectedError: nil,
			repoError:     nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			repoMock := repository.NewMockRepository(ctrl)
			repoMock.EXPECT().Upsert(gomock.Any(), model.NewKey(c.businessID, c.auditID), gomock.Any()).Return(c.repoError).AnyTimes()

			service := New(repoMock)

			err := service.UpsertAIOAuditResults(ctx, c.businessID, c.auditID, c.aioAuditResults)

			assert.Equal(t, c.expectedError, err)
		})
	}
}

func Test_GetAIOAuditResults(t *testing.T) {
	type testCase struct {
		name                    string
		businessID              string
		auditID                 string
		expectedAIOAuditResults *model.AIOAuditResults
		expectedError           error
		repoError               error
	}

	cases := []testCase{
		{
			name:       "should return AIOAuditResults for given business ID and audit ID",
			businessID: "business1",
			auditID:    "audit1",
			expectedAIOAuditResults: &model.AIOAuditResults{
				BusinessID: "business1",
				AuditID:    "audit1",
				AIModel:    "gpt-4",
				AuditDate:  "2024-01-15",
			},
			expectedError: nil,
			repoError:     nil,
		},
		{
			name:                    "should return error when repository get fails",
			businessID:              "business1",
			auditID:                 "audit1",
			expectedAIOAuditResults: nil,
			expectedError:           errors.New("repository error"),
			repoError:               errors.New("repository error"),
		},
		{
			name:       "should handle empty business ID",
			businessID: "",
			auditID:    "audit1",
			expectedAIOAuditResults: &model.AIOAuditResults{
				BusinessID: "",
				AuditID:    "audit1",
			},
			expectedError: nil,
			repoError:     nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			repoMock := repository.NewMockRepository(ctrl)
			repoMock.EXPECT().Get(gomock.Any(), model.NewKey(c.businessID, c.auditID)).Return(c.expectedAIOAuditResults, c.repoError).AnyTimes()

			service := New(repoMock)

			out, err := service.GetAIOAuditResults(ctx, c.businessID, c.auditID)

			assert.Equal(t, c.expectedError, err)
			assert.Equal(t, c.expectedAIOAuditResults, out)
		})
	}
}

func Test_GetAuditStatus(t *testing.T) {
	type testCase struct {
		name           string
		businessID     string
		auditID        string
		expectedStatus string
		expectedError  error
		repoError      error
		repoResult     *model.AIOAuditResults
	}

	cases := []testCase{
		{
			name:           "should return audit status for given business ID and audit ID",
			businessID:     "business1",
			auditID:        "audit1",
			expectedStatus: "completed",
			expectedError:  nil,
			repoError:      nil,
			repoResult: &model.AIOAuditResults{
				BusinessID:  "business1",
				AuditID:     "audit1",
				AuditStatus: "completed",
			},
		},
		{
			name:           "should return error when repository get fails",
			businessID:     "business1",
			auditID:        "audit1",
			expectedStatus: "",
			expectedError:  errors.New("repository error"),
			repoError:      errors.New("repository error"),
			repoResult:     nil,
		},
		{
			name:           "should return empty status when audit status is empty",
			businessID:     "business1",
			auditID:        "audit1",
			expectedStatus: "",
			expectedError:  nil,
			repoError:      nil,
			repoResult: &model.AIOAuditResults{
				BusinessID:  "business1",
				AuditID:     "audit1",
				AuditStatus: "",
			},
		},
		{
			name:           "should handle running status",
			businessID:     "business1",
			auditID:        "audit1",
			expectedStatus: "running",
			expectedError:  nil,
			repoError:      nil,
			repoResult: &model.AIOAuditResults{
				BusinessID:  "business1",
				AuditID:     "audit1",
				AuditStatus: "running",
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			repoMock := repository.NewMockRepository(ctrl)
			repoMock.EXPECT().Get(gomock.Any(), model.NewKey(c.businessID, c.auditID)).Return(c.repoResult, c.repoError).AnyTimes()

			service := New(repoMock)

			status, err := service.GetAuditStatus(ctx, c.businessID, c.auditID)

			assert.Equal(t, c.expectedError, err)
			assert.Equal(t, c.expectedStatus, status)
		})
	}
}

func Test_UpdateAuditStatus(t *testing.T) {
	type testCase struct {
		name          string
		businessID    string
		auditID       string
		auditStatus   string
		expectedError error
		repoError     error
	}

	cases := []testCase{
		{
			name:          "should successfully update audit status",
			businessID:    "business1",
			auditID:       "audit1",
			auditStatus:   "completed",
			expectedError: nil,
			repoError:     nil,
		},
		{
			name:          "should return error when repository mutate fails",
			businessID:    "business1",
			auditID:       "audit1",
			auditStatus:   "failed",
			expectedError: errors.New("repository error"),
			repoError:     errors.New("repository error"),
		},
		{
			name:          "should handle empty audit status",
			businessID:    "business1",
			auditID:       "audit1",
			auditStatus:   "",
			expectedError: nil,
			repoError:     nil,
		},
		{
			name:          "should handle running status",
			businessID:    "business1",
			auditID:       "audit1",
			auditStatus:   "running",
			expectedError: nil,
			repoError:     nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			repoMock := repository.NewMockRepository(ctrl)
			repoMock.EXPECT().Mutate(gomock.Any(), model.NewKey(c.businessID, c.auditID), gomock.Any()).Return(c.repoError).AnyTimes()

			service := New(repoMock)

			err := service.UpdateAuditStatus(ctx, c.businessID, c.auditID, c.auditStatus)

			assert.Equal(t, c.expectedError, err)
		})
	}
}

func Test_GetAllAIOAuditResultsByBusinessID(t *testing.T) {
	type testCase struct {
		name                    string
		businessID              string
		expectedAIOAuditResults []*model.AIOAuditResults
		expectedError           error
		repoError               error
	}

	cases := []testCase{
		{
			name:       "should return all AIOAuditResults for given business ID",
			businessID: "business1",
			expectedAIOAuditResults: []*model.AIOAuditResults{
				{
					BusinessID: "business1",
					AuditID:    "audit1",
					AIModel:    "gpt-4",
					AuditDate:  "2024-01-15",
				},
				{
					BusinessID: "business1",
					AuditID:    "audit2",
					AIModel:    "claude-3",
					AuditDate:  "2024-01-16",
				},
			},
			expectedError: nil,
			repoError:     nil,
		},
		{
			name:                    "should return empty slice when no results found",
			businessID:              "business2",
			expectedAIOAuditResults: []*model.AIOAuditResults{},
			expectedError:           nil,
			repoError:               nil,
		},
		{
			name:                    "should return error when repository list fails",
			businessID:              "business1",
			expectedAIOAuditResults: nil,
			expectedError:           errors.New("repository error"),
			repoError:               errors.New("repository error"),
		},
		{
			name:                    "should handle empty business ID",
			businessID:              "",
			expectedAIOAuditResults: []*model.AIOAuditResults{},
			expectedError:           nil,
			repoError:               nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			repoMock := repository.NewMockRepository(ctrl)
			repoMock.EXPECT().List(gomock.Any(), model.NewKey(c.businessID, ""), gomock.Any(), "", int64(0)).Return(c.expectedAIOAuditResults, "", false, c.repoError).AnyTimes()

			service := New(repoMock)

			out, err := service.GetAllAIOAuditResultsByBusinessID(ctx, c.businessID)

			assert.Equal(t, c.expectedError, err)
			assert.Equal(t, c.expectedAIOAuditResults, out)
		})
	}
}

func Test_GetAllAIOAuditResultsByModel(t *testing.T) {
	type testCase struct {
		name                    string
		businessID              string
		modelName               string
		expectedAIOAuditResults []*model.AIOAuditResults
		expectedError           error
		repoError               error
	}

	cases := []testCase{
		{
			name:       "should return all AIOAuditResults for given business ID and model",
			businessID: "business1",
			modelName:  "gpt-4",
			expectedAIOAuditResults: []*model.AIOAuditResults{
				{
					BusinessID: "business1",
					AuditID:    "audit1",
					AIModel:    "gpt-4",
					AuditDate:  "2024-01-15",
				},
				{
					BusinessID: "business1",
					AuditID:    "audit2",
					AIModel:    "gpt-4",
					AuditDate:  "2024-01-16",
				},
			},
			expectedError: nil,
			repoError:     nil,
		},
		{
			name:                    "should return empty slice when no results found for model",
			businessID:              "business1",
			modelName:               "claude-3",
			expectedAIOAuditResults: []*model.AIOAuditResults{},
			expectedError:           nil,
			repoError:               nil,
		},
		{
			name:                    "should return error when repository list fails",
			businessID:              "business1",
			modelName:               "gpt-4",
			expectedAIOAuditResults: nil,
			expectedError:           errors.New("repository error"),
			repoError:               errors.New("repository error"),
		},
		{
			name:                    "should handle empty model name",
			businessID:              "business1",
			modelName:               "",
			expectedAIOAuditResults: []*model.AIOAuditResults{},
			expectedError:           nil,
			repoError:               nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			repoMock := repository.NewMockRepository(ctrl)
			repoMock.EXPECT().List(gomock.Any(), model.NewKey(c.businessID, ""), gomock.Any(), "", int64(0)).Return(c.expectedAIOAuditResults, "", false, c.repoError).AnyTimes()

			service := New(repoMock)

			out, err := service.GetAllAIOAuditResultsByModel(ctx, c.businessID, c.modelName)

			assert.Equal(t, c.expectedError, err)
			assert.Equal(t, c.expectedAIOAuditResults, out)
		})
	}
}

func Test_GetAllAIOAuditResultsbyDateRange(t *testing.T) {
	type testCase struct {
		name                    string
		businessID              string
		startDate               string
		endDate                 string
		expectedAIOAuditResults []*model.AIOAuditResults
		expectedError           error
		repoError               error
	}

	cases := []testCase{
		{
			name:       "should return AIOAuditResults within date range",
			businessID: "business1",
			startDate:  "2024-01-15",
			endDate:    "2024-01-20",
			expectedAIOAuditResults: []*model.AIOAuditResults{
				{
					BusinessID: "business1",
					AuditID:    "audit1",
					AuditDate:  "2024-01-15",
				},
				{
					BusinessID: "business1",
					AuditID:    "audit2",
					AuditDate:  "2024-01-18",
				},
			},
			expectedError: nil,
			repoError:     nil,
		},
		{
			name:                    "should return empty slice when no results in date range",
			businessID:              "business1",
			startDate:               "2024-01-25",
			endDate:                 "2024-01-30",
			expectedAIOAuditResults: []*model.AIOAuditResults{},
			expectedError:           nil,
			repoError:               nil,
		},
		{
			name:                    "should return error when repository ListByDate fails",
			businessID:              "business1",
			startDate:               "2024-01-15",
			endDate:                 "2024-01-20",
			expectedAIOAuditResults: nil,
			expectedError:           errors.New("repository error"),
			repoError:               errors.New("repository error"),
		},
		{
			name:       "should handle single day range",
			businessID: "business1",
			startDate:  "2024-01-15",
			endDate:    "2024-01-16",
			expectedAIOAuditResults: []*model.AIOAuditResults{
				{
					BusinessID: "business1",
					AuditID:    "audit1",
					AuditDate:  "2024-01-15",
				},
			},
			expectedError: nil,
			repoError:     nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Parse dates for the test
			startDate, _ := time.Parse("2006-01-02", c.startDate)
			endDate, _ := time.Parse("2006-01-02", c.endDate)

			repoMock := repository.NewMockRepository(ctrl)
			repoMock.EXPECT().ListByDate(gomock.Any(), c.businessID, startDate, endDate).Return(c.expectedAIOAuditResults, c.repoError).AnyTimes()

			service := New(repoMock)

			out, err := service.GetAllAIOAuditResultsbyDateRange(ctx, c.businessID, startDate, endDate)

			assert.Equal(t, c.expectedError, err)
			assert.Equal(t, c.expectedAIOAuditResults, out)
		})
	}
}
