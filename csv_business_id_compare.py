#!/usr/bin/env python3
"""
CSV Full Row Comparison Tool
Compares all 4 columns (business_id, latitude, longitude, seo_keywords) between two CSV files 
and creates a result CSV with match indicators in the 5th column.
"""

import pandas as pd
import argparse
import sys
from pathlib import Path


def compare_csv_files(csv_file1: str, csv_file2: str, output_file: str = None) -> str:
    """
    Compare all 4 columns between two CSV files and create a result CSV with match status.
    
    Args:
        csv_file1: Path to the first CSV file
        csv_file2: Path to the second CSV file  
        output_file: Path to the output CSV file (optional)
        
    Returns:
        Path to the created comparison CSV file
    """
    
    # Generate output filename if not provided
    if output_file is None:
        file1_name = Path(csv_file1).stem
        file2_name = Path(csv_file2).stem
        output_file = f"csv_comparison_{file1_name}_vs_{file2_name}.csv"
    
    try:
        # Read both CSV files
        print(f"Reading {csv_file1}...")
        df1 = pd.read_csv(csv_file1)
        
        print(f"Reading {csv_file2}...")
        df2 = pd.read_csv(csv_file2)
        
        # Expected columns (first 4 columns)
        expected_columns = ['business_id', 'latitude', 'longitude', 'seo_keywords']
        
        # Check if expected columns exist in both files
        missing_cols_1 = [col for col in expected_columns if col not in df1.columns]
        missing_cols_2 = [col for col in expected_columns if col not in df2.columns]
        
        if missing_cols_1:
            raise ValueError(f"Missing columns in {csv_file1}: {missing_cols_1}")
        if missing_cols_2:
            raise ValueError(f"Missing columns in {csv_file2}: {missing_cols_2}")
        
        # Select only the 4 columns we're comparing
        df1_compare = df1[expected_columns].copy()
        df2_compare = df2[expected_columns].copy()
        
        # Convert to string for comparison (to handle NaN values)
        for col in expected_columns:
            df1_compare[col] = df1_compare[col].astype(str)
            df2_compare[col] = df2_compare[col].astype(str)
        
        print(f"Found {len(df1_compare):,} rows in file 1")
        print(f"Found {len(df2_compare):,} rows in file 2")
        
        # Create a combined dataframe with all unique rows
        all_rows = pd.concat([df1_compare, df2_compare]).drop_duplicates().reset_index(drop=True)
        print(f"Total unique rows: {len(all_rows):,}")
        
        # Add match status column
        comparison_data = []
        
        for _, row in all_rows.iterrows():
            # Check if this exact row exists in both files
            row_in_file1 = ((df1_compare == row).all(axis=1)).any()
            row_in_file2 = ((df2_compare == row).all(axis=1)).any()
            
            if row_in_file1 and row_in_file2:
                match_status = "match"
            else:
                match_status = "not match"
            
            # Create result row with all 4 columns plus match status
            result_row = {
                'business_id': row['business_id'],
                'latitude': row['latitude'],
                'longitude': row['longitude'],
                'seo_keywords': row['seo_keywords'],
                'match_status': match_status
            }
            comparison_data.append(result_row)
        
        # Create DataFrame
        result_df = pd.DataFrame(comparison_data)
        
        # Save to CSV
        result_df.to_csv(output_file, index=False)
        
        # Print summary statistics
        matches = len(result_df[result_df['match_status'] == 'match'])
        no_matches = len(result_df[result_df['match_status'] == 'not match'])
        
        print(f"\n=== COMPARISON RESULTS ===")
        print(f"Total unique rows: {len(result_df):,}")
        print(f"Exact matches (all 4 columns identical): {matches:,}")
        print(f"No matches (row exists in only one file): {no_matches:,}")
        print(f"Match percentage: {(matches / len(result_df) * 100):.2f}%")
        
        return output_file
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description='Compare all 4 columns between two CSV files')
    parser.add_argument('file1', help='Path to the first CSV file')
    parser.add_argument('file2', help='Path to the second CSV file')
    parser.add_argument('-o', '--output', help='Path to the output CSV file (optional)')
    parser.add_argument('--preview', action='store_true', help='Show a preview of the first few comparison results')
    
    args = parser.parse_args()
    
    try:
        output_path = compare_csv_files(args.file1, args.file2, args.output)
        print(f"\nComparison complete! Results saved to: {output_path}")
        
        # Show preview if requested
        if args.preview:
            print(f"\n=== PREVIEW (first 10 rows) ===")
            preview_df = pd.read_csv(output_path)
            print(preview_df.head(10).to_string(index=False))
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main() 