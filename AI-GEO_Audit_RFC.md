# RFC: AIO Audit Feature

**Status**: Implemented  
**Authors**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  
**Created**: July 2025  
**Last Updated**: July 30, 2025  

## 1. Overview

The AIO Audit feature is a comprehensive website analysis system that leverages artificial intelligence to provide automated website audits for businesses. The feature combines web crawling capabilities from DataForSEO with OpenAI's GPT models to generate detailed insights, recommendations, and scoring across multiple categories.

### Purpose
- Provide automated website audits for business websites
- Generate AI-powered insights and recommendations
- Score websites across key categories
- Enable businesses to understand and improve their online presence
- Integrate seamlessly with existing listing products infrastructure

## 2. Technical Implementation

### 2.1 New Files/Modules Added

#### Core Workflow Implementation
- `internal/seo/workflow/aioauditworkflow.go` - Main workflow orchestration
- `internal/seo/workflow/aioauditservice.go` - Workflow service interface
- `internal/seo/workflow/aioauditworkflow_test.go` - Comprehensive test suite

#### Data Model and Storage
- `internal/seo/aioauditresult/model.go` - VStore data model definitions
- `internal/seo/aioauditresult/service/interface.go` - Service interface
- `internal/seo/aioauditresult/service/service.go` - Service implementation
- `internal/seo/aioauditresult/validators.go` - Input validation

#### DataForSEO Integration
- `internal/dataforseo/on_page.go` - Enhanced OnPage API integration (1041 lines)
- `internal/dataforseo/mock_client.go` - Mock client for testing

#### API Layer
- Enhanced `internal/api/seo_server.go` - New RPC endpoints and website analysis

#### SDK Generation
- Updated TypeScript SDKs with new interfaces and API methods
- Generated protobuf definitions for new message types

### 2.2 Modified Existing Functionality

#### Server Configuration
- `server/main.go` - Added AIOAudit service initialization and HTTP endpoint
- `server/temporal.go` - Integrated AIOAudit workflow into temporal setup

#### Workflow Infrastructure
- `internal/seo/workflow/workflow.go` - Added AIOAudit worker registration
- `internal/seo/workflow/activity_services.go` - Extended activity context

#### Constants and Configuration
- `internal/constants/constants.go` - Added `AIOAuditTaskList` constant

### 2.3 API Changes and New Endpoints

#### New RPC Methods
```protobuf
rpc GetAllAIOAudit(GetAllAIOAuditRequest) returns (GetAllAIOAuditResponse);
rpc GetAIOAudit(GetAIOAuditRequest) returns (GetAIOAuditResponse);
rpc GetAllAIOAuditScoreResults(GetAllAIOAuditScoreResultsRequest) returns (GetAllAIOAuditScoreResultsResponse);
rpc GetAIOAuditStatus(GetAIOAuditStatusRequest) returns (GetAIOAuditStatusResponse);
rpc TriggerAIOAudit(TriggerAIOAuditRequest) returns (TriggerAIOAuditResponse);
```

#### New HTTP Endpoint
- `POST /start-aioaudit-workflow` - Direct workflow trigger endpoint

### 2.4 Database Schema Changes

#### New VStore Kind: AIOAuditResult
```go
type AIOAuditResult struct {
    BusinessID        string               `vstore:"business_id"`
    BrandName         string               `vstore:"brand_name"`
    WebsiteURL        string               `vstore:"website_url"`
    AuditDate         string               `vstore:"audit_date"`
    StartDate         time.Time            `vstore:"start_date"`
    TotalPages        int64                `vstore:"total_pages"`
    AuditStatus       string               `vstore:"audit_status"`
    AuditSummary      string               `vstore:"audit_summary"`
    AuditPages        []*AuditPageData     `vstore:"audit_pages"`
    AuditScoreResults []*AuditScoreResults `vstore:"audit_score_results"`
    Created           time.Time            `vstore:"created"`
    Updated           time.Time            `vstore:"updated"`
    Deleted           time.Time            `vstore:"deleted"`
}
```

#### Composite Indexes
- `business_id_deleted_index` - For efficient business queries
- `business_id_audit_date_deleted_index` - For date-based filtering

## 3. Architecture

### 3.1 System Integration

The AIO Audit feature integrates with existing systems through:

1. **Temporal Workflow Engine** - Orchestrates long-running audit processes
2. **VStore Database** - Stores audit results and metadata
3. **DataForSEO API** - Provides web crawling and technical data
4. **OpenAI API** - Generates AI-powered insights and recommendations
5. **Existing Infrastructure** - Leverages established patterns and services

### 3.2 Component Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Layer     │    │  Workflow Layer  │    │  Data Layer     │
│                 │    │                  │    │                 │
│ • TriggerAudit  │───▶│ • AIOAuditWF     │───▶│ • VStore        │
│ • GetAudit      │    │ • Activities     │    │ • BigQuery      │
│ • GetStatus     │    │ • Error Handling │    │ • PubSub        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ External APIs   │    │   AI Services    │    │   Monitoring    │
│                 │    │                  │    │                 │
│ • DataForSEO    │    │ • OpenAI GPT-4   │    │ • Logging       │
│ • Robots.txt    │    │ • JSON Schema    │    │ • Metrics       │
│ • Website Crawl │    │ • Prompt Eng.    │    │ • Alerting      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 4. Data Flow

### 4.1 Audit Process Flow

1. **Trigger Phase**
   - Client calls `TriggerAIOAudit` RPC
   - System analyzes robots.txt for crawlability
   - Validates prerequisites (robots.txt exists and allows crawling)
   - Starts Temporal workflow if prerequisites met

2. **Crawling Phase**
   - Builds DataForSEO OnPage request with specific configuration
   - Initiates site crawl with max 50 pages
   - Polls crawl status until completion
   - Retrieves crawl results (summary and pages data)

3. **Analysis Phase**
   - Fetches raw HTML for each discovered URL
   - Sends data to OpenAI with structured JSON schema
   - Generates insights across 6 key categories:
     - Schema Markup
     - Structured Data
     - Robots.txt Enablement
     - Metadata
     - Content Quality
     - Performance

4. **Storage Phase**
   - Stores audit results in VStore
   - Updates audit status and summary
   - Maintains page-level and overall scoring data

### 4.2 Data Processing Pipeline

```
Website URL → Robots.txt Check → DataForSEO Crawl → Raw HTML Fetch → 
AI Analysis → Score Generation → VStore Storage → API Response
```

## 5. Configuration

### 5.1 Workflow Configuration
- **Task Queue**: `aioaudit-task-list`
- **Max Concurrent Activities**: 2
- **Activities Per Second**: 5
- **Workflow Timeout**: 7 days
- **Retry Policy**: 3 attempts with exponential backoff

### 5.2 Crawling Configuration
- **Max Crawl Pages**: 50
- **Crawl Mode**: Recursive
- **User Agent**: `GPTBot/1.0 (+https://openai.com/gptbot)`
- **Load Resources**: Enabled
- **Store Raw HTML**: Enabled
- **AI Content Analysis**: Enabled

### 5.3 AI Configuration
- **Model**: GPT-4 Omni
- **Temperature**: 0.1 (for consistent results)
- **Response Format**: JSON Object with structured schema
- **Timeout**: 10 seconds for HTTP requests

## 6. Dependencies

### 6.1 New Dependencies
- **go.uber.org/mock v0.5.2** - Moved from indirect to direct dependency for testing

### 6.2 External Service Dependencies
- **DataForSEO OnPage API** - Web crawling and technical data
- **OpenAI API** - AI-powered analysis and insights generation
- **Temporal Cloud** - Workflow orchestration and reliability

### 6.3 Internal Service Dependencies
- **VStore** - Data persistence and querying
- **Listing Profile Service** - Business context and metadata
- **Settings Service** - Configuration management
- **BigQuery** - Analytics and reporting integration

## 7. Testing Strategy

### 7.1 Unit Testing
- Comprehensive test suite with 529 lines of test code
- Mock implementations for external dependencies
- Activity-level and workflow-level testing
- JSON schema validation testing

### 7.2 Integration Testing
- DataForSEO API integration tests
- OpenAI API response parsing tests
- VStore CRUD operation tests
- End-to-end workflow execution tests

### 7.3 Test Coverage Areas
- Workflow execution paths (success and failure scenarios)
- Activity retry mechanisms
- Error handling and recovery
- Data validation and sanitization
- API endpoint functionality

## 8. Performance Considerations

### 8.1 Optimizations
- **Concurrent Processing**: Limited to 2 concurrent workflows to manage resource usage
- **Rate Limiting**: 5 activities per second to respect external API limits
- **Heartbeat Mechanism**: Progress tracking for long-running operations
- **Efficient Querying**: Composite indexes for fast data retrieval

### 8.2 Resource Management
- **Memory**: Structured data storage minimizes memory footprint
- **Network**: Batched API calls where possible
- **Storage**: Compressed JSON storage for large audit data
- **CPU**: Optimized JSON parsing and validation

### 8.3 Scalability Considerations
- Horizontal scaling through Temporal task queues
- Database sharding support through VStore
- Stateless activity design for easy scaling
- Configurable concurrency limits

## 9. Security Considerations

### 9.1 Authentication and Authorization
- IAM integration for API access control
- Business-scoped data access patterns
- Secure credential management for external APIs

### 9.2 Data Protection
- Audit data encrypted at rest in VStore
- Secure transmission of sensitive website data
- PII handling compliance for business information
- Audit trail for all data access and modifications

### 9.3 External API Security
- Secure API key management for DataForSEO and OpenAI
- Request validation and sanitization
- Rate limiting to prevent abuse
- Error message sanitization to prevent information leakage

## 10. Deployment Requirements

### 10.1 Infrastructure Requirements
- Temporal worker deployment with AIOAudit task queue
- VStore schema registration for new data models
- Environment variables for API credentials
- Monitoring and alerting configuration

### 10.2 Configuration Updates
- Add AIOAudit task queue to Temporal configuration
- Configure external API credentials (DataForSEO, OpenAI)
- Set up BigQuery integration for analytics
- Configure PubSub topics for audit result streaming

### 10.3 Migration Steps
1. Deploy new VStore schema
2. Update Temporal worker configuration
3. Deploy application code with new endpoints
4. Configure external API credentials
5. Enable monitoring and alerting
6. Perform smoke tests on staging environment

## 11. Future Considerations

### 11.1 Potential Improvements
- **Enhanced AI Models**: Integration with newer GPT models as they become available
- **Multi-language Support**: Extend analysis to non-English websites
- **Custom Scoring Models**: Business-specific scoring algorithms
- **Real-time Monitoring**: Continuous website health monitoring
- **Competitive Analysis**: Compare against competitor websites

### 11.2 Known Limitations
- **Crawl Depth**: Limited to 50 pages per audit
- **JavaScript Rendering**: Currently disabled for performance
- **Robots.txt Dependency**: Requires accessible robots.txt for audit initiation
- **Language Support**: Optimized for English content analysis
- **Rate Limits**: Constrained by external API quotas

### 11.3 Technical Debt
- **Error Handling**: Could be enhanced with more granular error types
- **Monitoring**: Additional metrics for audit quality assessment
- **Caching**: Implement caching for frequently accessed audit results
- **Documentation**: API documentation could be expanded with more examples

---

This RFC documents the comprehensive AIO Audit feature implementation, providing a foundation for future development and maintenance activities.
