// Code generated by the Vendasta codegen tool (github.com/vendasta/codegen). DO NOT EDIT.
// This means that any changes you make to this file may be overwritten by a subsequent run of the codegen tool, so be cautious.

package aioauditresults

import (
	"context"

	"github.com/vendasta/gosdks/logging"
	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
)

// Kind is a constant defining the VStore table for this package's model
// You can never change this.
// TODO: Ensure you use the const when calling vstore.NewSchema and that you only have 1 const for the kind
const Kind = "AIOAuditResults"

// RegisterKind registers the kind with vstore
func RegisterKind(ctx context.Context, vstoreClient vstore.Interface) error {
	_, err := vstoreClient.RegisterKind(ctx, Kind, Schema())
	if err != nil {
		logging.Errorf(ctx, "Error Registering Kind: %s", err)
		return err
	}
	return nil
}
