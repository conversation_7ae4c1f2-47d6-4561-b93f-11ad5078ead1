    1  git push
    2  cd internal/seo/workflow
    3  cd -
    4  git branch
    5  git status
    6  git add internal/seo/workflow/service_test.go  internal/seo/workflow/service.go internal/seo/workflow/mock_service.go
    7  mockgen
    8  cd internal/temporalwrapper
    9  mockgen -source interface.go -destination mock.go
   10  ls
   11  pwd
   12  cd -
   13  git status
   14  git add .
   15  git status
   16  git commit -m "Adding test cases for sonarqube"
   17  git push
   18  git status
   19  git commit -am "added testcase"
   20  git push
   21  git commit -am "Adding Testcases"
   22  git push
   23  git commit -am "Adding Testcases"
   24  git push
   25  git status
   26  git status
   27  git commit -am "Remove comment from the query"
   28  git push
   29  git show
   30  git status
   31  git diff
   32  git commit -am "Remove comment from the query"
   33  git push
   34  git checkout master
   35  git pull
   36  git checkout master
   37  git pull
   38  git branch -r
   39  git checkout LIS-2053-KInd-BingClickSourceInsights&BingSimilarBusinessInsights
   40  git checkout -b LIS-2053-KInd-BingClickSourceInsights&BingSimilarBusinessInsights
   41  git branch 
   42  git checkout -r LIS-2053-KInd-BingClickSourceInsights&BingSimilarBusinessInsights
   43  git checkout -b LIS-2053-KInd-BingClickSourceInsights&BingSimilarBusinessInsights
   44  git checkout -b LIS-2053-KInd-BingClickSourceInsights'&'BingSimilarBusinessInsights
   45  git log
   46  git pull
   47  git status
   48  git commit -am "Resolve PR Comments"
   49  git push
   50   git push --set-upstream origin LIS-2053-KInd-BingClickSourceInsights&BingSimilarBusinessInsights
   51   git push --set-upstream origin LIS-2053-KInd-BingClickSourceInsights'&'BingSimilarBusinessInsights
   52  git branch 
   53  git status
   54  git commit -am "Resolve PR Comments"
   55   git push --set-upstream origin LIS-2053-KInd-BingClickSourceInsights'&'BingSimilarBusinessInsights
   56  git pull
   57   git push --set-upstream origin LIS-2053-KInd-BingClickSourceInsights'&'BingSimilarBusinessInsights
   58   git pull --set-upstream origin LIS-2053-KInd-BingClickSourceInsights'&'BingSimilarBusinessInsights
   59   git push --set-upstream origin LIS-2053-KInd-BingClickSourceInsights'&'BingSimilarBusinessInsights
   60  git pull
   61  git branch
   62  git revert b1dd15ffcef9e6f7bbb0048b6795d7667eac021b
   63  git stash
   64  git revert b1dd15ffcef9e6f7bbb0048b6795d7667eac021b
   65  git status
   66  git add .
   67  git status
   68  git push
   69  git status
   70  git commit -am "Adding test and mock files"
   71  git push
   72  git log
   73  git revert 65c64a6d562f2670ad17e85194f09c677098404f
   74  git revert -m 65c64a6d562f2670ad17e85194f09c677098404f
   75  git status
   76  git log
   77  git log
   78  git revert --no-commit 1224c05ad522d9d2ebc0ca89655b6132cbaf60d7
   79  git stash
   80  git status
   81  git stash
   82  git stash -force
   83  git stash -a
   84  git revert --no-commit 65c64a6d562f2670ad17e85194f09c677098404f
   85  git status
   86  git revert --abort
   87  git status
   88  git log
   89  git revert --no-commit 1224c05ad522d9d2ebc0ca89655b6132cbaf60d7
   90  git revert --no-commit 0c3c40f6b87e043e96982171599c64867e2edd19
   91  git revert --no-commit 65c64a6d562f2670ad17e85194f09c677098404f
   92  git revert -m 1 65c64a6d562f2670ad17e85194f09c677098404f
   93  git status
   94  git revert --continue
   95  git log
   96  git revert -m 1 65c64a6d562f2670ad17e85194f09c677098404f
   97  git log
   98  git revert --no-commit b1dd15ffcef9e6f7bbb0048b6795d7667eac021b
   99  git status
  100  git status
  101  git log
  102  git revert --no-commit 271f0926bccfddf58aaad53b185ff5624618cd23
  103  git revert --no-commit 271f0926bccfddf58aaad53b185ff5624618cd23
  104  git revert --no-commit 2c95c5eb33f5b1e3d35dd2cd02d7e6ea334b1022
  105  git status
  106  git stash
  107  git checkout master
  108  git status
  109  git stash
  110  cd ..
  111  cd -
  112  cd /Users/<USER>/Desktop/Vendasta/
  113  git clone https://github.com/vendasta/listing-products.git
  114  cd listing-products
  115  git checkout master
  116  git pull
  117  cd ..
  118  git clone https://github.com/vendasta/atlas.git
  119  cd listing-products
  120  cd -
  121  git clone https://github.com/vendasta/google-my-business.git
  122  pwd
  123  cd -
  124  git branch
  125  git checkout -b LIS-2053-Change-date-totimestamp
  126  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  127  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  128  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  129  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  130  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  131  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingdeviceplatforminsights -s BingDevicePlatfromInsights
  132  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingdeviceplatforminsights -s BingDevicePlatformInsights
  133  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingdeviceplatforminsights -s BingDevicePlatformInsights
  134  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingsimilarbusinessinsights -s BingSimilarBusinessInsights
  135  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  136  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  137  git status
  138  git commit -am "Adding Bq and change date to time stamp"
  139  git push
  140  git push --set-upstream origin LIS-2053-Change-date-totimestamp
  141  git status
  142  git commit -am "change build"
  143  git push
  144  gcloud init
  145  git status
  146  git commit -am 
  147  git commit -am "Changed property"
  148  git push
  149  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  150  git commit -am "Changed property for bingclickinsights"
  151  git diff
  152  gti show
  153  git show
  154  git status
  155  git status
  156  git push
  157  git checkout master
  158  git pull
  159  git checkout master
  160  git pull
  161  git status
  162  cd ..
  163  git clone https://github.com/vendasta/listing-products.git
  164  cd listing-products
  165  git checkout master
  166  git pull 
  167  git checkout -b LIS-2053-Adding-Bigquery-to-kind
  168  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  169  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingsimilarbusinessinsights -s BingSimilarBusinessInsights
  170  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingdeviceplatforminsights -s BingDevicePlatformInsights
  171  git status
  172  git commit -am "Adding BigQuery and creating sample Data for all 3 Kind"
  173  git push
  174   git push --set-upstream origin LIS-2053-Adding-Bigquery-to-kind
  175  git branch
  176  git status
  177  git commit -am "Change initialization point and data entry point"
  178  git push
  179  git branch
  180  git pull
  181  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  182  git stash
  183  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  184  git stash
  185  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingclicksourceinsights -s BingClickSourceInsights
  186  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingdeviceplatforminsights -s BingDevicePlatformInsights
  187  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingsimilarbusinessinsights -s BingSimilarBusinessInsights
  188  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingsimilarbusinessinsights -s BingSimilarBusinessInsights
  189  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingsimilarbusinessinsights -s BingSimilarBusinessInsights
  190  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingsimilarbusinessinsights -s BingSimilarBusinessInsights
  191  codegen model -p github.com/vendasta/listing-products/internal/binginsights/bingsimilarbusinessinsights -s BingSimilarBusinessInsights
  192  git status
  193  git status
  194  git status
  195  git commit -am "Adding timestamp field and add 10 records to check timeseries in UI"
  196  git status
  197  git commit -am "Adding timestamp field and add 10 records to check timeseries in UI"
  198  pwd
  199  git add .
  200  git status
  201  git add .
  202  git status
  203  git add .
  204  git add .
  205  rm .git/index.lock
  206  git status
  207  git add .
  208  git status
  209  git commit -am "Adding timestamp field and add 10 records to check timeseries in UI"
  210  git push
  211  git pull
  212  git config pull.rebase false
  213  git push
  214  git pull
  215  git log
  216  git status
  217  git diff
  218  git show
  219  git log
  220  git diff dde9917e031221337a86e7b2879a3273eab2b48c
  221  git status
  222  git status
  223  git diff
  224  git commit -am "Change key for tesseract"
  225  git push
  226  git branch
  227  git branch
  228  git pull
  229  git checkout -b Sample-Data-for-binginsights
  230  git status
  231  git commit -am "Sample data for kind"
  232  git push
  233  git push --set-upstream origin Sample-Data-for-binginsights
  234  git commit -am "Sample data for kind"
  235  git push
  236  git commit -am "Adding data for valid AGID"
  237  git push
  238  git checkout master
  239  go install github.com/vendasta/vetl/cli/vetl
  240  vetl
  241  vetl
  242  go mod tidy
  243  curl --location 'https://tesseract-api-demo.vendasta-internal.com/tesseract.v1.Admin/DeleteKind' \\n--header 'Content-Type: application/json' \\n--data '{\n  "namespace": "listing-products",\n  "kind": "BingClickSourceInsights"\n}'
  244  git master
  245  git checkout master
  246  git pull
  247  git checkout master
  248  git pull
  249  npm
  250  git pull
  251  git checkout -b LIS-2089-add-insights-posthog
  252  npm install
  253  git status
  254  git add .
  255  git commit -am "Adding posthog to insights"
  256  git push
  257  git push --set-upstream origin LIS-2089-add-insights-posthog
  258  git commit -am "Adding Properties"
  259  git commit -am "Adding Properties"git push
  260  git push
  261  git checkout master
  262  git pull
  263  cd ..
  264  git checkout https://github.com/vendasta/advertising.git
  265  git clone https://github.com/vendasta/advertising.git
  266  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  267  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  268  git branch -r
  269  git checkout origin/LIS-2104-Keyword-Metrics-Logic
  270  git branch
  271  git checkout  LIS-2104-Keyword-Metrics-Logic
  272  git pull
  273  git log
  274  ./local.sh
  275  chmod u+x local.sh
  276  ./local.sh
  277  /bin/zsh local.sh
  278  /bin/zsh local.sh
  279  /bin/zsh local.sh
  280  /bin/zsh /Users/<USER>/Desktop/Vendasta/advertising/local.sh
  281  chmod u-x local.sh
  282  /bin/zsh /Users/<USER>/Desktop/Vendasta/advertising/local.sh
  283  clear
  284  /bin/zsh /Users/<USER>/Desktop/Vendasta/advertising/local.sh
  285  kubectl
  286  kubectl config
  287  kubectl config view
  288  kubectl config current-context
  289  gcloud auth login\n\ngcloud auth application-default login\n\ngcloud auth configure-docker\n\nkubectl config set-context gke_repcore-prod_us-central1_vendasta-central\n\ngcloud beta container clusters get-credentials vendasta-central --region us-central1 --project repcore-prod
  290  /bin/zsh /Users/<USER>/Desktop/Vendasta/advertising/local.sh
  291  cd ..
  292  git clone https://github.com/vendasta/codegen.git
  293  git checkout master
  294  git pull
  295  cd ..
  296  cd ..
  297  cd ..
  298  cd Downloads
  299  cd sample
  300  git mod init
  301  go mod init
  302  git mod init googleadsapi
  303  go mod init googleadsapi
  304  go mod tidy
  305  go get google.golang.org/genproto/googleapis/ads/googleads/v13/services
  306  go get google.golang.org/genproto
  307  \ngo get google.golang.org/api/option\ngo get google.golang.org/genproto/googleapis/ads/googleads/v13/services\ngo get google.golang.org/genproto/googleapis/ads/googleads/v13/enums\ngo get google.golang.org/genproto/googleapis/ads/googleads/v13/common\n
  308  go clean -modcache\ngo get google.golang.org/genproto@latest\n
  309  ^[[200~go list -m -versions google.golang.org/genproto
  310  ~
  311  go list -m -versions google.golang.org/genproto\n
  312  go get google.golang.org/genproto@latest
  313  go mod init
  314  go mod tidy
  315  go get google.golang.org/genproto@latest\n
  316  go list -m google.golang.org/genproto@latest\n
  317  ls
  318  go mod tidy
  319  go mod tidy
  320  go mod tidy
  321  go mod tidy
  322  go mod tidy
  323  go mod tidy
  324  go get google.golang.org/genproto/googleapis/ads/googleads/v13/services\ngo get google.golang.org/api/option\n
  325  go get  google.golang.org/genproto@upgrade
  326  go get google.golang.org/genproto/googleapis/ads/googleads/v13/services\ngo get google.golang.org/api/option\n
  327  go get google.golang.org/genproto@latest\n
  328  go mod init
  329  go mod tidy
  330  go mod tidy
  331  go mod tidy
  332  cd -
  333  cd ..
  334  go mod tidy
  335  go get go.opencensus.io@v0.24.0
  336  go install golang.org/x/tools/cmd/goimports@latest\n\n
  337  $ go get -u go.opencensus.io
  338  go get -u go.opencensus.io
  339  go get -u go.opencensus.io
  340  go get -u go.opencensus.io@latest
  341  go get -u go.opencensus.io
  342  go install go.opencensus.io
  343  go get -u go.opencensus.io
  344  go get -u go.opencensus.io
  345  go mod tidy
  346  go mod tidy
  347  go get -u go.opencensus.io
  348  go mod edit -replace go.opencensus.io=github.com/census-instrumentation/opencensus-go@v0.22.5\ngo mod tidy\n
  349  go mod edit -replace go.opencensus.io=github.com/census-instrumentation/opencensus-go@v0.24.0
  350  go mod tidy
  351  go mod edit -replace go.opencensus.io=github.com/census-instrumentation/opencensus-go@v0.22.5\n
  352  go mod tidy
  353  cd vendor
  354  git clone https://github.com/census-instrumentation/opencensus-go.git\n
  355  cd opencensus-go
  356  git checkout v0.24.0
  357  git checkout v0.22.5
  358  cd ..
  359  cd ..
  360  go mod tidy
  361  go get go.opentelemetry.io/otel/exporters/metric/stackdriver\n
  362  git statsu
  363  git status
  364  cd advertising
  365  git checkout master
  366  .ls
  367  ./local.sh
  368  chmod u+x local.sh
  369  ./local.sh
  370  cd ..
  371  git clone https://github.com/vendasta/multi-location-analytics.git
  372  git branch
  373  git pull
  374  git stash
  375  git checkout master
  376  git pull
  377  git branch
  378  git pull
  379  git branch
  380  git status
  381  git commit -am "update logs"
  382  git push
  383  git commit -am "Comment ack for demo"
  384  git push
  385  git status
  386  git commit -am "comment ack for demo env"
  387  git push
  388  git log
  389  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  390  go install github.com/vendasta/codegen@latest
  391  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  392  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  393  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  394  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  395  go install github.com/vendasta/codegen@latest
  396  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  397  codegen -v
  398  codegen version
  399  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  400  go install github.com/vendasta/codegen@latest
  401  go install github.com/vendasta/codegen@latest
  402  go install github.com/vendasta/codegen@V0.22.5
  403  go install github.com/vendasta/codegen@v0.22.5
  404  go install github.com/vendasta/codegen@latest
  405  go install github.com/vendasta/codegen@latest
  406  export GOPROXY='https://proxy.golang.org,direct'
  407  go install github.com/vendasta/codegen@latest
  408  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  409  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  410  codegen model -p github.com/vendasta/listing-products/internal/seo/seogooglekeywordinfo -s SEOGoogleKeywordInfo
  411  codegen model -p github.com/vendasta/listing-products/internal/seo/seogooglekeywordinfo -s SEOGoogleKeywordInfo
  412  git status
  413  git status
  414  git restore  internal/seo/service/interface.go
  415  git status
  416  git status
  417  git add .
  418  git commit -am "Adding seogooglekeywordinfo package"
  419  git push
  420  git push --set-upstream origin LIS-2105-Data-parsing-storage
  421  git commit -am "Fixing build failure"
  422  git push
  423  git pull
  424  git status
  425  git commit -am "Initialize vstoreclinet for kind"
  426  git push
  427  git pull
  428  mscli app register vstore/update/SEOGoogleKeywordInfo -e demo
  429  mscli app register vstore/update/SEOGoogleKeywordInfo -e demo
  430  mscli app register vstore/update/SEOGoogleKeywordInfo -e demo
  431  mscli app register vstore/update/SEOGoogleKeywordInfo -e demo
  432  mscli app register
  433  mscli app register vstore/update/SEOGoogleKeywordInfo -e demo
  434  mscli app register 
  435  git checkout -b Lis-2105-Updated-data-parsing
  436  git status
  437  git commit -am "Register Schema"
  438  git diff
  439  git show
  440  git push
  441  git push --set-upstream origin Lis-2105-Updated-data-parsing
  442  mscli app register 
  443  mscli app register vstore/update/SEOGoogleKeywordInfo -e demo
  444  mscli app register vstore/update/SEOGoogleKeywordInfo -e prod
  445  git checkout master
  446  git pull
  447  git checkout -b Lis-2092-keyword-error-tracking
  448  codegen model -p github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo -s SEOFailedWorkflowInfo
  449  codegen model -p github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo -s SEOFailedWorkflowInfo
  450  codegen model -p github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo -s SEOFailedWorkflowInfo
  451  git status
  452  git restore internal/binginsights/bingclicksourceinsights/model.go
  453  git status
  454  codegen model -p github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo -s SEOFailedWorkflowInfo
  455  git status
  456  git add .
  457  git commit -am "Add base functionality for vstore kind"
  458  git push
  459  git push --set-upstream origin Lis-2092-keyword-error-tracking
  460  git status
  461  git commit -am "Initialize the kind service in main"
  462  git push
  463  git status
  464  git commit -am "Remove tesseract init"
  465  git push
  466  git status
  467  git commit -am "fix build issue"
  468  git push
  469  git checkout master
  470  git pull
  471  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  472  clear
  473  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  474  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  475  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  476  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  477  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  478  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  479  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  480  cd ..
  481  cd ..
  482  cd ..
  483  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  484  clear
  485  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  486  clear
  487  go install golang.org/x/tools/cmd/goimports@latest
  488  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  489  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  490  clear
  491  codegen model -p github.com/vendasta/listing-products/internal/seo/googlekeywordinfo -s SEOGoogleKeywordInfo
  492  git pull
  493  git log
  494  git diff 96726e5fc435a05ed2aa37c2e853e2349eabf79a
  495  git branch
  496  git pull
  497  git branch
  498  git pull
  499  git checkout -b Common-service-for-bingInsights
  500  git status
  501  git add .
  502  git status
  503  git commit -am "Creating Common Service for all bing Insights"
  504  git push
  505  git push --set-upstream origin Common-service-for-bingInsights
  506  git status
  507  git commit -am "Change workflow to call service"
  508  git push
  509  git branch
  510  git checkout master
  511  git pull
  512  cd ..
  513  git clone https://github.com/vendasta/listing-syndication.git
  514  git branch'\n,
  515  git branch
  516  cd listing-products
  517  git branch
  518  git pull
  519  ls
  520  git pull
  521  git pull
  522  git branch 
  523  git branch Common-service-for-bingInsights
  524  git checkout Common-service-for-bingInsights
  525  git branch --unset-upstream
  526  git pull
  527  git branch --set-upstream-to=origin/Common-service-for-bingInsights Common-service-for-bingInsights
  528  git fetch
  529  git branch
  530  git 
  531  git checkout master
  532  git pull
  533  git checkout -b LIS-2105-Data-parsing-storage
  534  cd ..
  535  ls
  536  cd -
  537  git branch
  538  git checkout master
  539  git pull
  540  git checkout -b LIS-2115-NR-for-rank
  541  git pull master
  542  git checkout master
  543  git pull
  544  git branch
  545  git checkout LIS-2115-NR-for-rank
  546  git status
  547  git commit -am "Adding logic to autofill ranks for pro accounts"
  548  git push
  549   git push --set-upstream origin LIS-2115-NR-for-rank
  550  git status
  551  git commit -am "Add logs to know getseodata"
  552  git push
  553  git status
  554  git commit -am "Add logs to know getseodata"
  555  git push
  556  git commit -am "Add logs to know getseodata"
  557  git push
  558  git status
  559  git commit -am "adding more logs"
  560  git push
  561  git branch
  562  git status
  563  git commit -am "Adding logs to check on prod data"
  564  git push
  565  git status
  566  git commit -am "Adding logs to check on prod data"
  567  git push
  568  git commit -am "Adding logs to check on prod data"
  569  git push
  570  git status
  571  git diff
  572  git commit -am "Add logic and add logs"
  573  git push
  574  git status
  575  git commit -am "fixing build issue"
  576  git push
  577  git status
  578  git commit -am "fixing build issue"
  579  git push
  580  git status
  581  git add .
  582  git commit -am "Removing upserting method for seodata"
  583  git push
  584  git status
  585  git commit -am "removing missing calls in testfile"
  586  git push
  587  git status
  588  git status
  589  git status
  590  git commit -am "Address PR comments"
  591  git diff
  592  git push
  593  git checkout master
  594  git pull
  595  git checkout master
  596  git pull
  597  ls
  598  git branch
  599  git checkout -b LIS-2105-Data-parsing-storage
  600  git checkout LIS-2105-Data-parsing-storage
  601  git checkout master
  602  git pull
  603  git checkout Lis-2105-Data-parsing-storage
  604  '\n\n'
  605  git commit -am "Base commit with model file"
  606  git push
  607  git push --set-upstream origin Lis-2105-Data-parsing-storage
  608  git push
  609  git push --set-upstream origin Lis-2105-Data-parsing-storage
  610  git push origin
  611  git push origin Lis-2105-Data-parsing-storage
  612  git checkout master
  613  git pull
  614  git branch
  615  git pull
  616  '\n\n\n\n'
  617  git status
  618  git master
  619  git checkout master
  620  git pull
  621  git checkout -b LIS-2124-ER-Keyword-NR
  622  git commit -am "base commit "
  623  git push
  624  git push --set-upstream origin LIS-2124-ER-Keyword-NR
  625  git checkout master
  626  git checkout master
  627  git pull
  628  git checkout -b refractoring-bing-insights
  629  git status
  630  git commit -am "changed ratelimitter"
  631  git push
  632  git push --set-upstream origin refractoring-bing-insights
  633  pwd
  634  cd Desktop
  635  go
  636  git clone https://github.com/vendasta/setup-new-computer-script.git
  637  ssh-keygen -t ed25519 -C "<EMAIL>"\n
  638   eval "$(ssh-agent -s)"
  639  open ~/.ssh/config
  640  touch ~/.ssh/config
  641  vim ~/.ssh/config
  642  pbcopy < ~/.ssh/id_ed25519.pub
  643  cd setup-new-computer-script
  644  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/vendasta/setup-new-computer-script/master/setup-new-computer.sh)"
  645  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/vendasta/setup-new-computer-script/master/setup-new-computer.sh)"
  646  cd Desktop
  647  cd setup-new-computer-script
  648  cd ..
  649  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/vendasta/setup-new-computer-script/master/setup-new-computer.sh)"
  650  mkdir Vendasta
  651  cd Vendasta
  652  git clone https://github.com/vendasta/listing-products.git
  653  git clone https://github.com/vendasta/listing-products.git
  654  git clone https://github.com/vendasta/listing-products.git
  655  git clone https://github.com/vendasta/galaxy.git
  656  cd ..
  657  cd ..
  658  cd Downloads
  659  cd Vendasta
  660  git clone https://github.com/vendasta/listing-products.git
  661  mscli app register
  662  mscli
  663  git clone https://github.com/vendasta/mscli.git
  664  pwd
  665  cd ..
  666  cd ..
  667  cd Desktop/Vendasta
  668  git clone https://github.com/vendasta/mscli.git
  669  cd mscli
  670  git checkout master
  671  git pull
  672  git install -mod=vendor
  673  go install -mod=vendor
  674  mscli
  675  go install -mod=vendor
  676  bash
  677  go
  678  restart
  679  go
  680  mscli app register vstore/delete/BingClickSourceInsights -e demo
  681  ls
  682  cd ..
  683  vetl -v
  684  pwd
  685  go install github.com/vendasta/vetl/cli/vetl
  686  go install github.com/vendasta/vetl/cli/vetl@latest
  687  vetl -v
  688  curl
  689  curl --help
  690  curl --location 'https://tesseract-api-demo.vendasta-internal.com/tesseract.v1.Admin/DeleteKind' \\n--header 'Content-Type: application/json' \\n--data '{\n  "namespace": "listing-products",\n  "kind": "BingClickSourceInsights"\n}'
  691  curl --location 'https://tesseract-api-demo.vendasta-internal.com/tesseract.v1.Admin/DeleteKind'\n--header 'Content-Type: application/json' \n--data '{\n  "namespace": "listing-products",\n  "kind": "BingClickSourceInsights"\n}'
  692  curl --location 'https://tesseract-api-demo.vendasta-internal.com/tesseract.v1.Admin/DeleteKind' \\n--header 'Content-Type: application/json' \\n--data '{\n  "namespace": "listing-products",\n  "kind": "BingClickSourceInsights"\n}'
  693  curl --location 'https://tesseract-api-demo.vendasta-internal.com/tesseract.v1.Admin/DeleteKind' \\n--header 'Content-Type: application/json' \\n--data '{\n  "namespace": "listing-products",\n  "kind": "BingClickSourceInsights"\n}'
  694  cd ..
  695  cd ..
  696  cd Downloads
  697  grep
  698  grep -r "id" workflows-54-1736275286205.json
  699  grep -r "id" workflows-54-1736275286205.json | print $2
  700  grep -r "id" workflows-54-1736275286205.json | print $1
  701  grep -rf "id" workflows-54-1736275286205.json 
  702  grep -rn "id" workflows-54-1736275286205.json 
  703  grep -rn "id" workflows-54-1736275286205.json | xargs
  704  grep -rn "id" workflows-54-1736275286205.json | xargs print $1
  705  grep -o 'AG-[^"]*' workflows-54-1736275286205.json\n
  706  grep -o '"id": ".*AG-[^"]*' workflows-54-1736275286205.json\n
  707  grep -o '"id": ".*AG-[^"]*' workflows-54-1736275286205.json |  grep -o 'AG-[^"]*'
  708  grep -o '"id": ".*AG-[^"]*' workflows-54-1736275286205.json |  grep -o 'AG-[^"]*' | sed 's/-[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\}$//'
  709  cd /tmp/advertising-local
  710  pwd
  711  ls
  712  pwd
  713  vi elastic7_key.txt
  714  pwd
  715  cd 
  716  cd Desktop/Vendasta/advertising
  717  ls
  718  ./local.sh
  719  cd ../
  720  cd ../
  721  ls
  722  cd ..
  723  ls
  724  cd Users/aprasath/
  725  ls
  726  cd Desktop/Vendasta/advertising
  727  /bin/zsh /Users/<USER>/Desktop/Vendasta/advertising/local.sh
  728  mscli app register
  729  git checkout -b lis-2092-vstore-creation
  730  git status
  731  git commit -am "Add schema call"
  732  git push
  733  git push --set-upstream origin lis-2092-vstore-creation
  734  git diff
  735  git show
  736  codegen model -p github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo -s SEOFailedWorkflowInfo
  737  codegen model -p github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo -s SEOFailedWorkflowInfo
  738  git status
  739  git retore server/main.go
  740  git restore server/main.go
  741  git status
  742  git status
  743  git status
  744  git status
  745  git restore internal/seo/workflow/activity_services.go
  746  git restore internal/seo/workflow/keyword_info_workflow_test.go
  747  git status
  748  git commit -am "Make workflowtype as primary key"
  749  git push
  750  git push --set-upstream origin LIS-2094-Failed-Workflow-Management
  751  codegen model -p github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo -s SEOFailedWorkflowInfo
  752  git status
  753  git commit -am "add functionality for retry keywordinfo work flow"
  754  git status
  755  git status
  756  git commit -am "Adding retry functionality"
  757  git push
  758  git status
  759  git commit -am "Adding mechanism for keyword workflow retry"
  760  git push
  761  git status
  762  git status
  763  git commit -am "add logs"
  764  git push
  765  git status
  766  git commit -am "add logs"
  767  git push
  768  git status
  769  git commit -am "adding error type in logs"
  770  git push
  771  git status
  772  git commit -am "adding error type in logs"
  773  git push
  774  git commit -am "adding error type in logs"
  775  git push
  776  git status
  777  git status
  778  git commit -am "Evaluting Error msg in log"
  779  git diff
  780  git show
  781  git push
  782  git status
  783  git commit -am "Change to temporal workflow"
  784  git push
  785  git status
  786  git status
  787  git commit -am "Adding Monitor logic to workflow"
  788  git push
  789  git status
  790  git status
  791  git commit -am "adding logs in monitor status"
  792  git push
  793  git status
  794  git checkout master
  795  git pull
  796  mscli app register vstore/update/SEOFailedWorkflowInfo -e demo
  797  mscli app register vstore/update/SEOFailedWorkflowInfo -e prod
  798  mscli app register
  799  mscli app register vstore/update/SEOFailedWorkflowInfo -e prod
  800  git pull
  801  git checkout -b LIS-2094-Failed-Workflow-Management
  802  git status
  803  git checkout master
  804  git pull
  805  git pull
  806  mscli app register
  807  mscli app register vstore/delete/SEOFailedWorkflowInfo -e demo
  808  mscli app register vstore/delete/SEOFailedWorkflowInfo -e prod
  809  mscli app register vstore/update/SEOFailedWorkflowInfo -e demo
  810  mscli app register vstore/update/SEOFailedWorkflowInfo -e prod
  811  kubectl config current-context
  812  history | grep kubectl
  813  go mod tidr
  814  go mod tidy
  815  git checkout master
  816  git pull
  817  git checkout -b LIS-2094-Add-missing-schema-call
  818  git status
  819  git log
  820  git checkout master
  821  git log
  822  git status
  823  git push
  824  git push --set-upstream origin LIS-2094-Add-missing-schema-call
  825  git log
  826  git pull
  827  git log
  828  git checkout -b LIS-2093-Workflow-Error-Handling
  829  git status
  830  git restore internal/seo/workflow/seofailedworkflowinfo/service/service.go
  831  git status
  832  go mod vendor
  833  git rebase origin/master\n
  834  git rebase origin/master\n
  835  git fetch origin
  836  git log
  837  git reset --hard 8364d2b3
  838  git log
  839  go get github.com/vendasta/generated-protos-go/advertising@v0.0.0-20250220204113-44410e140180
  840  go get github.com/vendasta/generated-protos-go/advertising@v0.0.0-20250221072803-f1e5e0c2c040
  841  go get github.com/vendasta/advertising/sdks/go@v0.0.0-20250220204113-44410e140180
  842  go get github.com/vendasta/advertising/sdks/go@master
  843  go mod vendor
  844  git fetch origin
  845  git pull
  846  git branch
  847  npm i
  848  git branch -r
  849  git checkout -b LIS-2161-Top10-Graph-2
  850  git fetch
  851  git pull
  852  git branch 
  853  git branch -d LIS-2161-Top10-Graph-2
  854  git branch -D LIS-2161-Top10-Graph-2
  855  git branch
  856  git pull
  857  git pull
  858  git config pull.rebase true 
  859  git status
  860  cd Downloads
  861  ls
  862  grep -rn "failed_precondition" downloaded-logs-20250217-184548.json
  863  grep -r "failed_precondition" downloaded-logs-20250217-184548.json
  864  grep -r "failed_precondition" downloaded-logs-20250217-184548.json
  865  grep "failed_precondition" downloaded-logs-20250217-184548.json\n
  866  grep "failed_precondition" downloaded-logs-20250217-184548.json | sed 's/.*): //'\n
  867  grep "failed_precondition" downloaded-logs-20250217-184548.json | awk -F'): ' '{print $2}'\n
  868  grep "failed_precondition" downloaded-logs-20250217-184548.json | awk -F'): ' '{print $2}' | grep "no keywords set"\n
  869  grep "failed_precondition" downloaded-logs-20250217-184548.json | awk -F'): ' '{print $2}' | grep -v "no keywords set"\n
  870  grep "failed_precondition" downloaded-logs-20250217-184548.json | awk -F'): ' '{print $2}'\n
  871  grep "failed_task" downloaded-logs-20250217-184548.json | awk -F'): ' '{print $2}'\n
  872  grep "failed_precondition" downloaded-logs-20250217-184548.json | awk -F'): ' '{print $2}'\n
  873  grep "failed_task" downloaded-logs-20250217-184548.json\n
  874  grep "failed_precondition" downloaded-logs-20250217-184548.json | awk -F'): ' '{print $2}' | grep -v "no keywords set"\n
  875  grep "failed_precondition" downloaded-logs-20250217-184548.json | grep -v "no keywords set"\n
  876  grep "unexpected_error" downloaded-logs-20250217-184548.json
  877        "Message": "SEO workflow failed: activity error (type: buildRequests, scheduledEventID: 10, startedEventID: 11, identity: 1@listing-products-5fccfb7f69-nw9h9@): error fetching listing profile (type: unexpected_error, retryable: true): https://cloud.temporal.io/namespaces/listings-products-prod.iwgwg/workflows/SERPWorkflow-AG-3WPWGG7L8P-2025-01-31/0194bb08-78dc-7b42-9bd5-0df4a1788da7/history",\n
  878        "Message": "SEO workflow failed: activity error (type: buildRequests, scheduledEventID: 10, startedEventID: 11, identity: 1@listing-products-5fccfb7f69-nw9h9@): error fetching listing profile (type: unexpected_error, retryable: true): https://cloud.temporal.io/namespaces/listings-products-prod.iwgwg/workflows/SERPWorkflow-AG-3WPWGG7L8P-2025-01-31/0194bb08-78dc-7b42-9bd5-0df4a1788da7/history",\n
  879  grep "unexpected_error" downloaded-logs-20250217-184548.json
  880        "Message": "SEO workflow failed: activity error (type: buildRequests, scheduledEventID: 10, startedEventID: 11, identity: 1@listing-products-5fccfb7f69-nw9h9@): error fetching listing profile (type: unexpected_error, retryable: true): https://cloud.temporal.io/namespaces/listings-products-prod.iwgwg/workflows/SERPWorkflow-AG-3WPWGG7L8P-2025-01-31/0194bb08-78dc-7b42-9bd5-0df4a1788da7/history",\n
  881  grep "unexpected_error" downloaded-logs-20250217-184548.json
  882  grep "failed_task" downloaded-logs-20250217-184548.json\n
  883  grep "failed_precondition" downloaded-logs-20250217-184548.json\n
  884  grep "failed_precondition" downloaded-logs-20250217-184548.json | wc -l\n 
  885  grep "failed_precondition" downloaded-logs-20250217-184548.json | grep -v "no keywords set" | wc -l\n
  886  grep "failed_precondition" downloaded-logs-20250217-184548.json | grep -v "no keywords set"
  887  grep "failed_task" downloaded-logs-20250217-184548.json\n
  888  grep "unexpected_error" downloaded-logs-20250217-184548.json
  889  grep "unexpected_error" downloaded-logs-20250217-184548.json | wc -l
  890  grep "failed_task" downloaded-logs-20250217-184548.json\n
  891  grep "failed_precondition" downloaded-logs-20250217-184548.json | grep -v "no keywords set"
  892  grep "failed_precondition" downloaded-logs-20250220-145724.json
  893  grep "failed_precondition" downloaded-logs-20250220-145724.json | wc -l
  894  grep "unexpected_error" downloaded-logs-20250220-145724.json | wc -l
  895  grep "unexpected_error" downloaded-logs-20250220-145724.json
  896  grep "failed_task" downloaded-logs-20250220-145724.json
  897  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/vendasta/setup-new-computer-script/master/setup-new-computer.sh)"
  898  cd ..
  899  cd Desktop/Vendasta/galaxy
  900  npm install
  901  npm install -g npm@11.2.0
  902  ENV=demo PID=ABC npm run start business-center-client
  903  git status
  904  ENV=demo PID=ABC npm run start business-center-client
  905  ENV=demo PID=ABC npm run start business-center-client
  906  ENV=demo PID=ABC npm run start:fast business-center-client
  907  ENV=demo PID=ABC npm run start:fast business-center-client
  908  ENV=prod PID=ABC npm run start:fast business-center-client
  909  ENV=prod PID=RCF0 npm run start:fast business-center-client
  910  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  911  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  912  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  913  npm i
  914  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  915  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  916  npm install
  917  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  918  npm i
  919  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  920  npm i
  921  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  922  npm i
  923  npm install
  924  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  925  npm install
  926  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  927  npm install
  928  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  929        "Message": "SEO workflow failed: activity error (type: buildRequests, scheduledEventID: 10, startedEventID: 11, identity: 1@listing-products-5fccfb7f69-nw9h9@): error fetching listing profile (type: unexpected_error, retryable: true): https://cloud.temporal.io/namespaces/listings-products-prod.iwgwg/workflows/SERPWorkflow-AG-3WPWGG7L8P-2025-01-31/0194bb08-78dc-7b42-9bd5-0df4a1788da7/history",\n
  930  history
  931  PROXY_ENV=prod PID=RCF0 npm run start:fast listing-builder-client
  932  ENV=demo PID=ABC npm run start:fast listing-builder-client
  933  npm install
  934  ENV=prod PID=ABC npm run start:fast listing-builder-client
  935  npm i
  936  ENV=prod PID=ABC npm run start:fast listing-builder-client
  937  ENV=demo PID=ABC npm run start listing-builder-client\n
  938  PROXY_ENV=prod PID=RCF0 npm run start:fast listing-builder-client
  939  PROXY_ENV=prod PID=RCF0 npm run start:fast listing-builder-client
  940  PROXY_ENV=prod PID=ABC npm run start:fast listing-builder-client
  941  PROXY_ENV=prod PID=ABC npm run start:fast listing-builder-client
  942  npm i
  943  PROXY_ENV=prod PID=TSEO npm run start:fast listing-builder-client
  944  npm install
  945  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  946  npm install
  947  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  948  npm install
  949  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  950  npm i
  951  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  952  npm i
  953  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  954  npm install
  955  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  956  npm install
  957  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  958  npm i
  959  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  960  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  961  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  962  npm install
  963  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  964  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  965  npm i
  966  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  967  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  968  npm i
  969  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  970  npm i
  971  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  972  npm i
  973  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  974  PROXY_ENV=prod PID=ABC npm run start:fast listing-builder-client
  975  npm i
  976  PROXY_ENV=prod PID=ABC npm run start:fast listing-builder-client
  977  npm i
  978  PROXY_ENV=prod PID=ABC npm run start:fast business-center-client
  979  npm i
  980  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  981  PROXY_ENV=demo PID=ABC npm run start:fast business-center-client
  982  npm install
  983  PROXY_ENV=demo PID=ABC npm run start:fast business-center-client
  984  PROXY_ENV=demo PID=ABC npm run start:fast business-center-client
  985  PROXY_ENV=prod PID=RCF0 npm run start:fast business-center-client
  986  cd ..
  987  cd ..
  988  login="<EMAIL>" \npassword="5301849cb49c4547" \ncred="$(printf ${login}:${password} | base64)" \ncurl --location --request POST "https://api.dataforseo.com/v3/on_page/task_post" \\n--header "Authorization: Basic ${cred}"  \\n--header "Content-Type: application/json" \\n--data-raw '[\n  {\n    "target": "corys-cut-shave.wp-premium-hosting.com",\n    "max_crawl_pages": 10,\n    "load_resources": true,\n    "enable_javascript": true,\n    "force_sitewide_checks": true,\n\t"store_raw_html": true,\n"enable_content_parsing": true,\n"calculate_keyword_density": true,\n    "custom_js": "meta = {}; meta.url = document.URL; meta;",\n    "tag": "some_string_123",\n  }\n]'\n
  989  login="<EMAIL>" \npassword="5301849cb49c4547" \ncred="$(printf ${login}:${password} | base64)" 
  990  env $cred
  991  print $cred
  992  curl https://corys-cut-shave.wp-premium-hosting.com/robots.txt\n
  993  curl https://www.rabbitgroomers.com/robots.txt
  994  curl https://www.rabbitgroomers.com/robots.txt > robot.txt
  995  ls
  996  cat robot.txt
  997  curl https://www.rabbitgroomers.com/scheme.org
  998  curl https://www.rabbitgroomers.com/sitemap.xml\n
  999  curl https://www.rabbitgroomers.com/sitemap.xml
 1000  curl https://www.rabbitgroomers.com/sitemap.xml > sitemap.xml
 1001  cat sitemap.xml
 1002  curl https://www.rabbitgroomers.com/sitemap.xml > sitemap.xml
 1003  history
 1004  history 0
