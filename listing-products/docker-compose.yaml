
version: "2"
services:
  listing-products:
    image: "gcr.io/repcore-prod/listing-products:0"
    ports:
      - "11200:11000"
      - "11201:11001"
    environment:
      GOOGLE_APPLICATION_CREDENTIALS: /creds/application_default_credentials.json
      ENVIRONMENT: local
    volumes:
      - ~/.config/gcloud:/creds
  listing-products-endpoints:
    image: "gcr.io/endpoints-release/endpoints-runtime:1"
    ports:
      - "11203:11003"
    environment:
      GOOGLE_APPLICATION_CREDENTIALS: /creds/application_default_credentials.json
    command: ["-slisting-products-api.vendasta-local.com", "-v[REPLACE THIS (INCLUDING THE []) WITH YOUR ENDPOINTS VERSION]", "-agrpc://listing-products:11000", "-p11003", "-zhealthz", "-k/creds/local-service-account.json"]
    depends_on:
      - "listing-products"
    volumes:
      - ./endpoints/local:/creds
