openapi: 3.0.0
info:
  title: listing-products
  x-cortex-tag: listing-products
  x-cortex-owners:
    - type: group
      name: vendasta/insync
      provider: GITHUB
  x-cortex-git:
    github:
      repository: vendasta/listing-products
  x-cortex-link:
    - name: listing-products Service Timeboard
      type: Dashboard
      url: https://app.datadoghq.com/dashboard/bgy-dh4-qqe
    - name: listing-products Logs (Prod)
      type: Logs
      url: https://console.cloud.google.com/logs/query;query=resource.type%3D%22k8s_container%22%0Aresource.labels.cluster_name%3D%22vendasta-central%22%0Aresource.labels.namespace_name%3D%22listing-products-prod%22?project=repcore-prod
    - name: listing-products Logs (Demo)
      type: Logs
      url: https://console.cloud.google.com/logs/query;query=resource.type%3D%22k8s_container%22%0Aresource.labels.cluster_name%3D%22vendasta-central%22%0Aresource.labels.namespace_name%3D%22listing-products-demo%22?project=repcore-prod
    - name: Deploy Info
      type: deploy_info
      url: https://mission-control-prod.vendasta-internal.com/applications/listing-products
    - name: Build Info
      type: build_info
      url: https://console.cloud.google.com/cloud-build/builds;region=global?project=repcore-prod&organizationId=39943578564&query=tags%3D%22ListingProducts%22
  x-cortex-slos:
    datadog:
      - id: ********************************
  x-cortex-custom-metadata:
    build-provider: CloudBuild
    deploy-provider: Mission-Control
    platform: gke
  x-cortex-apm:
    datadog:
      monitors:
        - 104453003
