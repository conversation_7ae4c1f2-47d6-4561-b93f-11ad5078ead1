steps:
- name: 'golang'
  args: ['go', 'test', '-mod=vendor', './internal/...']
  id: 'mscli-test'
  env: ['PROJECT_ROOT=github.com/vendasta/listing-products', 'ENVIRONMENT=demo']
- name: 'golang'
  args: ['go', 'build', '-mod=vendor', '-o', '/workspace/go-server', './server']
  waitFor: ['mscli-test']
  id: 'go-install'
  env:
    - "CGO_ENABLED=0"
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '--file=./mscli-Dockerfile', '--tag=gcr.io/$PROJECT_ID/listing-products:$BUILD_ID', '.']
  waitFor: ['go-install']
  id: 'build-container'
images: ['gcr.io/$PROJECT_ID/listing-products:$BUILD_ID']
tags:
- ListingProducts
timeout: 1200s
options:
  machineType: N1_HIGHCPU_8