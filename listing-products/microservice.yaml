microservice:
  name: listing-products
  goPackageName: github.com/vendasta/listing-products
  protoPath: ""
  protoPaths:
  - path: listing_products/v1/addon_attributes.proto
    excludeFromSdk: false
  - path: listing_products/v1/api.proto
    excludeFromSdk: false
  - path: listing_products/v1/citations.proto
    excludeFromSdk: false
  - path: listing_products/v1/date.proto
    excludeFromSdk: false
  - path: listing_products/v1/dayofweek.proto
    excludeFromSdk: false
  - path: listing_products/v1/insights.proto
    excludeFromSdk: false
  - path: listing_products/v1/listing_profile.proto
    excludeFromSdk: false
  - path: listing_products/v1/listing_sources.proto
    excludeFromSdk: false
  - path: listing_products/v1/seo.proto
    excludeFromSdk: false
  - path: listing_products/v1/suggestions.proto
    excludeFromSdk: false
  - path: listing_products/v1/timeofday.proto
    excludeFromSdk: false
  - path: listing_products/v1/turbolister.proto
    excludeFromSdk: false
  - path: listing_products/v1/partnersettings.proto
    excludeFromSdk: false
  - path: listing_products/v1/seosuggestedkeywords.proto
    excludeFromSdk: false
  environments:
  - name: prod
    k8sContext: gke_repcore-prod_us-central1_vendasta-central
    k8sNamespace: listing-products-prod
    jwtConfig: null
    secondarySSLConfig:
      host: listing-products-api-prod.apigateway.co
      httpsHost: listing-products-prod.apigateway.co
      name: wildcard-apigateway-co
    hosts: []
    apps:
      redis: null
    network:
      grpcHost: listing-products-api-prod.vendasta-internal.com
      grpcLoadBalancerIp: ""
      httpsHost: listing-products-prod.vendasta-internal.com
      httpsLoadBalancerIp: ""
      alternativeHost: ""
      loadBalancerIp: ""
    scaling:
      maxReplicas: 16
      minReplicas: 4
      targetCPU: 80
      disable: false
    resources:
      memoryRequest: 256Mi
      memoryLimit: 1024Mi
      cpuRequest: 200m
      cpuLimit: 1000m
      istioMemoryRequest: ""
      istioMemoryLimit: ""
      istioCpuRequest: ""
      istioCpuLimit: ""
    appConfig:
      endpointsVersion: ""
    podConfig:
      secrets:
      - name: bing-places-secret
        mountPath: /etc/bing-places-secret
        secretKey: ""
        envKey: ""
      - name: listing-products-key
        mountPath: /etc/listing-products/keys
        secretKey: ""
        envKey: ""
      - name: listing-products-core-services-key
        mountPath: /etc/listing-products/coreservices-keys
        secretKey: ""
        envKey: ""
      - name: listing-products-iam-key
        mountPath: /etc/listing-products/iam-keys
        secretKey: ""
        envKey: ""
      - name: marketplace-private-key
        mountPath: /etc/marketplace/keys/private
        secretKey: ""
        envKey: ""
      - name: marketplace-public-key
        mountPath: /etc/marketplace/keys/public
        secretKey: ""
        envKey: ""
      - name: taxonomy-secret
        mountPath: /etc/taxonomy-secret
        secretKey: ""
        envKey: ""
      - name: ms-api-key
        mountPath: /etc/ms-api-key
        secretKey: ""
        envKey: ""
      - name: cs-api-key
        mountPath: /etc/cs-api-key
        secretKey: ""
        envKey: ""
      - name: vbc-api-key
        mountPath: /etc/vbc-api-key
        secretKey: ""
        envKey: ""
      - name: gmb-consumer-key-secret
        mountPath: /etc/gmb-consumer-key
        secretKey: ""
        envKey: ""
      - name: google-maps-api-key
        mountPath: /etc/google-maps-api-key
        secretKey: ""
        envKey: ""
      - name: google-places-api-key
        mountPath: /etc/google-places-api-key
        secretKey: ""
        envKey: ""
      - name: facebook-app
        mountPath: /etc/facebook-app
        secretKey: ""
        envKey: ""
      - name: foursquare-sftp
        mountPath: /etc/foursquare-sftp
        secretKey: ""
        envKey: ""
      - name: data-axle-key
        mountPath: /etc/data-axle-key
        secretKey: ""
        envKey: ""
      - name: uberall-private-key
        mountPath: /etc/uberall-private-key
        secretKey: ""
        envKey: ""
      - name: neustar-login
        mountPath: /etc/neustar-login
        secretKey: ""
        envKey: ""
      - name: temporal-key
        mountPath: /etc/temporal/key
        secretKey: ""
        envKey: ""
      - name: temporal-cert
        mountPath: /etc/temporal/cert
        secretKey: ""
        envKey: ""
      - name: apple-bc-api-qualification-credentials
        mountPath: /etc/apple-bc-aq-creds
        secretKey: ""
        envKey: ""
      - name: apple-bc-data-qualification-credentials
        mountPath: /etc/apple-bc-dq-creds
        secretKey: ""
        envKey: ""
      - name: apple-bc-prod-credentials
        mountPath: /etc/apple-bc-prod-creds
        secretKey: ""
        envKey: ""
      - name: dataforseo-key
        mountPath: /etc/dataforseo-key
        secretKey: ""
        envKey: ""
      - name: twitter-credentials
        mountPath: /etc/twitter-credentials
        secretKey: ""
        envKey: ""
      - name: smartystreets-credentials
        mountPath: /etc/account-group/smartystreets-credentials
        secretKey: ""
        envKey: ""
      podEnv:
      - key: REDIS_HOST
        value: ************
      - key: REDIS_PORT
        value: "6379"
      - key: GOOGLE_APPLICATION_CREDENTIALS
        value: /etc/listing-products/keys/key.json
      - key: GOOGLE_CORE_SERVICES_APPLICATION_CREDENTIALS
        value: /etc/listing-products/coreservices-keys/key.json
      - key: SERVICE_ACCOUNT
        value: <EMAIL>
      - key: CORE_SERVICES_SERVICE_ACCOUNT
        value: <EMAIL>
      - key: VENDASTA_APPLICATION_CREDENTIALS
        value: /etc/listing-products/iam-keys/vendasta-service-account.key
      - key: VENDASTA_PUBLIC_KEY_ID
        value: /etc/listing-products/iam-keys/public-key-id
      - key: VENDASTA_SERVICE_ACCOUNT
        value: <EMAIL>
      - key: BING_PLACES_PEM_KEY
        value: /etc/bing-places-secret/server.cert
      - key: BING_PLACES_PRIVATE_KEY
        value: /etc/bing-places-secret/server.key
      - key: BING_PLACES_API_HOST
        value: https://api.bingplaces.com/trustedPartnerApi/v1/
      - key: REPCORE_PROJECT_ID
        value: repcore-prod
      - key: TAXONOMY_APIKEY
        value: /etc/taxonomy-secret/taxonomy-key.txt
      - key: MARKETPLACE_APP_IDS
        value: MS
      - key: MARKETPLACE_PRIVATE_KEY_PATH
        value: /etc/marketplace/keys/private/privkey.pem
      - key: MARKETPLACE_PUBLIC_KEY_PATH
        value: /etc/marketplace/keys/public/pubkey.pem
      - key: MS_API_USER
        value: /etc/ms-api-key/ms-api-user.txt
      - key: MS_API_KEY
        value: /etc/ms-api-key/ms-api-key.txt
      - key: CS_API_USER
        value: /etc/cs-api-key/cs-api-user.txt
      - key: CS_API_KEY
        value: /etc/cs-api-key/cs-api-key.txt
      - key: VBC_API_USER
        value: /etc/vbc-api-key/vbc-api-user.txt
      - key: VBC_API_KEY
        value: /etc/vbc-api-key/vbc-api-key.txt
      - key: GMB_CONSUMER_KEY
        value: /etc/gmb-consumer-key/gmb_consumer_key.txt
      - key: GMB_CONSUMER_SECRET
        value: /etc/gmb-consumer-key/gmb_consumer_secret.txt
      - key: GOOGLE_MAPS_API_KEY
        value: /etc/google-maps-api-key/api-key.txt
      - key: GOOGLE_PLACES_API_KEY
        value: /etc/google-places-api-key/api-key.txt
      - key: FACEBOOK_CLIENT_SECRET
        value: /etc/facebook-app/client-secret.txt
      - key: FOURSQUARE_SFTP_USERNAME
        value: /etc/foursquare-sftp/foursquare-sftp-username.txt
      - key: FOURSQUARE_SFTP_PASSWORD
        value: /etc/foursquare-sftp/foursquare-sftp-password.txt
      - key: FOURSQUARE_SFTP_SHA256_FINGERPRINT
        value: /etc/foursquare-sftp/foursquare-sftp-sha256-fingerprint.txt
      - key: DATA_AXLE_KEY
        value: /etc/data-axle-key/key.txt
      - key: UBERALL_PRIVATE_KEY
        value: /etc/uberall-private-key/uberall-private-key.txt
      - key: NEUSTAR_USERNAME
        value: /etc/neustar-login/username.txt
      - key: NEUSTAR_PASSWORD
        value: /etc/neustar-login/password.txt
      - key: NEUSTAR_ACCOUNT_ID
        value: "98482"
      - key: DATAFORSEO_KEY
        value: /etc/dataforseo-key/key.txt
      - key: DATASTORE_PROJECT_ID
        value: repcore-prod
      - key: MS_DATASTORE_PROJECT_ID
        value: microsite-prod
      - key: TWITTER_CREDENTIAL_KEY
        value: /etc/twitter-credentials/key.txt
      - key: TWITTER_CREDENTIAL_SECRET
        value: /etc/twitter-credentials/secret.txt
      - key: SMARTYSTREETS_AUTH_ID
        value: /etc/account-group/smartystreets-credentials/auth-id.txt
      - key: SMARTYSTREETS_AUTH_TOKEN
        value: /etc/account-group/smartystreets-credentials/auth-token.txt
      - key: APPLE_BC_PROD_CLIENT_ID
        value: /etc/apple-bc-prod-creds/client-id.txt
      - key: APPLE_BC_PROD_CLIENT_SECRET
        value: /etc/apple-bc-prod-creds/secret.txt
      - key: OPENAI_SECRET_KEY
        value: listing-products-open-ai-api-key-prod
      ports: []
    zones: []
    sideCars: []
    delivery:
      rolloutStrategy: bluegreen
      protocol: grpc
    multiDeployments: []
  - name: demo
    k8sContext: gke_repcore-prod_us-central1_vendasta-central
    k8sNamespace: listing-products-demo
    jwtConfig: null
    secondarySSLConfig:
      host: listing-products-api-demo.apigateway.co
      httpsHost: listing-products-demo.apigateway.co
      name: wildcard-apigateway-co
    hosts: []
    apps:
      redis: null
    network:
      grpcHost: listing-products-api-demo.vendasta-internal.com
      grpcLoadBalancerIp: ""
      httpsHost: listing-products-demo.vendasta-internal.com
      httpsLoadBalancerIp: ""
      alternativeHost: ""
      loadBalancerIp: ""
    scaling:
      maxReplicas: 3
      minReplicas: 1
      targetCPU: 80
      disable: false
    resources:
      memoryRequest: 0Mi
      memoryLimit: 0Mi
      cpuRequest: 0m
      cpuLimit: 0m
      istioMemoryRequest: ""
      istioMemoryLimit: ""
      istioCpuRequest: ""
      istioCpuLimit: ""
    appConfig:
      endpointsVersion: ""
    podConfig:
      secrets:
      - name: listing-products-key
        mountPath: /etc/listing-products/keys
        secretKey: ""
        envKey: ""
      - name: listing-products-core-services-key
        mountPath: /etc/listing-products/coreservices-keys
        secretKey: ""
        envKey: ""
      - name: listing-products-iam-key
        mountPath: /etc/listing-products/iam-keys
        secretKey: ""
        envKey: ""
      - name: bing-places-secret
        mountPath: /etc/bing-places-secret
        secretKey: ""
        envKey: ""
      - name: marketplace-private-key
        mountPath: /etc/marketplace/keys/private
        secretKey: ""
        envKey: ""
      - name: marketplace-public-key
        mountPath: /etc/marketplace/keys/public
        secretKey: ""
        envKey: ""
      - name: ms-api-key
        mountPath: /etc/ms-api-key
        secretKey: ""
        envKey: ""
      - name: cs-api-key
        mountPath: /etc/cs-api-key
        secretKey: ""
        envKey: ""
      - name: vbc-api-key
        mountPath: /etc/vbc-api-key
        secretKey: ""
        envKey: ""
      - name: gmb-consumer-key-secret
        mountPath: /etc/gmb-consumer-key
        secretKey: ""
        envKey: ""
      - name: google-maps-api-key
        mountPath: /etc/google-maps-api-key
        secretKey: ""
        envKey: ""
      - name: google-places-api-key
        mountPath: /etc/google-places-api-key
        secretKey: ""
        envKey: ""
      - name: facebook-app
        mountPath: /etc/facebook-app
        secretKey: ""
        envKey: ""
      - name: foursquare-sftp
        mountPath: /etc/foursquare-sftp
        secretKey: ""
        envKey: ""
      - name: data-axle-key
        mountPath: /etc/data-axle-key
        secretKey: ""
        envKey: ""
      - name: uberall-private-key
        mountPath: /etc/uberall-private-key
        secretKey: ""
        envKey: ""
      - name: neustar-login
        mountPath: /etc/neustar-login
        secretKey: ""
        envKey: ""
      - name: temporal-key
        mountPath: /etc/temporal/key
        secretKey: ""
        envKey: ""
      - name: temporal-cert
        mountPath: /etc/temporal/cert
        secretKey: ""
        envKey: ""
      - name: apple-bc-api-qualification-credentials
        mountPath: /etc/apple-bc-aq-creds
        secretKey: ""
        envKey: ""
      - name: apple-bc-data-qualification-credentials
        mountPath: /etc/apple-bc-dq-creds
        secretKey: ""
        envKey: ""
      - name: apple-bc-prod-credentials
        mountPath: /etc/apple-bc-prod-creds
        secretKey: ""
        envKey: ""
      - name: dataforseo-key
        mountPath: /etc/dataforseo-key
        secretKey: ""
        envKey: ""
      - name: twitter-credentials
        mountPath: /etc/twitter-credentials
        secretKey: ""
        envKey: ""
      - name: smartystreets-credentials
        mountPath: /etc/account-group/smartystreets-credentials
        secretKey: ""
        envKey: ""
      podEnv:
      - key: REDIS_HOST
        value: *************
      - key: REDIS_PORT
        value: "6379"
      - key: GOOGLE_APPLICATION_CREDENTIALS
        value: /etc/listing-products/keys/key.json
      - key: GOOGLE_CORE_SERVICES_APPLICATION_CREDENTIALS
        value: /etc/listing-products/coreservices-keys/key.json
      - key: SERVICE_ACCOUNT
        value: <EMAIL>
      - key: CORE_SERVICES_SERVICE_ACCOUNT
        value: <EMAIL>
      - key: VENDASTA_APPLICATION_CREDENTIALS
        value: /etc/listing-products/iam-keys/vendasta-service-account.key
      - key: VENDASTA_PUBLIC_KEY_ID
        value: /etc/listing-products/iam-keys/public-key-id
      - key: VENDASTA_SERVICE_ACCOUNT
        value: <EMAIL>
      - key: BING_PLACES_PEM_KEY
        value: /etc/bing-places-secret/server.cert
      - key: BING_PLACES_PRIVATE_KEY
        value: /etc/bing-places-secret/server.key
      - key: BING_PLACES_API_HOST
        value: https://api-test.bingplaces.com/trustedPartnerApi/v1/
      - key: REPCORE_PROJECT_ID
        value: repcore-prod
      - key: MARKETPLACE_APP_IDS
        value: MS
      - key: MARKETPLACE_PRIVATE_KEY_PATH
        value: /etc/marketplace/keys/private/privkey.pem
      - key: MARKETPLACE_PUBLIC_KEY_PATH
        value: /etc/marketplace/keys/public/pubkey.pem
      - key: MS_API_USER
        value: /etc/ms-api-key/ms-api-user.txt
      - key: MS_API_KEY
        value: /etc/ms-api-key/ms-api-key.txt
      - key: CS_API_USER
        value: /etc/cs-api-key/cs-api-user.txt
      - key: CS_API_KEY
        value: /etc/cs-api-key/cs-api-key.txt
      - key: VBC_API_USER
        value: /etc/vbc-api-key/vbc-api-user.txt
      - key: VBC_API_KEY
        value: /etc/vbc-api-key/vbc-api-key.txt
      - key: GMB_CONSUMER_KEY
        value: /etc/gmb-consumer-key/gmb_consumer_key.txt
      - key: GMB_CONSUMER_SECRET
        value: /etc/gmb-consumer-key/gmb_consumer_secret.txt
      - key: GOOGLE_MAPS_API_KEY
        value: /etc/google-maps-api-key/api-key.txt
      - key: GOOGLE_PLACES_API_KEY
        value: /etc/google-places-api-key/api-key.txt
      - key: FACEBOOK_CLIENT_SECRET
        value: /etc/facebook-app/client-secret.txt
      - key: FOURSQUARE_SFTP_USERNAME
        value: /etc/foursquare-sftp/foursquare-sftp-username.txt
      - key: FOURSQUARE_SFTP_PASSWORD
        value: /etc/foursquare-sftp/foursquare-sftp-password.txt
      - key: FOURSQUARE_SFTP_SHA256_FINGERPRINT
        value: /etc/foursquare-sftp/foursquare-sftp-sha256-fingerprint.txt
      - key: DATA_AXLE_KEY
        value: /etc/data-axle-key/key.txt
      - key: UBERALL_PRIVATE_KEY
        value: /etc/uberall-private-key/uberall-private-key.txt
      - key: NEUSTAR_USERNAME
        value: /etc/neustar-login/username.txt
      - key: NEUSTAR_PASSWORD
        value: /etc/neustar-login/password.txt
      - key: NEUSTAR_ACCOUNT_ID
        value: "98482"
      - key: DATAFORSEO_KEY
        value: /etc/dataforseo-key/key.txt
      - key: DATASTORE_PROJECT_ID
        value: repcore-demo
      - key: MS_DATASTORE_PROJECT_ID
        value: microsite-demo
      - key: TWITTER_CREDENTIAL_KEY
        value: /etc/twitter-credentials/key.txt
      - key: TWITTER_CREDENTIAL_SECRET
        value: /etc/twitter-credentials/secret.txt
      - key: SMARTYSTREETS_AUTH_ID
        value: /etc/account-group/smartystreets-credentials/auth-id.txt
      - key: SMARTYSTREETS_AUTH_TOKEN
        value: /etc/account-group/smartystreets-credentials/auth-token.txt
      - key: APPLE_BC_PROD_CLIENT_ID
        value: /etc/apple-bc-prod-creds/client-id.txt
      - key: APPLE_BC_PROD_CLIENT_SECRET
        value: /etc/apple-bc-prod-creds/secret.txt
      - key: OPENAI_SECRET_KEY
        value: listing-products-open-ai-api-key-demo
      ports: []
    zones: []
    sideCars: []
    delivery:
      rolloutStrategy: bluegreen
      protocol: grpc
    multiDeployments: []
  - name: local
    k8sContext: minikube
    k8sNamespace: default
    jwtConfig: null
    secondarySSLConfig: null
    hosts: []
    apps:
      redis: null
    network:
      grpcHost: listing-products-api.vendasta-local.com
      grpcLoadBalancerIp: ""
      httpsHost: listing-products.vendasta-local.com
      httpsLoadBalancerIp: ""
      alternativeHost: ""
      loadBalancerIp: ""
    scaling:
      maxReplicas: 3
      minReplicas: 1
      targetCPU: 80
      disable: false
    resources:
      memoryRequest: 0Mi
      memoryLimit: 0Mi
      cpuRequest: 0m
      cpuLimit: 0m
      istioMemoryRequest: ""
      istioMemoryLimit: ""
      istioCpuRequest: ""
      istioCpuLimit: ""
    appConfig:
      endpointsVersion: ""
    podConfig:
      secrets: []
      podEnv:
      - key: BING_PLACES_API_HOST
        value: ""
      ports: []
    zones: []
    sideCars: []
    delivery: {}
    multiDeployments: []
  repoUrl: https://github.com/vendasta/listing-products
  identityType: service-account-jwt
  dockerfile: ""
  useInternalPackage: true
  datadogDashboardIdv2: bgy-dh4-qqe
  datadogSloId: ********************************
  datadogAvailabilitySLOID: ********************************
  datadogPerformanceSLOID: ""
  publicRoutes: []
  javaArtifactId: ""
  google_cloud_project_id: ""
syntax: v1
