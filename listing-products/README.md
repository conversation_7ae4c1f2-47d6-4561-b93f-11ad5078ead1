# listing-products

## Alerts and Monitoring
https://vendasta.jira.com/wiki/spaces/IN/pages/1781466828/Listing+Products+monitoring

## Postman

[Download postman collection](https://storage.cloud.google.com/vendastaapis-proto2postman/listing-products.json)

## Running locally
```ENVIRONMENT=local VENDASTA_APPLICATION_CREDENTIALS_JSON=`mscli auth path -e demo` go run server/*```

Against Prod:
```ENVIRONMENT=prod VENDASTA_APPLICATION_CREDENTIALS_JSON=`mscli auth path -e prod` go run server/*```

## Updating Python SDK

https://vendasta.jira.com/wiki/spaces/RD/pages/992543135/Updating+Listing+Products+Python+SDK

## Update typescript SDK
1. Follow these steps
2. update version # in package.json. Example file for typescript: [sdks/typescript/src/listing_products_sdk/package.json](sdks/typescript/src/listing_products_sdk/package.json)
3. update readme, change log and version as needed. Example changelog for typescript: [sdks/typescript/src/listing_products_sdk/src/CHANGELOG.md](sdks/typescript/src/listing_products_sdk/src/CHANGELOG.md)


## Contents

1. [Turbolister Submissions](internal/syndication/submissions/README.md) AKA Syndication. This handles creating new businesses and activating them, as well as submitting updates. Turbolister would be our new listings product. Current (Jan 28, 2021) plan is to replace LS, LD and LSP with this in the future, possibly far to non-existant future. It will syndicate listings to any and all sources, and monitor the health of those listings. The big change with using Yext/Uberall is that we would only submit to a few (~8) important sources (Google, Bing, Apple, Yelp, etc). Once a location is active the updates are generally done with the same steps, and we want to ensure all steps are considered for all sources so there is a common workflow. The activation details around an individual source vary greatly so each source has its own activation workflow. For example Uberall has businesses(Partner)/locations(Account Group).
1. [Bing Places Submission](internal/bingplaces/README.md) This uses old tech and will eventually be replaced using a Turbolister workflow. I don't believe we currently (Jan 28, 2021) submit to Bing Places, although we have become a trusted partner now. We were not a trusted partner when this was written.
1. [Manage listing sources](internal/listingsource/README.md) - List, fetch details, create, delete, update, filter by partner

This list is not currently complete
