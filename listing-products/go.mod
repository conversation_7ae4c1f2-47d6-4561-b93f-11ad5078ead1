module github.com/vendasta/listing-products

go 1.22.4

require (
	cloud.google.com/go v0.112.2
	cloud.google.com/go/bigquery v1.60.0
	cloud.google.com/go/datastore v1.15.0
	cloud.google.com/go/pubsub v1.37.0
	cloud.google.com/go/secretmanager v1.12.0
	cloud.google.com/go/storage v1.39.1
	github.com/Pallinder/go-randomdata v1.2.0
	github.com/dghubble/oauth1 v0.7.2
	github.com/golang/mock v1.6.0
	github.com/golang/protobuf v1.5.4
	github.com/google/uuid v1.6.0
	github.com/iancoleman/strcase v0.2.0
	github.com/nyaruka/phonenumbers v1.1.7
	github.com/pkg/errors v0.9.1
	github.com/pkg/sftp v1.13.1
	github.com/short-hop/vmockhelper v1.2.1
	github.com/short-hop/vrender v1.2.7
	github.com/stretchr/testify v1.9.0
	github.com/vendasta/AA/sdks/go v0.0.0-**************-18559c2ae2b4
	github.com/vendasta/CS/sdks/go v0.0.0-**************-31178e92b3e2
	github.com/vendasta/IAM/sdks/go v1.12.0
	github.com/vendasta/NAP/sdks/go v0.0.0-**************-99092423f220
	github.com/vendasta/VBC/sdks/go v0.0.0-**************-31eb75946fc2
	github.com/vendasta/account-group-media/sdks/go v0.0.0-**************-ff379e20a6d8
	github.com/vendasta/account-group/sdks/go v0.0.0-**************-d6efbab5cfba
	github.com/vendasta/accounts/sdks/go v0.0.0-**************-841215dfd34e
	github.com/vendasta/address v0.0.0-**************-cbadf2a0990a
	github.com/vendasta/category v0.0.0-**************-c1aaad33bf0b
	github.com/vendasta/event-broker/sdks/go v1.9.1
	github.com/vendasta/generated-protos-go/account_group v0.0.0-**************-7e6564220889
	github.com/vendasta/generated-protos-go/account_group_media v0.0.0-**************-c502fb6f89aa
	github.com/vendasta/generated-protos-go/accounts v1.14.0
	github.com/vendasta/generated-protos-go/accounts/v2 v2.0.0-**************-01f50fa2ca3f
	github.com/vendasta/generated-protos-go/category v1.42.0
	github.com/vendasta/generated-protos-go/event_broker v1.0.0
	github.com/vendasta/generated-protos-go/listing_products v1.135.0
	github.com/vendasta/generated-protos-go/listing_score v0.0.0-**************-5ae3381fbeba
	github.com/vendasta/generated-protos-go/listing_sync_pro v1.50.0
	github.com/vendasta/generated-protos-go/listing_syndication v1.9.0
	github.com/vendasta/generated-protos-go/marketplace_apps/v2 v2.90.0
	github.com/vendasta/generated-protos-go/media/v2 v2.3.1
	github.com/vendasta/generated-protos-go/multi_location_analytics v0.0.0-**************-c502fb6f89aa
	github.com/vendasta/generated-protos-go/order_fulfillment v1.37.0
	github.com/vendasta/generated-protos-go/platform_integrations v0.37.0
	github.com/vendasta/generated-protos-go/snapshot v0.0.0-**************-d099cad42974
	github.com/vendasta/generated-protos-go/vanalytics v0.3.0
	github.com/vendasta/generated-protos-go/vendasta_types v1.10.0
	github.com/vendasta/generated-protos-go/vstorepb v1.9.0
	github.com/vendasta/generated-protos-go/yext v1.8.1
	github.com/vendasta/gosdks/basesdk v1.6.0
	github.com/vendasta/gosdks/basesdk/v2 v2.2.0
	github.com/vendasta/gosdks/bifrost v1.6.2
	github.com/vendasta/gosdks/cache v1.5.0
	github.com/vendasta/gosdks/catalogue v1.2.1-0.20220426191613-0a1650ac9094
	github.com/vendasta/gosdks/config v1.2.0
	github.com/vendasta/gosdks/fieldmask v1.0.1-0.20200627144944-6107a58bddc7
	github.com/vendasta/gosdks/logging v1.24.0
	github.com/vendasta/gosdks/openai v1.21.2
	github.com/vendasta/gosdks/pubsub v1.8.2
	github.com/vendasta/gosdks/serverconfig/v2 v2.4.4
	github.com/vendasta/gosdks/statsd v1.7.2-0.20230502170606-760522ccf4ee
	github.com/vendasta/gosdks/taskqueue v1.4.0
	github.com/vendasta/gosdks/temporal v0.24.0
	github.com/vendasta/gosdks/validation v1.5.1
	github.com/vendasta/gosdks/vax v1.9.0
	github.com/vendasta/gosdks/vendastatypes v1.3.0
	github.com/vendasta/gosdks/verrors v1.13.1
	github.com/vendasta/gosdks/vstrings v1.3.0
	github.com/vendasta/iam-resources v0.0.0-20241120065911-984a93d29666
	github.com/vendasta/libpostal/sdks/go v0.0.0-20200924041957-092eff5490aa
	github.com/vendasta/listing-score/sdks/go v0.0.0-20230413151117-b67a5bbb53f2
	github.com/vendasta/listing-sync-pro/sdks/go v0.0.0-20240705172033-0b5733a739f6
	github.com/vendasta/listing-syndication/sdks/go v0.0.0-20240926165155-25ab106bd04d
	github.com/vendasta/marketplace-apps/sdks/go v0.0.0-20241114195241-ba7d313ce0b7
	github.com/vendasta/marketplace/sdks/go v0.0.0-20210329214509-80acac3712cf
	github.com/vendasta/media v0.0.0-20210*********-0ea0c55fcfa0
	github.com/vendasta/multi-location-analytics/sdks/go v0.0.0-**************-6b6559ce4e6c
	github.com/vendasta/order-fulfillment/sdks/go v0.0.0-**************-17c83d948601
	github.com/vendasta/partner/sdks/go v0.0.0-**************-3507cff96a64
	github.com/vendasta/platform-integrations v0.0.0-**************-1a4ff9871fd9
	github.com/vendasta/snapshot v0.0.0-**************-f012b5a27a1d
	github.com/vendasta/vanalytics v0.0.0-**************-17a46488f806
	github.com/vendasta/vendastaevents/account-group/go/business-profile-updated v0.2.0
	github.com/vendasta/vendastaevents/listing-products/go/listingprofile v0.1.0
	github.com/vendasta/vendastaevents/snapshot/go/local-seo-data-updated v0.2.0
	github.com/vendasta/vendastaevents/snapshot/go/snapshotcreated v0.1.0
	github.com/vendasta/vstore v1.35.0
	github.com/vendasta/yext/sdks/go v0.0.0-**************-cd3502c08530
	github.com/youmark/pkcs8 v0.0.0-**************-94c173a94d60
	go.opencensus.io v0.24.0
	go.temporal.io/api v1.32.0
	go.temporal.io/sdk v1.26.1
	go.uber.org/zap v1.21.0
	golang.org/x/crypto v0.23.0
	golang.org/x/net v0.25.0
	golang.org/x/oauth2 v0.20.0
	golang.org/x/sync v0.8.0
	golang.org/x/text v0.17.0
	golang.org/x/time v0.5.0
	google.golang.org/api v0.175.0
	google.golang.org/genproto v0.0.0-**************-c3f982113cda
	google.golang.org/grpc v1.65.0
	google.golang.org/protobuf v1.34.1
	googlemaps.github.io/maps v1.3.2
	software.sslmate.com/src/go-pkcs12 v0.0.0-**************-6e380ad96778
)

require (
	cloud.google.com/go/auth v0.2.2 // indirect
	cloud.google.com/go/auth/oauth2adapt v0.2.1 // indirect
	github.com/GoogleCloudPlatform/opentelemetry-operations-go/propagator v0.45.0 // indirect
	github.com/apache/arrow/go/v14 v14.0.2 // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.19.1 // indirect
	github.com/pkoukk/tiktoken-go v0.1.6 // indirect
	github.com/pkoukk/tiktoken-go-loader v0.0.1 // indirect
	github.com/vendasta/generated-protos-go/marketplace_apps v0.0.0-20240725170513-1dd94f85684e // indirect
	github.com/vendasta/generated-protos-go/social-posts/v2 v2.28.2-0.20240318220222-4f7c0f7da5e6 // indirect
	github.com/vendasta/generated-protos-go/tesseract v0.0.0-20190718201450-36e297163915 // indirect
	github.com/vendasta/generated-protos-go/vetl v0.0.0-20190718201450-36e297163915 // indirect
	go.einride.tech/aip v0.66.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.49.0 // indirect
	golang.org/x/exp v0.0.0-20231127185646-65229373498e // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240528184218-531527333157 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240528184218-531527333157 // indirect
)

require (
	cloud.google.com/go/compute/metadata v0.3.0 // indirect
	cloud.google.com/go/container v1.35.0 // indirect
	cloud.google.com/go/iam v1.1.7 // indirect
	cloud.google.com/go/logging v1.9.0 // indirect
	cloud.google.com/go/longrunning v0.5.6 // indirect
	cloud.google.com/go/monitoring v1.18.1 // indirect
	cloud.google.com/go/trace v1.10.6 // indirect
	contrib.go.opencensus.io/exporter/stackdriver v0.13.4 // indirect
	github.com/DataDog/datadog-go/v5 v5.2.0 // indirect
	github.com/GoogleCloudPlatform/opentelemetry-operations-go/exporter/trace v1.21.0 // indirect
	github.com/GoogleCloudPlatform/opentelemetry-operations-go/internal/resourcemapping v0.45.0 // indirect
	github.com/Microsoft/go-winio v0.5.1 // indirect
	github.com/NYTimes/gziphandler v1.1.1 // indirect
	github.com/aws/aws-sdk-go v1.40.32 // indirect
	github.com/cactus/go-statsd-client/v5 v5.0.0 // indirect
	github.com/census-instrumentation/opencensus-proto v0.4.1 // indirect
	github.com/coreos/go-oidc v2.1.0+incompatible // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgrijalva/jwt-go v3.2.0+incompatible // indirect
	github.com/dlclark/regexp2 v1.10.0
	github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a // indirect
	github.com/go-logr/logr v1.4.1 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang-jwt/jwt/v4 v4.5.0 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/google/flatbuffers v23.5.26+incompatible // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/s2a-go v0.1.7 // indirect
	github.com/googleapis/enterprise-certificate-proxy v0.3.2 // indirect
	github.com/googleapis/gax-go/v2 v2.12.3 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware v1.3.0 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/jpillora/backoff v1.0.0 // indirect
	github.com/klauspost/compress v1.16.7 // indirect
	github.com/klauspost/cpuid/v2 v2.2.5 // indirect
	github.com/kr/fs v0.1.0 // indirect
	github.com/lestrrat/go-jwx v0.0.0-20180221005942-b7d4802280ae // indirect
	github.com/lestrrat/go-pdebug v0.0.0-20180220043741-569c97477ae8 // indirect
	github.com/mattheath/base62 v0.0.0-20150408093626-b80cdc656a7a // indirect
	github.com/mattheath/kala v0.0.0-20171219141654-d6276794bf0e // indirect
	github.com/pborman/uuid v1.2.1 // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/pquerna/cachecontrol v0.0.0-20180517163645-1555304b9b35 // indirect
	github.com/robfig/cron v1.2.0 // indirect
	github.com/sashabaranov/go-openai v1.24.0 // indirect
	github.com/soheilhy/cmux v0.1.4 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/twmb/murmur3 v1.1.6 // indirect
	github.com/uber-go/tally/v4 v4.1.1 // indirect
	github.com/vendasta/generated-protos-go/address v0.0.0-20200224204520-890006e52cb5 // indirect
	github.com/vendasta/generated-protos-go/auxiliary_data v0.4.0 // indirect
	github.com/vendasta/generated-protos-go/catalogue v0.2.0 // indirect
	github.com/vendasta/generated-protos-go/iam v1.54.0 // indirect
	github.com/vendasta/generated-protos-go/iam/v2 v2.48.0 // indirect
	github.com/vendasta/generated-protos-go/libpostal v0.0.0-**************-c502fb6f89aa // indirect
	github.com/vendasta/generated-protos-go/nap v1.0.2-0.20230605143718-f2e09fb342ac
	github.com/vendasta/generated-protos-go/partner v1.181.0 // indirect
	github.com/vendasta/generated-protos-go/vstore v1.9.0 // indirect
	github.com/vendasta/gosdks/registration v1.2.0 // indirect
	github.com/vendasta/gosdks/serverconfig v1.5.0 // indirect
	github.com/vendasta/gosdks/tracing v0.2.0 // indirect
	github.com/vendasta/gosdks/tracing/v2 v2.3.0 // indirect
	github.com/vendasta/vendastaevents/eventdefinition/go v0.1.0 // indirect
	github.com/vendasta/vetl/sdks/go v0.0.0-20230630210650-4458561bb2a1
	github.com/zeebo/xxh3 v1.0.2 // indirect
	go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.49.0 // indirect
	go.opentelemetry.io/otel v1.24.0 // indirect
	go.opentelemetry.io/otel/bridge/opencensus v1.24.0 // indirect
	go.opentelemetry.io/otel/metric v1.24.0 // indirect
	go.opentelemetry.io/otel/sdk v1.24.0 // indirect
	go.opentelemetry.io/otel/sdk/metric v1.24.0 // indirect
	go.opentelemetry.io/otel/trace v1.24.0 // indirect
	go.temporal.io/sdk/contrib/tally v0.1.0 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.8.0 // indirect
	golang.org/x/mod v0.17.0 // indirect
	golang.org/x/sys v0.20.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	golang.org/x/xerrors v0.0.0-20231012003039-104605ab7028 // indirect
	gopkg.in/square/go-jose.v2 v2.4.1 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
