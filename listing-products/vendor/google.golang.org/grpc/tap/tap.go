/*
 *
 * Copyright 2016 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// Package tap defines the function handles which are executed on the transport
// layer of gRPC-Go and related information.
//
// # Experimental
//
// Notice: This API is EXPERIMENTAL and may be changed or removed in a
// later release.
package tap

import (
	"context"

	"google.golang.org/grpc/metadata"
)

// Info defines the relevant information needed by the handles.
type Info struct {
	// FullMethodName is the string of grpc method (in the format of
	// /package.service/method).
	FullMethodName string

	// Header contains the header metadata received.
	Header metadata.MD

	// TODO: More to be added.
}

// ServerInHandle defines the function which runs before a new stream is
// created on the server side. If it returns a non-nil error, the stream will
// not be created and an error will be returned to the client.  If the error
// returned is a status error, that status code and message will be used,
// otherwise PermissionDenied will be the code and err.Error() will be the
// message.
//
// It's intended to be used in situations where you don't want to waste the
// resources to accept the new stream (e.g. rate-limiting). For other general
// usages, please use interceptors.
//
// Note that it is executed in the per-connection I/O goroutine(s) instead of
// per-RPC goroutine. Therefore, users should NOT have any
// blocking/time-consuming work in this handle. Otherwise all the RPCs would
// slow down. Also, for the same reason, this handle won't be called
// concurrently by gRPC.
type ServerInHandle func(ctx context.Context, info *Info) (context.Context, error)
