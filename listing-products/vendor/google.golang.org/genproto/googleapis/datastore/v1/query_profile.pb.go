// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.24.4
// source: google/datastore/v1/query_profile.proto

package datastore

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Explain options for the query.
type ExplainOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional. Whether to execute this query.
	//
	// When false (the default), the query will be planned, returning only
	// metrics from the planning stages.
	//
	// When true, the query will be planned and executed, returning the full
	// query results along with both planning and execution stage metrics.
	Analyze bool `protobuf:"varint,1,opt,name=analyze,proto3" json:"analyze,omitempty"`
}

func (x *ExplainOptions) Reset() {
	*x = ExplainOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_profile_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExplainOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExplainOptions) ProtoMessage() {}

func (x *ExplainOptions) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_profile_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExplainOptions.ProtoReflect.Descriptor instead.
func (*ExplainOptions) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_profile_proto_rawDescGZIP(), []int{0}
}

func (x *ExplainOptions) GetAnalyze() bool {
	if x != nil {
		return x.Analyze
	}
	return false
}

// Explain metrics for the query.
type ExplainMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Planning phase information for the query.
	PlanSummary *PlanSummary `protobuf:"bytes,1,opt,name=plan_summary,json=planSummary,proto3" json:"plan_summary,omitempty"`
	// Aggregated stats from the execution of the query. Only present when
	// [ExplainOptions.analyze][google.datastore.v1.ExplainOptions.analyze] is set
	// to true.
	ExecutionStats *ExecutionStats `protobuf:"bytes,2,opt,name=execution_stats,json=executionStats,proto3" json:"execution_stats,omitempty"`
}

func (x *ExplainMetrics) Reset() {
	*x = ExplainMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_profile_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExplainMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExplainMetrics) ProtoMessage() {}

func (x *ExplainMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_profile_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExplainMetrics.ProtoReflect.Descriptor instead.
func (*ExplainMetrics) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_profile_proto_rawDescGZIP(), []int{1}
}

func (x *ExplainMetrics) GetPlanSummary() *PlanSummary {
	if x != nil {
		return x.PlanSummary
	}
	return nil
}

func (x *ExplainMetrics) GetExecutionStats() *ExecutionStats {
	if x != nil {
		return x.ExecutionStats
	}
	return nil
}

// Planning phase information for the query.
type PlanSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The indexes selected for the query. For example:
	//
	//	[
	//	  {"query_scope": "Collection", "properties": "(foo ASC, __name__ ASC)"},
	//	  {"query_scope": "Collection", "properties": "(bar ASC, __name__ ASC)"}
	//	]
	IndexesUsed []*structpb.Struct `protobuf:"bytes,1,rep,name=indexes_used,json=indexesUsed,proto3" json:"indexes_used,omitempty"`
}

func (x *PlanSummary) Reset() {
	*x = PlanSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_profile_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlanSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanSummary) ProtoMessage() {}

func (x *PlanSummary) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_profile_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanSummary.ProtoReflect.Descriptor instead.
func (*PlanSummary) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_profile_proto_rawDescGZIP(), []int{2}
}

func (x *PlanSummary) GetIndexesUsed() []*structpb.Struct {
	if x != nil {
		return x.IndexesUsed
	}
	return nil
}

// Execution statistics for the query.
type ExecutionStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Total number of results returned, including documents, projections,
	// aggregation results, keys.
	ResultsReturned int64 `protobuf:"varint,1,opt,name=results_returned,json=resultsReturned,proto3" json:"results_returned,omitempty"`
	// Total time to execute the query in the backend.
	ExecutionDuration *durationpb.Duration `protobuf:"bytes,3,opt,name=execution_duration,json=executionDuration,proto3" json:"execution_duration,omitempty"`
	// Total billable read operations.
	ReadOperations int64 `protobuf:"varint,4,opt,name=read_operations,json=readOperations,proto3" json:"read_operations,omitempty"`
	// Debugging statistics from the execution of the query. Note that the
	// debugging stats are subject to change as Firestore evolves. It could
	// include:
	//
	//	{
	//	  "indexes_entries_scanned": "1000",
	//	  "documents_scanned": "20",
	//	  "billing_details" : {
	//	     "documents_billable": "20",
	//	     "index_entries_billable": "1000",
	//	     "min_query_cost": "0"
	//	  }
	//	}
	DebugStats *structpb.Struct `protobuf:"bytes,5,opt,name=debug_stats,json=debugStats,proto3" json:"debug_stats,omitempty"`
}

func (x *ExecutionStats) Reset() {
	*x = ExecutionStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_profile_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionStats) ProtoMessage() {}

func (x *ExecutionStats) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_profile_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionStats.ProtoReflect.Descriptor instead.
func (*ExecutionStats) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_profile_proto_rawDescGZIP(), []int{3}
}

func (x *ExecutionStats) GetResultsReturned() int64 {
	if x != nil {
		return x.ResultsReturned
	}
	return 0
}

func (x *ExecutionStats) GetExecutionDuration() *durationpb.Duration {
	if x != nil {
		return x.ExecutionDuration
	}
	return nil
}

func (x *ExecutionStats) GetReadOperations() int64 {
	if x != nil {
		return x.ReadOperations
	}
	return 0
}

func (x *ExecutionStats) GetDebugStats() *structpb.Struct {
	if x != nil {
		return x.DebugStats
	}
	return nil
}

var File_google_datastore_v1_query_profile_proto protoreflect.FileDescriptor

var file_google_datastore_v1_query_profile_proto_rawDesc = []byte{
	0x0a, 0x27, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2f, 0x0a,
	0x0e, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x1d, 0x0a, 0x07, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x07, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x22, 0xa3,
	0x01, 0x0a, 0x0e, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x12, 0x43, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c,
	0x61, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x6e, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x4c, 0x0a, 0x0f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x52, 0x0e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x22, 0x49, 0x0a, 0x0b, 0x50, 0x6c, 0x61, 0x6e, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x12, 0x3a, 0x0a, 0x0c, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x5f, 0x75,
	0x73, 0x65, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x52, 0x0b, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x55, 0x73, 0x65, 0x64, 0x22,
	0xe8, 0x01, 0x0a, 0x0e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x5f, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x65, 0x64, 0x12, 0x48, 0x0a,
	0x12, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x5f,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x38, 0x0a, 0x0b, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0a,
	0x64, 0x65, 0x62, 0x75, 0x67, 0x53, 0x74, 0x61, 0x74, 0x73, 0x42, 0xc3, 0x01, 0x0a, 0x17, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x42, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x3c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x3b,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0xaa, 0x02, 0x19, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x19, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x44, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5c, 0x56,
	0x31, 0xea, 0x02, 0x1c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x3a, 0x3a, 0x44, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x3a, 0x3a, 0x56, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_datastore_v1_query_profile_proto_rawDescOnce sync.Once
	file_google_datastore_v1_query_profile_proto_rawDescData = file_google_datastore_v1_query_profile_proto_rawDesc
)

func file_google_datastore_v1_query_profile_proto_rawDescGZIP() []byte {
	file_google_datastore_v1_query_profile_proto_rawDescOnce.Do(func() {
		file_google_datastore_v1_query_profile_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_datastore_v1_query_profile_proto_rawDescData)
	})
	return file_google_datastore_v1_query_profile_proto_rawDescData
}

var file_google_datastore_v1_query_profile_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_google_datastore_v1_query_profile_proto_goTypes = []interface{}{
	(*ExplainOptions)(nil),      // 0: google.datastore.v1.ExplainOptions
	(*ExplainMetrics)(nil),      // 1: google.datastore.v1.ExplainMetrics
	(*PlanSummary)(nil),         // 2: google.datastore.v1.PlanSummary
	(*ExecutionStats)(nil),      // 3: google.datastore.v1.ExecutionStats
	(*structpb.Struct)(nil),     // 4: google.protobuf.Struct
	(*durationpb.Duration)(nil), // 5: google.protobuf.Duration
}
var file_google_datastore_v1_query_profile_proto_depIdxs = []int32{
	2, // 0: google.datastore.v1.ExplainMetrics.plan_summary:type_name -> google.datastore.v1.PlanSummary
	3, // 1: google.datastore.v1.ExplainMetrics.execution_stats:type_name -> google.datastore.v1.ExecutionStats
	4, // 2: google.datastore.v1.PlanSummary.indexes_used:type_name -> google.protobuf.Struct
	5, // 3: google.datastore.v1.ExecutionStats.execution_duration:type_name -> google.protobuf.Duration
	4, // 4: google.datastore.v1.ExecutionStats.debug_stats:type_name -> google.protobuf.Struct
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_google_datastore_v1_query_profile_proto_init() }
func file_google_datastore_v1_query_profile_proto_init() {
	if File_google_datastore_v1_query_profile_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_datastore_v1_query_profile_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExplainOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_profile_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExplainMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_profile_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlanSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_profile_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_datastore_v1_query_profile_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_datastore_v1_query_profile_proto_goTypes,
		DependencyIndexes: file_google_datastore_v1_query_profile_proto_depIdxs,
		MessageInfos:      file_google_datastore_v1_query_profile_proto_msgTypes,
	}.Build()
	File_google_datastore_v1_query_profile_proto = out.File
	file_google_datastore_v1_query_profile_proto_rawDesc = nil
	file_google_datastore_v1_query_profile_proto_goTypes = nil
	file_google_datastore_v1_query_profile_proto_depIdxs = nil
}
