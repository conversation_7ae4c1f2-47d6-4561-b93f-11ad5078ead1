// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.12
// source: google/datastore/v1/query.proto

package datastore

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Specifies what data the 'entity' field contains.
// A `ResultType` is either implied (for example, in `LookupResponse.missing`
// from `datastore.proto`, it is always `KEY_ONLY`) or specified by context
// (for example, in message `QueryResultBatch`, field `entity_result_type`
// specifies a `ResultType` for all the values in field `entity_results`).
type EntityResult_ResultType int32

const (
	// Unspecified. This value is never used.
	EntityResult_RESULT_TYPE_UNSPECIFIED EntityResult_ResultType = 0
	// The key and properties.
	EntityResult_FULL EntityResult_ResultType = 1
	// A projected subset of properties. The entity may have no key.
	EntityResult_PROJECTION EntityResult_ResultType = 2
	// Only the key.
	EntityResult_KEY_ONLY EntityResult_ResultType = 3
)

// Enum value maps for EntityResult_ResultType.
var (
	EntityResult_ResultType_name = map[int32]string{
		0: "RESULT_TYPE_UNSPECIFIED",
		1: "FULL",
		2: "PROJECTION",
		3: "KEY_ONLY",
	}
	EntityResult_ResultType_value = map[string]int32{
		"RESULT_TYPE_UNSPECIFIED": 0,
		"FULL":                    1,
		"PROJECTION":              2,
		"KEY_ONLY":                3,
	}
)

func (x EntityResult_ResultType) Enum() *EntityResult_ResultType {
	p := new(EntityResult_ResultType)
	*p = x
	return p
}

func (x EntityResult_ResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EntityResult_ResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_datastore_v1_query_proto_enumTypes[0].Descriptor()
}

func (EntityResult_ResultType) Type() protoreflect.EnumType {
	return &file_google_datastore_v1_query_proto_enumTypes[0]
}

func (x EntityResult_ResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EntityResult_ResultType.Descriptor instead.
func (EntityResult_ResultType) EnumDescriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{0, 0}
}

// The sort direction.
type PropertyOrder_Direction int32

const (
	// Unspecified. This value must not be used.
	PropertyOrder_DIRECTION_UNSPECIFIED PropertyOrder_Direction = 0
	// Ascending.
	PropertyOrder_ASCENDING PropertyOrder_Direction = 1
	// Descending.
	PropertyOrder_DESCENDING PropertyOrder_Direction = 2
)

// Enum value maps for PropertyOrder_Direction.
var (
	PropertyOrder_Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "ASCENDING",
		2: "DESCENDING",
	}
	PropertyOrder_Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"ASCENDING":             1,
		"DESCENDING":            2,
	}
)

func (x PropertyOrder_Direction) Enum() *PropertyOrder_Direction {
	p := new(PropertyOrder_Direction)
	*p = x
	return p
}

func (x PropertyOrder_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PropertyOrder_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_google_datastore_v1_query_proto_enumTypes[1].Descriptor()
}

func (PropertyOrder_Direction) Type() protoreflect.EnumType {
	return &file_google_datastore_v1_query_proto_enumTypes[1]
}

func (x PropertyOrder_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PropertyOrder_Direction.Descriptor instead.
func (PropertyOrder_Direction) EnumDescriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{6, 0}
}

// A composite filter operator.
type CompositeFilter_Operator int32

const (
	// Unspecified. This value must not be used.
	CompositeFilter_OPERATOR_UNSPECIFIED CompositeFilter_Operator = 0
	// The results are required to satisfy each of the combined filters.
	CompositeFilter_AND CompositeFilter_Operator = 1
	// Documents are required to satisfy at least one of the combined filters.
	CompositeFilter_OR CompositeFilter_Operator = 2
)

// Enum value maps for CompositeFilter_Operator.
var (
	CompositeFilter_Operator_name = map[int32]string{
		0: "OPERATOR_UNSPECIFIED",
		1: "AND",
		2: "OR",
	}
	CompositeFilter_Operator_value = map[string]int32{
		"OPERATOR_UNSPECIFIED": 0,
		"AND":                  1,
		"OR":                   2,
	}
)

func (x CompositeFilter_Operator) Enum() *CompositeFilter_Operator {
	p := new(CompositeFilter_Operator)
	*p = x
	return p
}

func (x CompositeFilter_Operator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CompositeFilter_Operator) Descriptor() protoreflect.EnumDescriptor {
	return file_google_datastore_v1_query_proto_enumTypes[2].Descriptor()
}

func (CompositeFilter_Operator) Type() protoreflect.EnumType {
	return &file_google_datastore_v1_query_proto_enumTypes[2]
}

func (x CompositeFilter_Operator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CompositeFilter_Operator.Descriptor instead.
func (CompositeFilter_Operator) EnumDescriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{8, 0}
}

// A property filter operator.
type PropertyFilter_Operator int32

const (
	// Unspecified. This value must not be used.
	PropertyFilter_OPERATOR_UNSPECIFIED PropertyFilter_Operator = 0
	// The given `property` is less than the given `value`.
	//
	// Requires:
	//
	// * That `property` comes first in `order_by`.
	PropertyFilter_LESS_THAN PropertyFilter_Operator = 1
	// The given `property` is less than or equal to the given `value`.
	//
	// Requires:
	//
	// * That `property` comes first in `order_by`.
	PropertyFilter_LESS_THAN_OR_EQUAL PropertyFilter_Operator = 2
	// The given `property` is greater than the given `value`.
	//
	// Requires:
	//
	// * That `property` comes first in `order_by`.
	PropertyFilter_GREATER_THAN PropertyFilter_Operator = 3
	// The given `property` is greater than or equal to the given `value`.
	//
	// Requires:
	//
	// * That `property` comes first in `order_by`.
	PropertyFilter_GREATER_THAN_OR_EQUAL PropertyFilter_Operator = 4
	// The given `property` is equal to the given `value`.
	PropertyFilter_EQUAL PropertyFilter_Operator = 5
	// The given `property` is equal to at least one value in the given array.
	//
	// Requires:
	//
	//   - That `value` is a non-empty `ArrayValue`, subject to disjunction
	//     limits.
	//   - No `NOT_IN` is in the same query.
	PropertyFilter_IN PropertyFilter_Operator = 6
	// The given `property` is not equal to the given `value`.
	//
	// Requires:
	//
	// * No other `NOT_EQUAL` or `NOT_IN` is in the same query.
	// * That `property` comes first in the `order_by`.
	PropertyFilter_NOT_EQUAL PropertyFilter_Operator = 9
	// Limit the result set to the given entity and its descendants.
	//
	// Requires:
	//
	// * That `value` is an entity key.
	// * All evaluated disjunctions must have the same `HAS_ANCESTOR` filter.
	PropertyFilter_HAS_ANCESTOR PropertyFilter_Operator = 11
	// The value of the `property` is not in the given array.
	//
	// Requires:
	//
	// * That `value` is a non-empty `ArrayValue` with at most 10 values.
	// * No other `OR`, `IN`, `NOT_IN`, `NOT_EQUAL` is in the same query.
	// * That `field` comes first in the `order_by`.
	PropertyFilter_NOT_IN PropertyFilter_Operator = 13
)

// Enum value maps for PropertyFilter_Operator.
var (
	PropertyFilter_Operator_name = map[int32]string{
		0:  "OPERATOR_UNSPECIFIED",
		1:  "LESS_THAN",
		2:  "LESS_THAN_OR_EQUAL",
		3:  "GREATER_THAN",
		4:  "GREATER_THAN_OR_EQUAL",
		5:  "EQUAL",
		6:  "IN",
		9:  "NOT_EQUAL",
		11: "HAS_ANCESTOR",
		13: "NOT_IN",
	}
	PropertyFilter_Operator_value = map[string]int32{
		"OPERATOR_UNSPECIFIED":  0,
		"LESS_THAN":             1,
		"LESS_THAN_OR_EQUAL":    2,
		"GREATER_THAN":          3,
		"GREATER_THAN_OR_EQUAL": 4,
		"EQUAL":                 5,
		"IN":                    6,
		"NOT_EQUAL":             9,
		"HAS_ANCESTOR":          11,
		"NOT_IN":                13,
	}
)

func (x PropertyFilter_Operator) Enum() *PropertyFilter_Operator {
	p := new(PropertyFilter_Operator)
	*p = x
	return p
}

func (x PropertyFilter_Operator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PropertyFilter_Operator) Descriptor() protoreflect.EnumDescriptor {
	return file_google_datastore_v1_query_proto_enumTypes[3].Descriptor()
}

func (PropertyFilter_Operator) Type() protoreflect.EnumType {
	return &file_google_datastore_v1_query_proto_enumTypes[3]
}

func (x PropertyFilter_Operator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PropertyFilter_Operator.Descriptor instead.
func (PropertyFilter_Operator) EnumDescriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{9, 0}
}

// The possible values for the `more_results` field.
type QueryResultBatch_MoreResultsType int32

const (
	// Unspecified. This value is never used.
	QueryResultBatch_MORE_RESULTS_TYPE_UNSPECIFIED QueryResultBatch_MoreResultsType = 0
	// There may be additional batches to fetch from this query.
	QueryResultBatch_NOT_FINISHED QueryResultBatch_MoreResultsType = 1
	// The query is finished, but there may be more results after the limit.
	QueryResultBatch_MORE_RESULTS_AFTER_LIMIT QueryResultBatch_MoreResultsType = 2
	// The query is finished, but there may be more results after the end
	// cursor.
	QueryResultBatch_MORE_RESULTS_AFTER_CURSOR QueryResultBatch_MoreResultsType = 4
	// The query is finished, and there are no more results.
	QueryResultBatch_NO_MORE_RESULTS QueryResultBatch_MoreResultsType = 3
)

// Enum value maps for QueryResultBatch_MoreResultsType.
var (
	QueryResultBatch_MoreResultsType_name = map[int32]string{
		0: "MORE_RESULTS_TYPE_UNSPECIFIED",
		1: "NOT_FINISHED",
		2: "MORE_RESULTS_AFTER_LIMIT",
		4: "MORE_RESULTS_AFTER_CURSOR",
		3: "NO_MORE_RESULTS",
	}
	QueryResultBatch_MoreResultsType_value = map[string]int32{
		"MORE_RESULTS_TYPE_UNSPECIFIED": 0,
		"NOT_FINISHED":                  1,
		"MORE_RESULTS_AFTER_LIMIT":      2,
		"MORE_RESULTS_AFTER_CURSOR":     4,
		"NO_MORE_RESULTS":               3,
	}
)

func (x QueryResultBatch_MoreResultsType) Enum() *QueryResultBatch_MoreResultsType {
	p := new(QueryResultBatch_MoreResultsType)
	*p = x
	return p
}

func (x QueryResultBatch_MoreResultsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QueryResultBatch_MoreResultsType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_datastore_v1_query_proto_enumTypes[4].Descriptor()
}

func (QueryResultBatch_MoreResultsType) Type() protoreflect.EnumType {
	return &file_google_datastore_v1_query_proto_enumTypes[4]
}

func (x QueryResultBatch_MoreResultsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QueryResultBatch_MoreResultsType.Descriptor instead.
func (QueryResultBatch_MoreResultsType) EnumDescriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{12, 0}
}

// The result of fetching an entity from Datastore.
type EntityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The resulting entity.
	Entity *Entity `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	// The version of the entity, a strictly positive number that monotonically
	// increases with changes to the entity.
	//
	// This field is set for
	// [`FULL`][google.datastore.v1.EntityResult.ResultType.FULL] entity results.
	//
	// For [missing][google.datastore.v1.LookupResponse.missing] entities in
	// `LookupResponse`, this is the version of the snapshot that was used to look
	// up the entity, and it is always set except for eventually consistent reads.
	Version int64 `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	// The time at which the entity was created.
	// This field is set for
	// [`FULL`][google.datastore.v1.EntityResult.ResultType.FULL] entity results.
	// If this entity is missing, this field will not be set.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time at which the entity was last changed.
	// This field is set for
	// [`FULL`][google.datastore.v1.EntityResult.ResultType.FULL] entity results.
	// If this entity is missing, this field will not be set.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// A cursor that points to the position after the result entity.
	// Set only when the `EntityResult` is part of a `QueryResultBatch` message.
	Cursor []byte `protobuf:"bytes,3,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *EntityResult) Reset() {
	*x = EntityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityResult) ProtoMessage() {}

func (x *EntityResult) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityResult.ProtoReflect.Descriptor instead.
func (*EntityResult) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{0}
}

func (x *EntityResult) GetEntity() *Entity {
	if x != nil {
		return x.Entity
	}
	return nil
}

func (x *EntityResult) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *EntityResult) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *EntityResult) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *EntityResult) GetCursor() []byte {
	if x != nil {
		return x.Cursor
	}
	return nil
}

// A query for entities.
type Query struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The projection to return. Defaults to returning all properties.
	Projection []*Projection `protobuf:"bytes,2,rep,name=projection,proto3" json:"projection,omitempty"`
	// The kinds to query (if empty, returns entities of all kinds).
	// Currently at most 1 kind may be specified.
	Kind []*KindExpression `protobuf:"bytes,3,rep,name=kind,proto3" json:"kind,omitempty"`
	// The filter to apply.
	Filter *Filter `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// The order to apply to the query results (if empty, order is unspecified).
	Order []*PropertyOrder `protobuf:"bytes,5,rep,name=order,proto3" json:"order,omitempty"`
	// The properties to make distinct. The query results will contain the first
	// result for each distinct combination of values for the given properties
	// (if empty, all results are returned).
	//
	// Requires:
	//
	// * If `order` is specified, the set of distinct on properties must appear
	// before the non-distinct on properties in `order`.
	DistinctOn []*PropertyReference `protobuf:"bytes,6,rep,name=distinct_on,json=distinctOn,proto3" json:"distinct_on,omitempty"`
	// A starting point for the query results. Query cursors are
	// returned in query result batches and
	// [can only be used to continue the same
	// query](https://cloud.google.com/datastore/docs/concepts/queries#cursors_limits_and_offsets).
	StartCursor []byte `protobuf:"bytes,7,opt,name=start_cursor,json=startCursor,proto3" json:"start_cursor,omitempty"`
	// An ending point for the query results. Query cursors are
	// returned in query result batches and
	// [can only be used to limit the same
	// query](https://cloud.google.com/datastore/docs/concepts/queries#cursors_limits_and_offsets).
	EndCursor []byte `protobuf:"bytes,8,opt,name=end_cursor,json=endCursor,proto3" json:"end_cursor,omitempty"`
	// The number of results to skip. Applies before limit, but after all other
	// constraints. Optional. Must be >= 0 if specified.
	Offset int32 `protobuf:"varint,10,opt,name=offset,proto3" json:"offset,omitempty"`
	// The maximum number of results to return. Applies after all other
	// constraints. Optional.
	// Unspecified is interpreted as no limit.
	// Must be >= 0 if specified.
	Limit *wrapperspb.Int32Value `protobuf:"bytes,12,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *Query) Reset() {
	*x = Query{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Query) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Query) ProtoMessage() {}

func (x *Query) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Query.ProtoReflect.Descriptor instead.
func (*Query) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{1}
}

func (x *Query) GetProjection() []*Projection {
	if x != nil {
		return x.Projection
	}
	return nil
}

func (x *Query) GetKind() []*KindExpression {
	if x != nil {
		return x.Kind
	}
	return nil
}

func (x *Query) GetFilter() *Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *Query) GetOrder() []*PropertyOrder {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *Query) GetDistinctOn() []*PropertyReference {
	if x != nil {
		return x.DistinctOn
	}
	return nil
}

func (x *Query) GetStartCursor() []byte {
	if x != nil {
		return x.StartCursor
	}
	return nil
}

func (x *Query) GetEndCursor() []byte {
	if x != nil {
		return x.EndCursor
	}
	return nil
}

func (x *Query) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *Query) GetLimit() *wrapperspb.Int32Value {
	if x != nil {
		return x.Limit
	}
	return nil
}

// Datastore query for running an aggregation over a
// [Query][google.datastore.v1.Query].
type AggregationQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The base query to aggregate over.
	//
	// Types that are assignable to QueryType:
	//
	//	*AggregationQuery_NestedQuery
	QueryType isAggregationQuery_QueryType `protobuf_oneof:"query_type"`
	// Optional. Series of aggregations to apply over the results of the
	// `nested_query`.
	//
	// Requires:
	//
	// * A minimum of one and maximum of five aggregations per query.
	Aggregations []*AggregationQuery_Aggregation `protobuf:"bytes,3,rep,name=aggregations,proto3" json:"aggregations,omitempty"`
}

func (x *AggregationQuery) Reset() {
	*x = AggregationQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AggregationQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AggregationQuery) ProtoMessage() {}

func (x *AggregationQuery) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AggregationQuery.ProtoReflect.Descriptor instead.
func (*AggregationQuery) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{2}
}

func (m *AggregationQuery) GetQueryType() isAggregationQuery_QueryType {
	if m != nil {
		return m.QueryType
	}
	return nil
}

func (x *AggregationQuery) GetNestedQuery() *Query {
	if x, ok := x.GetQueryType().(*AggregationQuery_NestedQuery); ok {
		return x.NestedQuery
	}
	return nil
}

func (x *AggregationQuery) GetAggregations() []*AggregationQuery_Aggregation {
	if x != nil {
		return x.Aggregations
	}
	return nil
}

type isAggregationQuery_QueryType interface {
	isAggregationQuery_QueryType()
}

type AggregationQuery_NestedQuery struct {
	// Nested query for aggregation
	NestedQuery *Query `protobuf:"bytes,1,opt,name=nested_query,json=nestedQuery,proto3,oneof"`
}

func (*AggregationQuery_NestedQuery) isAggregationQuery_QueryType() {}

// A representation of a kind.
type KindExpression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the kind.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *KindExpression) Reset() {
	*x = KindExpression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KindExpression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KindExpression) ProtoMessage() {}

func (x *KindExpression) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KindExpression.ProtoReflect.Descriptor instead.
func (*KindExpression) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{3}
}

func (x *KindExpression) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// A reference to a property relative to the kind expressions.
type PropertyReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A reference to a property.
	//
	// Requires:
	//
	// * MUST be a dot-delimited (`.`) string of segments, where each segment
	// conforms to [entity property name][google.datastore.v1.Entity.properties]
	// limitations.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *PropertyReference) Reset() {
	*x = PropertyReference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropertyReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyReference) ProtoMessage() {}

func (x *PropertyReference) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyReference.ProtoReflect.Descriptor instead.
func (*PropertyReference) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{4}
}

func (x *PropertyReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// A representation of a property in a projection.
type Projection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The property to project.
	Property *PropertyReference `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
}

func (x *Projection) Reset() {
	*x = Projection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Projection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Projection) ProtoMessage() {}

func (x *Projection) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Projection.ProtoReflect.Descriptor instead.
func (*Projection) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{5}
}

func (x *Projection) GetProperty() *PropertyReference {
	if x != nil {
		return x.Property
	}
	return nil
}

// The desired order for a specific property.
type PropertyOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The property to order by.
	Property *PropertyReference `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	// The direction to order by. Defaults to `ASCENDING`.
	Direction PropertyOrder_Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=google.datastore.v1.PropertyOrder_Direction" json:"direction,omitempty"`
}

func (x *PropertyOrder) Reset() {
	*x = PropertyOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropertyOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyOrder) ProtoMessage() {}

func (x *PropertyOrder) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyOrder.ProtoReflect.Descriptor instead.
func (*PropertyOrder) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{6}
}

func (x *PropertyOrder) GetProperty() *PropertyReference {
	if x != nil {
		return x.Property
	}
	return nil
}

func (x *PropertyOrder) GetDirection() PropertyOrder_Direction {
	if x != nil {
		return x.Direction
	}
	return PropertyOrder_DIRECTION_UNSPECIFIED
}

// A holder for any type of filter.
type Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The type of filter.
	//
	// Types that are assignable to FilterType:
	//
	//	*Filter_CompositeFilter
	//	*Filter_PropertyFilter
	FilterType isFilter_FilterType `protobuf_oneof:"filter_type"`
}

func (x *Filter) Reset() {
	*x = Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filter) ProtoMessage() {}

func (x *Filter) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filter.ProtoReflect.Descriptor instead.
func (*Filter) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{7}
}

func (m *Filter) GetFilterType() isFilter_FilterType {
	if m != nil {
		return m.FilterType
	}
	return nil
}

func (x *Filter) GetCompositeFilter() *CompositeFilter {
	if x, ok := x.GetFilterType().(*Filter_CompositeFilter); ok {
		return x.CompositeFilter
	}
	return nil
}

func (x *Filter) GetPropertyFilter() *PropertyFilter {
	if x, ok := x.GetFilterType().(*Filter_PropertyFilter); ok {
		return x.PropertyFilter
	}
	return nil
}

type isFilter_FilterType interface {
	isFilter_FilterType()
}

type Filter_CompositeFilter struct {
	// A composite filter.
	CompositeFilter *CompositeFilter `protobuf:"bytes,1,opt,name=composite_filter,json=compositeFilter,proto3,oneof"`
}

type Filter_PropertyFilter struct {
	// A filter on a property.
	PropertyFilter *PropertyFilter `protobuf:"bytes,2,opt,name=property_filter,json=propertyFilter,proto3,oneof"`
}

func (*Filter_CompositeFilter) isFilter_FilterType() {}

func (*Filter_PropertyFilter) isFilter_FilterType() {}

// A filter that merges multiple other filters using the given operator.
type CompositeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The operator for combining multiple filters.
	Op CompositeFilter_Operator `protobuf:"varint,1,opt,name=op,proto3,enum=google.datastore.v1.CompositeFilter_Operator" json:"op,omitempty"`
	// The list of filters to combine.
	//
	// Requires:
	//
	// * At least one filter is present.
	Filters []*Filter `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *CompositeFilter) Reset() {
	*x = CompositeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompositeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompositeFilter) ProtoMessage() {}

func (x *CompositeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompositeFilter.ProtoReflect.Descriptor instead.
func (*CompositeFilter) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{8}
}

func (x *CompositeFilter) GetOp() CompositeFilter_Operator {
	if x != nil {
		return x.Op
	}
	return CompositeFilter_OPERATOR_UNSPECIFIED
}

func (x *CompositeFilter) GetFilters() []*Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

// A filter on a specific property.
type PropertyFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The property to filter by.
	Property *PropertyReference `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
	// The operator to filter by.
	Op PropertyFilter_Operator `protobuf:"varint,2,opt,name=op,proto3,enum=google.datastore.v1.PropertyFilter_Operator" json:"op,omitempty"`
	// The value to compare the property to.
	Value *Value `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *PropertyFilter) Reset() {
	*x = PropertyFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropertyFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyFilter) ProtoMessage() {}

func (x *PropertyFilter) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyFilter.ProtoReflect.Descriptor instead.
func (*PropertyFilter) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{9}
}

func (x *PropertyFilter) GetProperty() *PropertyReference {
	if x != nil {
		return x.Property
	}
	return nil
}

func (x *PropertyFilter) GetOp() PropertyFilter_Operator {
	if x != nil {
		return x.Op
	}
	return PropertyFilter_OPERATOR_UNSPECIFIED
}

func (x *PropertyFilter) GetValue() *Value {
	if x != nil {
		return x.Value
	}
	return nil
}

// A [GQL
// query](https://cloud.google.com/datastore/docs/apis/gql/gql_reference).
type GqlQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A string of the format described
	// [here](https://cloud.google.com/datastore/docs/apis/gql/gql_reference).
	QueryString string `protobuf:"bytes,1,opt,name=query_string,json=queryString,proto3" json:"query_string,omitempty"`
	// When false, the query string must not contain any literals and instead must
	// bind all values. For example,
	// `SELECT * FROM Kind WHERE a = 'string literal'` is not allowed, while
	// `SELECT * FROM Kind WHERE a = @value` is.
	AllowLiterals bool `protobuf:"varint,2,opt,name=allow_literals,json=allowLiterals,proto3" json:"allow_literals,omitempty"`
	// For each non-reserved named binding site in the query string, there must be
	// a named parameter with that name, but not necessarily the inverse.
	//
	// Key must match regex `[A-Za-z_$][A-Za-z_$0-9]*`, must not match regex
	// `__.*__`, and must not be `""`.
	NamedBindings map[string]*GqlQueryParameter `protobuf:"bytes,5,rep,name=named_bindings,json=namedBindings,proto3" json:"named_bindings,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Numbered binding site @1 references the first numbered parameter,
	// effectively using 1-based indexing, rather than the usual 0.
	//
	// For each binding site numbered i in `query_string`, there must be an i-th
	// numbered parameter. The inverse must also be true.
	PositionalBindings []*GqlQueryParameter `protobuf:"bytes,4,rep,name=positional_bindings,json=positionalBindings,proto3" json:"positional_bindings,omitempty"`
}

func (x *GqlQuery) Reset() {
	*x = GqlQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GqlQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GqlQuery) ProtoMessage() {}

func (x *GqlQuery) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GqlQuery.ProtoReflect.Descriptor instead.
func (*GqlQuery) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{10}
}

func (x *GqlQuery) GetQueryString() string {
	if x != nil {
		return x.QueryString
	}
	return ""
}

func (x *GqlQuery) GetAllowLiterals() bool {
	if x != nil {
		return x.AllowLiterals
	}
	return false
}

func (x *GqlQuery) GetNamedBindings() map[string]*GqlQueryParameter {
	if x != nil {
		return x.NamedBindings
	}
	return nil
}

func (x *GqlQuery) GetPositionalBindings() []*GqlQueryParameter {
	if x != nil {
		return x.PositionalBindings
	}
	return nil
}

// A binding parameter for a GQL query.
type GqlQueryParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The type of parameter.
	//
	// Types that are assignable to ParameterType:
	//
	//	*GqlQueryParameter_Value
	//	*GqlQueryParameter_Cursor
	ParameterType isGqlQueryParameter_ParameterType `protobuf_oneof:"parameter_type"`
}

func (x *GqlQueryParameter) Reset() {
	*x = GqlQueryParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GqlQueryParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GqlQueryParameter) ProtoMessage() {}

func (x *GqlQueryParameter) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GqlQueryParameter.ProtoReflect.Descriptor instead.
func (*GqlQueryParameter) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{11}
}

func (m *GqlQueryParameter) GetParameterType() isGqlQueryParameter_ParameterType {
	if m != nil {
		return m.ParameterType
	}
	return nil
}

func (x *GqlQueryParameter) GetValue() *Value {
	if x, ok := x.GetParameterType().(*GqlQueryParameter_Value); ok {
		return x.Value
	}
	return nil
}

func (x *GqlQueryParameter) GetCursor() []byte {
	if x, ok := x.GetParameterType().(*GqlQueryParameter_Cursor); ok {
		return x.Cursor
	}
	return nil
}

type isGqlQueryParameter_ParameterType interface {
	isGqlQueryParameter_ParameterType()
}

type GqlQueryParameter_Value struct {
	// A value parameter.
	Value *Value `protobuf:"bytes,2,opt,name=value,proto3,oneof"`
}

type GqlQueryParameter_Cursor struct {
	// A query cursor. Query cursors are returned in query
	// result batches.
	Cursor []byte `protobuf:"bytes,3,opt,name=cursor,proto3,oneof"`
}

func (*GqlQueryParameter_Value) isGqlQueryParameter_ParameterType() {}

func (*GqlQueryParameter_Cursor) isGqlQueryParameter_ParameterType() {}

// A batch of results produced by a query.
type QueryResultBatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The number of results skipped, typically because of an offset.
	SkippedResults int32 `protobuf:"varint,6,opt,name=skipped_results,json=skippedResults,proto3" json:"skipped_results,omitempty"`
	// A cursor that points to the position after the last skipped result.
	// Will be set when `skipped_results` != 0.
	SkippedCursor []byte `protobuf:"bytes,3,opt,name=skipped_cursor,json=skippedCursor,proto3" json:"skipped_cursor,omitempty"`
	// The result type for every entity in `entity_results`.
	EntityResultType EntityResult_ResultType `protobuf:"varint,1,opt,name=entity_result_type,json=entityResultType,proto3,enum=google.datastore.v1.EntityResult_ResultType" json:"entity_result_type,omitempty"`
	// The results for this batch.
	EntityResults []*EntityResult `protobuf:"bytes,2,rep,name=entity_results,json=entityResults,proto3" json:"entity_results,omitempty"`
	// A cursor that points to the position after the last result in the batch.
	EndCursor []byte `protobuf:"bytes,4,opt,name=end_cursor,json=endCursor,proto3" json:"end_cursor,omitempty"`
	// The state of the query after the current batch.
	MoreResults QueryResultBatch_MoreResultsType `protobuf:"varint,5,opt,name=more_results,json=moreResults,proto3,enum=google.datastore.v1.QueryResultBatch_MoreResultsType" json:"more_results,omitempty"`
	// The version number of the snapshot this batch was returned from.
	// This applies to the range of results from the query's `start_cursor` (or
	// the beginning of the query if no cursor was given) to this batch's
	// `end_cursor` (not the query's `end_cursor`).
	//
	// In a single transaction, subsequent query result batches for the same query
	// can have a greater snapshot version number. Each batch's snapshot version
	// is valid for all preceding batches.
	// The value will be zero for eventually consistent queries.
	SnapshotVersion int64 `protobuf:"varint,7,opt,name=snapshot_version,json=snapshotVersion,proto3" json:"snapshot_version,omitempty"`
	// Read timestamp this batch was returned from.
	// This applies to the range of results from the query's `start_cursor` (or
	// the beginning of the query if no cursor was given) to this batch's
	// `end_cursor` (not the query's `end_cursor`).
	//
	// In a single transaction, subsequent query result batches for the same query
	// can have a greater timestamp. Each batch's read timestamp
	// is valid for all preceding batches.
	// This value will not be set for eventually consistent queries in Cloud
	// Datastore.
	ReadTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=read_time,json=readTime,proto3" json:"read_time,omitempty"`
}

func (x *QueryResultBatch) Reset() {
	*x = QueryResultBatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryResultBatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryResultBatch) ProtoMessage() {}

func (x *QueryResultBatch) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryResultBatch.ProtoReflect.Descriptor instead.
func (*QueryResultBatch) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{12}
}

func (x *QueryResultBatch) GetSkippedResults() int32 {
	if x != nil {
		return x.SkippedResults
	}
	return 0
}

func (x *QueryResultBatch) GetSkippedCursor() []byte {
	if x != nil {
		return x.SkippedCursor
	}
	return nil
}

func (x *QueryResultBatch) GetEntityResultType() EntityResult_ResultType {
	if x != nil {
		return x.EntityResultType
	}
	return EntityResult_RESULT_TYPE_UNSPECIFIED
}

func (x *QueryResultBatch) GetEntityResults() []*EntityResult {
	if x != nil {
		return x.EntityResults
	}
	return nil
}

func (x *QueryResultBatch) GetEndCursor() []byte {
	if x != nil {
		return x.EndCursor
	}
	return nil
}

func (x *QueryResultBatch) GetMoreResults() QueryResultBatch_MoreResultsType {
	if x != nil {
		return x.MoreResults
	}
	return QueryResultBatch_MORE_RESULTS_TYPE_UNSPECIFIED
}

func (x *QueryResultBatch) GetSnapshotVersion() int64 {
	if x != nil {
		return x.SnapshotVersion
	}
	return 0
}

func (x *QueryResultBatch) GetReadTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadTime
	}
	return nil
}

// Defines an aggregation that produces a single result.
type AggregationQuery_Aggregation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The type of aggregation to perform, required.
	//
	// Types that are assignable to Operator:
	//
	//	*AggregationQuery_Aggregation_Count_
	//	*AggregationQuery_Aggregation_Sum_
	//	*AggregationQuery_Aggregation_Avg_
	Operator isAggregationQuery_Aggregation_Operator `protobuf_oneof:"operator"`
	// Optional. Optional name of the property to store the result of the
	// aggregation.
	//
	// If not provided, Datastore will pick a default name following the format
	// `property_<incremental_id++>`. For example:
	//
	// ```
	// AGGREGATE
	//
	//	COUNT_UP_TO(1) AS count_up_to_1,
	//	COUNT_UP_TO(2),
	//	COUNT_UP_TO(3) AS count_up_to_3,
	//	COUNT(*)
	//
	// OVER (
	//
	//	...
	//
	// );
	// ```
	//
	// becomes:
	//
	// ```
	// AGGREGATE
	//
	//	COUNT_UP_TO(1) AS count_up_to_1,
	//	COUNT_UP_TO(2) AS property_1,
	//	COUNT_UP_TO(3) AS count_up_to_3,
	//	COUNT(*) AS property_2
	//
	// OVER (
	//
	//	...
	//
	// );
	// ```
	//
	// Requires:
	//
	// * Must be unique across all aggregation aliases.
	// * Conform to [entity property
	// name][google.datastore.v1.Entity.properties] limitations.
	Alias string `protobuf:"bytes,7,opt,name=alias,proto3" json:"alias,omitempty"`
}

func (x *AggregationQuery_Aggregation) Reset() {
	*x = AggregationQuery_Aggregation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AggregationQuery_Aggregation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AggregationQuery_Aggregation) ProtoMessage() {}

func (x *AggregationQuery_Aggregation) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AggregationQuery_Aggregation.ProtoReflect.Descriptor instead.
func (*AggregationQuery_Aggregation) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{2, 0}
}

func (m *AggregationQuery_Aggregation) GetOperator() isAggregationQuery_Aggregation_Operator {
	if m != nil {
		return m.Operator
	}
	return nil
}

func (x *AggregationQuery_Aggregation) GetCount() *AggregationQuery_Aggregation_Count {
	if x, ok := x.GetOperator().(*AggregationQuery_Aggregation_Count_); ok {
		return x.Count
	}
	return nil
}

func (x *AggregationQuery_Aggregation) GetSum() *AggregationQuery_Aggregation_Sum {
	if x, ok := x.GetOperator().(*AggregationQuery_Aggregation_Sum_); ok {
		return x.Sum
	}
	return nil
}

func (x *AggregationQuery_Aggregation) GetAvg() *AggregationQuery_Aggregation_Avg {
	if x, ok := x.GetOperator().(*AggregationQuery_Aggregation_Avg_); ok {
		return x.Avg
	}
	return nil
}

func (x *AggregationQuery_Aggregation) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

type isAggregationQuery_Aggregation_Operator interface {
	isAggregationQuery_Aggregation_Operator()
}

type AggregationQuery_Aggregation_Count_ struct {
	// Count aggregator.
	Count *AggregationQuery_Aggregation_Count `protobuf:"bytes,1,opt,name=count,proto3,oneof"`
}

type AggregationQuery_Aggregation_Sum_ struct {
	// Sum aggregator.
	Sum *AggregationQuery_Aggregation_Sum `protobuf:"bytes,2,opt,name=sum,proto3,oneof"`
}

type AggregationQuery_Aggregation_Avg_ struct {
	// Average aggregator.
	Avg *AggregationQuery_Aggregation_Avg `protobuf:"bytes,3,opt,name=avg,proto3,oneof"`
}

func (*AggregationQuery_Aggregation_Count_) isAggregationQuery_Aggregation_Operator() {}

func (*AggregationQuery_Aggregation_Sum_) isAggregationQuery_Aggregation_Operator() {}

func (*AggregationQuery_Aggregation_Avg_) isAggregationQuery_Aggregation_Operator() {}

// Count of entities that match the query.
//
// The `COUNT(*)` aggregation function operates on the entire entity
// so it does not require a field reference.
type AggregationQuery_Aggregation_Count struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional. Optional constraint on the maximum number of entities to
	// count.
	//
	// This provides a way to set an upper bound on the number of entities
	// to scan, limiting latency, and cost.
	//
	// Unspecified is interpreted as no bound.
	//
	// If a zero value is provided, a count result of zero should always be
	// expected.
	//
	// High-Level Example:
	//
	// ```
	// AGGREGATE COUNT_UP_TO(1000) OVER ( SELECT * FROM k );
	// ```
	//
	// Requires:
	//
	// * Must be non-negative when present.
	UpTo *wrapperspb.Int64Value `protobuf:"bytes,1,opt,name=up_to,json=upTo,proto3" json:"up_to,omitempty"`
}

func (x *AggregationQuery_Aggregation_Count) Reset() {
	*x = AggregationQuery_Aggregation_Count{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AggregationQuery_Aggregation_Count) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AggregationQuery_Aggregation_Count) ProtoMessage() {}

func (x *AggregationQuery_Aggregation_Count) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AggregationQuery_Aggregation_Count.ProtoReflect.Descriptor instead.
func (*AggregationQuery_Aggregation_Count) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{2, 0, 0}
}

func (x *AggregationQuery_Aggregation_Count) GetUpTo() *wrapperspb.Int64Value {
	if x != nil {
		return x.UpTo
	}
	return nil
}

// Sum of the values of the requested property.
//
// * Only numeric values will be aggregated. All non-numeric values
// including `NULL` are skipped.
//
// * If the aggregated values contain `NaN`, returns `NaN`. Infinity math
// follows IEEE-754 standards.
//
// * If the aggregated value set is empty, returns 0.
//
// * Returns a 64-bit integer if all aggregated numbers are integers and the
// sum result does not overflow. Otherwise, the result is returned as a
// double. Note that even if all the aggregated values are integers, the
// result is returned as a double if it cannot fit within a 64-bit signed
// integer. When this occurs, the returned value will lose precision.
//
// * When underflow occurs, floating-point aggregation is non-deterministic.
// This means that running the same query repeatedly without any changes to
// the underlying values could produce slightly different results each
// time. In those cases, values should be stored as integers over
// floating-point numbers.
type AggregationQuery_Aggregation_Sum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The property to aggregate on.
	Property *PropertyReference `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
}

func (x *AggregationQuery_Aggregation_Sum) Reset() {
	*x = AggregationQuery_Aggregation_Sum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AggregationQuery_Aggregation_Sum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AggregationQuery_Aggregation_Sum) ProtoMessage() {}

func (x *AggregationQuery_Aggregation_Sum) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AggregationQuery_Aggregation_Sum.ProtoReflect.Descriptor instead.
func (*AggregationQuery_Aggregation_Sum) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{2, 0, 1}
}

func (x *AggregationQuery_Aggregation_Sum) GetProperty() *PropertyReference {
	if x != nil {
		return x.Property
	}
	return nil
}

// Average of the values of the requested property.
//
// * Only numeric values will be aggregated. All non-numeric values
// including `NULL` are skipped.
//
// * If the aggregated values contain `NaN`, returns `NaN`. Infinity math
// follows IEEE-754 standards.
//
// * If the aggregated value set is empty, returns `NULL`.
//
// * Always returns the result as a double.
type AggregationQuery_Aggregation_Avg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The property to aggregate on.
	Property *PropertyReference `protobuf:"bytes,1,opt,name=property,proto3" json:"property,omitempty"`
}

func (x *AggregationQuery_Aggregation_Avg) Reset() {
	*x = AggregationQuery_Aggregation_Avg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_query_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AggregationQuery_Aggregation_Avg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AggregationQuery_Aggregation_Avg) ProtoMessage() {}

func (x *AggregationQuery_Aggregation_Avg) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_query_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AggregationQuery_Aggregation_Avg.ProtoReflect.Descriptor instead.
func (*AggregationQuery_Aggregation_Avg) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_query_proto_rawDescGZIP(), []int{2, 0, 2}
}

func (x *AggregationQuery_Aggregation_Avg) GetProperty() *PropertyReference {
	if x != nil {
		return x.Property
	}
	return nil
}

var File_google_datastore_v1_query_proto protoreflect.FileDescriptor

var file_google_datastore_v1_query_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x13, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc2, 0x02, 0x0a, 0x0c, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x33, 0x0a, 0x06, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0x51, 0x0a, 0x0a,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45,
	0x53, 0x55, 0x4c, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x55, 0x4c, 0x4c, 0x10,
	0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4b, 0x45, 0x59, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x03, 0x22,
	0xc6, 0x03, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x3f, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x04, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b,
	0x69, 0x6e, 0x64, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6b,
	0x69, 0x6e, 0x64, 0x12, 0x33, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x12, 0x47, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x63, 0x74, 0x5f, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52,
	0x0a, 0x64, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x63, 0x74, 0x4f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1d,
	0x0a, 0x0a, 0x65, 0x6e, 0x64, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x31, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xb1, 0x05, 0x0a, 0x10, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x3f, 0x0a,
	0x0c, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48,
	0x00, 0x52, 0x0b, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x5a,
	0x0a, 0x0c, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x2e, 0x41, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x0c, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xf1, 0x03, 0x0a, 0x0b, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x48, 0x00, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x03, 0x73,
	0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x2e,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x75, 0x6d, 0x48,
	0x00, 0x52, 0x03, 0x73, 0x75, 0x6d, 0x12, 0x49, 0x0a, 0x03, 0x61, 0x76, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x76, 0x67, 0x48, 0x00, 0x52, 0x03, 0x61, 0x76,
	0x67, 0x12, 0x19, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x1a, 0x3e, 0x0a, 0x05,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x05, 0x75, 0x70, 0x5f, 0x74, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x04, 0x75, 0x70, 0x54, 0x6f, 0x1a, 0x49, 0x0a, 0x03,
	0x53, 0x75, 0x6d, 0x12, 0x42, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x1a, 0x49, 0x0a, 0x03, 0x41, 0x76, 0x67, 0x12, 0x42,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x42, 0x0a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x42, 0x0c,
	0x0a, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x24, 0x0a, 0x0e,
	0x4b, 0x69, 0x6e, 0x64, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x27, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x50, 0x0a, 0x0a, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22, 0xe6, 0x01,
	0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x42, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x12, 0x4a, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x45, 0x0a, 0x09, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x15,
	0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x53, 0x43, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x45, 0x53, 0x43, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x22, 0xba, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x51, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x48, 0x00, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x22, 0xbe, 0x01, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x02, 0x6f, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x52, 0x02, 0x6f, 0x70, 0x12, 0x35, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x35, 0x0a,
	0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x50, 0x45,
	0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x52, 0x10, 0x02, 0x22, 0xff, 0x02, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x3c, 0x0a, 0x02, 0x6f,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x02, 0x6f, 0x70, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb8, 0x01, 0x0a, 0x08,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x4f,
	0x52, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x47,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x4f, 0x52, 0x5f, 0x45,
	0x51, 0x55, 0x41, 0x4c, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10,
	0x05, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x4e, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54,
	0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c, 0x48, 0x41, 0x53, 0x5f,
	0x41, 0x4e, 0x43, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x10, 0x0b, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f,
	0x54, 0x5f, 0x49, 0x4e, 0x10, 0x0d, 0x22, 0xf0, 0x02, 0x0a, 0x08, 0x47, 0x71, 0x6c, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f,
	0x6c, 0x69, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x73, 0x12, 0x57, 0x0a,
	0x0e, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x71, 0x6c, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x57, 0x0a, 0x13, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x71, 0x6c, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x12, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x1a,
	0x68, 0x0a, 0x12, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x71, 0x6c,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x73, 0x0a, 0x11, 0x47, 0x71, 0x6c,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x32,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x48, 0x00, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x42, 0x10, 0x0a, 0x0e,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x80,
	0x05, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x6b,
	0x69, 0x70, 0x70, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x43, 0x75, 0x72,
	0x73, 0x6f, 0x72, 0x12, 0x5a, 0x0a, 0x12, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x48, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x64,
	0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x65,
	0x6e, 0x64, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x58, 0x0a, 0x0c, 0x6d, 0x6f, 0x72, 0x65,
	0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x4d, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6d, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a,
	0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x72, 0x65,
	0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x98, 0x01, 0x0a, 0x0f, 0x4d, 0x6f, 0x72, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x4d, 0x4f,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a,
	0x0c, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x01, 0x12,
	0x1c, 0x0a, 0x18, 0x4d, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x53, 0x5f,
	0x41, 0x46, 0x54, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x02, 0x12, 0x1d, 0x0a,
	0x19, 0x4d, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x53, 0x5f, 0x41, 0x46,
	0x54, 0x45, 0x52, 0x5f, 0x43, 0x55, 0x52, 0x53, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f,
	0x4e, 0x4f, 0x5f, 0x4d, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x53, 0x10,
	0x03, 0x42, 0xbc, 0x01, 0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x42, 0x0a, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x3c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x3b,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0xaa, 0x02, 0x19, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x19, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x44, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5c, 0x56,
	0x31, 0xea, 0x02, 0x1c, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x3a, 0x3a, 0x44, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x3a, 0x3a, 0x56, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_datastore_v1_query_proto_rawDescOnce sync.Once
	file_google_datastore_v1_query_proto_rawDescData = file_google_datastore_v1_query_proto_rawDesc
)

func file_google_datastore_v1_query_proto_rawDescGZIP() []byte {
	file_google_datastore_v1_query_proto_rawDescOnce.Do(func() {
		file_google_datastore_v1_query_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_datastore_v1_query_proto_rawDescData)
	})
	return file_google_datastore_v1_query_proto_rawDescData
}

var file_google_datastore_v1_query_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_google_datastore_v1_query_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_google_datastore_v1_query_proto_goTypes = []interface{}{
	(EntityResult_ResultType)(0),               // 0: google.datastore.v1.EntityResult.ResultType
	(PropertyOrder_Direction)(0),               // 1: google.datastore.v1.PropertyOrder.Direction
	(CompositeFilter_Operator)(0),              // 2: google.datastore.v1.CompositeFilter.Operator
	(PropertyFilter_Operator)(0),               // 3: google.datastore.v1.PropertyFilter.Operator
	(QueryResultBatch_MoreResultsType)(0),      // 4: google.datastore.v1.QueryResultBatch.MoreResultsType
	(*EntityResult)(nil),                       // 5: google.datastore.v1.EntityResult
	(*Query)(nil),                              // 6: google.datastore.v1.Query
	(*AggregationQuery)(nil),                   // 7: google.datastore.v1.AggregationQuery
	(*KindExpression)(nil),                     // 8: google.datastore.v1.KindExpression
	(*PropertyReference)(nil),                  // 9: google.datastore.v1.PropertyReference
	(*Projection)(nil),                         // 10: google.datastore.v1.Projection
	(*PropertyOrder)(nil),                      // 11: google.datastore.v1.PropertyOrder
	(*Filter)(nil),                             // 12: google.datastore.v1.Filter
	(*CompositeFilter)(nil),                    // 13: google.datastore.v1.CompositeFilter
	(*PropertyFilter)(nil),                     // 14: google.datastore.v1.PropertyFilter
	(*GqlQuery)(nil),                           // 15: google.datastore.v1.GqlQuery
	(*GqlQueryParameter)(nil),                  // 16: google.datastore.v1.GqlQueryParameter
	(*QueryResultBatch)(nil),                   // 17: google.datastore.v1.QueryResultBatch
	(*AggregationQuery_Aggregation)(nil),       // 18: google.datastore.v1.AggregationQuery.Aggregation
	(*AggregationQuery_Aggregation_Count)(nil), // 19: google.datastore.v1.AggregationQuery.Aggregation.Count
	(*AggregationQuery_Aggregation_Sum)(nil),   // 20: google.datastore.v1.AggregationQuery.Aggregation.Sum
	(*AggregationQuery_Aggregation_Avg)(nil),   // 21: google.datastore.v1.AggregationQuery.Aggregation.Avg
	nil,                                        // 22: google.datastore.v1.GqlQuery.NamedBindingsEntry
	(*Entity)(nil),                             // 23: google.datastore.v1.Entity
	(*timestamppb.Timestamp)(nil),              // 24: google.protobuf.Timestamp
	(*wrapperspb.Int32Value)(nil),              // 25: google.protobuf.Int32Value
	(*Value)(nil),                              // 26: google.datastore.v1.Value
	(*wrapperspb.Int64Value)(nil),              // 27: google.protobuf.Int64Value
}
var file_google_datastore_v1_query_proto_depIdxs = []int32{
	23, // 0: google.datastore.v1.EntityResult.entity:type_name -> google.datastore.v1.Entity
	24, // 1: google.datastore.v1.EntityResult.create_time:type_name -> google.protobuf.Timestamp
	24, // 2: google.datastore.v1.EntityResult.update_time:type_name -> google.protobuf.Timestamp
	10, // 3: google.datastore.v1.Query.projection:type_name -> google.datastore.v1.Projection
	8,  // 4: google.datastore.v1.Query.kind:type_name -> google.datastore.v1.KindExpression
	12, // 5: google.datastore.v1.Query.filter:type_name -> google.datastore.v1.Filter
	11, // 6: google.datastore.v1.Query.order:type_name -> google.datastore.v1.PropertyOrder
	9,  // 7: google.datastore.v1.Query.distinct_on:type_name -> google.datastore.v1.PropertyReference
	25, // 8: google.datastore.v1.Query.limit:type_name -> google.protobuf.Int32Value
	6,  // 9: google.datastore.v1.AggregationQuery.nested_query:type_name -> google.datastore.v1.Query
	18, // 10: google.datastore.v1.AggregationQuery.aggregations:type_name -> google.datastore.v1.AggregationQuery.Aggregation
	9,  // 11: google.datastore.v1.Projection.property:type_name -> google.datastore.v1.PropertyReference
	9,  // 12: google.datastore.v1.PropertyOrder.property:type_name -> google.datastore.v1.PropertyReference
	1,  // 13: google.datastore.v1.PropertyOrder.direction:type_name -> google.datastore.v1.PropertyOrder.Direction
	13, // 14: google.datastore.v1.Filter.composite_filter:type_name -> google.datastore.v1.CompositeFilter
	14, // 15: google.datastore.v1.Filter.property_filter:type_name -> google.datastore.v1.PropertyFilter
	2,  // 16: google.datastore.v1.CompositeFilter.op:type_name -> google.datastore.v1.CompositeFilter.Operator
	12, // 17: google.datastore.v1.CompositeFilter.filters:type_name -> google.datastore.v1.Filter
	9,  // 18: google.datastore.v1.PropertyFilter.property:type_name -> google.datastore.v1.PropertyReference
	3,  // 19: google.datastore.v1.PropertyFilter.op:type_name -> google.datastore.v1.PropertyFilter.Operator
	26, // 20: google.datastore.v1.PropertyFilter.value:type_name -> google.datastore.v1.Value
	22, // 21: google.datastore.v1.GqlQuery.named_bindings:type_name -> google.datastore.v1.GqlQuery.NamedBindingsEntry
	16, // 22: google.datastore.v1.GqlQuery.positional_bindings:type_name -> google.datastore.v1.GqlQueryParameter
	26, // 23: google.datastore.v1.GqlQueryParameter.value:type_name -> google.datastore.v1.Value
	0,  // 24: google.datastore.v1.QueryResultBatch.entity_result_type:type_name -> google.datastore.v1.EntityResult.ResultType
	5,  // 25: google.datastore.v1.QueryResultBatch.entity_results:type_name -> google.datastore.v1.EntityResult
	4,  // 26: google.datastore.v1.QueryResultBatch.more_results:type_name -> google.datastore.v1.QueryResultBatch.MoreResultsType
	24, // 27: google.datastore.v1.QueryResultBatch.read_time:type_name -> google.protobuf.Timestamp
	19, // 28: google.datastore.v1.AggregationQuery.Aggregation.count:type_name -> google.datastore.v1.AggregationQuery.Aggregation.Count
	20, // 29: google.datastore.v1.AggregationQuery.Aggregation.sum:type_name -> google.datastore.v1.AggregationQuery.Aggregation.Sum
	21, // 30: google.datastore.v1.AggregationQuery.Aggregation.avg:type_name -> google.datastore.v1.AggregationQuery.Aggregation.Avg
	27, // 31: google.datastore.v1.AggregationQuery.Aggregation.Count.up_to:type_name -> google.protobuf.Int64Value
	9,  // 32: google.datastore.v1.AggregationQuery.Aggregation.Sum.property:type_name -> google.datastore.v1.PropertyReference
	9,  // 33: google.datastore.v1.AggregationQuery.Aggregation.Avg.property:type_name -> google.datastore.v1.PropertyReference
	16, // 34: google.datastore.v1.GqlQuery.NamedBindingsEntry.value:type_name -> google.datastore.v1.GqlQueryParameter
	35, // [35:35] is the sub-list for method output_type
	35, // [35:35] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_google_datastore_v1_query_proto_init() }
func file_google_datastore_v1_query_proto_init() {
	if File_google_datastore_v1_query_proto != nil {
		return
	}
	file_google_datastore_v1_entity_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_datastore_v1_query_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Query); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AggregationQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KindExpression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PropertyReference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Projection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PropertyOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompositeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PropertyFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GqlQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GqlQueryParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryResultBatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AggregationQuery_Aggregation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AggregationQuery_Aggregation_Count); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AggregationQuery_Aggregation_Sum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_query_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AggregationQuery_Aggregation_Avg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_datastore_v1_query_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*AggregationQuery_NestedQuery)(nil),
	}
	file_google_datastore_v1_query_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*Filter_CompositeFilter)(nil),
		(*Filter_PropertyFilter)(nil),
	}
	file_google_datastore_v1_query_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*GqlQueryParameter_Value)(nil),
		(*GqlQueryParameter_Cursor)(nil),
	}
	file_google_datastore_v1_query_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*AggregationQuery_Aggregation_Count_)(nil),
		(*AggregationQuery_Aggregation_Sum_)(nil),
		(*AggregationQuery_Aggregation_Avg_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_datastore_v1_query_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_datastore_v1_query_proto_goTypes,
		DependencyIndexes: file_google_datastore_v1_query_proto_depIdxs,
		EnumInfos:         file_google_datastore_v1_query_proto_enumTypes,
		MessageInfos:      file_google_datastore_v1_query_proto_msgTypes,
	}.Build()
	File_google_datastore_v1_query_proto = out.File
	file_google_datastore_v1_query_proto_rawDesc = nil
	file_google_datastore_v1_query_proto_goTypes = nil
	file_google_datastore_v1_query_proto_depIdxs = nil
}
