// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.24.4
// source: google/datastore/v1/datastore.proto

package datastore

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The modes available for commits.
type CommitRequest_Mode int32

const (
	// Unspecified. This value must not be used.
	CommitRequest_MODE_UNSPECIFIED CommitRequest_Mode = 0
	// Transactional: The mutations are either all applied, or none are applied.
	// Learn about transactions
	// [here](https://cloud.google.com/datastore/docs/concepts/transactions).
	CommitRequest_TRANSACTIONAL CommitRequest_Mode = 1
	// Non-transactional: The mutations may not apply as all or none.
	CommitRequest_NON_TRANSACTIONAL CommitRequest_Mode = 2
)

// Enum value maps for CommitRequest_Mode.
var (
	CommitRequest_Mode_name = map[int32]string{
		0: "MODE_UNSPECIFIED",
		1: "TRANSACTIONAL",
		2: "NON_TRANSACTIONAL",
	}
	CommitRequest_Mode_value = map[string]int32{
		"MODE_UNSPECIFIED":  0,
		"TRANSACTIONAL":     1,
		"NON_TRANSACTIONAL": 2,
	}
)

func (x CommitRequest_Mode) Enum() *CommitRequest_Mode {
	p := new(CommitRequest_Mode)
	*p = x
	return p
}

func (x CommitRequest_Mode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommitRequest_Mode) Descriptor() protoreflect.EnumDescriptor {
	return file_google_datastore_v1_datastore_proto_enumTypes[0].Descriptor()
}

func (CommitRequest_Mode) Type() protoreflect.EnumType {
	return &file_google_datastore_v1_datastore_proto_enumTypes[0]
}

func (x CommitRequest_Mode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommitRequest_Mode.Descriptor instead.
func (CommitRequest_Mode) EnumDescriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{10, 0}
}

// The possible values for read consistencies.
type ReadOptions_ReadConsistency int32

const (
	// Unspecified. This value must not be used.
	ReadOptions_READ_CONSISTENCY_UNSPECIFIED ReadOptions_ReadConsistency = 0
	// Strong consistency.
	ReadOptions_STRONG ReadOptions_ReadConsistency = 1
	// Eventual consistency.
	ReadOptions_EVENTUAL ReadOptions_ReadConsistency = 2
)

// Enum value maps for ReadOptions_ReadConsistency.
var (
	ReadOptions_ReadConsistency_name = map[int32]string{
		0: "READ_CONSISTENCY_UNSPECIFIED",
		1: "STRONG",
		2: "EVENTUAL",
	}
	ReadOptions_ReadConsistency_value = map[string]int32{
		"READ_CONSISTENCY_UNSPECIFIED": 0,
		"STRONG":                       1,
		"EVENTUAL":                     2,
	}
)

func (x ReadOptions_ReadConsistency) Enum() *ReadOptions_ReadConsistency {
	p := new(ReadOptions_ReadConsistency)
	*p = x
	return p
}

func (x ReadOptions_ReadConsistency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReadOptions_ReadConsistency) Descriptor() protoreflect.EnumDescriptor {
	return file_google_datastore_v1_datastore_proto_enumTypes[1].Descriptor()
}

func (ReadOptions_ReadConsistency) Type() protoreflect.EnumType {
	return &file_google_datastore_v1_datastore_proto_enumTypes[1]
}

func (x ReadOptions_ReadConsistency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReadOptions_ReadConsistency.Descriptor instead.
func (ReadOptions_ReadConsistency) EnumDescriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{18, 0}
}

// The request for [Datastore.Lookup][google.datastore.v1.Datastore.Lookup].
type LookupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The ID of the project against which to make the request.
	ProjectId string `protobuf:"bytes,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The ID of the database against which to make the request.
	//
	// '(default)' is not allowed; please use empty string ” to refer the default
	// database.
	DatabaseId string `protobuf:"bytes,9,opt,name=database_id,json=databaseId,proto3" json:"database_id,omitempty"`
	// The options for this lookup request.
	ReadOptions *ReadOptions `protobuf:"bytes,1,opt,name=read_options,json=readOptions,proto3" json:"read_options,omitempty"`
	// Required. Keys of entities to look up.
	Keys []*Key `protobuf:"bytes,3,rep,name=keys,proto3" json:"keys,omitempty"`
}

func (x *LookupRequest) Reset() {
	*x = LookupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LookupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LookupRequest) ProtoMessage() {}

func (x *LookupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LookupRequest.ProtoReflect.Descriptor instead.
func (*LookupRequest) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{0}
}

func (x *LookupRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *LookupRequest) GetDatabaseId() string {
	if x != nil {
		return x.DatabaseId
	}
	return ""
}

func (x *LookupRequest) GetReadOptions() *ReadOptions {
	if x != nil {
		return x.ReadOptions
	}
	return nil
}

func (x *LookupRequest) GetKeys() []*Key {
	if x != nil {
		return x.Keys
	}
	return nil
}

// The response for [Datastore.Lookup][google.datastore.v1.Datastore.Lookup].
type LookupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Entities found as `ResultType.FULL` entities. The order of results in this
	// field is undefined and has no relation to the order of the keys in the
	// input.
	Found []*EntityResult `protobuf:"bytes,1,rep,name=found,proto3" json:"found,omitempty"`
	// Entities not found as `ResultType.KEY_ONLY` entities. The order of results
	// in this field is undefined and has no relation to the order of the keys
	// in the input.
	Missing []*EntityResult `protobuf:"bytes,2,rep,name=missing,proto3" json:"missing,omitempty"`
	// A list of keys that were not looked up due to resource constraints. The
	// order of results in this field is undefined and has no relation to the
	// order of the keys in the input.
	Deferred []*Key `protobuf:"bytes,3,rep,name=deferred,proto3" json:"deferred,omitempty"`
	// The identifier of the transaction that was started as part of this Lookup
	// request.
	//
	// Set only when
	// [ReadOptions.new_transaction][google.datastore.v1.ReadOptions.new_transaction]
	// was set in
	// [LookupRequest.read_options][google.datastore.v1.LookupRequest.read_options].
	Transaction []byte `protobuf:"bytes,5,opt,name=transaction,proto3" json:"transaction,omitempty"`
	// The time at which these entities were read or found missing.
	ReadTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=read_time,json=readTime,proto3" json:"read_time,omitempty"`
}

func (x *LookupResponse) Reset() {
	*x = LookupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LookupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LookupResponse) ProtoMessage() {}

func (x *LookupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LookupResponse.ProtoReflect.Descriptor instead.
func (*LookupResponse) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{1}
}

func (x *LookupResponse) GetFound() []*EntityResult {
	if x != nil {
		return x.Found
	}
	return nil
}

func (x *LookupResponse) GetMissing() []*EntityResult {
	if x != nil {
		return x.Missing
	}
	return nil
}

func (x *LookupResponse) GetDeferred() []*Key {
	if x != nil {
		return x.Deferred
	}
	return nil
}

func (x *LookupResponse) GetTransaction() []byte {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *LookupResponse) GetReadTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadTime
	}
	return nil
}

// The request for [Datastore.RunQuery][google.datastore.v1.Datastore.RunQuery].
type RunQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The ID of the project against which to make the request.
	ProjectId string `protobuf:"bytes,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The ID of the database against which to make the request.
	//
	// '(default)' is not allowed; please use empty string ” to refer the default
	// database.
	DatabaseId string `protobuf:"bytes,9,opt,name=database_id,json=databaseId,proto3" json:"database_id,omitempty"`
	// Entities are partitioned into subsets, identified by a partition ID.
	// Queries are scoped to a single partition.
	// This partition ID is normalized with the standard default context
	// partition ID.
	PartitionId *PartitionId `protobuf:"bytes,2,opt,name=partition_id,json=partitionId,proto3" json:"partition_id,omitempty"`
	// The options for this query.
	ReadOptions *ReadOptions `protobuf:"bytes,1,opt,name=read_options,json=readOptions,proto3" json:"read_options,omitempty"`
	// The type of query.
	//
	// Types that are assignable to QueryType:
	//
	//	*RunQueryRequest_Query
	//	*RunQueryRequest_GqlQuery
	QueryType isRunQueryRequest_QueryType `protobuf_oneof:"query_type"`
	// Optional. Explain options for the query. If set, additional query
	// statistics will be returned. If not, only query results will be returned.
	ExplainOptions *ExplainOptions `protobuf:"bytes,12,opt,name=explain_options,json=explainOptions,proto3" json:"explain_options,omitempty"`
}

func (x *RunQueryRequest) Reset() {
	*x = RunQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunQueryRequest) ProtoMessage() {}

func (x *RunQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunQueryRequest.ProtoReflect.Descriptor instead.
func (*RunQueryRequest) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{2}
}

func (x *RunQueryRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *RunQueryRequest) GetDatabaseId() string {
	if x != nil {
		return x.DatabaseId
	}
	return ""
}

func (x *RunQueryRequest) GetPartitionId() *PartitionId {
	if x != nil {
		return x.PartitionId
	}
	return nil
}

func (x *RunQueryRequest) GetReadOptions() *ReadOptions {
	if x != nil {
		return x.ReadOptions
	}
	return nil
}

func (m *RunQueryRequest) GetQueryType() isRunQueryRequest_QueryType {
	if m != nil {
		return m.QueryType
	}
	return nil
}

func (x *RunQueryRequest) GetQuery() *Query {
	if x, ok := x.GetQueryType().(*RunQueryRequest_Query); ok {
		return x.Query
	}
	return nil
}

func (x *RunQueryRequest) GetGqlQuery() *GqlQuery {
	if x, ok := x.GetQueryType().(*RunQueryRequest_GqlQuery); ok {
		return x.GqlQuery
	}
	return nil
}

func (x *RunQueryRequest) GetExplainOptions() *ExplainOptions {
	if x != nil {
		return x.ExplainOptions
	}
	return nil
}

type isRunQueryRequest_QueryType interface {
	isRunQueryRequest_QueryType()
}

type RunQueryRequest_Query struct {
	// The query to run.
	Query *Query `protobuf:"bytes,3,opt,name=query,proto3,oneof"`
}

type RunQueryRequest_GqlQuery struct {
	// The GQL query to run. This query must be a non-aggregation query.
	GqlQuery *GqlQuery `protobuf:"bytes,7,opt,name=gql_query,json=gqlQuery,proto3,oneof"`
}

func (*RunQueryRequest_Query) isRunQueryRequest_QueryType() {}

func (*RunQueryRequest_GqlQuery) isRunQueryRequest_QueryType() {}

// The response for
// [Datastore.RunQuery][google.datastore.v1.Datastore.RunQuery].
type RunQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A batch of query results (always present).
	Batch *QueryResultBatch `protobuf:"bytes,1,opt,name=batch,proto3" json:"batch,omitempty"`
	// The parsed form of the `GqlQuery` from the request, if it was set.
	Query *Query `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	// The identifier of the transaction that was started as part of this
	// RunQuery request.
	//
	// Set only when
	// [ReadOptions.new_transaction][google.datastore.v1.ReadOptions.new_transaction]
	// was set in
	// [RunQueryRequest.read_options][google.datastore.v1.RunQueryRequest.read_options].
	Transaction []byte `protobuf:"bytes,5,opt,name=transaction,proto3" json:"transaction,omitempty"`
	// Query explain metrics. This is only present when the
	// [RunQueryRequest.explain_options][google.datastore.v1.RunQueryRequest.explain_options]
	// is provided, and it is sent only once with the last response in the stream.
	ExplainMetrics *ExplainMetrics `protobuf:"bytes,9,opt,name=explain_metrics,json=explainMetrics,proto3" json:"explain_metrics,omitempty"`
}

func (x *RunQueryResponse) Reset() {
	*x = RunQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunQueryResponse) ProtoMessage() {}

func (x *RunQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunQueryResponse.ProtoReflect.Descriptor instead.
func (*RunQueryResponse) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{3}
}

func (x *RunQueryResponse) GetBatch() *QueryResultBatch {
	if x != nil {
		return x.Batch
	}
	return nil
}

func (x *RunQueryResponse) GetQuery() *Query {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *RunQueryResponse) GetTransaction() []byte {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *RunQueryResponse) GetExplainMetrics() *ExplainMetrics {
	if x != nil {
		return x.ExplainMetrics
	}
	return nil
}

// The request for
// [Datastore.RunAggregationQuery][google.datastore.v1.Datastore.RunAggregationQuery].
type RunAggregationQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The ID of the project against which to make the request.
	ProjectId string `protobuf:"bytes,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The ID of the database against which to make the request.
	//
	// '(default)' is not allowed; please use empty string ” to refer the default
	// database.
	DatabaseId string `protobuf:"bytes,9,opt,name=database_id,json=databaseId,proto3" json:"database_id,omitempty"`
	// Entities are partitioned into subsets, identified by a partition ID.
	// Queries are scoped to a single partition.
	// This partition ID is normalized with the standard default context
	// partition ID.
	PartitionId *PartitionId `protobuf:"bytes,2,opt,name=partition_id,json=partitionId,proto3" json:"partition_id,omitempty"`
	// The options for this query.
	ReadOptions *ReadOptions `protobuf:"bytes,1,opt,name=read_options,json=readOptions,proto3" json:"read_options,omitempty"`
	// The type of query.
	//
	// Types that are assignable to QueryType:
	//
	//	*RunAggregationQueryRequest_AggregationQuery
	//	*RunAggregationQueryRequest_GqlQuery
	QueryType isRunAggregationQueryRequest_QueryType `protobuf_oneof:"query_type"`
	// Optional. Explain options for the query. If set, additional query
	// statistics will be returned. If not, only query results will be returned.
	ExplainOptions *ExplainOptions `protobuf:"bytes,11,opt,name=explain_options,json=explainOptions,proto3" json:"explain_options,omitempty"`
}

func (x *RunAggregationQueryRequest) Reset() {
	*x = RunAggregationQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunAggregationQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunAggregationQueryRequest) ProtoMessage() {}

func (x *RunAggregationQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunAggregationQueryRequest.ProtoReflect.Descriptor instead.
func (*RunAggregationQueryRequest) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{4}
}

func (x *RunAggregationQueryRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *RunAggregationQueryRequest) GetDatabaseId() string {
	if x != nil {
		return x.DatabaseId
	}
	return ""
}

func (x *RunAggregationQueryRequest) GetPartitionId() *PartitionId {
	if x != nil {
		return x.PartitionId
	}
	return nil
}

func (x *RunAggregationQueryRequest) GetReadOptions() *ReadOptions {
	if x != nil {
		return x.ReadOptions
	}
	return nil
}

func (m *RunAggregationQueryRequest) GetQueryType() isRunAggregationQueryRequest_QueryType {
	if m != nil {
		return m.QueryType
	}
	return nil
}

func (x *RunAggregationQueryRequest) GetAggregationQuery() *AggregationQuery {
	if x, ok := x.GetQueryType().(*RunAggregationQueryRequest_AggregationQuery); ok {
		return x.AggregationQuery
	}
	return nil
}

func (x *RunAggregationQueryRequest) GetGqlQuery() *GqlQuery {
	if x, ok := x.GetQueryType().(*RunAggregationQueryRequest_GqlQuery); ok {
		return x.GqlQuery
	}
	return nil
}

func (x *RunAggregationQueryRequest) GetExplainOptions() *ExplainOptions {
	if x != nil {
		return x.ExplainOptions
	}
	return nil
}

type isRunAggregationQueryRequest_QueryType interface {
	isRunAggregationQueryRequest_QueryType()
}

type RunAggregationQueryRequest_AggregationQuery struct {
	// The query to run.
	AggregationQuery *AggregationQuery `protobuf:"bytes,3,opt,name=aggregation_query,json=aggregationQuery,proto3,oneof"`
}

type RunAggregationQueryRequest_GqlQuery struct {
	// The GQL query to run. This query must be an aggregation query.
	GqlQuery *GqlQuery `protobuf:"bytes,7,opt,name=gql_query,json=gqlQuery,proto3,oneof"`
}

func (*RunAggregationQueryRequest_AggregationQuery) isRunAggregationQueryRequest_QueryType() {}

func (*RunAggregationQueryRequest_GqlQuery) isRunAggregationQueryRequest_QueryType() {}

// The response for
// [Datastore.RunAggregationQuery][google.datastore.v1.Datastore.RunAggregationQuery].
type RunAggregationQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A batch of aggregation results. Always present.
	Batch *AggregationResultBatch `protobuf:"bytes,1,opt,name=batch,proto3" json:"batch,omitempty"`
	// The parsed form of the `GqlQuery` from the request, if it was set.
	Query *AggregationQuery `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	// The identifier of the transaction that was started as part of this
	// RunAggregationQuery request.
	//
	// Set only when
	// [ReadOptions.new_transaction][google.datastore.v1.ReadOptions.new_transaction]
	// was set in
	// [RunAggregationQueryRequest.read_options][google.datastore.v1.RunAggregationQueryRequest.read_options].
	Transaction []byte `protobuf:"bytes,5,opt,name=transaction,proto3" json:"transaction,omitempty"`
	// Query explain metrics. This is only present when the
	// [RunAggregationQueryRequest.explain_options][google.datastore.v1.RunAggregationQueryRequest.explain_options]
	// is provided, and it is sent only once with the last response in the stream.
	ExplainMetrics *ExplainMetrics `protobuf:"bytes,9,opt,name=explain_metrics,json=explainMetrics,proto3" json:"explain_metrics,omitempty"`
}

func (x *RunAggregationQueryResponse) Reset() {
	*x = RunAggregationQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunAggregationQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunAggregationQueryResponse) ProtoMessage() {}

func (x *RunAggregationQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunAggregationQueryResponse.ProtoReflect.Descriptor instead.
func (*RunAggregationQueryResponse) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{5}
}

func (x *RunAggregationQueryResponse) GetBatch() *AggregationResultBatch {
	if x != nil {
		return x.Batch
	}
	return nil
}

func (x *RunAggregationQueryResponse) GetQuery() *AggregationQuery {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *RunAggregationQueryResponse) GetTransaction() []byte {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *RunAggregationQueryResponse) GetExplainMetrics() *ExplainMetrics {
	if x != nil {
		return x.ExplainMetrics
	}
	return nil
}

// The request for
// [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction].
type BeginTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The ID of the project against which to make the request.
	ProjectId string `protobuf:"bytes,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The ID of the database against which to make the request.
	//
	// '(default)' is not allowed; please use empty string ” to refer the default
	// database.
	DatabaseId string `protobuf:"bytes,9,opt,name=database_id,json=databaseId,proto3" json:"database_id,omitempty"`
	// Options for a new transaction.
	TransactionOptions *TransactionOptions `protobuf:"bytes,10,opt,name=transaction_options,json=transactionOptions,proto3" json:"transaction_options,omitempty"`
}

func (x *BeginTransactionRequest) Reset() {
	*x = BeginTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BeginTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BeginTransactionRequest) ProtoMessage() {}

func (x *BeginTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BeginTransactionRequest.ProtoReflect.Descriptor instead.
func (*BeginTransactionRequest) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{6}
}

func (x *BeginTransactionRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *BeginTransactionRequest) GetDatabaseId() string {
	if x != nil {
		return x.DatabaseId
	}
	return ""
}

func (x *BeginTransactionRequest) GetTransactionOptions() *TransactionOptions {
	if x != nil {
		return x.TransactionOptions
	}
	return nil
}

// The response for
// [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction].
type BeginTransactionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The transaction identifier (always present).
	Transaction []byte `protobuf:"bytes,1,opt,name=transaction,proto3" json:"transaction,omitempty"`
}

func (x *BeginTransactionResponse) Reset() {
	*x = BeginTransactionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BeginTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BeginTransactionResponse) ProtoMessage() {}

func (x *BeginTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BeginTransactionResponse.ProtoReflect.Descriptor instead.
func (*BeginTransactionResponse) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{7}
}

func (x *BeginTransactionResponse) GetTransaction() []byte {
	if x != nil {
		return x.Transaction
	}
	return nil
}

// The request for [Datastore.Rollback][google.datastore.v1.Datastore.Rollback].
type RollbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The ID of the project against which to make the request.
	ProjectId string `protobuf:"bytes,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The ID of the database against which to make the request.
	//
	// '(default)' is not allowed; please use empty string ” to refer the default
	// database.
	DatabaseId string `protobuf:"bytes,9,opt,name=database_id,json=databaseId,proto3" json:"database_id,omitempty"`
	// Required. The transaction identifier, returned by a call to
	// [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction].
	Transaction []byte `protobuf:"bytes,1,opt,name=transaction,proto3" json:"transaction,omitempty"`
}

func (x *RollbackRequest) Reset() {
	*x = RollbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackRequest) ProtoMessage() {}

func (x *RollbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackRequest.ProtoReflect.Descriptor instead.
func (*RollbackRequest) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{8}
}

func (x *RollbackRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *RollbackRequest) GetDatabaseId() string {
	if x != nil {
		return x.DatabaseId
	}
	return ""
}

func (x *RollbackRequest) GetTransaction() []byte {
	if x != nil {
		return x.Transaction
	}
	return nil
}

// The response for
// [Datastore.Rollback][google.datastore.v1.Datastore.Rollback]. (an empty
// message).
type RollbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RollbackResponse) Reset() {
	*x = RollbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackResponse) ProtoMessage() {}

func (x *RollbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackResponse.ProtoReflect.Descriptor instead.
func (*RollbackResponse) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{9}
}

// The request for [Datastore.Commit][google.datastore.v1.Datastore.Commit].
type CommitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The ID of the project against which to make the request.
	ProjectId string `protobuf:"bytes,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The ID of the database against which to make the request.
	//
	// '(default)' is not allowed; please use empty string ” to refer the default
	// database.
	DatabaseId string `protobuf:"bytes,9,opt,name=database_id,json=databaseId,proto3" json:"database_id,omitempty"`
	// The type of commit to perform. Defaults to `TRANSACTIONAL`.
	Mode CommitRequest_Mode `protobuf:"varint,5,opt,name=mode,proto3,enum=google.datastore.v1.CommitRequest_Mode" json:"mode,omitempty"`
	// Must be set when mode is `TRANSACTIONAL`.
	//
	// Types that are assignable to TransactionSelector:
	//
	//	*CommitRequest_Transaction
	//	*CommitRequest_SingleUseTransaction
	TransactionSelector isCommitRequest_TransactionSelector `protobuf_oneof:"transaction_selector"`
	// The mutations to perform.
	//
	// When mode is `TRANSACTIONAL`, mutations affecting a single entity are
	// applied in order. The following sequences of mutations affecting a single
	// entity are not permitted in a single `Commit` request:
	//
	// - `insert` followed by `insert`
	// - `update` followed by `insert`
	// - `upsert` followed by `insert`
	// - `delete` followed by `update`
	//
	// When mode is `NON_TRANSACTIONAL`, no two mutations may affect a single
	// entity.
	Mutations []*Mutation `protobuf:"bytes,6,rep,name=mutations,proto3" json:"mutations,omitempty"`
}

func (x *CommitRequest) Reset() {
	*x = CommitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitRequest) ProtoMessage() {}

func (x *CommitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitRequest.ProtoReflect.Descriptor instead.
func (*CommitRequest) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{10}
}

func (x *CommitRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CommitRequest) GetDatabaseId() string {
	if x != nil {
		return x.DatabaseId
	}
	return ""
}

func (x *CommitRequest) GetMode() CommitRequest_Mode {
	if x != nil {
		return x.Mode
	}
	return CommitRequest_MODE_UNSPECIFIED
}

func (m *CommitRequest) GetTransactionSelector() isCommitRequest_TransactionSelector {
	if m != nil {
		return m.TransactionSelector
	}
	return nil
}

func (x *CommitRequest) GetTransaction() []byte {
	if x, ok := x.GetTransactionSelector().(*CommitRequest_Transaction); ok {
		return x.Transaction
	}
	return nil
}

func (x *CommitRequest) GetSingleUseTransaction() *TransactionOptions {
	if x, ok := x.GetTransactionSelector().(*CommitRequest_SingleUseTransaction); ok {
		return x.SingleUseTransaction
	}
	return nil
}

func (x *CommitRequest) GetMutations() []*Mutation {
	if x != nil {
		return x.Mutations
	}
	return nil
}

type isCommitRequest_TransactionSelector interface {
	isCommitRequest_TransactionSelector()
}

type CommitRequest_Transaction struct {
	// The identifier of the transaction associated with the commit. A
	// transaction identifier is returned by a call to
	// [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction].
	Transaction []byte `protobuf:"bytes,1,opt,name=transaction,proto3,oneof"`
}

type CommitRequest_SingleUseTransaction struct {
	// Options for beginning a new transaction for this request.
	// The transaction is committed when the request completes. If specified,
	// [TransactionOptions.mode][google.datastore.v1.TransactionOptions] must be
	// [TransactionOptions.ReadWrite][google.datastore.v1.TransactionOptions.ReadWrite].
	SingleUseTransaction *TransactionOptions `protobuf:"bytes,10,opt,name=single_use_transaction,json=singleUseTransaction,proto3,oneof"`
}

func (*CommitRequest_Transaction) isCommitRequest_TransactionSelector() {}

func (*CommitRequest_SingleUseTransaction) isCommitRequest_TransactionSelector() {}

// The response for [Datastore.Commit][google.datastore.v1.Datastore.Commit].
type CommitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The result of performing the mutations.
	// The i-th mutation result corresponds to the i-th mutation in the request.
	MutationResults []*MutationResult `protobuf:"bytes,3,rep,name=mutation_results,json=mutationResults,proto3" json:"mutation_results,omitempty"`
	// The number of index entries updated during the commit, or zero if none were
	// updated.
	IndexUpdates int32 `protobuf:"varint,4,opt,name=index_updates,json=indexUpdates,proto3" json:"index_updates,omitempty"`
	// The transaction commit timestamp. Not set for non-transactional commits.
	CommitTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=commit_time,json=commitTime,proto3" json:"commit_time,omitempty"`
}

func (x *CommitResponse) Reset() {
	*x = CommitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitResponse) ProtoMessage() {}

func (x *CommitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitResponse.ProtoReflect.Descriptor instead.
func (*CommitResponse) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{11}
}

func (x *CommitResponse) GetMutationResults() []*MutationResult {
	if x != nil {
		return x.MutationResults
	}
	return nil
}

func (x *CommitResponse) GetIndexUpdates() int32 {
	if x != nil {
		return x.IndexUpdates
	}
	return 0
}

func (x *CommitResponse) GetCommitTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CommitTime
	}
	return nil
}

// The request for
// [Datastore.AllocateIds][google.datastore.v1.Datastore.AllocateIds].
type AllocateIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The ID of the project against which to make the request.
	ProjectId string `protobuf:"bytes,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The ID of the database against which to make the request.
	//
	// '(default)' is not allowed; please use empty string ” to refer the default
	// database.
	DatabaseId string `protobuf:"bytes,9,opt,name=database_id,json=databaseId,proto3" json:"database_id,omitempty"`
	// Required. A list of keys with incomplete key paths for which to allocate
	// IDs. No key may be reserved/read-only.
	Keys []*Key `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
}

func (x *AllocateIdsRequest) Reset() {
	*x = AllocateIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllocateIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllocateIdsRequest) ProtoMessage() {}

func (x *AllocateIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllocateIdsRequest.ProtoReflect.Descriptor instead.
func (*AllocateIdsRequest) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{12}
}

func (x *AllocateIdsRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *AllocateIdsRequest) GetDatabaseId() string {
	if x != nil {
		return x.DatabaseId
	}
	return ""
}

func (x *AllocateIdsRequest) GetKeys() []*Key {
	if x != nil {
		return x.Keys
	}
	return nil
}

// The response for
// [Datastore.AllocateIds][google.datastore.v1.Datastore.AllocateIds].
type AllocateIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The keys specified in the request (in the same order), each with
	// its key path completed with a newly allocated ID.
	Keys []*Key `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
}

func (x *AllocateIdsResponse) Reset() {
	*x = AllocateIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllocateIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllocateIdsResponse) ProtoMessage() {}

func (x *AllocateIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllocateIdsResponse.ProtoReflect.Descriptor instead.
func (*AllocateIdsResponse) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{13}
}

func (x *AllocateIdsResponse) GetKeys() []*Key {
	if x != nil {
		return x.Keys
	}
	return nil
}

// The request for
// [Datastore.ReserveIds][google.datastore.v1.Datastore.ReserveIds].
type ReserveIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The ID of the project against which to make the request.
	ProjectId string `protobuf:"bytes,8,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The ID of the database against which to make the request.
	//
	// '(default)' is not allowed; please use empty string ” to refer the default
	// database.
	DatabaseId string `protobuf:"bytes,9,opt,name=database_id,json=databaseId,proto3" json:"database_id,omitempty"`
	// Required. A list of keys with complete key paths whose numeric IDs should
	// not be auto-allocated.
	Keys []*Key `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
}

func (x *ReserveIdsRequest) Reset() {
	*x = ReserveIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReserveIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReserveIdsRequest) ProtoMessage() {}

func (x *ReserveIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReserveIdsRequest.ProtoReflect.Descriptor instead.
func (*ReserveIdsRequest) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{14}
}

func (x *ReserveIdsRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ReserveIdsRequest) GetDatabaseId() string {
	if x != nil {
		return x.DatabaseId
	}
	return ""
}

func (x *ReserveIdsRequest) GetKeys() []*Key {
	if x != nil {
		return x.Keys
	}
	return nil
}

// The response for
// [Datastore.ReserveIds][google.datastore.v1.Datastore.ReserveIds].
type ReserveIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReserveIdsResponse) Reset() {
	*x = ReserveIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReserveIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReserveIdsResponse) ProtoMessage() {}

func (x *ReserveIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReserveIdsResponse.ProtoReflect.Descriptor instead.
func (*ReserveIdsResponse) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{15}
}

// A mutation to apply to an entity.
type Mutation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The mutation operation.
	//
	// For `insert`, `update`, and `upsert`:
	//   - The entity's key must not be reserved/read-only.
	//   - No property in the entity may have a reserved name,
	//     not even a property in an entity in a value.
	//   - No value in the entity may have meaning 18,
	//     not even a value in an entity in another value.
	//
	// Types that are assignable to Operation:
	//
	//	*Mutation_Insert
	//	*Mutation_Update
	//	*Mutation_Upsert
	//	*Mutation_Delete
	Operation isMutation_Operation `protobuf_oneof:"operation"`
	// When set, the server will detect whether or not this mutation conflicts
	// with the current version of the entity on the server. Conflicting mutations
	// are not applied, and are marked as such in MutationResult.
	//
	// Types that are assignable to ConflictDetectionStrategy:
	//
	//	*Mutation_BaseVersion
	//	*Mutation_UpdateTime
	ConflictDetectionStrategy isMutation_ConflictDetectionStrategy `protobuf_oneof:"conflict_detection_strategy"`
}

func (x *Mutation) Reset() {
	*x = Mutation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mutation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mutation) ProtoMessage() {}

func (x *Mutation) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mutation.ProtoReflect.Descriptor instead.
func (*Mutation) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{16}
}

func (m *Mutation) GetOperation() isMutation_Operation {
	if m != nil {
		return m.Operation
	}
	return nil
}

func (x *Mutation) GetInsert() *Entity {
	if x, ok := x.GetOperation().(*Mutation_Insert); ok {
		return x.Insert
	}
	return nil
}

func (x *Mutation) GetUpdate() *Entity {
	if x, ok := x.GetOperation().(*Mutation_Update); ok {
		return x.Update
	}
	return nil
}

func (x *Mutation) GetUpsert() *Entity {
	if x, ok := x.GetOperation().(*Mutation_Upsert); ok {
		return x.Upsert
	}
	return nil
}

func (x *Mutation) GetDelete() *Key {
	if x, ok := x.GetOperation().(*Mutation_Delete); ok {
		return x.Delete
	}
	return nil
}

func (m *Mutation) GetConflictDetectionStrategy() isMutation_ConflictDetectionStrategy {
	if m != nil {
		return m.ConflictDetectionStrategy
	}
	return nil
}

func (x *Mutation) GetBaseVersion() int64 {
	if x, ok := x.GetConflictDetectionStrategy().(*Mutation_BaseVersion); ok {
		return x.BaseVersion
	}
	return 0
}

func (x *Mutation) GetUpdateTime() *timestamppb.Timestamp {
	if x, ok := x.GetConflictDetectionStrategy().(*Mutation_UpdateTime); ok {
		return x.UpdateTime
	}
	return nil
}

type isMutation_Operation interface {
	isMutation_Operation()
}

type Mutation_Insert struct {
	// The entity to insert. The entity must not already exist.
	// The entity key's final path element may be incomplete.
	Insert *Entity `protobuf:"bytes,4,opt,name=insert,proto3,oneof"`
}

type Mutation_Update struct {
	// The entity to update. The entity must already exist.
	// Must have a complete key path.
	Update *Entity `protobuf:"bytes,5,opt,name=update,proto3,oneof"`
}

type Mutation_Upsert struct {
	// The entity to upsert. The entity may or may not already exist.
	// The entity key's final path element may be incomplete.
	Upsert *Entity `protobuf:"bytes,6,opt,name=upsert,proto3,oneof"`
}

type Mutation_Delete struct {
	// The key of the entity to delete. The entity may or may not already exist.
	// Must have a complete key path and must not be reserved/read-only.
	Delete *Key `protobuf:"bytes,7,opt,name=delete,proto3,oneof"`
}

func (*Mutation_Insert) isMutation_Operation() {}

func (*Mutation_Update) isMutation_Operation() {}

func (*Mutation_Upsert) isMutation_Operation() {}

func (*Mutation_Delete) isMutation_Operation() {}

type isMutation_ConflictDetectionStrategy interface {
	isMutation_ConflictDetectionStrategy()
}

type Mutation_BaseVersion struct {
	// The version of the entity that this mutation is being applied
	// to. If this does not match the current version on the server, the
	// mutation conflicts.
	BaseVersion int64 `protobuf:"varint,8,opt,name=base_version,json=baseVersion,proto3,oneof"`
}

type Mutation_UpdateTime struct {
	// The update time of the entity that this mutation is being applied
	// to. If this does not match the current update time on the server, the
	// mutation conflicts.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3,oneof"`
}

func (*Mutation_BaseVersion) isMutation_ConflictDetectionStrategy() {}

func (*Mutation_UpdateTime) isMutation_ConflictDetectionStrategy() {}

// The result of applying a mutation.
type MutationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The automatically allocated key.
	// Set only when the mutation allocated a key.
	Key *Key `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	// The version of the entity on the server after processing the mutation. If
	// the mutation doesn't change anything on the server, then the version will
	// be the version of the current entity or, if no entity is present, a version
	// that is strictly greater than the version of any previous entity and less
	// than the version of any possible future entity.
	Version int64 `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	// The create time of the entity. This field will not be set after a 'delete'.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The update time of the entity on the server after processing the mutation.
	// If the mutation doesn't change anything on the server, then the timestamp
	// will be the update timestamp of the current entity. This field will not be
	// set after a 'delete'.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// Whether a conflict was detected for this mutation. Always false when a
	// conflict detection strategy field is not set in the mutation.
	ConflictDetected bool `protobuf:"varint,5,opt,name=conflict_detected,json=conflictDetected,proto3" json:"conflict_detected,omitempty"`
}

func (x *MutationResult) Reset() {
	*x = MutationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutationResult) ProtoMessage() {}

func (x *MutationResult) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutationResult.ProtoReflect.Descriptor instead.
func (*MutationResult) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{17}
}

func (x *MutationResult) GetKey() *Key {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *MutationResult) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *MutationResult) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *MutationResult) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *MutationResult) GetConflictDetected() bool {
	if x != nil {
		return x.ConflictDetected
	}
	return false
}

// The options shared by read requests.
type ReadOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// For Cloud Datastore, if read_consistency is not specified, then lookups and
	// ancestor queries default to `read_consistency`=`STRONG`, global queries
	// default to `read_consistency`=`EVENTUAL`.
	//
	// For Cloud Firestore in Datastore mode, if read_consistency is not specified
	// then lookups and all queries default to `read_consistency`=`STRONG`.
	//
	// Explicitly setting `read_consistency`=`EVENTUAL` will result in eventually
	// consistent lookups & queries in both Cloud Datastore & Cloud Firestore in
	// Datastore mode.
	//
	// Types that are assignable to ConsistencyType:
	//
	//	*ReadOptions_ReadConsistency_
	//	*ReadOptions_Transaction
	//	*ReadOptions_NewTransaction
	//	*ReadOptions_ReadTime
	ConsistencyType isReadOptions_ConsistencyType `protobuf_oneof:"consistency_type"`
}

func (x *ReadOptions) Reset() {
	*x = ReadOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadOptions) ProtoMessage() {}

func (x *ReadOptions) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadOptions.ProtoReflect.Descriptor instead.
func (*ReadOptions) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{18}
}

func (m *ReadOptions) GetConsistencyType() isReadOptions_ConsistencyType {
	if m != nil {
		return m.ConsistencyType
	}
	return nil
}

func (x *ReadOptions) GetReadConsistency() ReadOptions_ReadConsistency {
	if x, ok := x.GetConsistencyType().(*ReadOptions_ReadConsistency_); ok {
		return x.ReadConsistency
	}
	return ReadOptions_READ_CONSISTENCY_UNSPECIFIED
}

func (x *ReadOptions) GetTransaction() []byte {
	if x, ok := x.GetConsistencyType().(*ReadOptions_Transaction); ok {
		return x.Transaction
	}
	return nil
}

func (x *ReadOptions) GetNewTransaction() *TransactionOptions {
	if x, ok := x.GetConsistencyType().(*ReadOptions_NewTransaction); ok {
		return x.NewTransaction
	}
	return nil
}

func (x *ReadOptions) GetReadTime() *timestamppb.Timestamp {
	if x, ok := x.GetConsistencyType().(*ReadOptions_ReadTime); ok {
		return x.ReadTime
	}
	return nil
}

type isReadOptions_ConsistencyType interface {
	isReadOptions_ConsistencyType()
}

type ReadOptions_ReadConsistency_ struct {
	// The non-transactional read consistency to use.
	ReadConsistency ReadOptions_ReadConsistency `protobuf:"varint,1,opt,name=read_consistency,json=readConsistency,proto3,enum=google.datastore.v1.ReadOptions_ReadConsistency,oneof"`
}

type ReadOptions_Transaction struct {
	// The identifier of the transaction in which to read. A
	// transaction identifier is returned by a call to
	// [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction].
	Transaction []byte `protobuf:"bytes,2,opt,name=transaction,proto3,oneof"`
}

type ReadOptions_NewTransaction struct {
	// Options for beginning a new transaction for this request.
	//
	// The new transaction identifier will be returned in the corresponding
	// response as either
	// [LookupResponse.transaction][google.datastore.v1.LookupResponse.transaction]
	// or
	// [RunQueryResponse.transaction][google.datastore.v1.RunQueryResponse.transaction].
	NewTransaction *TransactionOptions `protobuf:"bytes,3,opt,name=new_transaction,json=newTransaction,proto3,oneof"`
}

type ReadOptions_ReadTime struct {
	// Reads entities as they were at the given time. This value is only
	// supported for Cloud Firestore in Datastore mode.
	//
	// This must be a microsecond precision timestamp within the past one hour,
	// or if Point-in-Time Recovery is enabled, can additionally be a whole
	// minute timestamp within the past 7 days.
	ReadTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=read_time,json=readTime,proto3,oneof"`
}

func (*ReadOptions_ReadConsistency_) isReadOptions_ConsistencyType() {}

func (*ReadOptions_Transaction) isReadOptions_ConsistencyType() {}

func (*ReadOptions_NewTransaction) isReadOptions_ConsistencyType() {}

func (*ReadOptions_ReadTime) isReadOptions_ConsistencyType() {}

// Options for beginning a new transaction.
//
// Transactions can be created explicitly with calls to
// [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction]
// or implicitly by setting
// [ReadOptions.new_transaction][google.datastore.v1.ReadOptions.new_transaction]
// in read requests.
type TransactionOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The `mode` of the transaction, indicating whether write operations are
	// supported.
	//
	// Types that are assignable to Mode:
	//
	//	*TransactionOptions_ReadWrite_
	//	*TransactionOptions_ReadOnly_
	Mode isTransactionOptions_Mode `protobuf_oneof:"mode"`
}

func (x *TransactionOptions) Reset() {
	*x = TransactionOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionOptions) ProtoMessage() {}

func (x *TransactionOptions) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionOptions.ProtoReflect.Descriptor instead.
func (*TransactionOptions) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{19}
}

func (m *TransactionOptions) GetMode() isTransactionOptions_Mode {
	if m != nil {
		return m.Mode
	}
	return nil
}

func (x *TransactionOptions) GetReadWrite() *TransactionOptions_ReadWrite {
	if x, ok := x.GetMode().(*TransactionOptions_ReadWrite_); ok {
		return x.ReadWrite
	}
	return nil
}

func (x *TransactionOptions) GetReadOnly() *TransactionOptions_ReadOnly {
	if x, ok := x.GetMode().(*TransactionOptions_ReadOnly_); ok {
		return x.ReadOnly
	}
	return nil
}

type isTransactionOptions_Mode interface {
	isTransactionOptions_Mode()
}

type TransactionOptions_ReadWrite_ struct {
	// The transaction should allow both reads and writes.
	ReadWrite *TransactionOptions_ReadWrite `protobuf:"bytes,1,opt,name=read_write,json=readWrite,proto3,oneof"`
}

type TransactionOptions_ReadOnly_ struct {
	// The transaction should only allow reads.
	ReadOnly *TransactionOptions_ReadOnly `protobuf:"bytes,2,opt,name=read_only,json=readOnly,proto3,oneof"`
}

func (*TransactionOptions_ReadWrite_) isTransactionOptions_Mode() {}

func (*TransactionOptions_ReadOnly_) isTransactionOptions_Mode() {}

// Options specific to read / write transactions.
type TransactionOptions_ReadWrite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The transaction identifier of the transaction being retried.
	PreviousTransaction []byte `protobuf:"bytes,1,opt,name=previous_transaction,json=previousTransaction,proto3" json:"previous_transaction,omitempty"`
}

func (x *TransactionOptions_ReadWrite) Reset() {
	*x = TransactionOptions_ReadWrite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionOptions_ReadWrite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionOptions_ReadWrite) ProtoMessage() {}

func (x *TransactionOptions_ReadWrite) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionOptions_ReadWrite.ProtoReflect.Descriptor instead.
func (*TransactionOptions_ReadWrite) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{19, 0}
}

func (x *TransactionOptions_ReadWrite) GetPreviousTransaction() []byte {
	if x != nil {
		return x.PreviousTransaction
	}
	return nil
}

// Options specific to read-only transactions.
type TransactionOptions_ReadOnly struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Reads entities at the given time.
	//
	// This must be a microsecond precision timestamp within the past one hour,
	// or if Point-in-Time Recovery is enabled, can additionally be a whole
	// minute timestamp within the past 7 days.
	ReadTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=read_time,json=readTime,proto3" json:"read_time,omitempty"`
}

func (x *TransactionOptions_ReadOnly) Reset() {
	*x = TransactionOptions_ReadOnly{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_datastore_v1_datastore_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionOptions_ReadOnly) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionOptions_ReadOnly) ProtoMessage() {}

func (x *TransactionOptions_ReadOnly) ProtoReflect() protoreflect.Message {
	mi := &file_google_datastore_v1_datastore_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionOptions_ReadOnly.ProtoReflect.Descriptor instead.
func (*TransactionOptions_ReadOnly) Descriptor() ([]byte, []int) {
	return file_google_datastore_v1_datastore_proto_rawDescGZIP(), []int{19, 1}
}

func (x *TransactionOptions_ReadOnly) GetReadTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadTime
	}
	return nil
}

var File_google_datastore_v1_datastore_proto protoreflect.FileDescriptor

var file_google_datastore_v1_datastore_proto_rawDesc = []byte{
	0x0a, 0x23, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f,
	0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcc, 0x01, 0x0a, 0x0d, 0x4c, 0x6f, 0x6f, 0x6b,
	0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x43,
	0x0a, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x31, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x22, 0x97, 0x02, 0x0a, 0x0e, 0x4c, 0x6f, 0x6f, 0x6b, 0x75,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x66, 0x6f, 0x75,
	0x6e, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x66, 0x6f, 0x75,
	0x6e, 0x64, 0x12, 0x3b, 0x0a, 0x07, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x12,
	0x34, 0x0a, 0x08, 0x64, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79, 0x52, 0x08, 0x64, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0xb3, 0x03, 0x0a, 0x0f, 0x52, 0x75, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0c, 0x70, 0x61, 0x72,
	0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x43,
	0x0a, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x00,
	0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x3c, 0x0a, 0x09, 0x67, 0x71, 0x6c, 0x5f, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x71, 0x6c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x00, 0x52, 0x08, 0x67, 0x71, 0x6c,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x51, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xf1, 0x01, 0x0a, 0x10, 0x52, 0x75, 0x6e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x05, 0x62,
	0x61, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x05, 0x62, 0x61, 0x74, 0x63, 0x68, 0x12, 0x30, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x0f,
	0x65, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22, 0xe0, 0x03, 0x0a, 0x1a, 0x52,
	0x75, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x43,
	0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x61, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x72, 0x65, 0x61,
	0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x54, 0x0a, 0x11, 0x61, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x00, 0x52, 0x10, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x3c,
	0x0a, 0x09, 0x67, 0x71, 0x6c, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x71, 0x6c, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x48, 0x00, 0x52, 0x08, 0x67, 0x71, 0x6c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x51, 0x0a, 0x0f,
	0x65, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52,
	0x0e, 0x65, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42,
	0x0c, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x8d, 0x02,
	0x0a, 0x1b, 0x52, 0x75, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a,
	0x05, 0x62, 0x61, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x05, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x12, 0x3b, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x20, 0x0a,
	0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x4c, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x0e, 0x65,
	0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22, 0xb8, 0x01,
	0x0a, 0x17, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x58,
	0x0a, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x3c, 0x0a, 0x18, 0x42, 0x65, 0x67, 0x69,
	0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x7d, 0x0a, 0x0f, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x12, 0x0a, 0x10, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb3, 0x03, 0x0a, 0x0d, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x3b, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a,
	0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x48, 0x00, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x5f, 0x0a, 0x16, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x5f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x14, 0x73, 0x69,
	0x6e, 0x67, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x09, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x75, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x46, 0x0a, 0x04, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f, 0x44, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a,
	0x0d, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x01,
	0x12, 0x15, 0x0a, 0x11, 0x4e, 0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x02, 0x42, 0x16, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x22,
	0xc2, 0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4e, 0x0a, 0x10, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x0f, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x12, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x31, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x6b,
	0x65, 0x79, 0x73, 0x22, 0x43, 0x0a, 0x13, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x6b, 0x65,
	0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b,
	0x65, 0x79, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x22, 0x8b, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf3, 0x02, 0x0a,
	0x08, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x06, 0x69, 0x6e, 0x73,
	0x65, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x73, 0x65, 0x72, 0x74,
	0x12, 0x35, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52,
	0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x06, 0x75, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x06, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x12, 0x32,
	0x0a, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79, 0x48, 0x00, 0x52, 0x06, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0b, 0x62, 0x61, 0x73, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x01, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x1d, 0x0a, 0x1b, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x22, 0xfd, 0x01, 0x0a, 0x0e, 0x4d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2a, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x22, 0x82, 0x03, 0x0a, 0x0b, 0x52, 0x65, 0x61, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x5d, 0x0a, 0x10, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x52,
	0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x48, 0x00,
	0x52, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63,
	0x79, 0x12, 0x22, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x0f, 0x6e, 0x65, 0x77, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x6e, 0x65, 0x77, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x09, 0x72, 0x65, 0x61,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x4d, 0x0a, 0x0f, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x41, 0x44, 0x5f,
	0x43, 0x4f, 0x4e, 0x53, 0x49, 0x53, 0x54, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52,
	0x4f, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x55, 0x41,
	0x4c, 0x10, 0x02, 0x42, 0x12, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xc6, 0x02, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x52,
	0x0a, 0x0a, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x52, 0x65, 0x61, 0x64,
	0x57, 0x72, 0x69, 0x74, 0x65, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x61, 0x64, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x12, 0x4f, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x52,
	0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f,
	0x6e, 0x6c, 0x79, 0x1a, 0x3e, 0x0a, 0x09, 0x52, 0x65, 0x61, 0x64, 0x57, 0x72, 0x69, 0x74, 0x65,
	0x12, 0x31, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x13,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x43, 0x0a, 0x08, 0x52, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x12,
	0x37, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08,
	0x72, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65,
	0x32, 0xe1, 0x0d, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x12, 0xc0,
	0x01, 0x0a, 0x06, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x12, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x6d, 0xda, 0x41, 0x1c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x2c, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2c, 0x6b,
	0x65, 0x79, 0x73, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x22, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x7d, 0x3a, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x3a, 0x01, 0x2a, 0x8a, 0xd3,
	0xe4, 0x93, 0x02, 0x1d, 0x12, 0x0c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x12, 0x0d, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x12, 0xa9, 0x01, 0x0a, 0x08, 0x52, 0x75, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x24,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x50, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x27, 0x22, 0x22, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x3a, 0x72,
	0x75, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x1d,
	0x12, 0x0c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x0d,
	0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x12, 0xd5, 0x01,
	0x0a, 0x13, 0x52, 0x75, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6e,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x5b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32,
	0x22, 0x2d, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x3a, 0x72, 0x75, 0x6e, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x3a,
	0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x0c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x0d, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x12, 0xd6, 0x01, 0x0a, 0x10, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x65, 0x67, 0x69, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x65, 0xda, 0x41, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x22, 0x2a, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x3a, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02,
	0x1d, 0x12, 0x0c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12,
	0x0d, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x12, 0xe6,
	0x01, 0x0a, 0x06, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x12, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x92, 0x01, 0xda, 0x41, 0x25, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x2c, 0x6d, 0x6f, 0x64, 0x65, 0x2c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2c, 0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0xda, 0x41, 0x19,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x2c, 0x6d, 0x6f, 0x64, 0x65, 0x2c,
	0x6d, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x22,
	0x20, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x3a, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x0c, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x0d, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x12, 0xc2, 0x01, 0x0a, 0x08, 0x52, 0x6f, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x12, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x69, 0xda, 0x41, 0x16, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x2c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x27, 0x22, 0x22, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x3a, 0x72, 0x6f,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12,
	0x0c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x0d, 0x0a,
	0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x12, 0xc7, 0x01, 0x0a,
	0x0b, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x49, 0x64, 0x73, 0x12, 0x27, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x49, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x65, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x65, 0xda, 0x41, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x2c, 0x6b,
	0x65, 0x79, 0x73, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x22, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x7d, 0x3a, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x49, 0x64, 0x73,
	0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x0c, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x0d, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x12, 0xc3, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x49, 0x64, 0x73, 0x12, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x64, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x64, 0xda, 0x41, 0x0f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x2c, 0x6b, 0x65, 0x79, 0x73, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29,
	0x22, 0x24, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x3a, 0x72, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x49, 0x64, 0x73, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12,
	0x0c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x0d, 0x0a,
	0x0b, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x1a, 0x76, 0xca, 0x41,
	0x18, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0xd2, 0x41, 0x58, 0x68, 0x74, 0x74, 0x70,
	0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2c, 0x68, 0x74, 0x74, 0x70, 0x73,
	0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x42, 0xc0, 0x01, 0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x42, 0x0e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x50, 0x01, 0x5a, 0x3c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e,
	0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x64, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0xaa, 0x02, 0x19, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x19, 0x47,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x44, 0x61, 0x74, 0x61,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x5c, 0x56, 0x31, 0xea, 0x02, 0x1c, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x3a, 0x3a, 0x44, 0x61, 0x74, 0x61, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_datastore_v1_datastore_proto_rawDescOnce sync.Once
	file_google_datastore_v1_datastore_proto_rawDescData = file_google_datastore_v1_datastore_proto_rawDesc
)

func file_google_datastore_v1_datastore_proto_rawDescGZIP() []byte {
	file_google_datastore_v1_datastore_proto_rawDescOnce.Do(func() {
		file_google_datastore_v1_datastore_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_datastore_v1_datastore_proto_rawDescData)
	})
	return file_google_datastore_v1_datastore_proto_rawDescData
}

var file_google_datastore_v1_datastore_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_google_datastore_v1_datastore_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_google_datastore_v1_datastore_proto_goTypes = []interface{}{
	(CommitRequest_Mode)(0),              // 0: google.datastore.v1.CommitRequest.Mode
	(ReadOptions_ReadConsistency)(0),     // 1: google.datastore.v1.ReadOptions.ReadConsistency
	(*LookupRequest)(nil),                // 2: google.datastore.v1.LookupRequest
	(*LookupResponse)(nil),               // 3: google.datastore.v1.LookupResponse
	(*RunQueryRequest)(nil),              // 4: google.datastore.v1.RunQueryRequest
	(*RunQueryResponse)(nil),             // 5: google.datastore.v1.RunQueryResponse
	(*RunAggregationQueryRequest)(nil),   // 6: google.datastore.v1.RunAggregationQueryRequest
	(*RunAggregationQueryResponse)(nil),  // 7: google.datastore.v1.RunAggregationQueryResponse
	(*BeginTransactionRequest)(nil),      // 8: google.datastore.v1.BeginTransactionRequest
	(*BeginTransactionResponse)(nil),     // 9: google.datastore.v1.BeginTransactionResponse
	(*RollbackRequest)(nil),              // 10: google.datastore.v1.RollbackRequest
	(*RollbackResponse)(nil),             // 11: google.datastore.v1.RollbackResponse
	(*CommitRequest)(nil),                // 12: google.datastore.v1.CommitRequest
	(*CommitResponse)(nil),               // 13: google.datastore.v1.CommitResponse
	(*AllocateIdsRequest)(nil),           // 14: google.datastore.v1.AllocateIdsRequest
	(*AllocateIdsResponse)(nil),          // 15: google.datastore.v1.AllocateIdsResponse
	(*ReserveIdsRequest)(nil),            // 16: google.datastore.v1.ReserveIdsRequest
	(*ReserveIdsResponse)(nil),           // 17: google.datastore.v1.ReserveIdsResponse
	(*Mutation)(nil),                     // 18: google.datastore.v1.Mutation
	(*MutationResult)(nil),               // 19: google.datastore.v1.MutationResult
	(*ReadOptions)(nil),                  // 20: google.datastore.v1.ReadOptions
	(*TransactionOptions)(nil),           // 21: google.datastore.v1.TransactionOptions
	(*TransactionOptions_ReadWrite)(nil), // 22: google.datastore.v1.TransactionOptions.ReadWrite
	(*TransactionOptions_ReadOnly)(nil),  // 23: google.datastore.v1.TransactionOptions.ReadOnly
	(*Key)(nil),                          // 24: google.datastore.v1.Key
	(*EntityResult)(nil),                 // 25: google.datastore.v1.EntityResult
	(*timestamppb.Timestamp)(nil),        // 26: google.protobuf.Timestamp
	(*PartitionId)(nil),                  // 27: google.datastore.v1.PartitionId
	(*Query)(nil),                        // 28: google.datastore.v1.Query
	(*GqlQuery)(nil),                     // 29: google.datastore.v1.GqlQuery
	(*ExplainOptions)(nil),               // 30: google.datastore.v1.ExplainOptions
	(*QueryResultBatch)(nil),             // 31: google.datastore.v1.QueryResultBatch
	(*ExplainMetrics)(nil),               // 32: google.datastore.v1.ExplainMetrics
	(*AggregationQuery)(nil),             // 33: google.datastore.v1.AggregationQuery
	(*AggregationResultBatch)(nil),       // 34: google.datastore.v1.AggregationResultBatch
	(*Entity)(nil),                       // 35: google.datastore.v1.Entity
}
var file_google_datastore_v1_datastore_proto_depIdxs = []int32{
	20, // 0: google.datastore.v1.LookupRequest.read_options:type_name -> google.datastore.v1.ReadOptions
	24, // 1: google.datastore.v1.LookupRequest.keys:type_name -> google.datastore.v1.Key
	25, // 2: google.datastore.v1.LookupResponse.found:type_name -> google.datastore.v1.EntityResult
	25, // 3: google.datastore.v1.LookupResponse.missing:type_name -> google.datastore.v1.EntityResult
	24, // 4: google.datastore.v1.LookupResponse.deferred:type_name -> google.datastore.v1.Key
	26, // 5: google.datastore.v1.LookupResponse.read_time:type_name -> google.protobuf.Timestamp
	27, // 6: google.datastore.v1.RunQueryRequest.partition_id:type_name -> google.datastore.v1.PartitionId
	20, // 7: google.datastore.v1.RunQueryRequest.read_options:type_name -> google.datastore.v1.ReadOptions
	28, // 8: google.datastore.v1.RunQueryRequest.query:type_name -> google.datastore.v1.Query
	29, // 9: google.datastore.v1.RunQueryRequest.gql_query:type_name -> google.datastore.v1.GqlQuery
	30, // 10: google.datastore.v1.RunQueryRequest.explain_options:type_name -> google.datastore.v1.ExplainOptions
	31, // 11: google.datastore.v1.RunQueryResponse.batch:type_name -> google.datastore.v1.QueryResultBatch
	28, // 12: google.datastore.v1.RunQueryResponse.query:type_name -> google.datastore.v1.Query
	32, // 13: google.datastore.v1.RunQueryResponse.explain_metrics:type_name -> google.datastore.v1.ExplainMetrics
	27, // 14: google.datastore.v1.RunAggregationQueryRequest.partition_id:type_name -> google.datastore.v1.PartitionId
	20, // 15: google.datastore.v1.RunAggregationQueryRequest.read_options:type_name -> google.datastore.v1.ReadOptions
	33, // 16: google.datastore.v1.RunAggregationQueryRequest.aggregation_query:type_name -> google.datastore.v1.AggregationQuery
	29, // 17: google.datastore.v1.RunAggregationQueryRequest.gql_query:type_name -> google.datastore.v1.GqlQuery
	30, // 18: google.datastore.v1.RunAggregationQueryRequest.explain_options:type_name -> google.datastore.v1.ExplainOptions
	34, // 19: google.datastore.v1.RunAggregationQueryResponse.batch:type_name -> google.datastore.v1.AggregationResultBatch
	33, // 20: google.datastore.v1.RunAggregationQueryResponse.query:type_name -> google.datastore.v1.AggregationQuery
	32, // 21: google.datastore.v1.RunAggregationQueryResponse.explain_metrics:type_name -> google.datastore.v1.ExplainMetrics
	21, // 22: google.datastore.v1.BeginTransactionRequest.transaction_options:type_name -> google.datastore.v1.TransactionOptions
	0,  // 23: google.datastore.v1.CommitRequest.mode:type_name -> google.datastore.v1.CommitRequest.Mode
	21, // 24: google.datastore.v1.CommitRequest.single_use_transaction:type_name -> google.datastore.v1.TransactionOptions
	18, // 25: google.datastore.v1.CommitRequest.mutations:type_name -> google.datastore.v1.Mutation
	19, // 26: google.datastore.v1.CommitResponse.mutation_results:type_name -> google.datastore.v1.MutationResult
	26, // 27: google.datastore.v1.CommitResponse.commit_time:type_name -> google.protobuf.Timestamp
	24, // 28: google.datastore.v1.AllocateIdsRequest.keys:type_name -> google.datastore.v1.Key
	24, // 29: google.datastore.v1.AllocateIdsResponse.keys:type_name -> google.datastore.v1.Key
	24, // 30: google.datastore.v1.ReserveIdsRequest.keys:type_name -> google.datastore.v1.Key
	35, // 31: google.datastore.v1.Mutation.insert:type_name -> google.datastore.v1.Entity
	35, // 32: google.datastore.v1.Mutation.update:type_name -> google.datastore.v1.Entity
	35, // 33: google.datastore.v1.Mutation.upsert:type_name -> google.datastore.v1.Entity
	24, // 34: google.datastore.v1.Mutation.delete:type_name -> google.datastore.v1.Key
	26, // 35: google.datastore.v1.Mutation.update_time:type_name -> google.protobuf.Timestamp
	24, // 36: google.datastore.v1.MutationResult.key:type_name -> google.datastore.v1.Key
	26, // 37: google.datastore.v1.MutationResult.create_time:type_name -> google.protobuf.Timestamp
	26, // 38: google.datastore.v1.MutationResult.update_time:type_name -> google.protobuf.Timestamp
	1,  // 39: google.datastore.v1.ReadOptions.read_consistency:type_name -> google.datastore.v1.ReadOptions.ReadConsistency
	21, // 40: google.datastore.v1.ReadOptions.new_transaction:type_name -> google.datastore.v1.TransactionOptions
	26, // 41: google.datastore.v1.ReadOptions.read_time:type_name -> google.protobuf.Timestamp
	22, // 42: google.datastore.v1.TransactionOptions.read_write:type_name -> google.datastore.v1.TransactionOptions.ReadWrite
	23, // 43: google.datastore.v1.TransactionOptions.read_only:type_name -> google.datastore.v1.TransactionOptions.ReadOnly
	26, // 44: google.datastore.v1.TransactionOptions.ReadOnly.read_time:type_name -> google.protobuf.Timestamp
	2,  // 45: google.datastore.v1.Datastore.Lookup:input_type -> google.datastore.v1.LookupRequest
	4,  // 46: google.datastore.v1.Datastore.RunQuery:input_type -> google.datastore.v1.RunQueryRequest
	6,  // 47: google.datastore.v1.Datastore.RunAggregationQuery:input_type -> google.datastore.v1.RunAggregationQueryRequest
	8,  // 48: google.datastore.v1.Datastore.BeginTransaction:input_type -> google.datastore.v1.BeginTransactionRequest
	12, // 49: google.datastore.v1.Datastore.Commit:input_type -> google.datastore.v1.CommitRequest
	10, // 50: google.datastore.v1.Datastore.Rollback:input_type -> google.datastore.v1.RollbackRequest
	14, // 51: google.datastore.v1.Datastore.AllocateIds:input_type -> google.datastore.v1.AllocateIdsRequest
	16, // 52: google.datastore.v1.Datastore.ReserveIds:input_type -> google.datastore.v1.ReserveIdsRequest
	3,  // 53: google.datastore.v1.Datastore.Lookup:output_type -> google.datastore.v1.LookupResponse
	5,  // 54: google.datastore.v1.Datastore.RunQuery:output_type -> google.datastore.v1.RunQueryResponse
	7,  // 55: google.datastore.v1.Datastore.RunAggregationQuery:output_type -> google.datastore.v1.RunAggregationQueryResponse
	9,  // 56: google.datastore.v1.Datastore.BeginTransaction:output_type -> google.datastore.v1.BeginTransactionResponse
	13, // 57: google.datastore.v1.Datastore.Commit:output_type -> google.datastore.v1.CommitResponse
	11, // 58: google.datastore.v1.Datastore.Rollback:output_type -> google.datastore.v1.RollbackResponse
	15, // 59: google.datastore.v1.Datastore.AllocateIds:output_type -> google.datastore.v1.AllocateIdsResponse
	17, // 60: google.datastore.v1.Datastore.ReserveIds:output_type -> google.datastore.v1.ReserveIdsResponse
	53, // [53:61] is the sub-list for method output_type
	45, // [45:53] is the sub-list for method input_type
	45, // [45:45] is the sub-list for extension type_name
	45, // [45:45] is the sub-list for extension extendee
	0,  // [0:45] is the sub-list for field type_name
}

func init() { file_google_datastore_v1_datastore_proto_init() }
func file_google_datastore_v1_datastore_proto_init() {
	if File_google_datastore_v1_datastore_proto != nil {
		return
	}
	file_google_datastore_v1_aggregation_result_proto_init()
	file_google_datastore_v1_entity_proto_init()
	file_google_datastore_v1_query_proto_init()
	file_google_datastore_v1_query_profile_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_datastore_v1_datastore_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LookupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LookupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunAggregationQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunAggregationQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BeginTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BeginTransactionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllocateIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllocateIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReserveIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReserveIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mutation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionOptions_ReadWrite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_datastore_v1_datastore_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionOptions_ReadOnly); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_datastore_v1_datastore_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*RunQueryRequest_Query)(nil),
		(*RunQueryRequest_GqlQuery)(nil),
	}
	file_google_datastore_v1_datastore_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*RunAggregationQueryRequest_AggregationQuery)(nil),
		(*RunAggregationQueryRequest_GqlQuery)(nil),
	}
	file_google_datastore_v1_datastore_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*CommitRequest_Transaction)(nil),
		(*CommitRequest_SingleUseTransaction)(nil),
	}
	file_google_datastore_v1_datastore_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*Mutation_Insert)(nil),
		(*Mutation_Update)(nil),
		(*Mutation_Upsert)(nil),
		(*Mutation_Delete)(nil),
		(*Mutation_BaseVersion)(nil),
		(*Mutation_UpdateTime)(nil),
	}
	file_google_datastore_v1_datastore_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*ReadOptions_ReadConsistency_)(nil),
		(*ReadOptions_Transaction)(nil),
		(*ReadOptions_NewTransaction)(nil),
		(*ReadOptions_ReadTime)(nil),
	}
	file_google_datastore_v1_datastore_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*TransactionOptions_ReadWrite_)(nil),
		(*TransactionOptions_ReadOnly_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_datastore_v1_datastore_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_datastore_v1_datastore_proto_goTypes,
		DependencyIndexes: file_google_datastore_v1_datastore_proto_depIdxs,
		EnumInfos:         file_google_datastore_v1_datastore_proto_enumTypes,
		MessageInfos:      file_google_datastore_v1_datastore_proto_msgTypes,
	}.Build()
	File_google_datastore_v1_datastore_proto = out.File
	file_google_datastore_v1_datastore_proto_rawDesc = nil
	file_google_datastore_v1_datastore_proto_goTypes = nil
	file_google_datastore_v1_datastore_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// DatastoreClient is the client API for Datastore service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DatastoreClient interface {
	// Looks up entities by key.
	Lookup(ctx context.Context, in *LookupRequest, opts ...grpc.CallOption) (*LookupResponse, error)
	// Queries for entities.
	RunQuery(ctx context.Context, in *RunQueryRequest, opts ...grpc.CallOption) (*RunQueryResponse, error)
	// Runs an aggregation query.
	RunAggregationQuery(ctx context.Context, in *RunAggregationQueryRequest, opts ...grpc.CallOption) (*RunAggregationQueryResponse, error)
	// Begins a new transaction.
	BeginTransaction(ctx context.Context, in *BeginTransactionRequest, opts ...grpc.CallOption) (*BeginTransactionResponse, error)
	// Commits a transaction, optionally creating, deleting or modifying some
	// entities.
	Commit(ctx context.Context, in *CommitRequest, opts ...grpc.CallOption) (*CommitResponse, error)
	// Rolls back a transaction.
	Rollback(ctx context.Context, in *RollbackRequest, opts ...grpc.CallOption) (*RollbackResponse, error)
	// Allocates IDs for the given keys, which is useful for referencing an entity
	// before it is inserted.
	AllocateIds(ctx context.Context, in *AllocateIdsRequest, opts ...grpc.CallOption) (*AllocateIdsResponse, error)
	// Prevents the supplied keys' IDs from being auto-allocated by Cloud
	// Datastore.
	ReserveIds(ctx context.Context, in *ReserveIdsRequest, opts ...grpc.CallOption) (*ReserveIdsResponse, error)
}

type datastoreClient struct {
	cc grpc.ClientConnInterface
}

func NewDatastoreClient(cc grpc.ClientConnInterface) DatastoreClient {
	return &datastoreClient{cc}
}

func (c *datastoreClient) Lookup(ctx context.Context, in *LookupRequest, opts ...grpc.CallOption) (*LookupResponse, error) {
	out := new(LookupResponse)
	err := c.cc.Invoke(ctx, "/google.datastore.v1.Datastore/Lookup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datastoreClient) RunQuery(ctx context.Context, in *RunQueryRequest, opts ...grpc.CallOption) (*RunQueryResponse, error) {
	out := new(RunQueryResponse)
	err := c.cc.Invoke(ctx, "/google.datastore.v1.Datastore/RunQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datastoreClient) RunAggregationQuery(ctx context.Context, in *RunAggregationQueryRequest, opts ...grpc.CallOption) (*RunAggregationQueryResponse, error) {
	out := new(RunAggregationQueryResponse)
	err := c.cc.Invoke(ctx, "/google.datastore.v1.Datastore/RunAggregationQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datastoreClient) BeginTransaction(ctx context.Context, in *BeginTransactionRequest, opts ...grpc.CallOption) (*BeginTransactionResponse, error) {
	out := new(BeginTransactionResponse)
	err := c.cc.Invoke(ctx, "/google.datastore.v1.Datastore/BeginTransaction", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datastoreClient) Commit(ctx context.Context, in *CommitRequest, opts ...grpc.CallOption) (*CommitResponse, error) {
	out := new(CommitResponse)
	err := c.cc.Invoke(ctx, "/google.datastore.v1.Datastore/Commit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datastoreClient) Rollback(ctx context.Context, in *RollbackRequest, opts ...grpc.CallOption) (*RollbackResponse, error) {
	out := new(RollbackResponse)
	err := c.cc.Invoke(ctx, "/google.datastore.v1.Datastore/Rollback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datastoreClient) AllocateIds(ctx context.Context, in *AllocateIdsRequest, opts ...grpc.CallOption) (*AllocateIdsResponse, error) {
	out := new(AllocateIdsResponse)
	err := c.cc.Invoke(ctx, "/google.datastore.v1.Datastore/AllocateIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *datastoreClient) ReserveIds(ctx context.Context, in *ReserveIdsRequest, opts ...grpc.CallOption) (*ReserveIdsResponse, error) {
	out := new(ReserveIdsResponse)
	err := c.cc.Invoke(ctx, "/google.datastore.v1.Datastore/ReserveIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DatastoreServer is the server API for Datastore service.
type DatastoreServer interface {
	// Looks up entities by key.
	Lookup(context.Context, *LookupRequest) (*LookupResponse, error)
	// Queries for entities.
	RunQuery(context.Context, *RunQueryRequest) (*RunQueryResponse, error)
	// Runs an aggregation query.
	RunAggregationQuery(context.Context, *RunAggregationQueryRequest) (*RunAggregationQueryResponse, error)
	// Begins a new transaction.
	BeginTransaction(context.Context, *BeginTransactionRequest) (*BeginTransactionResponse, error)
	// Commits a transaction, optionally creating, deleting or modifying some
	// entities.
	Commit(context.Context, *CommitRequest) (*CommitResponse, error)
	// Rolls back a transaction.
	Rollback(context.Context, *RollbackRequest) (*RollbackResponse, error)
	// Allocates IDs for the given keys, which is useful for referencing an entity
	// before it is inserted.
	AllocateIds(context.Context, *AllocateIdsRequest) (*AllocateIdsResponse, error)
	// Prevents the supplied keys' IDs from being auto-allocated by Cloud
	// Datastore.
	ReserveIds(context.Context, *ReserveIdsRequest) (*ReserveIdsResponse, error)
}

// UnimplementedDatastoreServer can be embedded to have forward compatible implementations.
type UnimplementedDatastoreServer struct {
}

func (*UnimplementedDatastoreServer) Lookup(context.Context, *LookupRequest) (*LookupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Lookup not implemented")
}
func (*UnimplementedDatastoreServer) RunQuery(context.Context, *RunQueryRequest) (*RunQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunQuery not implemented")
}
func (*UnimplementedDatastoreServer) RunAggregationQuery(context.Context, *RunAggregationQueryRequest) (*RunAggregationQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunAggregationQuery not implemented")
}
func (*UnimplementedDatastoreServer) BeginTransaction(context.Context, *BeginTransactionRequest) (*BeginTransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BeginTransaction not implemented")
}
func (*UnimplementedDatastoreServer) Commit(context.Context, *CommitRequest) (*CommitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Commit not implemented")
}
func (*UnimplementedDatastoreServer) Rollback(context.Context, *RollbackRequest) (*RollbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Rollback not implemented")
}
func (*UnimplementedDatastoreServer) AllocateIds(context.Context, *AllocateIdsRequest) (*AllocateIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllocateIds not implemented")
}
func (*UnimplementedDatastoreServer) ReserveIds(context.Context, *ReserveIdsRequest) (*ReserveIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReserveIds not implemented")
}

func RegisterDatastoreServer(s *grpc.Server, srv DatastoreServer) {
	s.RegisterService(&_Datastore_serviceDesc, srv)
}

func _Datastore_Lookup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LookupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatastoreServer).Lookup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.datastore.v1.Datastore/Lookup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatastoreServer).Lookup(ctx, req.(*LookupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datastore_RunQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatastoreServer).RunQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.datastore.v1.Datastore/RunQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatastoreServer).RunQuery(ctx, req.(*RunQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datastore_RunAggregationQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunAggregationQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatastoreServer).RunAggregationQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.datastore.v1.Datastore/RunAggregationQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatastoreServer).RunAggregationQuery(ctx, req.(*RunAggregationQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datastore_BeginTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BeginTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatastoreServer).BeginTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.datastore.v1.Datastore/BeginTransaction",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatastoreServer).BeginTransaction(ctx, req.(*BeginTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datastore_Commit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatastoreServer).Commit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.datastore.v1.Datastore/Commit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatastoreServer).Commit(ctx, req.(*CommitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datastore_Rollback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatastoreServer).Rollback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.datastore.v1.Datastore/Rollback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatastoreServer).Rollback(ctx, req.(*RollbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datastore_AllocateIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocateIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatastoreServer).AllocateIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.datastore.v1.Datastore/AllocateIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatastoreServer).AllocateIds(ctx, req.(*AllocateIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Datastore_ReserveIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReserveIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DatastoreServer).ReserveIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.datastore.v1.Datastore/ReserveIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DatastoreServer).ReserveIds(ctx, req.(*ReserveIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Datastore_serviceDesc = grpc.ServiceDesc{
	ServiceName: "google.datastore.v1.Datastore",
	HandlerType: (*DatastoreServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Lookup",
			Handler:    _Datastore_Lookup_Handler,
		},
		{
			MethodName: "RunQuery",
			Handler:    _Datastore_RunQuery_Handler,
		},
		{
			MethodName: "RunAggregationQuery",
			Handler:    _Datastore_RunAggregationQuery_Handler,
		},
		{
			MethodName: "BeginTransaction",
			Handler:    _Datastore_BeginTransaction_Handler,
		},
		{
			MethodName: "Commit",
			Handler:    _Datastore_Commit_Handler,
		},
		{
			MethodName: "Rollback",
			Handler:    _Datastore_Rollback_Handler,
		},
		{
			MethodName: "AllocateIds",
			Handler:    _Datastore_AllocateIds_Handler,
		},
		{
			MethodName: "ReserveIds",
			Handler:    _Datastore_ReserveIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "google/datastore/v1/datastore.proto",
}
