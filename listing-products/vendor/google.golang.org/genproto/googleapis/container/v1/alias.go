// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by aliasgen. DO NOT EDIT.

// Package container aliases all exported identifiers in package
// "cloud.google.com/go/container/apiv1/containerpb".
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb.
// Please read https://github.com/googleapis/google-cloud-go/blob/main/migration.md
// for more details.
package container

import (
	src "cloud.google.com/go/container/apiv1/containerpb"
	grpc "google.golang.org/grpc"
)

// Deprecated: Please use consts in: cloud.google.com/go/container/apiv1/containerpb
const (
	BinaryAuthorization_DISABLED                                     = src.BinaryAuthorization_DISABLED
	BinaryAuthorization_EVALUATION_MODE_UNSPECIFIED                  = src.BinaryAuthorization_EVALUATION_MODE_UNSPECIFIED
	BinaryAuthorization_PROJECT_SINGLETON_POLICY_ENFORCE             = src.BinaryAuthorization_PROJECT_SINGLETON_POLICY_ENFORCE
	CloudRunConfig_LOAD_BALANCER_TYPE_EXTERNAL                       = src.CloudRunConfig_LOAD_BALANCER_TYPE_EXTERNAL
	CloudRunConfig_LOAD_BALANCER_TYPE_INTERNAL                       = src.CloudRunConfig_LOAD_BALANCER_TYPE_INTERNAL
	CloudRunConfig_LOAD_BALANCER_TYPE_UNSPECIFIED                    = src.CloudRunConfig_LOAD_BALANCER_TYPE_UNSPECIFIED
	ClusterAutoscaling_BALANCED                                      = src.ClusterAutoscaling_BALANCED
	ClusterAutoscaling_OPTIMIZE_UTILIZATION                          = src.ClusterAutoscaling_OPTIMIZE_UTILIZATION
	ClusterAutoscaling_PROFILE_UNSPECIFIED                           = src.ClusterAutoscaling_PROFILE_UNSPECIFIED
	Cluster_DEGRADED                                                 = src.Cluster_DEGRADED
	Cluster_ERROR                                                    = src.Cluster_ERROR
	Cluster_PROVISIONING                                             = src.Cluster_PROVISIONING
	Cluster_RECONCILING                                              = src.Cluster_RECONCILING
	Cluster_RUNNING                                                  = src.Cluster_RUNNING
	Cluster_STATUS_UNSPECIFIED                                       = src.Cluster_STATUS_UNSPECIFIED
	Cluster_STOPPING                                                 = src.Cluster_STOPPING
	DNSConfig_CLOUD_DNS                                              = src.DNSConfig_CLOUD_DNS
	DNSConfig_DNS_SCOPE_UNSPECIFIED                                  = src.DNSConfig_DNS_SCOPE_UNSPECIFIED
	DNSConfig_PLATFORM_DEFAULT                                       = src.DNSConfig_PLATFORM_DEFAULT
	DNSConfig_PROVIDER_UNSPECIFIED                                   = src.DNSConfig_PROVIDER_UNSPECIFIED
	DNSConfig_VPC_SCOPE                                              = src.DNSConfig_VPC_SCOPE
	DatabaseEncryption_DECRYPTED                                     = src.DatabaseEncryption_DECRYPTED
	DatabaseEncryption_ENCRYPTED                                     = src.DatabaseEncryption_ENCRYPTED
	DatabaseEncryption_UNKNOWN                                       = src.DatabaseEncryption_UNKNOWN
	DatapathProvider_ADVANCED_DATAPATH                               = src.DatapathProvider_ADVANCED_DATAPATH
	DatapathProvider_DATAPATH_PROVIDER_UNSPECIFIED                   = src.DatapathProvider_DATAPATH_PROVIDER_UNSPECIFIED
	DatapathProvider_LEGACY_DATAPATH                                 = src.DatapathProvider_LEGACY_DATAPATH
	GPUSharingConfig_GPU_SHARING_STRATEGY_UNSPECIFIED                = src.GPUSharingConfig_GPU_SHARING_STRATEGY_UNSPECIFIED
	GPUSharingConfig_TIME_SHARING                                    = src.GPUSharingConfig_TIME_SHARING
	IPv6AccessType_EXTERNAL                                          = src.IPv6AccessType_EXTERNAL
	IPv6AccessType_INTERNAL                                          = src.IPv6AccessType_INTERNAL
	IPv6AccessType_IPV6_ACCESS_TYPE_UNSPECIFIED                      = src.IPv6AccessType_IPV6_ACCESS_TYPE_UNSPECIFIED
	LoggingComponentConfig_COMPONENT_UNSPECIFIED                     = src.LoggingComponentConfig_COMPONENT_UNSPECIFIED
	LoggingComponentConfig_SYSTEM_COMPONENTS                         = src.LoggingComponentConfig_SYSTEM_COMPONENTS
	LoggingComponentConfig_WORKLOADS                                 = src.LoggingComponentConfig_WORKLOADS
	LoggingVariantConfig_DEFAULT                                     = src.LoggingVariantConfig_DEFAULT
	LoggingVariantConfig_MAX_THROUGHPUT                              = src.LoggingVariantConfig_MAX_THROUGHPUT
	LoggingVariantConfig_VARIANT_UNSPECIFIED                         = src.LoggingVariantConfig_VARIANT_UNSPECIFIED
	MaintenanceExclusionOptions_NO_MINOR_OR_NODE_UPGRADES            = src.MaintenanceExclusionOptions_NO_MINOR_OR_NODE_UPGRADES
	MaintenanceExclusionOptions_NO_MINOR_UPGRADES                    = src.MaintenanceExclusionOptions_NO_MINOR_UPGRADES
	MaintenanceExclusionOptions_NO_UPGRADES                          = src.MaintenanceExclusionOptions_NO_UPGRADES
	MonitoringComponentConfig_APISERVER                              = src.MonitoringComponentConfig_APISERVER
	MonitoringComponentConfig_COMPONENT_UNSPECIFIED                  = src.MonitoringComponentConfig_COMPONENT_UNSPECIFIED
	MonitoringComponentConfig_CONTROLLER_MANAGER                     = src.MonitoringComponentConfig_CONTROLLER_MANAGER
	MonitoringComponentConfig_SCHEDULER                              = src.MonitoringComponentConfig_SCHEDULER
	MonitoringComponentConfig_SYSTEM_COMPONENTS                      = src.MonitoringComponentConfig_SYSTEM_COMPONENTS
	NetworkPolicy_CALICO                                             = src.NetworkPolicy_CALICO
	NetworkPolicy_PROVIDER_UNSPECIFIED                               = src.NetworkPolicy_PROVIDER_UNSPECIFIED
	NodeNetworkConfig_NetworkPerformanceConfig_TIER_1                = src.NodeNetworkConfig_NetworkPerformanceConfig_TIER_1
	NodeNetworkConfig_NetworkPerformanceConfig_TIER_UNSPECIFIED      = src.NodeNetworkConfig_NetworkPerformanceConfig_TIER_UNSPECIFIED
	NodePoolAutoscaling_ANY                                          = src.NodePoolAutoscaling_ANY
	NodePoolAutoscaling_BALANCED                                     = src.NodePoolAutoscaling_BALANCED
	NodePoolAutoscaling_LOCATION_POLICY_UNSPECIFIED                  = src.NodePoolAutoscaling_LOCATION_POLICY_UNSPECIFIED
	NodePoolUpdateStrategy_BLUE_GREEN                                = src.NodePoolUpdateStrategy_BLUE_GREEN
	NodePoolUpdateStrategy_NODE_POOL_UPDATE_STRATEGY_UNSPECIFIED     = src.NodePoolUpdateStrategy_NODE_POOL_UPDATE_STRATEGY_UNSPECIFIED
	NodePoolUpdateStrategy_SURGE                                     = src.NodePoolUpdateStrategy_SURGE
	NodePool_ERROR                                                   = src.NodePool_ERROR
	NodePool_PROVISIONING                                            = src.NodePool_PROVISIONING
	NodePool_RECONCILING                                             = src.NodePool_RECONCILING
	NodePool_RUNNING                                                 = src.NodePool_RUNNING
	NodePool_RUNNING_WITH_ERROR                                      = src.NodePool_RUNNING_WITH_ERROR
	NodePool_STATUS_UNSPECIFIED                                      = src.NodePool_STATUS_UNSPECIFIED
	NodePool_STOPPING                                                = src.NodePool_STOPPING
	NodePool_UpdateInfo_BlueGreenInfo_CORDONING_BLUE_POOL            = src.NodePool_UpdateInfo_BlueGreenInfo_CORDONING_BLUE_POOL
	NodePool_UpdateInfo_BlueGreenInfo_CREATING_GREEN_POOL            = src.NodePool_UpdateInfo_BlueGreenInfo_CREATING_GREEN_POOL
	NodePool_UpdateInfo_BlueGreenInfo_DELETING_BLUE_POOL             = src.NodePool_UpdateInfo_BlueGreenInfo_DELETING_BLUE_POOL
	NodePool_UpdateInfo_BlueGreenInfo_DRAINING_BLUE_POOL             = src.NodePool_UpdateInfo_BlueGreenInfo_DRAINING_BLUE_POOL
	NodePool_UpdateInfo_BlueGreenInfo_NODE_POOL_SOAKING              = src.NodePool_UpdateInfo_BlueGreenInfo_NODE_POOL_SOAKING
	NodePool_UpdateInfo_BlueGreenInfo_PHASE_UNSPECIFIED              = src.NodePool_UpdateInfo_BlueGreenInfo_PHASE_UNSPECIFIED
	NodePool_UpdateInfo_BlueGreenInfo_ROLLBACK_STARTED               = src.NodePool_UpdateInfo_BlueGreenInfo_ROLLBACK_STARTED
	NodePool_UpdateInfo_BlueGreenInfo_UPDATE_STARTED                 = src.NodePool_UpdateInfo_BlueGreenInfo_UPDATE_STARTED
	NodeTaint_EFFECT_UNSPECIFIED                                     = src.NodeTaint_EFFECT_UNSPECIFIED
	NodeTaint_NO_EXECUTE                                             = src.NodeTaint_NO_EXECUTE
	NodeTaint_NO_SCHEDULE                                            = src.NodeTaint_NO_SCHEDULE
	NodeTaint_PREFER_NO_SCHEDULE                                     = src.NodeTaint_PREFER_NO_SCHEDULE
	NotificationConfig_EVENT_TYPE_UNSPECIFIED                        = src.NotificationConfig_EVENT_TYPE_UNSPECIFIED
	NotificationConfig_SECURITY_BULLETIN_EVENT                       = src.NotificationConfig_SECURITY_BULLETIN_EVENT
	NotificationConfig_UPGRADE_AVAILABLE_EVENT                       = src.NotificationConfig_UPGRADE_AVAILABLE_EVENT
	NotificationConfig_UPGRADE_EVENT                                 = src.NotificationConfig_UPGRADE_EVENT
	Operation_ABORTING                                               = src.Operation_ABORTING
	Operation_AUTO_REPAIR_NODES                                      = src.Operation_AUTO_REPAIR_NODES
	Operation_AUTO_UPGRADE_NODES                                     = src.Operation_AUTO_UPGRADE_NODES
	Operation_CREATE_CLUSTER                                         = src.Operation_CREATE_CLUSTER
	Operation_CREATE_NODE_POOL                                       = src.Operation_CREATE_NODE_POOL
	Operation_DELETE_CLUSTER                                         = src.Operation_DELETE_CLUSTER
	Operation_DELETE_NODE_POOL                                       = src.Operation_DELETE_NODE_POOL
	Operation_DONE                                                   = src.Operation_DONE
	Operation_PENDING                                                = src.Operation_PENDING
	Operation_REPAIR_CLUSTER                                         = src.Operation_REPAIR_CLUSTER
	Operation_RUNNING                                                = src.Operation_RUNNING
	Operation_SET_LABELS                                             = src.Operation_SET_LABELS
	Operation_SET_MAINTENANCE_POLICY                                 = src.Operation_SET_MAINTENANCE_POLICY
	Operation_SET_MASTER_AUTH                                        = src.Operation_SET_MASTER_AUTH
	Operation_SET_NETWORK_POLICY                                     = src.Operation_SET_NETWORK_POLICY
	Operation_SET_NODE_POOL_MANAGEMENT                               = src.Operation_SET_NODE_POOL_MANAGEMENT
	Operation_SET_NODE_POOL_SIZE                                     = src.Operation_SET_NODE_POOL_SIZE
	Operation_STATUS_UNSPECIFIED                                     = src.Operation_STATUS_UNSPECIFIED
	Operation_TYPE_UNSPECIFIED                                       = src.Operation_TYPE_UNSPECIFIED
	Operation_UPDATE_CLUSTER                                         = src.Operation_UPDATE_CLUSTER
	Operation_UPGRADE_MASTER                                         = src.Operation_UPGRADE_MASTER
	Operation_UPGRADE_NODES                                          = src.Operation_UPGRADE_NODES
	PrivateIPv6GoogleAccess_PRIVATE_IPV6_GOOGLE_ACCESS_BIDIRECTIONAL = src.PrivateIPv6GoogleAccess_PRIVATE_IPV6_GOOGLE_ACCESS_BIDIRECTIONAL
	PrivateIPv6GoogleAccess_PRIVATE_IPV6_GOOGLE_ACCESS_DISABLED      = src.PrivateIPv6GoogleAccess_PRIVATE_IPV6_GOOGLE_ACCESS_DISABLED
	PrivateIPv6GoogleAccess_PRIVATE_IPV6_GOOGLE_ACCESS_TO_GOOGLE     = src.PrivateIPv6GoogleAccess_PRIVATE_IPV6_GOOGLE_ACCESS_TO_GOOGLE
	PrivateIPv6GoogleAccess_PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED   = src.PrivateIPv6GoogleAccess_PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED
	ReleaseChannel_RAPID                                             = src.ReleaseChannel_RAPID
	ReleaseChannel_REGULAR                                           = src.ReleaseChannel_REGULAR
	ReleaseChannel_STABLE                                            = src.ReleaseChannel_STABLE
	ReleaseChannel_UNSPECIFIED                                       = src.ReleaseChannel_UNSPECIFIED
	ReservationAffinity_ANY_RESERVATION                              = src.ReservationAffinity_ANY_RESERVATION
	ReservationAffinity_NO_RESERVATION                               = src.ReservationAffinity_NO_RESERVATION
	ReservationAffinity_SPECIFIC_RESERVATION                         = src.ReservationAffinity_SPECIFIC_RESERVATION
	ReservationAffinity_UNSPECIFIED                                  = src.ReservationAffinity_UNSPECIFIED
	SandboxConfig_GVISOR                                             = src.SandboxConfig_GVISOR
	SandboxConfig_UNSPECIFIED                                        = src.SandboxConfig_UNSPECIFIED
	SetMasterAuthRequest_GENERATE_PASSWORD                           = src.SetMasterAuthRequest_GENERATE_PASSWORD
	SetMasterAuthRequest_SET_PASSWORD                                = src.SetMasterAuthRequest_SET_PASSWORD
	SetMasterAuthRequest_SET_USERNAME                                = src.SetMasterAuthRequest_SET_USERNAME
	SetMasterAuthRequest_UNKNOWN                                     = src.SetMasterAuthRequest_UNKNOWN
	StackType_IPV4                                                   = src.StackType_IPV4
	StackType_IPV4_IPV6                                              = src.StackType_IPV4_IPV6
	StackType_STACK_TYPE_UNSPECIFIED                                 = src.StackType_STACK_TYPE_UNSPECIFIED
	StatusCondition_CA_EXPIRING                                      = src.StatusCondition_CA_EXPIRING
	StatusCondition_CLOUD_KMS_KEY_ERROR                              = src.StatusCondition_CLOUD_KMS_KEY_ERROR
	StatusCondition_GCE_QUOTA_EXCEEDED                               = src.StatusCondition_GCE_QUOTA_EXCEEDED
	StatusCondition_GCE_STOCKOUT                                     = src.StatusCondition_GCE_STOCKOUT
	StatusCondition_GKE_SERVICE_ACCOUNT_DELETED                      = src.StatusCondition_GKE_SERVICE_ACCOUNT_DELETED
	StatusCondition_SET_BY_OPERATOR                                  = src.StatusCondition_SET_BY_OPERATOR
	StatusCondition_UNKNOWN                                          = src.StatusCondition_UNKNOWN
	UpgradeResourceType_MASTER                                       = src.UpgradeResourceType_MASTER
	UpgradeResourceType_NODE_POOL                                    = src.UpgradeResourceType_NODE_POOL
	UpgradeResourceType_UPGRADE_RESOURCE_TYPE_UNSPECIFIED            = src.UpgradeResourceType_UPGRADE_RESOURCE_TYPE_UNSPECIFIED
	UsableSubnetworkSecondaryRange_IN_USE_MANAGED_POD                = src.UsableSubnetworkSecondaryRange_IN_USE_MANAGED_POD
	UsableSubnetworkSecondaryRange_IN_USE_SERVICE                    = src.UsableSubnetworkSecondaryRange_IN_USE_SERVICE
	UsableSubnetworkSecondaryRange_IN_USE_SHAREABLE_POD              = src.UsableSubnetworkSecondaryRange_IN_USE_SHAREABLE_POD
	UsableSubnetworkSecondaryRange_UNKNOWN                           = src.UsableSubnetworkSecondaryRange_UNKNOWN
	UsableSubnetworkSecondaryRange_UNUSED                            = src.UsableSubnetworkSecondaryRange_UNUSED
	WorkloadMetadataConfig_GCE_METADATA                              = src.WorkloadMetadataConfig_GCE_METADATA
	WorkloadMetadataConfig_GKE_METADATA                              = src.WorkloadMetadataConfig_GKE_METADATA
	WorkloadMetadataConfig_MODE_UNSPECIFIED                          = src.WorkloadMetadataConfig_MODE_UNSPECIFIED
)

// Deprecated: Please use vars in: cloud.google.com/go/container/apiv1/containerpb
var (
	BinaryAuthorization_EvaluationMode_name               = src.BinaryAuthorization_EvaluationMode_name
	BinaryAuthorization_EvaluationMode_value              = src.BinaryAuthorization_EvaluationMode_value
	CloudRunConfig_LoadBalancerType_name                  = src.CloudRunConfig_LoadBalancerType_name
	CloudRunConfig_LoadBalancerType_value                 = src.CloudRunConfig_LoadBalancerType_value
	ClusterAutoscaling_AutoscalingProfile_name            = src.ClusterAutoscaling_AutoscalingProfile_name
	ClusterAutoscaling_AutoscalingProfile_value           = src.ClusterAutoscaling_AutoscalingProfile_value
	Cluster_Status_name                                   = src.Cluster_Status_name
	Cluster_Status_value                                  = src.Cluster_Status_value
	DNSConfig_DNSScope_name                               = src.DNSConfig_DNSScope_name
	DNSConfig_DNSScope_value                              = src.DNSConfig_DNSScope_value
	DNSConfig_Provider_name                               = src.DNSConfig_Provider_name
	DNSConfig_Provider_value                              = src.DNSConfig_Provider_value
	DatabaseEncryption_State_name                         = src.DatabaseEncryption_State_name
	DatabaseEncryption_State_value                        = src.DatabaseEncryption_State_value
	DatapathProvider_name                                 = src.DatapathProvider_name
	DatapathProvider_value                                = src.DatapathProvider_value
	File_google_container_v1_cluster_service_proto        = src.File_google_container_v1_cluster_service_proto
	GPUSharingConfig_GPUSharingStrategy_name              = src.GPUSharingConfig_GPUSharingStrategy_name
	GPUSharingConfig_GPUSharingStrategy_value             = src.GPUSharingConfig_GPUSharingStrategy_value
	IPv6AccessType_name                                   = src.IPv6AccessType_name
	IPv6AccessType_value                                  = src.IPv6AccessType_value
	LoggingComponentConfig_Component_name                 = src.LoggingComponentConfig_Component_name
	LoggingComponentConfig_Component_value                = src.LoggingComponentConfig_Component_value
	LoggingVariantConfig_Variant_name                     = src.LoggingVariantConfig_Variant_name
	LoggingVariantConfig_Variant_value                    = src.LoggingVariantConfig_Variant_value
	MaintenanceExclusionOptions_Scope_name                = src.MaintenanceExclusionOptions_Scope_name
	MaintenanceExclusionOptions_Scope_value               = src.MaintenanceExclusionOptions_Scope_value
	MonitoringComponentConfig_Component_name              = src.MonitoringComponentConfig_Component_name
	MonitoringComponentConfig_Component_value             = src.MonitoringComponentConfig_Component_value
	NetworkPolicy_Provider_name                           = src.NetworkPolicy_Provider_name
	NetworkPolicy_Provider_value                          = src.NetworkPolicy_Provider_value
	NodeNetworkConfig_NetworkPerformanceConfig_Tier_name  = src.NodeNetworkConfig_NetworkPerformanceConfig_Tier_name
	NodeNetworkConfig_NetworkPerformanceConfig_Tier_value = src.NodeNetworkConfig_NetworkPerformanceConfig_Tier_value
	NodePoolAutoscaling_LocationPolicy_name               = src.NodePoolAutoscaling_LocationPolicy_name
	NodePoolAutoscaling_LocationPolicy_value              = src.NodePoolAutoscaling_LocationPolicy_value
	NodePoolUpdateStrategy_name                           = src.NodePoolUpdateStrategy_name
	NodePoolUpdateStrategy_value                          = src.NodePoolUpdateStrategy_value
	NodePool_Status_name                                  = src.NodePool_Status_name
	NodePool_Status_value                                 = src.NodePool_Status_value
	NodePool_UpdateInfo_BlueGreenInfo_Phase_name          = src.NodePool_UpdateInfo_BlueGreenInfo_Phase_name
	NodePool_UpdateInfo_BlueGreenInfo_Phase_value         = src.NodePool_UpdateInfo_BlueGreenInfo_Phase_value
	NodeTaint_Effect_name                                 = src.NodeTaint_Effect_name
	NodeTaint_Effect_value                                = src.NodeTaint_Effect_value
	NotificationConfig_EventType_name                     = src.NotificationConfig_EventType_name
	NotificationConfig_EventType_value                    = src.NotificationConfig_EventType_value
	Operation_Status_name                                 = src.Operation_Status_name
	Operation_Status_value                                = src.Operation_Status_value
	Operation_Type_name                                   = src.Operation_Type_name
	Operation_Type_value                                  = src.Operation_Type_value
	PrivateIPv6GoogleAccess_name                          = src.PrivateIPv6GoogleAccess_name
	PrivateIPv6GoogleAccess_value                         = src.PrivateIPv6GoogleAccess_value
	ReleaseChannel_Channel_name                           = src.ReleaseChannel_Channel_name
	ReleaseChannel_Channel_value                          = src.ReleaseChannel_Channel_value
	ReservationAffinity_Type_name                         = src.ReservationAffinity_Type_name
	ReservationAffinity_Type_value                        = src.ReservationAffinity_Type_value
	SandboxConfig_Type_name                               = src.SandboxConfig_Type_name
	SandboxConfig_Type_value                              = src.SandboxConfig_Type_value
	SetMasterAuthRequest_Action_name                      = src.SetMasterAuthRequest_Action_name
	SetMasterAuthRequest_Action_value                     = src.SetMasterAuthRequest_Action_value
	StackType_name                                        = src.StackType_name
	StackType_value                                       = src.StackType_value
	StatusCondition_Code_name                             = src.StatusCondition_Code_name
	StatusCondition_Code_value                            = src.StatusCondition_Code_value
	UpgradeResourceType_name                              = src.UpgradeResourceType_name
	UpgradeResourceType_value                             = src.UpgradeResourceType_value
	UsableSubnetworkSecondaryRange_Status_name            = src.UsableSubnetworkSecondaryRange_Status_name
	UsableSubnetworkSecondaryRange_Status_value           = src.UsableSubnetworkSecondaryRange_Status_value
	WorkloadMetadataConfig_Mode_name                      = src.WorkloadMetadataConfig_Mode_name
	WorkloadMetadataConfig_Mode_value                     = src.WorkloadMetadataConfig_Mode_value
)

// AcceleratorConfig represents a Hardware Accelerator request.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type AcceleratorConfig = src.AcceleratorConfig

// Configuration for the addons that can be automatically spun up in the
// cluster, enabling additional functionality.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type AddonsConfig = src.AddonsConfig

// Specifies options for controlling advanced machine features.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type AdvancedMachineFeatures = src.AdvancedMachineFeatures

// Configuration for returning group information from authenticators.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type AuthenticatorGroupsConfig = src.AuthenticatorGroupsConfig

// AutoUpgradeOptions defines the set of options for the user to control how
// the Auto Upgrades will proceed.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type AutoUpgradeOptions = src.AutoUpgradeOptions

// Autopilot is the configuration for Autopilot settings on the cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type Autopilot = src.Autopilot

// AutoprovisioningNodePoolDefaults contains defaults for a node pool created
// by NAP.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type AutoprovisioningNodePoolDefaults = src.AutoprovisioningNodePoolDefaults

// Configuration for Binary Authorization.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type BinaryAuthorization = src.BinaryAuthorization

// Binary Authorization mode of operation.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type BinaryAuthorization_EvaluationMode = src.BinaryAuthorization_EvaluationMode

// Settings for blue-green upgrade.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type BlueGreenSettings = src.BlueGreenSettings

// Standard rollout policy is the default policy for blue-green.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type BlueGreenSettings_StandardRolloutPolicy = src.BlueGreenSettings_StandardRolloutPolicy
type BlueGreenSettings_StandardRolloutPolicy_ = src.BlueGreenSettings_StandardRolloutPolicy_
type BlueGreenSettings_StandardRolloutPolicy_BatchNodeCount = src.BlueGreenSettings_StandardRolloutPolicy_BatchNodeCount
type BlueGreenSettings_StandardRolloutPolicy_BatchPercentage = src.BlueGreenSettings_StandardRolloutPolicy_BatchPercentage

// CancelOperationRequest cancels a single operation.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type CancelOperationRequest = src.CancelOperationRequest

// Configuration for client certificates on the cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ClientCertificateConfig = src.ClientCertificateConfig

// Configuration options for the Cloud Run feature.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type CloudRunConfig = src.CloudRunConfig

// Load balancer type of ingress service of Cloud Run.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type CloudRunConfig_LoadBalancerType = src.CloudRunConfig_LoadBalancerType

// A Google Kubernetes Engine cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type Cluster = src.Cluster

// ClusterAutoscaling contains global, per-cluster information required by
// Cluster Autoscaler to automatically adjust the size of the cluster and
// create/delete node pools based on the current needs.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ClusterAutoscaling = src.ClusterAutoscaling

// Defines possible options for autoscaling_profile field.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ClusterAutoscaling_AutoscalingProfile = src.ClusterAutoscaling_AutoscalingProfile

// ClusterManagerClient is the client API for ClusterManager service. For
// semantics around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ClusterManagerClient = src.ClusterManagerClient

// ClusterManagerServer is the server API for ClusterManager service.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ClusterManagerServer = src.ClusterManagerServer

// ClusterUpdate describes an update to the cluster. Exactly one update can be
// applied to a cluster with each request, so at most one field can be
// provided.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ClusterUpdate = src.ClusterUpdate

// The current status of the cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type Cluster_Status = src.Cluster_Status

// CompleteIPRotationRequest moves the cluster master back into single-IP
// mode.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type CompleteIPRotationRequest = src.CompleteIPRotationRequest

// CompleteNodePoolUpgradeRequest sets the name of target node pool to
// complete upgrade.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type CompleteNodePoolUpgradeRequest = src.CompleteNodePoolUpgradeRequest

// ConfidentialNodes is configuration for the confidential nodes feature,
// which makes nodes run on confidential VMs.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ConfidentialNodes = src.ConfidentialNodes

// Configuration options for the Config Connector add-on.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ConfigConnectorConfig = src.ConfigConnectorConfig

// Configuration for fine-grained cost management feature.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type CostManagementConfig = src.CostManagementConfig

// CreateClusterRequest creates a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type CreateClusterRequest = src.CreateClusterRequest

// CreateNodePoolRequest creates a node pool for a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type CreateNodePoolRequest = src.CreateNodePoolRequest

// DNSConfig contains the desired set of options for configuring clusterDNS.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DNSConfig = src.DNSConfig

// DNSScope lists the various scopes of access to cluster DNS records.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DNSConfig_DNSScope = src.DNSConfig_DNSScope

// Provider lists the various in-cluster DNS providers.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DNSConfig_Provider = src.DNSConfig_Provider

// Time window specified for daily maintenance operations.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DailyMaintenanceWindow = src.DailyMaintenanceWindow

// Configuration of etcd encryption.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DatabaseEncryption = src.DatabaseEncryption

// State of etcd encryption.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DatabaseEncryption_State = src.DatabaseEncryption_State

// The datapath provider selects the implementation of the Kubernetes
// networking model for service resolution and network policy enforcement.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DatapathProvider = src.DatapathProvider

// DefaultSnatStatus contains the desired state of whether default sNAT should
// be disabled on the cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DefaultSnatStatus = src.DefaultSnatStatus

// DeleteClusterRequest deletes a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DeleteClusterRequest = src.DeleteClusterRequest

// DeleteNodePoolRequest deletes a node pool for a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DeleteNodePoolRequest = src.DeleteNodePoolRequest

// Configuration for NodeLocal DNSCache
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type DnsCacheConfig = src.DnsCacheConfig

// GPUSharingConfig represents the GPU sharing configuration for Hardware
// Accelerators.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GPUSharingConfig = src.GPUSharingConfig

// The type of GPU sharing strategy currently provided.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GPUSharingConfig_GPUSharingStrategy = src.GPUSharingConfig_GPUSharingStrategy

// Configuration for the Compute Engine PD CSI driver.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GcePersistentDiskCsiDriverConfig = src.GcePersistentDiskCsiDriverConfig

// GcfsConfig contains configurations of Google Container File System (image
// streaming).
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GcfsConfig = src.GcfsConfig

// Configuration for the GCP Filestore CSI driver.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GcpFilestoreCsiDriverConfig = src.GcpFilestoreCsiDriverConfig

// GetClusterRequest gets the settings of a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GetClusterRequest = src.GetClusterRequest

// GetJSONWebKeysRequest gets the public component of the keys used by the
// cluster to sign token requests. This will be the jwks_uri for the discover
// document returned by getOpenIDConfig. See the OpenID Connect Discovery 1.0
// specification for details.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GetJSONWebKeysRequest = src.GetJSONWebKeysRequest

// GetJSONWebKeysResponse is a valid JSON Web Key Set as specififed in rfc
// 7517
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GetJSONWebKeysResponse = src.GetJSONWebKeysResponse

// GetNodePoolRequest retrieves a node pool for a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GetNodePoolRequest = src.GetNodePoolRequest

// GetOpenIDConfigRequest gets the OIDC discovery document for the cluster.
// See the OpenID Connect Discovery 1.0 specification for details.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GetOpenIDConfigRequest = src.GetOpenIDConfigRequest

// GetOpenIDConfigResponse is an OIDC discovery document for the cluster. See
// the OpenID Connect Discovery 1.0 specification for details.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GetOpenIDConfigResponse = src.GetOpenIDConfigResponse

// GetOperationRequest gets a single operation.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GetOperationRequest = src.GetOperationRequest

// Gets the current Kubernetes Engine service configuration.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GetServerConfigRequest = src.GetServerConfigRequest

// Configuration for the Backup for GKE Agent.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type GkeBackupAgentConfig = src.GkeBackupAgentConfig

// Configuration options for the horizontal pod autoscaling feature, which
// increases or decreases the number of replica pods a replication controller
// has based on the resource usage of the existing pods.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type HorizontalPodAutoscaling = src.HorizontalPodAutoscaling

// Configuration options for the HTTP (L7) load balancing controller addon,
// which makes it easy to set up HTTP load balancers for services in a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type HttpLoadBalancing = src.HttpLoadBalancing

// ILBSubsettingConfig contains the desired config of L4 Internal LoadBalancer
// subsetting on this cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ILBSubsettingConfig = src.ILBSubsettingConfig

// Configuration for controlling how IPs are allocated in the cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type IPAllocationPolicy = src.IPAllocationPolicy

// Possible values for IPv6 access type
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type IPv6AccessType = src.IPv6AccessType

// IdentityServiceConfig is configuration for Identity Service which allows
// customers to use external identity providers with the K8S API
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type IdentityServiceConfig = src.IdentityServiceConfig

// IntraNodeVisibilityConfig contains the desired config of the intra-node
// visibility on this cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type IntraNodeVisibilityConfig = src.IntraNodeVisibilityConfig

// Jwk is a JSON Web Key as specified in RFC 7517
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type Jwk = src.Jwk

// Configuration for the Kubernetes Dashboard.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type KubernetesDashboard = src.KubernetesDashboard

// Configuration for the legacy Attribute Based Access Control authorization
// mode.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type LegacyAbac = src.LegacyAbac

// Parameters that can be configured on Linux nodes.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type LinuxNodeConfig = src.LinuxNodeConfig

// ListClustersRequest lists clusters.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ListClustersRequest = src.ListClustersRequest

// ListClustersResponse is the result of ListClustersRequest.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ListClustersResponse = src.ListClustersResponse

// ListNodePoolsRequest lists the node pool(s) for a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ListNodePoolsRequest = src.ListNodePoolsRequest

// ListNodePoolsResponse is the result of ListNodePoolsRequest.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ListNodePoolsResponse = src.ListNodePoolsResponse

// ListOperationsRequest lists operations.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ListOperationsRequest = src.ListOperationsRequest

// ListOperationsResponse is the result of ListOperationsRequest.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ListOperationsResponse = src.ListOperationsResponse

// ListUsableSubnetworksRequest requests the list of usable subnetworks
// available to a user for creating clusters.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ListUsableSubnetworksRequest = src.ListUsableSubnetworksRequest

// ListUsableSubnetworksResponse is the response of
// ListUsableSubnetworksRequest.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ListUsableSubnetworksResponse = src.ListUsableSubnetworksResponse

// LoggingComponentConfig is cluster logging component configuration.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type LoggingComponentConfig = src.LoggingComponentConfig

// GKE components exposing logs
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type LoggingComponentConfig_Component = src.LoggingComponentConfig_Component

// LoggingConfig is cluster logging configuration.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type LoggingConfig = src.LoggingConfig

// LoggingVariantConfig specifies the behaviour of the logging component.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type LoggingVariantConfig = src.LoggingVariantConfig

// Logging component variants.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type LoggingVariantConfig_Variant = src.LoggingVariantConfig_Variant

// Represents the Maintenance exclusion option.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MaintenanceExclusionOptions = src.MaintenanceExclusionOptions

// Scope of exclusion.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MaintenanceExclusionOptions_Scope = src.MaintenanceExclusionOptions_Scope

// MaintenancePolicy defines the maintenance policy to be used for the
// cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MaintenancePolicy = src.MaintenancePolicy

// MaintenanceWindow defines the maintenance window to be used for the
// cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MaintenanceWindow = src.MaintenanceWindow
type MaintenanceWindow_DailyMaintenanceWindow = src.MaintenanceWindow_DailyMaintenanceWindow
type MaintenanceWindow_RecurringWindow = src.MaintenanceWindow_RecurringWindow

// ManagedPrometheusConfig defines the configuration for Google Cloud Managed
// Service for Prometheus.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ManagedPrometheusConfig = src.ManagedPrometheusConfig

// The authentication information for accessing the master endpoint.
// Authentication can be done using HTTP basic auth or using client
// certificates.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MasterAuth = src.MasterAuth

// Configuration options for the master authorized networks feature. Enabled
// master authorized networks will disallow all external traffic to access
// Kubernetes master through HTTPS except traffic from the given CIDR blocks,
// Google Compute Engine Public IPs and Google Prod IPs.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MasterAuthorizedNetworksConfig = src.MasterAuthorizedNetworksConfig

// CidrBlock contains an optional name and one CIDR block.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MasterAuthorizedNetworksConfig_CidrBlock = src.MasterAuthorizedNetworksConfig_CidrBlock

// Constraints applied to pods.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MaxPodsConstraint = src.MaxPodsConstraint

// Configuration for issuance of mTLS keys and certificates to Kubernetes
// pods.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MeshCertificates = src.MeshCertificates

// MonitoringComponentConfig is cluster monitoring component configuration.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MonitoringComponentConfig = src.MonitoringComponentConfig

// GKE components exposing metrics
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MonitoringComponentConfig_Component = src.MonitoringComponentConfig_Component

// MonitoringConfig is cluster monitoring configuration.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type MonitoringConfig = src.MonitoringConfig

// NetworkConfig reports the relative names of network & subnetwork.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NetworkConfig = src.NetworkConfig

// Configuration options for the NetworkPolicy feature.
// https://kubernetes.io/docs/concepts/services-networking/networkpolicies/
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NetworkPolicy = src.NetworkPolicy

// Configuration for NetworkPolicy. This only tracks whether the addon is
// enabled or not on the Master, it does not track whether network policy is
// enabled for the nodes.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NetworkPolicyConfig = src.NetworkPolicyConfig

// Allowed Network Policy providers.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NetworkPolicy_Provider = src.NetworkPolicy_Provider

// Collection of Compute Engine network tags that can be applied to a node's
// underlying VM instance.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NetworkTags = src.NetworkTags

// Parameters that describe the nodes in a cluster. GKE Autopilot clusters do
// not recognize parameters in `NodeConfig`. Use
// [AutoprovisioningNodePoolDefaults][google.container.v1.AutoprovisioningNodePoolDefaults]
// instead.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeConfig = src.NodeConfig

// Subset of NodeConfig message that has defaults.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeConfigDefaults = src.NodeConfigDefaults

// Node kubelet configs.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeKubeletConfig = src.NodeKubeletConfig

// Collection of node-level [Kubernetes
// labels](https://kubernetes.io/docs/concepts/overview/working-with-objects/labels).
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeLabels = src.NodeLabels

// NodeManagement defines the set of node management services turned on for
// the node pool.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeManagement = src.NodeManagement

// Parameters for node pool-level network config.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeNetworkConfig = src.NodeNetworkConfig

// Configuration of all network bandwidth tiers
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeNetworkConfig_NetworkPerformanceConfig = src.NodeNetworkConfig_NetworkPerformanceConfig

// Node network tier
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeNetworkConfig_NetworkPerformanceConfig_Tier = src.NodeNetworkConfig_NetworkPerformanceConfig_Tier

// NodePool contains the name and configuration for a cluster's node pool.
// Node pools are a set of nodes (i.e. VM's), with a common configuration and
// specification, under the control of the cluster master. They may have a set
// of Kubernetes labels applied to them, which may be used to reference them
// during pod scheduling. They may also be resized up or down, to accommodate
// the workload.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePool = src.NodePool

// Node pool configs that apply to all auto-provisioned node pools in
// autopilot clusters and node auto-provisioning enabled clusters.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePoolAutoConfig = src.NodePoolAutoConfig

// NodePoolAutoscaling contains information required by cluster autoscaler to
// adjust the size of the node pool to the current cluster usage.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePoolAutoscaling = src.NodePoolAutoscaling

// Location policy specifies how zones are picked when scaling up the
// nodepool.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePoolAutoscaling_LocationPolicy = src.NodePoolAutoscaling_LocationPolicy

// Subset of Nodepool message that has defaults.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePoolDefaults = src.NodePoolDefaults

// NodePoolLoggingConfig specifies logging configuration for nodepools.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePoolLoggingConfig = src.NodePoolLoggingConfig

// Strategy used for node pool update.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePoolUpdateStrategy = src.NodePoolUpdateStrategy

// The current status of the node pool instance.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePool_Status = src.NodePool_Status

// UpdateInfo contains resource (instance groups, etc), status and other
// intermediate information relevant to a node pool upgrade.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePool_UpdateInfo = src.NodePool_UpdateInfo

// Information relevant to blue-green upgrade.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePool_UpdateInfo_BlueGreenInfo = src.NodePool_UpdateInfo_BlueGreenInfo

// Phase represents the different stages blue-green upgrade is running in.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePool_UpdateInfo_BlueGreenInfo_Phase = src.NodePool_UpdateInfo_BlueGreenInfo_Phase

// These upgrade settings control the level of parallelism and the level of
// disruption caused by an upgrade. maxUnavailable controls the number of nodes
// that can be simultaneously unavailable. maxSurge controls the number of
// additional nodes that can be added to the node pool temporarily for the time
// of the upgrade to increase the number of available nodes. (maxUnavailable +
// maxSurge) determines the level of parallelism (how many nodes are being
// upgraded at the same time). Note: upgrades inevitably introduce some
// disruption since workloads need to be moved from old nodes to new, upgraded
// ones. Even if maxUnavailable=0, this holds true. (Disruption stays within
// the limits of PodDisruptionBudget, if it is configured.) Consider a
// hypothetical node pool with 5 nodes having maxSurge=2, maxUnavailable=1.
// This means the upgrade process upgrades 3 nodes simultaneously. It creates 2
// additional (upgraded) nodes, then it brings down 3 old (not yet upgraded)
// nodes at the same time. This ensures that there are always at least 4 nodes
// available. These upgrade settings configure the upgrade strategy for the
// node pool. Use strategy to switch between the strategies applied to the node
// pool. If the strategy is ROLLING, use max_surge and max_unavailable to
// control the level of parallelism and the level of disruption caused by
// upgrade. 1. maxSurge controls the number of additional nodes that can be
// added to the node pool temporarily for the time of the upgrade to increase
// the number of available nodes. 2. maxUnavailable controls the number of
// nodes that can be simultaneously unavailable. 3. (maxUnavailable + maxSurge)
// determines the level of parallelism (how many nodes are being upgraded at
// the same time). If the strategy is BLUE_GREEN, use blue_green_settings to
// configure the blue-green upgrade related settings. 1.
// standard_rollout_policy is the default policy. The policy is used to control
// the way blue pool gets drained. The draining is executed in the batch mode.
// The batch size could be specified as either percentage of the node pool size
// or the number of nodes. batch_soak_duration is the soak time after each
// batch gets drained. 2. node_pool_soak_duration is the soak time after all
// blue nodes are drained. After this period, the blue pool nodes will be
// deleted.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodePool_UpgradeSettings = src.NodePool_UpgradeSettings

// Kubernetes taint is comprised of three fields: key, value, and effect.
// Effect can only be one of three types: NoSchedule, PreferNoSchedule or
// NoExecute. See
// [here](https://kubernetes.io/docs/concepts/configuration/taint-and-toleration)
// for more information, including usage and the valid values.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeTaint = src.NodeTaint

// Possible values for Effect in taint.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeTaint_Effect = src.NodeTaint_Effect

// Collection of Kubernetes [node
// taints](https://kubernetes.io/docs/concepts/configuration/taint-and-toleration).
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NodeTaints = src.NodeTaints

// NotificationConfig is the configuration of notifications.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NotificationConfig = src.NotificationConfig

// Types of notifications currently supported. Can be used to filter what
// notifications are sent.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NotificationConfig_EventType = src.NotificationConfig_EventType

// Allows filtering to one or more specific event types. If event types are
// present, those and only those event types will be transmitted to the
// cluster. Other types will be skipped. If no filter is specified, or no event
// types are present, all event types will be sent
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NotificationConfig_Filter = src.NotificationConfig_Filter

// Pub/Sub specific notification config.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type NotificationConfig_PubSub = src.NotificationConfig_PubSub

// This operation resource represents operations that may have happened or are
// happening on the cluster. All fields are output only.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type Operation = src.Operation

// Information about operation (or operation stage) progress.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type OperationProgress = src.OperationProgress

// Progress metric is (string, int|float|string) pair.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type OperationProgress_Metric = src.OperationProgress_Metric
type OperationProgress_Metric_DoubleValue = src.OperationProgress_Metric_DoubleValue
type OperationProgress_Metric_IntValue = src.OperationProgress_Metric_IntValue
type OperationProgress_Metric_StringValue = src.OperationProgress_Metric_StringValue

// Current status of the operation.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type Operation_Status = src.Operation_Status

// Operation type.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type Operation_Type = src.Operation_Type

// Configuration options for private clusters.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type PrivateClusterConfig = src.PrivateClusterConfig

// Configuration for controlling master global access settings.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type PrivateClusterMasterGlobalAccessConfig = src.PrivateClusterMasterGlobalAccessConfig

// PrivateIPv6GoogleAccess controls whether and how the pods can communicate
// with Google Services through gRPC over IPv6.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type PrivateIPv6GoogleAccess = src.PrivateIPv6GoogleAccess

// Represents an arbitrary window of time that recurs.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type RecurringTimeWindow = src.RecurringTimeWindow

// ReleaseChannel indicates which release channel a cluster is subscribed to.
// Release channels are arranged in order of risk. When a cluster is subscribed
// to a release channel, Google maintains both the master version and the node
// version. Node auto-upgrade defaults to true and cannot be disabled.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ReleaseChannel = src.ReleaseChannel

// Possible values for 'channel'.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ReleaseChannel_Channel = src.ReleaseChannel_Channel

// [ReservationAffinity](https://cloud.google.com/compute/docs/instances/reserving-zonal-resources)
// is the configuration of desired reservation which instances could take
// capacity from.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ReservationAffinity = src.ReservationAffinity

// Indicates whether to consume capacity from a reservation or not.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ReservationAffinity_Type = src.ReservationAffinity_Type

// Contains information about amount of some resource in the cluster. For
// memory, value should be in GB.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ResourceLimit = src.ResourceLimit

// Configuration for exporting cluster resource usages.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ResourceUsageExportConfig = src.ResourceUsageExportConfig

// Parameters for using BigQuery as the destination of resource usage export.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ResourceUsageExportConfig_BigQueryDestination = src.ResourceUsageExportConfig_BigQueryDestination

// Parameters for controlling consumption metering.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ResourceUsageExportConfig_ConsumptionMeteringConfig = src.ResourceUsageExportConfig_ConsumptionMeteringConfig

// RollbackNodePoolUpgradeRequest rollbacks the previously Aborted or Failed
// NodePool upgrade. This will be an no-op if the last upgrade successfully
// completed.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type RollbackNodePoolUpgradeRequest = src.RollbackNodePoolUpgradeRequest

// SandboxConfig contains configurations of the sandbox to use for the node.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SandboxConfig = src.SandboxConfig

// Possible types of sandboxes.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SandboxConfig_Type = src.SandboxConfig_Type

// SecurityBulletinEvent is a notification sent to customers when a security
// bulletin has been posted that they are vulnerable to.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SecurityBulletinEvent = src.SecurityBulletinEvent

// Kubernetes Engine service configuration.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ServerConfig = src.ServerConfig

// ReleaseChannelConfig exposes configuration for a release channel.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ServerConfig_ReleaseChannelConfig = src.ServerConfig_ReleaseChannelConfig

// Config to block services with externalIPs field.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ServiceExternalIPsConfig = src.ServiceExternalIPsConfig

// SetAddonsConfigRequest sets the addons associated with the cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetAddonsConfigRequest = src.SetAddonsConfigRequest

// SetLabelsRequest sets the Google Cloud Platform labels on a Google
// Container Engine cluster, which will in turn set them for Google Compute
// Engine resources used by that cluster
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetLabelsRequest = src.SetLabelsRequest

// SetLegacyAbacRequest enables or disables the ABAC authorization mechanism
// for a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetLegacyAbacRequest = src.SetLegacyAbacRequest

// SetLocationsRequest sets the locations of the cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetLocationsRequest = src.SetLocationsRequest

// SetLoggingServiceRequest sets the logging service of a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetLoggingServiceRequest = src.SetLoggingServiceRequest

// SetMaintenancePolicyRequest sets the maintenance policy for a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetMaintenancePolicyRequest = src.SetMaintenancePolicyRequest

// SetMasterAuthRequest updates the admin password of a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetMasterAuthRequest = src.SetMasterAuthRequest

// Operation type: what type update to perform.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetMasterAuthRequest_Action = src.SetMasterAuthRequest_Action

// SetMonitoringServiceRequest sets the monitoring service of a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetMonitoringServiceRequest = src.SetMonitoringServiceRequest

// SetNetworkPolicyRequest enables/disables network policy for a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetNetworkPolicyRequest = src.SetNetworkPolicyRequest

// SetNodePoolAutoscalingRequest sets the autoscaler settings of a node pool.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetNodePoolAutoscalingRequest = src.SetNodePoolAutoscalingRequest

// SetNodePoolManagementRequest sets the node management properties of a node
// pool.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetNodePoolManagementRequest = src.SetNodePoolManagementRequest

// SetNodePoolSizeRequest sets the size of a node pool.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type SetNodePoolSizeRequest = src.SetNodePoolSizeRequest

// A set of Shielded Instance options.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ShieldedInstanceConfig = src.ShieldedInstanceConfig

// Configuration of Shielded Nodes feature.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type ShieldedNodes = src.ShieldedNodes

// Possible values for IP stack type
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type StackType = src.StackType

// StartIPRotationRequest creates a new IP for the cluster and then performs a
// node upgrade on each node pool to point to the new IP.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type StartIPRotationRequest = src.StartIPRotationRequest

// StatusCondition describes why a cluster or a node pool has a certain status
// (e.g., ERROR or DEGRADED).
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type StatusCondition = src.StatusCondition

// Code for each condition
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type StatusCondition_Code = src.StatusCondition_Code

// Represents an arbitrary window of time.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type TimeWindow = src.TimeWindow
type TimeWindow_MaintenanceExclusionOptions = src.TimeWindow_MaintenanceExclusionOptions

// UnimplementedClusterManagerServer can be embedded to have forward
// compatible implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type UnimplementedClusterManagerServer = src.UnimplementedClusterManagerServer

// UpdateClusterRequest updates the settings of a cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type UpdateClusterRequest = src.UpdateClusterRequest

// UpdateMasterRequest updates the master of the cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type UpdateMasterRequest = src.UpdateMasterRequest

// UpdateNodePoolRequests update a node pool's image and/or version.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type UpdateNodePoolRequest = src.UpdateNodePoolRequest

// UpgradeAvailableEvent is a notification sent to customers when a new
// available version is released.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type UpgradeAvailableEvent = src.UpgradeAvailableEvent

// UpgradeEvent is a notification sent to customers by the cluster server when
// a resource is upgrading.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type UpgradeEvent = src.UpgradeEvent

// UpgradeResourceType is the resource type that is upgrading. It is used in
// upgrade notifications.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type UpgradeResourceType = src.UpgradeResourceType

// UsableSubnetwork resource returns the subnetwork name, its associated
// network and the primary CIDR range.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type UsableSubnetwork = src.UsableSubnetwork

// Secondary IP range of a usable subnetwork.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type UsableSubnetworkSecondaryRange = src.UsableSubnetworkSecondaryRange

// Status shows the current usage of a secondary IP range.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type UsableSubnetworkSecondaryRange_Status = src.UsableSubnetworkSecondaryRange_Status

// VerticalPodAutoscaling contains global, per-cluster information required by
// Vertical Pod Autoscaler to automatically adjust the resources of pods
// controlled by it.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type VerticalPodAutoscaling = src.VerticalPodAutoscaling

// Configuration of gVNIC feature.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type VirtualNIC = src.VirtualNIC

// Configuration for the use of Kubernetes Service Accounts in GCP IAM
// policies.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type WorkloadIdentityConfig = src.WorkloadIdentityConfig

// WorkloadMetadataConfig defines the metadata configuration to expose to
// workloads on the node pool.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type WorkloadMetadataConfig = src.WorkloadMetadataConfig

// Mode is the configuration for how to expose metadata to workloads running
// on the node.
//
// Deprecated: Please use types in: cloud.google.com/go/container/apiv1/containerpb
type WorkloadMetadataConfig_Mode = src.WorkloadMetadataConfig_Mode

// Deprecated: Please use funcs in: cloud.google.com/go/container/apiv1/containerpb
func NewClusterManagerClient(cc grpc.ClientConnInterface) ClusterManagerClient {
	return src.NewClusterManagerClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/container/apiv1/containerpb
func RegisterClusterManagerServer(s *grpc.Server, srv ClusterManagerServer) {
	src.RegisterClusterManagerServer(s, srv)
}
