// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by aliasgen. DO NOT EDIT.

// Package monitoring aliases all exported identifiers in package
// "cloud.google.com/go/monitoring/apiv3/v2/monitoringpb".
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb.
// Please read https://github.com/googleapis/google-cloud-go/blob/main/migration.md
// for more details.
package monitoring

import (
	src "cloud.google.com/go/monitoring/apiv3/v2/monitoringpb"
	grpc "google.golang.org/grpc"
)

// Deprecated: Please use consts in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
const (
	Aggregation_ALIGN_COUNT                                             = src.Aggregation_ALIGN_COUNT
	Aggregation_ALIGN_COUNT_FALSE                                       = src.Aggregation_ALIGN_COUNT_FALSE
	Aggregation_ALIGN_COUNT_TRUE                                        = src.Aggregation_ALIGN_COUNT_TRUE
	Aggregation_ALIGN_DELTA                                             = src.Aggregation_ALIGN_DELTA
	Aggregation_ALIGN_FRACTION_TRUE                                     = src.Aggregation_ALIGN_FRACTION_TRUE
	Aggregation_ALIGN_INTERPOLATE                                       = src.Aggregation_ALIGN_INTERPOLATE
	Aggregation_ALIGN_MAX                                               = src.Aggregation_ALIGN_MAX
	Aggregation_ALIGN_MEAN                                              = src.Aggregation_ALIGN_MEAN
	Aggregation_ALIGN_MIN                                               = src.Aggregation_ALIGN_MIN
	Aggregation_ALIGN_NEXT_OLDER                                        = src.Aggregation_ALIGN_NEXT_OLDER
	Aggregation_ALIGN_NONE                                              = src.Aggregation_ALIGN_NONE
	Aggregation_ALIGN_PERCENTILE_05                                     = src.Aggregation_ALIGN_PERCENTILE_05
	Aggregation_ALIGN_PERCENTILE_50                                     = src.Aggregation_ALIGN_PERCENTILE_50
	Aggregation_ALIGN_PERCENTILE_95                                     = src.Aggregation_ALIGN_PERCENTILE_95
	Aggregation_ALIGN_PERCENTILE_99                                     = src.Aggregation_ALIGN_PERCENTILE_99
	Aggregation_ALIGN_PERCENT_CHANGE                                    = src.Aggregation_ALIGN_PERCENT_CHANGE
	Aggregation_ALIGN_RATE                                              = src.Aggregation_ALIGN_RATE
	Aggregation_ALIGN_STDDEV                                            = src.Aggregation_ALIGN_STDDEV
	Aggregation_ALIGN_SUM                                               = src.Aggregation_ALIGN_SUM
	Aggregation_REDUCE_COUNT                                            = src.Aggregation_REDUCE_COUNT
	Aggregation_REDUCE_COUNT_FALSE                                      = src.Aggregation_REDUCE_COUNT_FALSE
	Aggregation_REDUCE_COUNT_TRUE                                       = src.Aggregation_REDUCE_COUNT_TRUE
	Aggregation_REDUCE_FRACTION_TRUE                                    = src.Aggregation_REDUCE_FRACTION_TRUE
	Aggregation_REDUCE_MAX                                              = src.Aggregation_REDUCE_MAX
	Aggregation_REDUCE_MEAN                                             = src.Aggregation_REDUCE_MEAN
	Aggregation_REDUCE_MIN                                              = src.Aggregation_REDUCE_MIN
	Aggregation_REDUCE_NONE                                             = src.Aggregation_REDUCE_NONE
	Aggregation_REDUCE_PERCENTILE_05                                    = src.Aggregation_REDUCE_PERCENTILE_05
	Aggregation_REDUCE_PERCENTILE_50                                    = src.Aggregation_REDUCE_PERCENTILE_50
	Aggregation_REDUCE_PERCENTILE_95                                    = src.Aggregation_REDUCE_PERCENTILE_95
	Aggregation_REDUCE_PERCENTILE_99                                    = src.Aggregation_REDUCE_PERCENTILE_99
	Aggregation_REDUCE_STDDEV                                           = src.Aggregation_REDUCE_STDDEV
	Aggregation_REDUCE_SUM                                              = src.Aggregation_REDUCE_SUM
	AlertPolicy_AND                                                     = src.AlertPolicy_AND
	AlertPolicy_AND_WITH_MATCHING_RESOURCE                              = src.AlertPolicy_AND_WITH_MATCHING_RESOURCE
	AlertPolicy_COMBINE_UNSPECIFIED                                     = src.AlertPolicy_COMBINE_UNSPECIFIED
	AlertPolicy_Condition_EVALUATION_MISSING_DATA_ACTIVE                = src.AlertPolicy_Condition_EVALUATION_MISSING_DATA_ACTIVE
	AlertPolicy_Condition_EVALUATION_MISSING_DATA_INACTIVE              = src.AlertPolicy_Condition_EVALUATION_MISSING_DATA_INACTIVE
	AlertPolicy_Condition_EVALUATION_MISSING_DATA_NO_OP                 = src.AlertPolicy_Condition_EVALUATION_MISSING_DATA_NO_OP
	AlertPolicy_Condition_EVALUATION_MISSING_DATA_UNSPECIFIED           = src.AlertPolicy_Condition_EVALUATION_MISSING_DATA_UNSPECIFIED
	AlertPolicy_OR                                                      = src.AlertPolicy_OR
	ComparisonType_COMPARISON_EQ                                        = src.ComparisonType_COMPARISON_EQ
	ComparisonType_COMPARISON_GE                                        = src.ComparisonType_COMPARISON_GE
	ComparisonType_COMPARISON_GT                                        = src.ComparisonType_COMPARISON_GT
	ComparisonType_COMPARISON_LE                                        = src.ComparisonType_COMPARISON_LE
	ComparisonType_COMPARISON_LT                                        = src.ComparisonType_COMPARISON_LT
	ComparisonType_COMPARISON_NE                                        = src.ComparisonType_COMPARISON_NE
	ComparisonType_COMPARISON_UNSPECIFIED                               = src.ComparisonType_COMPARISON_UNSPECIFIED
	GroupResourceType_AWS_ELB_LOAD_BALANCER                             = src.GroupResourceType_AWS_ELB_LOAD_BALANCER
	GroupResourceType_INSTANCE                                          = src.GroupResourceType_INSTANCE
	GroupResourceType_RESOURCE_TYPE_UNSPECIFIED                         = src.GroupResourceType_RESOURCE_TYPE_UNSPECIFIED
	InternalChecker_CREATING                                            = src.InternalChecker_CREATING
	InternalChecker_RUNNING                                             = src.InternalChecker_RUNNING
	InternalChecker_UNSPECIFIED                                         = src.InternalChecker_UNSPECIFIED
	ListTimeSeriesRequest_FULL                                          = src.ListTimeSeriesRequest_FULL
	ListTimeSeriesRequest_HEADERS                                       = src.ListTimeSeriesRequest_HEADERS
	NotificationChannel_UNVERIFIED                                      = src.NotificationChannel_UNVERIFIED
	NotificationChannel_VERIFICATION_STATUS_UNSPECIFIED                 = src.NotificationChannel_VERIFICATION_STATUS_UNSPECIFIED
	NotificationChannel_VERIFIED                                        = src.NotificationChannel_VERIFIED
	ServiceLevelObjective_EXPLICIT                                      = src.ServiceLevelObjective_EXPLICIT
	ServiceLevelObjective_FULL                                          = src.ServiceLevelObjective_FULL
	ServiceLevelObjective_VIEW_UNSPECIFIED                              = src.ServiceLevelObjective_VIEW_UNSPECIFIED
	ServiceTier_SERVICE_TIER_BASIC                                      = src.ServiceTier_SERVICE_TIER_BASIC
	ServiceTier_SERVICE_TIER_PREMIUM                                    = src.ServiceTier_SERVICE_TIER_PREMIUM
	ServiceTier_SERVICE_TIER_UNSPECIFIED                                = src.ServiceTier_SERVICE_TIER_UNSPECIFIED
	UptimeCheckConfig_ContentMatcher_CONTAINS_STRING                    = src.UptimeCheckConfig_ContentMatcher_CONTAINS_STRING
	UptimeCheckConfig_ContentMatcher_CONTENT_MATCHER_OPTION_UNSPECIFIED = src.UptimeCheckConfig_ContentMatcher_CONTENT_MATCHER_OPTION_UNSPECIFIED
	UptimeCheckConfig_ContentMatcher_MATCHES_REGEX                      = src.UptimeCheckConfig_ContentMatcher_MATCHES_REGEX
	UptimeCheckConfig_ContentMatcher_NOT_CONTAINS_STRING                = src.UptimeCheckConfig_ContentMatcher_NOT_CONTAINS_STRING
	UptimeCheckConfig_ContentMatcher_NOT_MATCHES_REGEX                  = src.UptimeCheckConfig_ContentMatcher_NOT_MATCHES_REGEX
	UptimeCheckConfig_HttpCheck_GET                                     = src.UptimeCheckConfig_HttpCheck_GET
	UptimeCheckConfig_HttpCheck_METHOD_UNSPECIFIED                      = src.UptimeCheckConfig_HttpCheck_METHOD_UNSPECIFIED
	UptimeCheckConfig_HttpCheck_POST                                    = src.UptimeCheckConfig_HttpCheck_POST
	UptimeCheckConfig_HttpCheck_TYPE_UNSPECIFIED                        = src.UptimeCheckConfig_HttpCheck_TYPE_UNSPECIFIED
	UptimeCheckConfig_HttpCheck_URL_ENCODED                             = src.UptimeCheckConfig_HttpCheck_URL_ENCODED
	UptimeCheckRegion_ASIA_PACIFIC                                      = src.UptimeCheckRegion_ASIA_PACIFIC
	UptimeCheckRegion_EUROPE                                            = src.UptimeCheckRegion_EUROPE
	UptimeCheckRegion_REGION_UNSPECIFIED                                = src.UptimeCheckRegion_REGION_UNSPECIFIED
	UptimeCheckRegion_SOUTH_AMERICA                                     = src.UptimeCheckRegion_SOUTH_AMERICA
	UptimeCheckRegion_USA                                               = src.UptimeCheckRegion_USA
)

// Deprecated: Please use vars in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
var (
	Aggregation_Aligner_name                                    = src.Aggregation_Aligner_name
	Aggregation_Aligner_value                                   = src.Aggregation_Aligner_value
	Aggregation_Reducer_name                                    = src.Aggregation_Reducer_name
	Aggregation_Reducer_value                                   = src.Aggregation_Reducer_value
	AlertPolicy_ConditionCombinerType_name                      = src.AlertPolicy_ConditionCombinerType_name
	AlertPolicy_ConditionCombinerType_value                     = src.AlertPolicy_ConditionCombinerType_value
	AlertPolicy_Condition_EvaluationMissingData_name            = src.AlertPolicy_Condition_EvaluationMissingData_name
	AlertPolicy_Condition_EvaluationMissingData_value           = src.AlertPolicy_Condition_EvaluationMissingData_value
	ComparisonType_name                                         = src.ComparisonType_name
	ComparisonType_value                                        = src.ComparisonType_value
	File_google_monitoring_v3_alert_proto                       = src.File_google_monitoring_v3_alert_proto
	File_google_monitoring_v3_alert_service_proto               = src.File_google_monitoring_v3_alert_service_proto
	File_google_monitoring_v3_common_proto                      = src.File_google_monitoring_v3_common_proto
	File_google_monitoring_v3_dropped_labels_proto              = src.File_google_monitoring_v3_dropped_labels_proto
	File_google_monitoring_v3_group_proto                       = src.File_google_monitoring_v3_group_proto
	File_google_monitoring_v3_group_service_proto               = src.File_google_monitoring_v3_group_service_proto
	File_google_monitoring_v3_metric_proto                      = src.File_google_monitoring_v3_metric_proto
	File_google_monitoring_v3_metric_service_proto              = src.File_google_monitoring_v3_metric_service_proto
	File_google_monitoring_v3_mutation_record_proto             = src.File_google_monitoring_v3_mutation_record_proto
	File_google_monitoring_v3_notification_proto                = src.File_google_monitoring_v3_notification_proto
	File_google_monitoring_v3_notification_service_proto        = src.File_google_monitoring_v3_notification_service_proto
	File_google_monitoring_v3_query_service_proto               = src.File_google_monitoring_v3_query_service_proto
	File_google_monitoring_v3_service_proto                     = src.File_google_monitoring_v3_service_proto
	File_google_monitoring_v3_service_service_proto             = src.File_google_monitoring_v3_service_service_proto
	File_google_monitoring_v3_span_context_proto                = src.File_google_monitoring_v3_span_context_proto
	File_google_monitoring_v3_uptime_proto                      = src.File_google_monitoring_v3_uptime_proto
	File_google_monitoring_v3_uptime_service_proto              = src.File_google_monitoring_v3_uptime_service_proto
	GroupResourceType_name                                      = src.GroupResourceType_name
	GroupResourceType_value                                     = src.GroupResourceType_value
	InternalChecker_State_name                                  = src.InternalChecker_State_name
	InternalChecker_State_value                                 = src.InternalChecker_State_value
	ListTimeSeriesRequest_TimeSeriesView_name                   = src.ListTimeSeriesRequest_TimeSeriesView_name
	ListTimeSeriesRequest_TimeSeriesView_value                  = src.ListTimeSeriesRequest_TimeSeriesView_value
	NotificationChannel_VerificationStatus_name                 = src.NotificationChannel_VerificationStatus_name
	NotificationChannel_VerificationStatus_value                = src.NotificationChannel_VerificationStatus_value
	ServiceLevelObjective_View_name                             = src.ServiceLevelObjective_View_name
	ServiceLevelObjective_View_value                            = src.ServiceLevelObjective_View_value
	ServiceTier_name                                            = src.ServiceTier_name
	ServiceTier_value                                           = src.ServiceTier_value
	UptimeCheckConfig_ContentMatcher_ContentMatcherOption_name  = src.UptimeCheckConfig_ContentMatcher_ContentMatcherOption_name
	UptimeCheckConfig_ContentMatcher_ContentMatcherOption_value = src.UptimeCheckConfig_ContentMatcher_ContentMatcherOption_value
	UptimeCheckConfig_HttpCheck_ContentType_name                = src.UptimeCheckConfig_HttpCheck_ContentType_name
	UptimeCheckConfig_HttpCheck_ContentType_value               = src.UptimeCheckConfig_HttpCheck_ContentType_value
	UptimeCheckConfig_HttpCheck_RequestMethod_name              = src.UptimeCheckConfig_HttpCheck_RequestMethod_name
	UptimeCheckConfig_HttpCheck_RequestMethod_value             = src.UptimeCheckConfig_HttpCheck_RequestMethod_value
	UptimeCheckRegion_name                                      = src.UptimeCheckRegion_name
	UptimeCheckRegion_value                                     = src.UptimeCheckRegion_value
)

// Describes how to combine multiple time series to provide a different view
// of the data. Aggregation of time series is done in two steps. First, each
// time series in the set is _aligned_ to the same time interval boundaries,
// then the set of time series is optionally _reduced_ in number. Alignment
// consists of applying the `per_series_aligner` operation to each time series
// after its data has been divided into regular `alignment_period` time
// intervals. This process takes _all_ of the data points in an alignment
// period, applies a mathematical transformation such as averaging, minimum,
// maximum, delta, etc., and converts them into a single data point per period.
// Reduction is when the aligned and transformed time series can optionally be
// combined, reducing the number of time series through similar mathematical
// transformations. Reduction involves applying a `cross_series_reducer` to all
// the time series, optionally sorting the time series into subsets with
// `group_by_fields`, and applying the reducer to each subset. The raw time
// series data can contain a huge amount of information from multiple sources.
// Alignment and reduction transforms this mass of data into a more manageable
// and representative collection of data, for example "the 95% latency across
// the average of all tasks in a cluster". This representative data can be more
// easily graphed and comprehended, and the individual time series data is
// still available for later drilldown. For more details, see [Filtering and
// aggregation](https://cloud.google.com/monitoring/api/v3/aggregation).
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Aggregation = src.Aggregation

// The `Aligner` specifies the operation that will be applied to the data
// points in each alignment period in a time series. Except for `ALIGN_NONE`,
// which specifies that no operation be applied, each alignment operation
// replaces the set of data values in each alignment period with a single
// value: the result of applying the operation to the data values. An aligned
// time series has a single data value at the end of each `alignment_period`.
// An alignment operation can change the data type of the values, too. For
// example, if you apply a counting operation to boolean values, the data
// `value_type` in the original time series is `BOOLEAN`, but the `value_type`
// in the aligned result is `INT64`.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Aggregation_Aligner = src.Aggregation_Aligner

// A Reducer operation describes how to aggregate data points from multiple
// time series into a single time series, where the value of each data point in
// the resulting series is a function of all the already aligned values in the
// input time series.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Aggregation_Reducer = src.Aggregation_Reducer

// A description of the conditions under which some aspect of your system is
// considered to be "unhealthy" and the ways to notify people or services about
// this state. For an overview of alert policies, see [Introduction to
// Alerting](https://cloud.google.com/monitoring/alerts/).
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy = src.AlertPolicy

// AlertPolicyServiceClient is the client API for AlertPolicyService service.
// For semantics around ctx use and closing/ending streaming RPCs, please refer
// to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicyServiceClient = src.AlertPolicyServiceClient

// AlertPolicyServiceServer is the server API for AlertPolicyService service.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicyServiceServer = src.AlertPolicyServiceServer

// Control over how the notification channels in `notification_channels` are
// notified when this alert fires.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_AlertStrategy = src.AlertPolicy_AlertStrategy

// Control over the rate of notifications sent to this alert policy's
// notification channels.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_AlertStrategy_NotificationRateLimit = src.AlertPolicy_AlertStrategy_NotificationRateLimit

// A condition is a true/false test that determines when an alerting policy
// should open an incident. If a condition evaluates to true, it signifies that
// something is wrong.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_Condition = src.AlertPolicy_Condition

// Operators for combining conditions.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_ConditionCombinerType = src.AlertPolicy_ConditionCombinerType
type AlertPolicy_Condition_ConditionAbsent = src.AlertPolicy_Condition_ConditionAbsent
type AlertPolicy_Condition_ConditionMatchedLog = src.AlertPolicy_Condition_ConditionMatchedLog
type AlertPolicy_Condition_ConditionMonitoringQueryLanguage = src.AlertPolicy_Condition_ConditionMonitoringQueryLanguage
type AlertPolicy_Condition_ConditionThreshold = src.AlertPolicy_Condition_ConditionThreshold

// A condition control that determines how metric-threshold conditions are
// evaluated when data stops arriving. This control doesn't affect
// metric-absence policies.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_Condition_EvaluationMissingData = src.AlertPolicy_Condition_EvaluationMissingData

// A condition type that checks whether a log message in the [scoping
// project](https://cloud.google.com/monitoring/api/v3#project_name) satisfies
// the given filter. Logs from other projects in the metrics scope are not
// evaluated.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_Condition_LogMatch = src.AlertPolicy_Condition_LogMatch

// A condition type that checks that monitored resources are reporting data.
// The configuration defines a metric and a set of monitored resources. The
// predicate is considered in violation when a time series for the specified
// metric of a monitored resource does not include any data in the specified
// `duration`.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_Condition_MetricAbsence = src.AlertPolicy_Condition_MetricAbsence

// A condition type that compares a collection of time series against a
// threshold.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_Condition_MetricThreshold = src.AlertPolicy_Condition_MetricThreshold

// A condition type that allows alert policies to be defined using [Monitoring
// Query Language](https://cloud.google.com/monitoring/mql).
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_Condition_MonitoringQueryLanguageCondition = src.AlertPolicy_Condition_MonitoringQueryLanguageCondition

// Specifies how many time series must fail a predicate to trigger a
// condition. If not specified, then a `{count: 1}` trigger is used.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_Condition_Trigger = src.AlertPolicy_Condition_Trigger
type AlertPolicy_Condition_Trigger_Count = src.AlertPolicy_Condition_Trigger_Count
type AlertPolicy_Condition_Trigger_Percent = src.AlertPolicy_Condition_Trigger_Percent

// A content string and a MIME type that describes the content string's
// format.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type AlertPolicy_Documentation = src.AlertPolicy_Documentation

// An SLI measuring performance on a well-known service type. Performance will
// be computed on the basis of pre-defined metrics. The type of the
// `service_resource` determines the metrics to use and the
// `service_resource.labels` and `metric_labels` are used to construct a
// monitoring filter to filter that metric down to just the data relevant to
// this service.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type BasicSli = src.BasicSli
type BasicSli_Availability = src.BasicSli_Availability

// Future parameters for the availability SLI.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type BasicSli_AvailabilityCriteria = src.BasicSli_AvailabilityCriteria
type BasicSli_Latency = src.BasicSli_Latency

// Parameters for a latency threshold SLI.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type BasicSli_LatencyCriteria = src.BasicSli_LatencyCriteria

// Specifies an ordering relationship on two arguments, called `left` and
// `right`.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ComparisonType = src.ComparisonType

// The protocol for the `CreateAlertPolicy` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateAlertPolicyRequest = src.CreateAlertPolicyRequest

// The `CreateGroup` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateGroupRequest = src.CreateGroupRequest

// The `CreateMetricDescriptor` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateMetricDescriptorRequest = src.CreateMetricDescriptorRequest

// The `CreateNotificationChannel` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateNotificationChannelRequest = src.CreateNotificationChannelRequest

// The `CreateServiceLevelObjective` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateServiceLevelObjectiveRequest = src.CreateServiceLevelObjectiveRequest

// The `CreateService` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateServiceRequest = src.CreateServiceRequest

// DEPRECATED. Used to hold per-time-series error status.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateTimeSeriesError = src.CreateTimeSeriesError

// The `CreateTimeSeries` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateTimeSeriesRequest = src.CreateTimeSeriesRequest

// Summary of the result of a failed request to write data to a time series.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateTimeSeriesSummary = src.CreateTimeSeriesSummary

// Detailed information about an error category.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateTimeSeriesSummary_Error = src.CreateTimeSeriesSummary_Error

// The protocol for the `CreateUptimeCheckConfig` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type CreateUptimeCheckConfigRequest = src.CreateUptimeCheckConfigRequest

// The protocol for the `DeleteAlertPolicy` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type DeleteAlertPolicyRequest = src.DeleteAlertPolicyRequest

// The `DeleteGroup` request. The default behavior is to be able to delete a
// single group without any descendants.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type DeleteGroupRequest = src.DeleteGroupRequest

// The `DeleteMetricDescriptor` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type DeleteMetricDescriptorRequest = src.DeleteMetricDescriptorRequest

// The `DeleteNotificationChannel` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type DeleteNotificationChannelRequest = src.DeleteNotificationChannelRequest

// The `DeleteServiceLevelObjective` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type DeleteServiceLevelObjectiveRequest = src.DeleteServiceLevelObjectiveRequest

// The `DeleteService` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type DeleteServiceRequest = src.DeleteServiceRequest

// The protocol for the `DeleteUptimeCheckConfig` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type DeleteUptimeCheckConfigRequest = src.DeleteUptimeCheckConfigRequest

// A `DistributionCut` defines a `TimeSeries` and thresholds used for
// measuring good service and total service. The `TimeSeries` must have
// `ValueType = DISTRIBUTION` and `MetricKind = DELTA` or `MetricKind =
// CUMULATIVE`. The computed `good_service` will be the estimated count of
// values in the `Distribution` that fall within the specified `min` and `max`.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type DistributionCut = src.DistributionCut

// A set of (label, value) pairs that were removed from a Distribution time
// series during aggregation and then added as an attachment to a
// Distribution.Exemplar. The full label set for the exemplars is constructed
// by using the dropped pairs in combination with the label values that remain
// on the aggregated Distribution time series. The constructed full label set
// can be used to identify the specific entity, such as the instance or job,
// which might be contributing to a long-tail. However, with dropped labels,
// the storage requirements are reduced because only the aggregated
// distribution values for a large group of time series are stored. Note that
// there are no guarantees on ordering of the labels from exemplar-to-exemplar
// and from distribution-to-distribution in the same stream, and there may be
// duplicates. It is up to clients to resolve any ambiguities.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type DroppedLabels = src.DroppedLabels

// The protocol for the `GetAlertPolicy` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetAlertPolicyRequest = src.GetAlertPolicyRequest

// The `GetGroup` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetGroupRequest = src.GetGroupRequest

// The `GetMetricDescriptor` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetMetricDescriptorRequest = src.GetMetricDescriptorRequest

// The `GetMonitoredResourceDescriptor` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetMonitoredResourceDescriptorRequest = src.GetMonitoredResourceDescriptorRequest

// The `GetNotificationChannelDescriptor` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetNotificationChannelDescriptorRequest = src.GetNotificationChannelDescriptorRequest

// The `GetNotificationChannel` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetNotificationChannelRequest = src.GetNotificationChannelRequest

// The `GetNotificationChannelVerificationCode` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetNotificationChannelVerificationCodeRequest = src.GetNotificationChannelVerificationCodeRequest

// The `GetNotificationChannelVerificationCode` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetNotificationChannelVerificationCodeResponse = src.GetNotificationChannelVerificationCodeResponse

// The `GetServiceLevelObjective` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetServiceLevelObjectiveRequest = src.GetServiceLevelObjectiveRequest

// The `GetService` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetServiceRequest = src.GetServiceRequest

// The protocol for the `GetUptimeCheckConfig` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GetUptimeCheckConfigRequest = src.GetUptimeCheckConfigRequest

// The description of a dynamic collection of monitored resources. Each group
// has a filter that is matched against monitored resources and their
// associated metadata. If a group's filter matches an available monitored
// resource, then that resource is a member of that group. Groups can contain
// any number of monitored resources, and each monitored resource can be a
// member of any number of groups. Groups can be nested in parent-child
// hierarchies. The `parentName` field identifies an optional parent for each
// group. If a group has a parent, then the only monitored resources available
// to be matched by the group's filter are the resources contained in the
// parent group. In other words, a group contains the monitored resources that
// match its filter and the filters of all the group's ancestors. A group
// without a parent can contain any monitored resource. For example, consider
// an infrastructure running a set of instances with two user-defined tags:
// `"environment"` and `"role"`. A parent group has a filter,
// `environment="production"`. A child of that parent group has a filter,
// `role="transcoder"`. The parent group contains all instances in the
// production environment, regardless of their roles. The child group contains
// instances that have the transcoder role *and* are in the production
// environment. The monitored resources contained in a group can change at any
// moment, depending on what resources exist and what filters are associated
// with the group and its ancestors.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Group = src.Group

// The supported resource types that can be used as values of
// `group_resource.resource_type`. `INSTANCE` includes `gce_instance` and
// `aws_ec2_instance` resource types. The resource types `gae_app` and
// `uptime_url` are not valid here because group checks on App Engine modules
// and URLs are not allowed.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GroupResourceType = src.GroupResourceType

// GroupServiceClient is the client API for GroupService service. For
// semantics around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GroupServiceClient = src.GroupServiceClient

// GroupServiceServer is the server API for GroupService service.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type GroupServiceServer = src.GroupServiceServer

// An internal checker allows Uptime checks to run on private/internal GCP
// resources. Deprecated: Do not use.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type InternalChecker = src.InternalChecker

// Operational states for an internal checker.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type InternalChecker_State = src.InternalChecker_State

// A label value.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type LabelValue = src.LabelValue
type LabelValue_BoolValue = src.LabelValue_BoolValue
type LabelValue_Int64Value = src.LabelValue_Int64Value
type LabelValue_StringValue = src.LabelValue_StringValue

// The protocol for the `ListAlertPolicies` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListAlertPoliciesRequest = src.ListAlertPoliciesRequest

// The protocol for the `ListAlertPolicies` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListAlertPoliciesResponse = src.ListAlertPoliciesResponse

// The `ListGroupMembers` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListGroupMembersRequest = src.ListGroupMembersRequest

// The `ListGroupMembers` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListGroupMembersResponse = src.ListGroupMembersResponse

// The `ListGroup` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListGroupsRequest = src.ListGroupsRequest
type ListGroupsRequest_AncestorsOfGroup = src.ListGroupsRequest_AncestorsOfGroup
type ListGroupsRequest_ChildrenOfGroup = src.ListGroupsRequest_ChildrenOfGroup
type ListGroupsRequest_DescendantsOfGroup = src.ListGroupsRequest_DescendantsOfGroup

// The `ListGroups` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListGroupsResponse = src.ListGroupsResponse

// The `ListMetricDescriptors` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListMetricDescriptorsRequest = src.ListMetricDescriptorsRequest

// The `ListMetricDescriptors` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListMetricDescriptorsResponse = src.ListMetricDescriptorsResponse

// The `ListMonitoredResourceDescriptors` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListMonitoredResourceDescriptorsRequest = src.ListMonitoredResourceDescriptorsRequest

// The `ListMonitoredResourceDescriptors` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListMonitoredResourceDescriptorsResponse = src.ListMonitoredResourceDescriptorsResponse

// The `ListNotificationChannelDescriptors` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListNotificationChannelDescriptorsRequest = src.ListNotificationChannelDescriptorsRequest

// The `ListNotificationChannelDescriptors` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListNotificationChannelDescriptorsResponse = src.ListNotificationChannelDescriptorsResponse

// The `ListNotificationChannels` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListNotificationChannelsRequest = src.ListNotificationChannelsRequest

// The `ListNotificationChannels` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListNotificationChannelsResponse = src.ListNotificationChannelsResponse

// The `ListServiceLevelObjectives` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListServiceLevelObjectivesRequest = src.ListServiceLevelObjectivesRequest

// The `ListServiceLevelObjectives` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListServiceLevelObjectivesResponse = src.ListServiceLevelObjectivesResponse

// The `ListServices` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListServicesRequest = src.ListServicesRequest

// The `ListServices` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListServicesResponse = src.ListServicesResponse

// The `ListTimeSeries` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListTimeSeriesRequest = src.ListTimeSeriesRequest

// Controls which fields are returned by `ListTimeSeries`.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListTimeSeriesRequest_TimeSeriesView = src.ListTimeSeriesRequest_TimeSeriesView

// The `ListTimeSeries` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListTimeSeriesResponse = src.ListTimeSeriesResponse

// The protocol for the `ListUptimeCheckConfigs` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListUptimeCheckConfigsRequest = src.ListUptimeCheckConfigsRequest

// The protocol for the `ListUptimeCheckConfigs` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListUptimeCheckConfigsResponse = src.ListUptimeCheckConfigsResponse

// The protocol for the `ListUptimeCheckIps` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListUptimeCheckIpsRequest = src.ListUptimeCheckIpsRequest

// The protocol for the `ListUptimeCheckIps` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ListUptimeCheckIpsResponse = src.ListUptimeCheckIpsResponse

// MetricServiceClient is the client API for MetricService service. For
// semantics around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type MetricServiceClient = src.MetricServiceClient

// MetricServiceServer is the server API for MetricService service.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type MetricServiceServer = src.MetricServiceServer

// Describes a change made to a configuration.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type MutationRecord = src.MutationRecord

// A `NotificationChannel` is a medium through which an alert is delivered
// when a policy violation is detected. Examples of channels include email,
// SMS, and third-party messaging applications. Fields containing sensitive
// information like authentication tokens or contact info are only partially
// populated on retrieval.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type NotificationChannel = src.NotificationChannel

// A description of a notification channel. The descriptor includes the
// properties of the channel and the set of labels or fields that must be
// specified to configure channels of a given type.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type NotificationChannelDescriptor = src.NotificationChannelDescriptor

// NotificationChannelServiceClient is the client API for
// NotificationChannelService service. For semantics around ctx use and
// closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type NotificationChannelServiceClient = src.NotificationChannelServiceClient

// NotificationChannelServiceServer is the server API for
// NotificationChannelService service.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type NotificationChannelServiceServer = src.NotificationChannelServiceServer

// Indicates whether the channel has been verified or not. It is illegal to
// specify this field in a
// [`CreateNotificationChannel`][google.monitoring.v3.NotificationChannelService.CreateNotificationChannel]
// or an
// [`UpdateNotificationChannel`][google.monitoring.v3.NotificationChannelService.UpdateNotificationChannel]
// operation.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type NotificationChannel_VerificationStatus = src.NotificationChannel_VerificationStatus

// A single data point in a time series.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Point = src.Point

// An error associated with a query in the time series query language format.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type QueryError = src.QueryError

// This is an error detail intended to be used with INVALID_ARGUMENT errors.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type QueryErrorList = src.QueryErrorList

// QueryServiceClient is the client API for QueryService service. For
// semantics around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type QueryServiceClient = src.QueryServiceClient

// QueryServiceServer is the server API for QueryService service.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type QueryServiceServer = src.QueryServiceServer

// The `QueryTimeSeries` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type QueryTimeSeriesRequest = src.QueryTimeSeriesRequest

// The `QueryTimeSeries` response.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type QueryTimeSeriesResponse = src.QueryTimeSeriesResponse

// Range of numerical values within `min` and `max`.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Range = src.Range

// Service Level Indicators for which atomic units of service are counted
// directly.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type RequestBasedSli = src.RequestBasedSli
type RequestBasedSli_DistributionCut = src.RequestBasedSli_DistributionCut
type RequestBasedSli_GoodTotalRatio = src.RequestBasedSli_GoodTotalRatio

// The `SendNotificationChannelVerificationCode` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type SendNotificationChannelVerificationCodeRequest = src.SendNotificationChannelVerificationCodeRequest

// A `Service` is a discrete, autonomous, and network-accessible unit,
// designed to solve an individual concern
// ([Wikipedia](https://en.wikipedia.org/wiki/Service-orientation)). In Cloud
// Monitoring, a `Service` acts as the root resource under which operational
// aspects of the service are accessible.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Service = src.Service

// A Service-Level Indicator (SLI) describes the "performance" of a service.
// For some services, the SLI is well-defined. In such cases, the SLI can be
// described easily by referencing the well-known SLI and providing the needed
// parameters. Alternatively, a "custom" SLI can be defined with a query to the
// underlying metric store. An SLI is defined to be `good_service /
// total_service` over any queried time interval. The value of performance
// always falls into the range `0 <= performance <= 1`. A custom SLI describes
// how to compute this ratio, whether this is by dividing values from a pair of
// time series, cutting a `Distribution` into good and bad counts, or counting
// time windows in which the service complies with a criterion. For separation
// of concerns, a single Service-Level Indicator measures performance for only
// one aspect of service quality, such as fraction of successful queries or
// fast-enough queries.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ServiceLevelIndicator = src.ServiceLevelIndicator
type ServiceLevelIndicator_BasicSli = src.ServiceLevelIndicator_BasicSli
type ServiceLevelIndicator_RequestBased = src.ServiceLevelIndicator_RequestBased
type ServiceLevelIndicator_WindowsBased = src.ServiceLevelIndicator_WindowsBased

// A Service-Level Objective (SLO) describes a level of desired good service.
// It consists of a service-level indicator (SLI), a performance goal, and a
// period over which the objective is to be evaluated against that goal. The
// SLO can use SLIs defined in a number of different manners. Typical SLOs
// might include "99% of requests in each rolling week have latency below 200
// milliseconds" or "99.5% of requests in each calendar month return
// successfully."
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ServiceLevelObjective = src.ServiceLevelObjective
type ServiceLevelObjective_CalendarPeriod = src.ServiceLevelObjective_CalendarPeriod
type ServiceLevelObjective_RollingPeriod = src.ServiceLevelObjective_RollingPeriod

// `ServiceLevelObjective.View` determines what form of
// `ServiceLevelObjective` is returned from `GetServiceLevelObjective`,
// `ListServiceLevelObjectives`, and `ListServiceLevelObjectiveVersions` RPCs.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ServiceLevelObjective_View = src.ServiceLevelObjective_View

// ServiceMonitoringServiceClient is the client API for
// ServiceMonitoringService service. For semantics around ctx use and
// closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ServiceMonitoringServiceClient = src.ServiceMonitoringServiceClient

// ServiceMonitoringServiceServer is the server API for
// ServiceMonitoringService service.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ServiceMonitoringServiceServer = src.ServiceMonitoringServiceServer

// The tier of service for a Workspace. Please see the [service tiers
// documentation](https://cloud.google.com/monitoring/workspaces/tiers) for
// more details. Deprecated: Do not use.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type ServiceTier = src.ServiceTier

// App Engine service. Learn more at https://cloud.google.com/appengine.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Service_AppEngine = src.Service_AppEngine
type Service_AppEngine_ = src.Service_AppEngine_

// Cloud Endpoints service. Learn more at https://cloud.google.com/endpoints.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Service_CloudEndpoints = src.Service_CloudEndpoints
type Service_CloudEndpoints_ = src.Service_CloudEndpoints_

// Istio service scoped to a single Kubernetes cluster. Learn more at
// https://istio.io. Clusters running OSS Istio will have their services
// ingested as this type.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Service_ClusterIstio = src.Service_ClusterIstio
type Service_ClusterIstio_ = src.Service_ClusterIstio_

// Custom view of service telemetry. Currently a place-holder pending final
// design.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Service_Custom = src.Service_Custom
type Service_Custom_ = src.Service_Custom_

// Canonical service scoped to an Istio mesh. Anthos clusters running ASM >=
// 1.6.8 will have their services ingested as this type.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Service_IstioCanonicalService = src.Service_IstioCanonicalService
type Service_IstioCanonicalService_ = src.Service_IstioCanonicalService_

// Istio service scoped to an Istio mesh. Anthos clusters running ASM < 1.6.8
// will have their services ingested as this type.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Service_MeshIstio = src.Service_MeshIstio
type Service_MeshIstio_ = src.Service_MeshIstio_

// Configuration for how to query telemetry on a Service.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type Service_Telemetry = src.Service_Telemetry

// The context of a span. This is attached to an
// [Exemplar][google.api.Distribution.Exemplar] in
// [Distribution][google.api.Distribution] values during aggregation. It
// contains the name of a span with format:
// projects/[PROJECT_ID_OR_NUMBER]/traces/[TRACE_ID]/spans/[SPAN_ID]
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type SpanContext = src.SpanContext

// A locator for text. Indicates a particular part of the text of a request or
// of an object referenced in the request. For example, suppose the request
// field `text` contains: text: "The quick brown fox jumps over the lazy dog."
// Then the locator: source: "text" start_position { line: 1 column: 17 }
// end_position { line: 1 column: 19 } refers to the part of the text: "fox".
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type TextLocator = src.TextLocator

// The position of a byte within the text.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type TextLocator_Position = src.TextLocator_Position

// A closed time interval. It extends from the start time to the end time, and
// includes both: `[startTime, endTime]`. Valid time intervals depend on the
// [`MetricKind`](https://cloud.google.com/monitoring/api/ref_v3/rest/v3/projects.metricDescriptors#MetricKind)
// of the metric value. The end time must not be earlier than the start time.
// When writing data points, the start time must not be more than 25 hours in
// the past and the end time must not be more than five minutes in the future.
// - For `GAUGE` metrics, the `startTime` value is technically optional; if no
// value is specified, the start time defaults to the value of the end time,
// and the interval represents a single point in time. If both start and end
// times are specified, they must be identical. Such an interval is valid only
// for `GAUGE` metrics, which are point-in-time measurements. The end time of a
// new interval must be at least a millisecond after the end time of the
// previous interval. - For `DELTA` metrics, the start time and end time must
// specify a non-zero interval, with subsequent points specifying contiguous
// and non-overlapping intervals. For `DELTA` metrics, the start time of the
// next interval must be at least a millisecond after the end time of the
// previous interval. - For `CUMULATIVE` metrics, the start time and end time
// must specify a non-zero interval, with subsequent points specifying the same
// start time and increasing end times, until an event resets the cumulative
// value to zero and sets a new start time for the following points. The new
// start time must be at least a millisecond after the end time of the previous
// interval. - The start time of a new interval must be at least a millisecond
// after the end time of the previous interval because intervals are closed. If
// the start time of a new interval is the same as the end time of the previous
// interval, then data written at the new start time could overwrite data
// written at the previous end time.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type TimeInterval = src.TimeInterval

// A collection of data points that describes the time-varying values of a
// metric. A time series is identified by a combination of a fully-specified
// monitored resource and a fully-specified metric. This type is used for both
// listing and creating time series.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type TimeSeries = src.TimeSeries

// Represents the values of a time series associated with a
// TimeSeriesDescriptor.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type TimeSeriesData = src.TimeSeriesData

// A point's value columns and time interval. Each point has one or more point
// values corresponding to the entries in `point_descriptors` field in the
// TimeSeriesDescriptor associated with this object.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type TimeSeriesData_PointData = src.TimeSeriesData_PointData

// A descriptor for the labels and points in a time series.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type TimeSeriesDescriptor = src.TimeSeriesDescriptor

// A descriptor for the value columns in a data point.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type TimeSeriesDescriptor_ValueDescriptor = src.TimeSeriesDescriptor_ValueDescriptor

// A `TimeSeriesRatio` specifies two `TimeSeries` to use for computing the
// `good_service / total_service` ratio. The specified `TimeSeries` must have
// `ValueType = DOUBLE` or `ValueType = INT64` and must have `MetricKind =
// DELTA` or `MetricKind = CUMULATIVE`. The `TimeSeriesRatio` must specify
// exactly two of good, bad, and total, and the relationship `good_service +
// bad_service = total_service` will be assumed.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type TimeSeriesRatio = src.TimeSeriesRatio

// A single strongly-typed value.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type TypedValue = src.TypedValue
type TypedValue_BoolValue = src.TypedValue_BoolValue
type TypedValue_DistributionValue = src.TypedValue_DistributionValue
type TypedValue_DoubleValue = src.TypedValue_DoubleValue
type TypedValue_Int64Value = src.TypedValue_Int64Value
type TypedValue_StringValue = src.TypedValue_StringValue

// UnimplementedAlertPolicyServiceServer can be embedded to have forward
// compatible implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UnimplementedAlertPolicyServiceServer = src.UnimplementedAlertPolicyServiceServer

// UnimplementedGroupServiceServer can be embedded to have forward compatible
// implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UnimplementedGroupServiceServer = src.UnimplementedGroupServiceServer

// UnimplementedMetricServiceServer can be embedded to have forward compatible
// implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UnimplementedMetricServiceServer = src.UnimplementedMetricServiceServer

// UnimplementedNotificationChannelServiceServer can be embedded to have
// forward compatible implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UnimplementedNotificationChannelServiceServer = src.UnimplementedNotificationChannelServiceServer

// UnimplementedQueryServiceServer can be embedded to have forward compatible
// implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UnimplementedQueryServiceServer = src.UnimplementedQueryServiceServer

// UnimplementedServiceMonitoringServiceServer can be embedded to have forward
// compatible implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UnimplementedServiceMonitoringServiceServer = src.UnimplementedServiceMonitoringServiceServer

// UnimplementedUptimeCheckServiceServer can be embedded to have forward
// compatible implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UnimplementedUptimeCheckServiceServer = src.UnimplementedUptimeCheckServiceServer

// The protocol for the `UpdateAlertPolicy` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UpdateAlertPolicyRequest = src.UpdateAlertPolicyRequest

// The `UpdateGroup` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UpdateGroupRequest = src.UpdateGroupRequest

// The `UpdateNotificationChannel` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UpdateNotificationChannelRequest = src.UpdateNotificationChannelRequest

// The `UpdateServiceLevelObjective` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UpdateServiceLevelObjectiveRequest = src.UpdateServiceLevelObjectiveRequest

// The `UpdateService` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UpdateServiceRequest = src.UpdateServiceRequest

// The protocol for the `UpdateUptimeCheckConfig` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UpdateUptimeCheckConfigRequest = src.UpdateUptimeCheckConfigRequest

// This message configures which resources and services to monitor for
// availability.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckConfig = src.UptimeCheckConfig

// Optional. Used to perform content matching. This allows matching based on
// substrings and regular expressions, together with their negations. Only the
// first 4&nbsp;MB of an HTTP or HTTPS check's response (and the first
// 1&nbsp;MB of a TCP check's response) are examined for purposes of content
// matching.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckConfig_ContentMatcher = src.UptimeCheckConfig_ContentMatcher

// Options to perform content matching.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckConfig_ContentMatcher_ContentMatcherOption = src.UptimeCheckConfig_ContentMatcher_ContentMatcherOption

// Information involved in an HTTP/HTTPS Uptime check request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckConfig_HttpCheck = src.UptimeCheckConfig_HttpCheck
type UptimeCheckConfig_HttpCheck_ = src.UptimeCheckConfig_HttpCheck_

// The authentication parameters to provide to the specified resource or URL
// that requires a username and password. Currently, only [Basic HTTP
// authentication](https://tools.ietf.org/html/rfc7617) is supported in Uptime
// checks.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckConfig_HttpCheck_BasicAuthentication = src.UptimeCheckConfig_HttpCheck_BasicAuthentication

// Header options corresponding to the content type of a HTTP request body.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckConfig_HttpCheck_ContentType = src.UptimeCheckConfig_HttpCheck_ContentType

// The HTTP request method options.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckConfig_HttpCheck_RequestMethod = src.UptimeCheckConfig_HttpCheck_RequestMethod
type UptimeCheckConfig_MonitoredResource = src.UptimeCheckConfig_MonitoredResource

// The resource submessage for group checks. It can be used instead of a
// monitored resource, when multiple resources are being monitored.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckConfig_ResourceGroup = src.UptimeCheckConfig_ResourceGroup
type UptimeCheckConfig_ResourceGroup_ = src.UptimeCheckConfig_ResourceGroup_

// Information required for a TCP Uptime check request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckConfig_TcpCheck = src.UptimeCheckConfig_TcpCheck
type UptimeCheckConfig_TcpCheck_ = src.UptimeCheckConfig_TcpCheck_

// Contains the region, location, and list of IP addresses where checkers in
// the location run from.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckIp = src.UptimeCheckIp

// The regions from which an Uptime check can be run.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckRegion = src.UptimeCheckRegion

// UptimeCheckServiceClient is the client API for UptimeCheckService service.
// For semantics around ctx use and closing/ending streaming RPCs, please refer
// to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckServiceClient = src.UptimeCheckServiceClient

// UptimeCheckServiceServer is the server API for UptimeCheckService service.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type UptimeCheckServiceServer = src.UptimeCheckServiceServer

// The `VerifyNotificationChannel` request.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type VerifyNotificationChannelRequest = src.VerifyNotificationChannelRequest

// A `WindowsBasedSli` defines `good_service` as the count of time windows for
// which the provided service was of good quality. Criteria for determining if
// service was good are embedded in the `window_criterion`.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type WindowsBasedSli = src.WindowsBasedSli
type WindowsBasedSli_GoodBadMetricFilter = src.WindowsBasedSli_GoodBadMetricFilter
type WindowsBasedSli_GoodTotalRatioThreshold = src.WindowsBasedSli_GoodTotalRatioThreshold
type WindowsBasedSli_MetricMeanInRange = src.WindowsBasedSli_MetricMeanInRange

// A `MetricRange` is used when each window is good when the value x of a
// single `TimeSeries` satisfies `range.min <= x <= range.max`. The provided
// `TimeSeries` must have `ValueType = INT64` or `ValueType = DOUBLE` and
// `MetricKind = GAUGE`.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type WindowsBasedSli_MetricRange = src.WindowsBasedSli_MetricRange
type WindowsBasedSli_MetricSumInRange = src.WindowsBasedSli_MetricSumInRange

// A `PerformanceThreshold` is used when each window is good when that window
// has a sufficiently high `performance`.
//
// Deprecated: Please use types in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
type WindowsBasedSli_PerformanceThreshold = src.WindowsBasedSli_PerformanceThreshold
type WindowsBasedSli_PerformanceThreshold_BasicSliPerformance = src.WindowsBasedSli_PerformanceThreshold_BasicSliPerformance
type WindowsBasedSli_PerformanceThreshold_Performance = src.WindowsBasedSli_PerformanceThreshold_Performance

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func NewAlertPolicyServiceClient(cc grpc.ClientConnInterface) AlertPolicyServiceClient {
	return src.NewAlertPolicyServiceClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func NewGroupServiceClient(cc grpc.ClientConnInterface) GroupServiceClient {
	return src.NewGroupServiceClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func NewMetricServiceClient(cc grpc.ClientConnInterface) MetricServiceClient {
	return src.NewMetricServiceClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func NewNotificationChannelServiceClient(cc grpc.ClientConnInterface) NotificationChannelServiceClient {
	return src.NewNotificationChannelServiceClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func NewQueryServiceClient(cc grpc.ClientConnInterface) QueryServiceClient {
	return src.NewQueryServiceClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func NewServiceMonitoringServiceClient(cc grpc.ClientConnInterface) ServiceMonitoringServiceClient {
	return src.NewServiceMonitoringServiceClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func NewUptimeCheckServiceClient(cc grpc.ClientConnInterface) UptimeCheckServiceClient {
	return src.NewUptimeCheckServiceClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func RegisterAlertPolicyServiceServer(s *grpc.Server, srv AlertPolicyServiceServer) {
	src.RegisterAlertPolicyServiceServer(s, srv)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func RegisterGroupServiceServer(s *grpc.Server, srv GroupServiceServer) {
	src.RegisterGroupServiceServer(s, srv)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func RegisterMetricServiceServer(s *grpc.Server, srv MetricServiceServer) {
	src.RegisterMetricServiceServer(s, srv)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func RegisterNotificationChannelServiceServer(s *grpc.Server, srv NotificationChannelServiceServer) {
	src.RegisterNotificationChannelServiceServer(s, srv)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func RegisterQueryServiceServer(s *grpc.Server, srv QueryServiceServer) {
	src.RegisterQueryServiceServer(s, srv)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func RegisterServiceMonitoringServiceServer(s *grpc.Server, srv ServiceMonitoringServiceServer) {
	src.RegisterServiceMonitoringServiceServer(s, srv)
}

// Deprecated: Please use funcs in: cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
func RegisterUptimeCheckServiceServer(s *grpc.Server, srv UptimeCheckServiceServer) {
	src.RegisterUptimeCheckServiceServer(s, srv)
}
