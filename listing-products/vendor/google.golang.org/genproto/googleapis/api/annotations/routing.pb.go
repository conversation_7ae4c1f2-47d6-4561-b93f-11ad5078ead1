// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.24.4
// source: google/api/routing.proto

package annotations

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Specifies the routing information that should be sent along with the request
// in the form of routing header.
// **NOTE:** All service configuration rules follow the "last one wins" order.
//
// The examples below will apply to an RPC which has the following request type:
//
// Message Definition:
//
//	message Request {
//	  // The name of the Table
//	  // Values can be of the following formats:
//	  // - `projects/<project>/tables/<table>`
//	  // - `projects/<project>/instances/<instance>/tables/<table>`
//	  // - `region/<region>/zones/<zone>/tables/<table>`
//	  string table_name = 1;
//
//	  // This value specifies routing for replication.
//	  // It can be in the following formats:
//	  // - `profiles/<profile_id>`
//	  // - a legacy `profile_id` that can be any string
//	  string app_profile_id = 2;
//	}
//
// Example message:
//
//	{
//	  table_name: projects/proj_foo/instances/instance_bar/table/table_baz,
//	  app_profile_id: profiles/prof_qux
//	}
//
// The routing header consists of one or multiple key-value pairs. Every key
// and value must be percent-encoded, and joined together in the format of
// `key1=value1&key2=value2`.
// In the examples below I am skipping the percent-encoding for readablity.
//
// # Example 1
//
// Extracting a field from the request to put into the routing header
// unchanged, with the key equal to the field name.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // Take the `app_profile_id`.
//	  routing_parameters {
//	    field: "app_profile_id"
//	  }
//	};
//
// result:
//
//	x-goog-request-params: app_profile_id=profiles/prof_qux
//
// # Example 2
//
// Extracting a field from the request to put into the routing header
// unchanged, with the key different from the field name.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // Take the `app_profile_id`, but name it `routing_id` in the header.
//	  routing_parameters {
//	    field: "app_profile_id"
//	    path_template: "{routing_id=**}"
//	  }
//	};
//
// result:
//
//	x-goog-request-params: routing_id=profiles/prof_qux
//
// # Example 3
//
// Extracting a field from the request to put into the routing
// header, while matching a path template syntax on the field's value.
//
// NB: it is more useful to send nothing than to send garbage for the purpose
// of dynamic routing, since garbage pollutes cache. Thus the matching.
//
// # Sub-example 3a
//
// The field matches the template.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // Take the `table_name`, if it's well-formed (with project-based
//	  // syntax).
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{table_name=projects/*/instances/*/**}"
//	  }
//	};
//
// result:
//
//	x-goog-request-params:
//	table_name=projects/proj_foo/instances/instance_bar/table/table_baz
//
// # Sub-example 3b
//
// The field does not match the template.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // Take the `table_name`, if it's well-formed (with region-based
//	  // syntax).
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{table_name=regions/*/zones/*/**}"
//	  }
//	};
//
// result:
//
//	<no routing header will be sent>
//
// # Sub-example 3c
//
// Multiple alternative conflictingly named path templates are
// specified. The one that matches is used to construct the header.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // Take the `table_name`, if it's well-formed, whether
//	  // using the region- or projects-based syntax.
//
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{table_name=regions/*/zones/*/**}"
//	  }
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{table_name=projects/*/instances/*/**}"
//	  }
//	};
//
// result:
//
//	x-goog-request-params:
//	table_name=projects/proj_foo/instances/instance_bar/table/table_baz
//
// # Example 4
//
// Extracting a single routing header key-value pair by matching a
// template syntax on (a part of) a single request field.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // Take just the project id from the `table_name` field.
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{routing_id=projects/*}/**"
//	  }
//	};
//
// result:
//
//	x-goog-request-params: routing_id=projects/proj_foo
//
// # Example 5
//
// Extracting a single routing header key-value pair by matching
// several conflictingly named path templates on (parts of) a single request
// field. The last template to match "wins" the conflict.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // If the `table_name` does not have instances information,
//	  // take just the project id for routing.
//	  // Otherwise take project + instance.
//
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{routing_id=projects/*}/**"
//	  }
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{routing_id=projects/*/instances/*}/**"
//	  }
//	};
//
// result:
//
//	x-goog-request-params:
//	routing_id=projects/proj_foo/instances/instance_bar
//
// # Example 6
//
// Extracting multiple routing header key-value pairs by matching
// several non-conflicting path templates on (parts of) a single request field.
//
// # Sub-example 6a
//
// Make the templates strict, so that if the `table_name` does not
// have an instance information, nothing is sent.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // The routing code needs two keys instead of one composite
//	  // but works only for the tables with the "project-instance" name
//	  // syntax.
//
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{project_id=projects/*}/instances/*/**"
//	  }
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "projects/*/{instance_id=instances/*}/**"
//	  }
//	};
//
// result:
//
//	x-goog-request-params:
//	project_id=projects/proj_foo&instance_id=instances/instance_bar
//
// # Sub-example 6b
//
// Make the templates loose, so that if the `table_name` does not
// have an instance information, just the project id part is sent.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // The routing code wants two keys instead of one composite
//	  // but will work with just the `project_id` for tables without
//	  // an instance in the `table_name`.
//
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{project_id=projects/*}/**"
//	  }
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "projects/*/{instance_id=instances/*}/**"
//	  }
//	};
//
// result (is the same as 6a for our example message because it has the instance
// information):
//
//	x-goog-request-params:
//	project_id=projects/proj_foo&instance_id=instances/instance_bar
//
// # Example 7
//
// Extracting multiple routing header key-value pairs by matching
// several path templates on multiple request fields.
//
// NB: note that here there is no way to specify sending nothing if one of the
// fields does not match its template. E.g. if the `table_name` is in the wrong
// format, the `project_id` will not be sent, but the `routing_id` will be.
// The backend routing code has to be aware of that and be prepared to not
// receive a full complement of keys if it expects multiple.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // The routing needs both `project_id` and `routing_id`
//	  // (from the `app_profile_id` field) for routing.
//
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{project_id=projects/*}/**"
//	  }
//	  routing_parameters {
//	    field: "app_profile_id"
//	    path_template: "{routing_id=**}"
//	  }
//	};
//
// result:
//
//	x-goog-request-params:
//	project_id=projects/proj_foo&routing_id=profiles/prof_qux
//
// # Example 8
//
// Extracting a single routing header key-value pair by matching
// several conflictingly named path templates on several request fields. The
// last template to match "wins" the conflict.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // The `routing_id` can be a project id or a region id depending on
//	  // the table name format, but only if the `app_profile_id` is not set.
//	  // If `app_profile_id` is set it should be used instead.
//
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{routing_id=projects/*}/**"
//	  }
//	  routing_parameters {
//	     field: "table_name"
//	     path_template: "{routing_id=regions/*}/**"
//	  }
//	  routing_parameters {
//	    field: "app_profile_id"
//	    path_template: "{routing_id=**}"
//	  }
//	};
//
// result:
//
//	x-goog-request-params: routing_id=profiles/prof_qux
//
// # Example 9
//
// Bringing it all together.
//
// annotation:
//
//	option (google.api.routing) = {
//	  // For routing both `table_location` and a `routing_id` are needed.
//	  //
//	  // table_location can be either an instance id or a region+zone id.
//	  //
//	  // For `routing_id`, take the value of `app_profile_id`
//	  // - If it's in the format `profiles/<profile_id>`, send
//	  // just the `<profile_id>` part.
//	  // - If it's any other literal, send it as is.
//	  // If the `app_profile_id` is empty, and the `table_name` starts with
//	  // the project_id, send that instead.
//
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "projects/*/{table_location=instances/*}/tables/*"
//	  }
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{table_location=regions/*/zones/*}/tables/*"
//	  }
//	  routing_parameters {
//	    field: "table_name"
//	    path_template: "{routing_id=projects/*}/**"
//	  }
//	  routing_parameters {
//	    field: "app_profile_id"
//	    path_template: "{routing_id=**}"
//	  }
//	  routing_parameters {
//	    field: "app_profile_id"
//	    path_template: "profiles/{routing_id=*}"
//	  }
//	};
//
// result:
//
//	x-goog-request-params:
//	table_location=instances/instance_bar&routing_id=prof_qux
type RoutingRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A collection of Routing Parameter specifications.
	// **NOTE:** If multiple Routing Parameters describe the same key
	// (via the `path_template` field or via the `field` field when
	// `path_template` is not provided), "last one wins" rule
	// determines which Parameter gets used.
	// See the examples for more details.
	RoutingParameters []*RoutingParameter `protobuf:"bytes,2,rep,name=routing_parameters,json=routingParameters,proto3" json:"routing_parameters,omitempty"`
}

func (x *RoutingRule) Reset() {
	*x = RoutingRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_routing_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoutingRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoutingRule) ProtoMessage() {}

func (x *RoutingRule) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_routing_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoutingRule.ProtoReflect.Descriptor instead.
func (*RoutingRule) Descriptor() ([]byte, []int) {
	return file_google_api_routing_proto_rawDescGZIP(), []int{0}
}

func (x *RoutingRule) GetRoutingParameters() []*RoutingParameter {
	if x != nil {
		return x.RoutingParameters
	}
	return nil
}

// A projection from an input message to the GRPC or REST header.
type RoutingParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A request field to extract the header key-value pair from.
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// A pattern matching the key-value field. Optional.
	// If not specified, the whole field specified in the `field` field will be
	// taken as value, and its name used as key. If specified, it MUST contain
	// exactly one named segment (along with any number of unnamed segments) The
	// pattern will be matched over the field specified in the `field` field, then
	// if the match is successful:
	// - the name of the single named segment will be used as a header name,
	// - the match value of the segment will be used as a header value;
	// if the match is NOT successful, nothing will be sent.
	//
	// Example:
	//
	//	          -- This is a field in the request message
	//	         |   that the header value will be extracted from.
	//	         |
	//	         |                     -- This is the key name in the
	//	         |                    |   routing header.
	//	         V                    |
	//	field: "table_name"           v
	//	path_template: "projects/*/{table_location=instances/*}/tables/*"
	//	                                           ^            ^
	//	                                           |            |
	//	  In the {} brackets is the pattern that --             |
	//	  specifies what to extract from the                    |
	//	  field as a value to be sent.                          |
	//	                                                        |
	//	 The string in the field must match the whole pattern --
	//	 before brackets, inside brackets, after brackets.
	//
	// When looking at this specific example, we can see that:
	//   - A key-value pair with the key `table_location`
	//     and the value matching `instances/*` should be added
	//     to the x-goog-request-params routing header.
	//   - The value is extracted from the request message's `table_name` field
	//     if it matches the full pattern specified:
	//     `projects/*/instances/*/tables/*`.
	//
	// **NB:** If the `path_template` field is not provided, the key name is
	// equal to the field name, and the whole field should be sent as a value.
	// This makes the pattern for the field and the value functionally equivalent
	// to `**`, and the configuration
	//
	//	{
	//	  field: "table_name"
	//	}
	//
	// is a functionally equivalent shorthand to:
	//
	//	{
	//	  field: "table_name"
	//	  path_template: "{table_name=**}"
	//	}
	//
	// See Example 1 for more details.
	PathTemplate string `protobuf:"bytes,2,opt,name=path_template,json=pathTemplate,proto3" json:"path_template,omitempty"`
}

func (x *RoutingParameter) Reset() {
	*x = RoutingParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_routing_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoutingParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoutingParameter) ProtoMessage() {}

func (x *RoutingParameter) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_routing_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoutingParameter.ProtoReflect.Descriptor instead.
func (*RoutingParameter) Descriptor() ([]byte, []int) {
	return file_google_api_routing_proto_rawDescGZIP(), []int{1}
}

func (x *RoutingParameter) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *RoutingParameter) GetPathTemplate() string {
	if x != nil {
		return x.PathTemplate
	}
	return ""
}

var file_google_api_routing_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.MethodOptions)(nil),
		ExtensionType: (*RoutingRule)(nil),
		Field:         ********,
		Name:          "google.api.routing",
		Tag:           "bytes,********,opt,name=routing",
		Filename:      "google/api/routing.proto",
	},
}

// Extension fields to descriptorpb.MethodOptions.
var (
	// See RoutingRule.
	//
	// optional google.api.RoutingRule routing = ********;
	E_Routing = &file_google_api_routing_proto_extTypes[0]
)

var File_google_api_routing_proto protoreflect.FileDescriptor

var file_google_api_routing_proto_rawDesc = []byte{
	0x0a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x6f, 0x75,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5a, 0x0a, 0x0b, 0x52, 0x6f, 0x75, 0x74,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x4b, 0x0a, 0x12, 0x72, 0x6f, 0x75, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x52, 0x11, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x22, 0x4d, 0x0a, 0x10, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x61, 0x74, 0x68, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x3a, 0x54, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1e,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xb1,
	0xca, 0xbc, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x07, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x6a, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x42, 0x0c, 0x52, 0x6f, 0x75,
	0x74, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x41, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0xa2, 0x02,
	0x04, 0x47, 0x41, 0x50, 0x49, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_api_routing_proto_rawDescOnce sync.Once
	file_google_api_routing_proto_rawDescData = file_google_api_routing_proto_rawDesc
)

func file_google_api_routing_proto_rawDescGZIP() []byte {
	file_google_api_routing_proto_rawDescOnce.Do(func() {
		file_google_api_routing_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_api_routing_proto_rawDescData)
	})
	return file_google_api_routing_proto_rawDescData
}

var file_google_api_routing_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_google_api_routing_proto_goTypes = []interface{}{
	(*RoutingRule)(nil),                // 0: google.api.RoutingRule
	(*RoutingParameter)(nil),           // 1: google.api.RoutingParameter
	(*descriptorpb.MethodOptions)(nil), // 2: google.protobuf.MethodOptions
}
var file_google_api_routing_proto_depIdxs = []int32{
	1, // 0: google.api.RoutingRule.routing_parameters:type_name -> google.api.RoutingParameter
	2, // 1: google.api.routing:extendee -> google.protobuf.MethodOptions
	0, // 2: google.api.routing:type_name -> google.api.RoutingRule
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	2, // [2:3] is the sub-list for extension type_name
	1, // [1:2] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_google_api_routing_proto_init() }
func file_google_api_routing_proto_init() {
	if File_google_api_routing_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_api_routing_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoutingRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_routing_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoutingParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_api_routing_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 1,
			NumServices:   0,
		},
		GoTypes:           file_google_api_routing_proto_goTypes,
		DependencyIndexes: file_google_api_routing_proto_depIdxs,
		MessageInfos:      file_google_api_routing_proto_msgTypes,
		ExtensionInfos:    file_google_api_routing_proto_extTypes,
	}.Build()
	File_google_api_routing_proto = out.File
	file_google_api_routing_proto_rawDesc = nil
	file_google_api_routing_proto_goTypes = nil
	file_google_api_routing_proto_depIdxs = nil
}
