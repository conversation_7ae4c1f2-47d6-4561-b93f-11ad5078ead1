// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by generate-protos. DO NOT EDIT.

package genid

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
)

const File_google_protobuf_wrappers_proto = "google/protobuf/wrappers.proto"

// Names for google.protobuf.DoubleValue.
const (
	DoubleValue_message_name     protoreflect.Name     = "DoubleValue"
	DoubleValue_message_fullname protoreflect.FullName = "google.protobuf.DoubleValue"
)

// Field names for google.protobuf.DoubleValue.
const (
	DoubleValue_Value_field_name protoreflect.Name = "value"

	DoubleValue_Value_field_fullname protoreflect.FullName = "google.protobuf.DoubleValue.value"
)

// Field numbers for google.protobuf.DoubleValue.
const (
	DoubleValue_Value_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.FloatValue.
const (
	FloatValue_message_name     protoreflect.Name     = "FloatValue"
	FloatValue_message_fullname protoreflect.FullName = "google.protobuf.FloatValue"
)

// Field names for google.protobuf.FloatValue.
const (
	FloatValue_Value_field_name protoreflect.Name = "value"

	FloatValue_Value_field_fullname protoreflect.FullName = "google.protobuf.FloatValue.value"
)

// Field numbers for google.protobuf.FloatValue.
const (
	FloatValue_Value_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.Int64Value.
const (
	Int64Value_message_name     protoreflect.Name     = "Int64Value"
	Int64Value_message_fullname protoreflect.FullName = "google.protobuf.Int64Value"
)

// Field names for google.protobuf.Int64Value.
const (
	Int64Value_Value_field_name protoreflect.Name = "value"

	Int64Value_Value_field_fullname protoreflect.FullName = "google.protobuf.Int64Value.value"
)

// Field numbers for google.protobuf.Int64Value.
const (
	Int64Value_Value_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.UInt64Value.
const (
	UInt64Value_message_name     protoreflect.Name     = "UInt64Value"
	UInt64Value_message_fullname protoreflect.FullName = "google.protobuf.UInt64Value"
)

// Field names for google.protobuf.UInt64Value.
const (
	UInt64Value_Value_field_name protoreflect.Name = "value"

	UInt64Value_Value_field_fullname protoreflect.FullName = "google.protobuf.UInt64Value.value"
)

// Field numbers for google.protobuf.UInt64Value.
const (
	UInt64Value_Value_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.Int32Value.
const (
	Int32Value_message_name     protoreflect.Name     = "Int32Value"
	Int32Value_message_fullname protoreflect.FullName = "google.protobuf.Int32Value"
)

// Field names for google.protobuf.Int32Value.
const (
	Int32Value_Value_field_name protoreflect.Name = "value"

	Int32Value_Value_field_fullname protoreflect.FullName = "google.protobuf.Int32Value.value"
)

// Field numbers for google.protobuf.Int32Value.
const (
	Int32Value_Value_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.UInt32Value.
const (
	UInt32Value_message_name     protoreflect.Name     = "UInt32Value"
	UInt32Value_message_fullname protoreflect.FullName = "google.protobuf.UInt32Value"
)

// Field names for google.protobuf.UInt32Value.
const (
	UInt32Value_Value_field_name protoreflect.Name = "value"

	UInt32Value_Value_field_fullname protoreflect.FullName = "google.protobuf.UInt32Value.value"
)

// Field numbers for google.protobuf.UInt32Value.
const (
	UInt32Value_Value_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.BoolValue.
const (
	BoolValue_message_name     protoreflect.Name     = "BoolValue"
	BoolValue_message_fullname protoreflect.FullName = "google.protobuf.BoolValue"
)

// Field names for google.protobuf.BoolValue.
const (
	BoolValue_Value_field_name protoreflect.Name = "value"

	BoolValue_Value_field_fullname protoreflect.FullName = "google.protobuf.BoolValue.value"
)

// Field numbers for google.protobuf.BoolValue.
const (
	BoolValue_Value_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.StringValue.
const (
	StringValue_message_name     protoreflect.Name     = "StringValue"
	StringValue_message_fullname protoreflect.FullName = "google.protobuf.StringValue"
)

// Field names for google.protobuf.StringValue.
const (
	StringValue_Value_field_name protoreflect.Name = "value"

	StringValue_Value_field_fullname protoreflect.FullName = "google.protobuf.StringValue.value"
)

// Field numbers for google.protobuf.StringValue.
const (
	StringValue_Value_field_number protoreflect.FieldNumber = 1
)

// Names for google.protobuf.BytesValue.
const (
	BytesValue_message_name     protoreflect.Name     = "BytesValue"
	BytesValue_message_fullname protoreflect.FullName = "google.protobuf.BytesValue"
)

// Field names for google.protobuf.BytesValue.
const (
	BytesValue_Value_field_name protoreflect.Name = "value"

	BytesValue_Value_field_fullname protoreflect.FullName = "google.protobuf.BytesValue.value"
)

// Field numbers for google.protobuf.BytesValue.
const (
	BytesValue_Value_field_number protoreflect.FieldNumber = 1
)
