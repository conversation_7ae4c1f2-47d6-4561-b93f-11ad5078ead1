# Architecture / Design / Etc
## Immediate Plans
This repository / project will house all of the functionality for Listing Builder and its related addons,
except for Listing Sync Pro, which was built as its own project before this existed.
Things like:
- keep track of which businesses have purchased Listing Distribution
- sync business information (name, address, phone, etc) to Localeze, Infogroup, etc
- provide data to the `listing-builder-client` frontend client

We might split this up into other repos/projects or merge this with other projects later, but `Eclectic Nerd[sz]` decided it's most important
to quickly start moving our Python / Appengine code into Golang, so that we can use non-deprecated tools,
which other developers at Vendasta use:
- Python 2 is at end-of-life
- lots of our app-engine-related libraries are deprecated experiments
- Golang is strongly-typed
    - catch simple mistakes with your IDE, before you run tests, run linters, push a branch to Github, etc  
    - unit-tests more reliably indicate your code won't break on Demo/Prod, so you don't *need* to run the project locally like Python

## Long Term plans
Move LSP into this microservice.
Separate the microservice into Go modules with their own repos. For example, each listing source, listing distributor, mylistings, insights (, etc?).

## Use dependency-inversion so our business-domain code isn't coupled to anything else.
This started as a whiteboard discussion, about how we don't have distinct layers in our GMB-insights code,
like we do in our other projects.
We've also put in the work for dependency-injection (interfaces, other set-up), but aren't getting the full benefits.
Namely, our GMB service is still coupled to multi-location-analytics, and to our proto definitions for our service.
Being coupled to our API layer (proto stuff) is probably not too bad, since it's our public interface, but the
MLA stuff should be abstracted, so that our business-domain logic/code isn't tied to MLA for getting data.
Long-term, our code should look more like this:

```
listing-products/
    api/
        // logic that converts business-domain to RPCs / proto-defs goes here
        // currently, a lot of that is defined in lp_response_utils.go
        google_my_business_insights.go
        listing_distribution.go
        ...
    internal/
        google_my_business_insights/
            multi-location-analytics-client/
                // things about talking to MLA here
                // returns data in abstracted interfaces / business-domain data-structures
                ...
            // business-domain files here
            service.go
            ...
        listing-distribution/
            ...
```

See also, [this diagram](README/dependency-inversion.jpg).

## Don't mirror the code in our `microservice.yaml` file.
Our `microservice.yaml` file in `listing-sync-pro` has the same parallel-stuff problem as our Python code has/had.
Namely, we've got constants defined for local/demo/prod for various things, which only matter once the project
is running, and which are specific to workflows defined in our actual Golang code.
So, you end up with a lot of lines of text in the YAML file, that conceptually mirror the folder/file layout
of the Golang code, which means you need to look in two different places for some values.
([see this image](README/microservice-yaml-shouldnt-mirror-code.png))
Most of our other Golang (and Typescript code) has been written to have related things co-located in modules,
files, folders, etc, and we should continue that pattern here.

Only put things into `microservice.yaml`, which are needed when the service first boots up, or which cannot
otherwise be located next to other files where the information is needed.

## Use the interfaces we've already defined in listing-builder-client
For a lot of this stuff, it's probably worth looking at the wrapper layer (`listing-distribution.api.service.ts`)
in `listing-builder-client`, to see what data we actually need.
The interfaces we have there aren't completely clean though;
For example `ListingDistributionStatus.status` could probably be named better as `ListingDistributionStatus.subscriptionIsActive`.

## Listing Distribution
- has [a diagram for the push-data-out workflow](README/listing-distribution-push.png),
    that we could move into this repo

## Misc
- ServiceModel (VBC) is a duplicate of SyndicationOrder (CS) except for the auto-renew flag.

## Code Refactoring ideas
- Make "LD active for AG?" endpoint
    - 1st slice: proxy to MP (wrong but less gross than GAE) then make CS can the listing-products endpoint
https://github.com/vendasta/vendastaapis/compare/en-HACK-59-listing-distribution

- Move provider and listings source Id lists out of CS
