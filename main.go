package main

import (
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"strings"
)

// For sitemap index
type SitemapIndex struct {
	Sitemaps []struct {
		Loc string `xml:"loc"`
	} `xml:"sitemap"`
}

// For regular sitemap
type URLSet struct {
	URLs []struct {
		Loc string `xml:"loc"`
	} `xml:"url"`
}

// Fetch content from URL
func fetchXML(url string) ([]byte, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("error fetching URL %s: %v", url, err)
	}
	defer resp.Body.Close()
	return io.ReadAll(resp.Body)
}

// Parse either sitemap index or URL set
func parseSitemap(url string, visited map[string]bool, results *[]string) {
	if visited[url] {
		return // avoid duplicates
	}
	visited[url] = true

	data, err := fetchXML(url)
	if err != nil {
		fmt.Println("❌", err)
		return
	}

	// Try as SitemapIndex first
	var index SitemapIndex
	if err := xml.Unmarshal(data, &index); err == nil && len(index.Sitemaps) > 0 {
		for _, sitemap := range index.Sitemaps {
			parseSitemap(strings.TrimSpace(sitemap.Loc), visited, results)
		}
		return
	}

	// Try as flat URLSet
	var urlSet URLSet
	if err := xml.Unmarshal(data, &urlSet); err == nil && len(urlSet.URLs) > 0 {
		for _, u := range urlSet.URLs {
			*results = append(*results, strings.TrimSpace(u.Loc))
		}
		return
	}

	fmt.Println("⚠️  No valid <url> or <sitemap> entries found in:", url)
}

func main() {
	startURL := "https://www.rabbitgroomers.com/wp-sitemap.xml" // Replace with your sitemap.xml URL

	visited := make(map[string]bool)
	var results []string

	parseSitemap(startURL, visited, &results)

	fmt.Printf("\n🚀 Total Pages Found: %d\n", len(results))
	fmt.Println("🔗 URLs:")
	for _, url := range results {
		fmt.Println(" -", url)
	}
}

