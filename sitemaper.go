package main

import (
	"bufio"
	"encoding/xml"
	"flag"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
)

// -- Structures --
type SitemapIndex struct {
	Sitemaps []struct {
		Loc string `xml:"loc"`
	} `xml:"sitemap"`
}
type URLSet struct {
	URLs []struct {
		Loc string `xml:"loc"`
	} `xml:"url"`
}

// -- Globals --
var (
	wg       sync.WaitGroup
	mu       sync.Mutex
	visited  = make(map[string]bool)
	results  []PageResult
	rootHost string
)

// PageResult holds a URL and its raw HTML size in bytes
type PageResult struct {
	URL      string
	HTMLSize int
}

// -- Fetch functions --
func fetchAndParse(url string) ([]byte, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("error fetching %s: %v", url, err)
	}
	defer resp.Body.Close()
	return io.ReadAll(resp.Body)
}

func fetchRobotsTxt(domain string) ([]string, error) {
	robotsURL := domain + "/robots.txt"
	resp, err := http.Get(robotsURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch robots.txt: %v", err)
	}
	defer resp.Body.Close()

	fmt.Printf("\n📄 robots.txt content from %s:\n\n", robotsURL)
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading robots.txt: %v", err)
	}
	fmt.Println(string(body)) // 👈 Print full robots.txt

	// Rewind and parse lines
	resp.Body = io.NopCloser(strings.NewReader(string(body)))
	var sitemaps []string
	scanner := bufio.NewScanner(strings.NewReader(string(body)))
	for scanner.Scan() {
		line := scanner.Text()
		if strings.HasPrefix(strings.ToLower(line), "sitemap:") {
			sitemap := strings.TrimSpace(strings.TrimPrefix(line, "Sitemap:"))
			sitemaps = append(sitemaps, sitemap)
		}
	}
	return sitemaps, nil
}

// -- Crawler --
func parseSitemap(sitemapURL string) {
	defer wg.Done()

	mu.Lock()
	if visited[sitemapURL] {
		mu.Unlock()
		return
	}
	visited[sitemapURL] = true
	mu.Unlock()

	data, err := fetchAndParse(sitemapURL)
	if err != nil {
		fmt.Println("❌", err)
		return
	}

	// Try sitemap index
	var index SitemapIndex
	if err := xml.Unmarshal(data, &index); err == nil && len(index.Sitemaps) > 0 {
		for _, s := range index.Sitemaps {
			wg.Add(1)
			go parseSitemap(strings.TrimSpace(s.Loc))
		}
		return
	}

	// Try flat URL set
	var urlSet URLSet
	if err := xml.Unmarshal(data, &urlSet); err == nil && len(urlSet.URLs) > 0 {
		for _, u := range urlSet.URLs {
			link := strings.TrimSpace(u.Loc)
			if strings.Contains(link, rootHost) {
				wg.Add(1)
				go fetchPageSize(link)
			}
		}
		return
	}

	// Try to extract URLs from any XML format
	bodyStr := string(data)
	urls := extractURLsFromXML(bodyStr)
	for _, link := range urls {
		if strings.Contains(link, rootHost) {
			wg.Add(1)
			go fetchPageSize(link)
		}
	}
}

func fetchPageSize(pageURL string) {
	defer wg.Done()

	resp, err := http.Get(pageURL)
	if err != nil {
		fmt.Printf("❌ Error fetching %s: %v\n", pageURL, err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Error reading body %s: %v\n", pageURL, err)
		return
	}

	mu.Lock()
	results = append(results, PageResult{URL: pageURL, HTMLSize: len(body)})
	mu.Unlock()
}

func extractURLsFromXML(xmlContent string) []string {
	var urls []string
	lines := strings.Split(xmlContent, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "<loc>") && strings.HasSuffix(line, "</loc>") {
			url := strings.TrimPrefix(line, "<loc>")
			url = strings.TrimSuffix(url, "</loc>")
			url = strings.TrimSpace(url)
			if url != "" {
				urls = append(urls, url)
			}
		}
	}
	return urls
}

// -- Main --
func main() {
	input := flag.String("url", "", "Website URL (e.g., https://example.com)")
	flag.Parse()

	if *input == "" {
		fmt.Println("❌ Please provide a website URL using the -url flag.")
		return
	}

	parsed, err := url.Parse(*input)
	if err != nil || parsed.Host == "" {
		fmt.Println("❌ Invalid URL")
		return
	}
	rootHost = parsed.Host

	sitemaps, err := fetchRobotsTxt(parsed.Scheme + "://" + rootHost)
	if err != nil || len(sitemaps) == 0 {
		fmt.Println("❌ No sitemap found in robots.txt")
		return
	}

	for _, sitemap := range sitemaps {
		wg.Add(1)
		go parseSitemap(sitemap)
	}

	wg.Wait()

	fmt.Printf("\n✅ Total Pages: %d\n", len(results))
	totalSize := 0
	for _, r := range results {
		fmt.Printf(" - %s (%d bytes)\n", r.URL, r.HTMLSize)
		totalSize += r.HTMLSize
	}
	fmt.Printf("\n📦 Total HTML size: %.2f KB\n", float64(totalSize)/1024)
}
