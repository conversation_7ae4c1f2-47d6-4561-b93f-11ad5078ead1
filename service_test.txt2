package seoworkflow

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	dataforseo "github.com/vendasta/listing-products/internal/dataforseo"
	listingprofilemodel "github.com/vendasta/listing-products/internal/listingprofile/model"
)

func TestSEOWorkflowService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()

	type testCase struct {
		name                           string
		method                         string
		args                           interface{}
		expectedResult                 interface{}
		expectedError                  error
		mockSetup                      func()
		workflowParams                 *KeywordInfoWorkflowParams
		listingProfiles                []*listingprofilemodel.ListingProfile
		startDate                      time.Time
		endDate                        time.Time
		dataForSEOAdsStatusGet         *ExpectedAdsStatusGet
		dataForSEOPost                 *ExpectedKeywordInfoPost
		dataForSEOGets                 []*ExpectedKeywordInfoGet
		expectedUpsertKeywordInfoCalls []*ExpectedUpsertKeywordInfo
		expectedResponse               interface{}
		expectedWorkflowErr            error
	}

	date := time.Now()
	testCases := []*testCase{
		// {
		// 	name:      "Test retries when results are not ready yet",
		// 	method:    "StartKeywordInfoWorkflows",
		// 	startDate: time.Date(2019, 12, 1, 0, 0, 0, 0, time.UTC),
		// 	endDate:   time.Date(2019, 12, 1, 0, 0, 0, 0, time.UTC),
		// 	workflowParams: &KeywordInfoWorkflowParams{
		// 		LocationID:   1,
		// 		LocationName: "Location Name",
		// 		Businesses: []*KeywordSearchParams{
		// 			{
		// 				BusinessID: "AG-1",
		// 			},
		// 			{
		// 				BusinessID: "AG-2",
		// 			},
		// 		},
		// 		Date: "2020-02-10",
		// 	},
		// 	listingProfiles: []*listingprofilemodel.ListingProfile{
		// 		{
		// 			BusinessID: "AG-1",
		// 			Website:    "www.website.com",
		// 			RichData: &listingprofilemodel.RichData{
		// 				SEOKeywords: []string{"Keyword1", "Keyword2"},
		// 			},
		// 		},
		// 		{
		// 			BusinessID: "AG-2",
		// 			Website:    "www.website.com",
		// 			RichData: &listingprofilemodel.RichData{
		// 				SEOKeywords: []string{"Keyword3", "Keyword4"},
		// 			},
		// 		},
		// 	},
		// 	dataForSEOAdsStatusGet: &ExpectedAdsStatusGet{
		// 		TaskID: "id1",
		// 		Resp: &dataforseo.GoogleAdsStatusResponse{
		// 			Tasks: []*dataforseo.GoogleAdsStatusTask{
		// 				{
		// 					Result: []*dataforseo.GoogleAdsStatusResult{
		// 						{
		// 							DateUpdate:                 "2019-12-01",
		// 							LastYearInMonthlySearches:  2019,
		// 							LastMonthInMonthlySearches: 12,
		// 						},
		// 					},
		// 				},
		// 			},
		// 		},
		// 	},
		// 	dataForSEOPost: &ExpectedKeywordInfoPost{
		// 		Req: &dataforseo.GoogleKeywordVolumeTaskPostRequest{
		// 			Keywords:     []string{"Keyword1", "Keyword2", "Keyword3", "Keyword4"},
		// 			LanguageCode: "en",
		// 			LocationCode: "1",
		// 		},
		// 		Resp: &dataforseo.GoogleKeywordVolumeResponse{
		// 			Tasks: []*dataforseo.GoogleKeywordTask{
		// 				{
		// 					ID: "id1",
		// 				},
		// 			},
		// 		},
		// 	},
		// 	dataForSEOGets: []*ExpectedKeywordInfoGet{
		// 		{
		// 			TaskID: "id1",
		// 			Resp: &dataforseo.GoogleKeywordVolumeResponse{
		// 				Tasks: []*dataforseo.GoogleKeywordTask{
		// 					{
		// 						ID:            "id1",
		// 						StatusCode:    dataforseo.ErrorTaskInQueue,
		// 						StatusMessage: "",
		// 						ResultCount:   0,
		// 						Data:          nil,
		// 						Result:        []*dataforseo.GoogleKeywordResult{},
		// 					},
		// 				},
		// 			},
		// 		},
		// 		{
		// 			TaskID: "id1",
		// 			Resp: &dataforseo.GoogleKeywordVolumeResponse{
		// 				Tasks: []*dataforseo.GoogleKeywordTask{
		// 					{
		// 						ID:            "",
		// 						StatusCode:    0,
		// 						StatusMessage: "",
		// 						ResultCount:   0,
		// 						Data:          nil,
		// 						Result: []*dataforseo.GoogleKeywordResult{
		// 							{
		// 								Keyword:          "Keyword1",
		// 								CompetitionIndex: 1,
		// 								SearchVolume:     100,
		// 							},
		// 							{
		// 								Keyword:          "Keyword2",
		// 								CompetitionIndex: 2,
		// 								SearchVolume:     200,
		// 							},
		// 							{
		// 								Keyword:          "Keyword3",
		// 								CompetitionIndex: 3,
		// 								SearchVolume:     300,
		// 							},
		// 							{
		// 								Keyword:          "Keyword4",
		// 								CompetitionIndex: 4,
		// 								SearchVolume:     400,
		// 							},
		// 						},
		// 					},
		// 				},
		// 			},
		// 		},
		// 	},
		// 	expectedUpsertKeywordInfoCalls: []*ExpectedUpsertKeywordInfo{
		// 		{
		// 			BusinessID:       "AG-1",
		// 			Keyword:          "Keyword1",
		// 			CompetitionIndex: 1,
		// 			SearchVolume:     100,
		// 		},
		// 		{
		// 			BusinessID:       "AG-1",
		// 			Keyword:          "Keyword2",
		// 			CompetitionIndex: 2,
		// 			SearchVolume:     200,
		// 		},
		// 		{
		// 			BusinessID:       "AG-2",
		// 			Keyword:          "Keyword3",
		// 			CompetitionIndex: 3,
		// 			SearchVolume:     300,
		// 		},
		// 		{
		// 			BusinessID:       "AG-2",
		// 			Keyword:          "Keyword4",
		// 			CompetitionIndex: 4,
		// 			SearchVolume:     400,
		// 		},
		// 	},
		// 	expectedResponse:    nil,
		// 	expectedWorkflowErr: nil,
		// },
		{
			name:   "StartKeywordInfoWorkflows - success",
			method: "StartKeywordInfoWorkflows",
			args: struct {
				date       time.Time
				businesses []*KeywordSearchParams
			}{
				date: date,
				businesses: []*KeywordSearchParams{
					{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
				},
			},
			expectedResult: []string{"workflow1"},
			expectedError:  nil,
			mockSetup: func() {
				mockService.EXPECT().
					StartKeywordInfoWorkflows(ctx, date, []*KeywordSearchParams{
						{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
					}).
					Return([]string{"workflow1"}, nil).Times(1)
			},
		},
		{
			name:   "StartSERPWorkflows - missing tags error",
			method: "StartSERPWorkflows",
			args: struct {
				force                 bool
				ignoreDataLakeResults bool
				businesses            []*KeywordSearchParams
				date                  time.Time
				tags                  []string
			}{
				force:                 true,
				ignoreDataLakeResults: false,
				businesses: []*KeywordSearchParams{
					{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
				},
				date: date,
				tags: nil,
			},
			expectedResult: []string{},
			expectedError:  nil,
			mockSetup: func() {

				mockService.EXPECT().
					StartSERPWorkflows(ctx, true, false, []*KeywordSearchParams{
						{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
					}, date).
					Return([]string{}, nil).Times(1)
			},
		},
		// Existing test cases...
		{
			name:   "StartSuggestedKeywordsWorkflows - success",
			method: "StartSuggestedKeywordsWorkflows",
			args: struct {
				date       time.Time
				businesses []*KeywordSearchParams
			}{
				date: date,
				businesses: []*KeywordSearchParams{
					{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
				},
			},
			expectedResult: []string{"workflow1"},
			expectedError:  nil,
			mockSetup: func() {
				mockService.EXPECT().
					StartSuggestedKeywordsWorkflows(ctx, date, []*KeywordSearchParams{
						{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
					}).
					Return([]string{"workflow1"}, nil).Times(1)
			},
		},
		{
			name:   "StartSuggestedKeywordsWorkflows - error",
			method: "StartSuggestedKeywordsWorkflows",
			args: struct {
				date       time.Time
				businesses []*KeywordSearchParams
			}{
				date: date,
				businesses: []*KeywordSearchParams{
					{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
				},
			},
			expectedResult: []string{},
			expectedError:  errors.New("workflow failed"),
			mockSetup: func() {
				mockService.EXPECT().
					StartSuggestedKeywordsWorkflows(ctx, date, []*KeywordSearchParams{
						{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
					}).
					Return([]string{}, errors.New("workflow failed")).Times(1)
			},
		},
		// New test cases for GetDataForSEOLocationForBusiness
		{
			name:   "GetDataForSEOLocationForBusiness - success",
			method: "GetDataForSEOLocationForBusiness",
			args: struct {
				businessID string
			}{
				businessID: "AG-H7TQNDHQ5C",
			},
			expectedResult: &dataforseo.Location{
				Code:           0,
				Name:           "",
				CodeParent:     0,
				CountryISOCode: "",
				Type:           "",
			},
			expectedError: nil,
			mockSetup: func() {
				mockService.EXPECT().
					GetDataForSEOLocationForBusiness(ctx, "AG-H7TQNDHQ5C").
					Return(&dataforseo.Location{
						Code:           0,
						Name:           "",
						CodeParent:     0,
						CountryISOCode: "",
						Type:           "",
					}, nil).
					Times(1)
			},
		},
		{
			name:   "GetDataForSEOLocationForBusiness - error",
			method: "GetDataForSEOLocationForBusiness",
			args: struct {
				businessID string
			}{
				businessID: "AG-H7TQNDHQ5C",
			},
			expectedResult: &dataforseo.Location{
				Code:           0,
				Name:           "",
				CodeParent:     0,
				CountryISOCode: "",
				Type:           "",
			},
			expectedError: errors.New("business not found"),
			mockSetup: func() {
				mockService.EXPECT().
					GetDataForSEOLocationForBusiness(ctx, "AG-H7TQNDHQ5C").
					Return(&dataforseo.Location{
						Code:           0,
						Name:           "",
						CodeParent:     0,
						CountryISOCode: "",
						Type:           "",
					}, errors.New("business not found")).
					Times(1)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mocks
			tc.mockSetup()

			// Execute the appropriate method
			var result interface{}
			var err error

			switch tc.method {
			case "StartCategoryWorkflow":
				args := tc.args.(struct {
					date       time.Time
					businesses []string
				})
				result, err = mockService.StartCategoryWorkflow(ctx, args.date, args.businesses)

			case "StartKeywordInfoWorkflows":
				args := tc.args.(struct {
					date       time.Time
					businesses []*KeywordSearchParams
				})
				// mockLpService := listingprofileservice.NewMockInterface(ctrl)

				// for _, lp := range tc.listingProfiles {
				// 	mockLpService.EXPECT().Get(gomock.Any(), lp.BusinessID, false).Return(lp, nil).AnyTimes()
				// }

				// mockDataForSEO := dataforseo.NewMockSERPClient(ctrl)

				// mockDataForSEO.EXPECT().GoogleKeywordVolumeTaskPost(gomock.Any(), tc.dataForSEOPost.Req, gomock.Any()).Return(tc.dataForSEOPost.Resp, nil).Times(1)

				// for _, get := range tc.dataForSEOGets {
				// 	mockDataForSEO.EXPECT().GoogleKeywordVolumeTaskGet(gomock.Any(), get.TaskID).Return(get.Resp, nil).Times(len(tc.workflowParams.Businesses))
				// }

				// mockSuggestedKeywords := seosuggestedkeywordsservice.NewMockService(ctrl)

				// mockSEODataService := seodataservice.NewMockService(ctrl)

				// for _, call := range tc.expectedUpsertKeywordInfoCalls {
				// 	mockSEODataService.EXPECT().UpsertKeywordInfo(gomock.Any(), call.BusinessID, call.Keyword, tc.workflowParams.Date, call.SearchVolume, call.CompetitionIndex).Return(nil).Times(1)
				// }
				// mockSEODataService.EXPECT().UpsertKeywordLocationInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				// mockKeywordInfoService := keywordinfoservice.NewMockService(ctrl)
				// mockKeywordInfoService.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(nil).Times(len(tc.expectedUpsertKeywordInfoCalls))

				// testSuite := &testsuite.WorkflowTestSuite{}
				// env := testSuite.NewTestWorkflowEnvironment()
				// env.RegisterWorkflow(KeywordInfoWorkflow)
				// env.RegisterActivity(getGoogleDataLastUpdateDate)
				// env.RegisterActivity(buildKeywordInfoRequest)
				// env.RegisterActivity(postKeywordInfoRequest)
				// env.RegisterActivity(getAndStoreKeywordInfo)
				// ctx = NewActivityContext(ctx, nil, mockLpService, nil, mockSEODataService, mockDataForSEO, nil, nil, nil, nil, nil, mockSuggestedKeywords, nil, mockKeywordInfoService, nil, nil, nil)

				// env.SetWorkerOptions(worker.Options{
				// 	BackgroundActivityContext: ctx,
				// })

				// env.ExecuteWorkflow(KeywordInfoWorkflow, tc.workflowParams)

				// if !env.IsWorkflowCompleted() {
				// 	t.Errorf("Workflow failed to complete. Error (%s)", env.GetWorkflowError())
				// }

				result, err = mockService.StartKeywordInfoWorkflows(ctx, args.date, args.businesses)

			case "StartSERPWorkflows":
				args := tc.args.(struct {
					force                 bool
					ignoreDataLakeResults bool
					businesses            []*KeywordSearchParams
					date                  time.Time
					tags                  []string
				})
				result, err = mockService.StartSERPWorkflows(ctx, args.force, args.ignoreDataLakeResults, args.businesses, args.date, args.tags...)

			case "StartSuggestedKeywordsWorkflows":
				args := tc.args.(struct {
					date       time.Time
					businesses []*KeywordSearchParams
				})
				result, err = mockService.StartSuggestedKeywordsWorkflows(ctx, args.date, args.businesses)

			case "GetDataForSEOLocationForBusiness":
				args := tc.args.(struct {
					businessID string
				})
				result, err = mockService.GetDataForSEOLocationForBusiness(ctx, args.businessID)

			default:
				t.Fatalf("Unsupported method: %s", tc.method)
			}

			// Validate the result
			assert.Equal(t, tc.expectedError, err)
			assert.Equal(t, tc.expectedResult, result)
		})
	}
}

// // MockService is a mock implementation of the Service type.
// type MockService struct {
// 	mock.Mock
// }

// func (m *MockService) getQueryResults(ctx context.Context, date time.Time) ([]string, error) {
// 	args := m.Called(ctx, date)
// 	return args.Get(0).([]string), args.Error(1)
// }

// func (m *MockService) getQueryStdResults(ctx context.Context) ([]string, error) {
// 	args := m.Called(ctx)
// 	return args.Get(0).([]string), args.Error(1)
// }
// func (m *MockService) StartSEOWorkflows(ctx context.Context, forceSERPWorkflows bool, forceKeywordInfoWorkflows bool, forceSuggestedKeywordWorkflows bool, ignoreDataLakeResults bool, date time.Time) error {
// 	args := m.Called(ctx, forceSERPWorkflows, forceKeywordInfoWorkflows, forceSuggestedKeywordWorkflows, ignoreDataLakeResults, date)
// 	return args.Error(0)
// }

// func TestStartSEOWorkflows(t *testing.T) {
// 	ctx := context.Background()
// 	date := time.Date(2024, 11, 30, 0, 0, 0, 0, time.UTC) // Last day of the month
// 	results := []string{"result1", "result2"}

// 	mockService := new(MockService)
// 	mockService.On("getQueryResults", ctx, date).Return(results, nil)
// 	mockService.On("getQueryStdResults", ctx).Return([]string{"stdResult1", "stdResult2"}, nil)
// 	mockService.On("StartSERPWorkflows", ctx, true, false, mock.Anything, date).Return(nil, nil)
// 	mockService.On("StartSuggestedKeywordsWorkflows", ctx, date, results).Return(nil, nil)
// 	mockService.On("StartKeywordInfoWorkflows", ctx, date, results).Return(nil, nil)

// 	err := mockService.StartSEOWorkflows(ctx, true, true, true, false, date)
// 	assert.NoError(t, err)

// 	mockService.AssertExpectations(t)
// }
// func TestStartSEOWorkflows_ErrorOnGetQueryResults(t *testing.T) {
// 	ctx := context.Background()
// 	date := time.Now()

// 	mockService := new(MockService)
// 	mockService.On("getQueryResults", ctx, date).Return(nil, errors.New("query results error"))

// 	err := mockService.StartSEOWorkflows(ctx, false, false, false, false, date)
// 	assert.Error(t, err)
// 	assert.True(t, strings.Contains(err.Error(), "query results error"))

// 	mockService.AssertExpectations(t)
// }

// func TestStartSEOWorkflows_NoTriggers(t *testing.T) {
// 	ctx := context.Background()
// 	date := time.Date(2024, 11, 29, 0, 0, 0, 0, time.UTC) // Not the last day, not Saturday

// 	mockService := new(MockService)
// 	mockService.On("getQueryResults", ctx, date).Return([]string{"result1"}, nil)

// 	err := mockService.StartSEOWorkflows(ctx, false, false, false, false, date)
// 	assert.NoError(t, err)

// 	// Verify no workflows are triggered
// 	mockService.AssertNotCalled(t, "StartSERPWorkflows", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
// 	mockService.AssertNotCalled(t, "StartSuggestedKeywordsWorkflows", mock.Anything, mock.Anything, mock.Anything)
// 	mockService.AssertNotCalled(t, "StartKeywordInfoWorkflows", mock.Anything, mock.Anything, mock.Anything)
// }

// func TestStartSEOWorkflows_LastDayOfMonth(t *testing.T) {
// 	ctx := context.Background()
// 	date := time.Date(2024, 11, 30, 0, 0, 0, 0, time.UTC) // Last day of the month
// 	results := []string{"result1"}

// 	mockService := new(MockService)
// 	mockService.On("getQueryResults", ctx, date).Return(results, nil)
// 	mockService.On("getQueryStdResults", ctx).Return([]string{"stdResult1"}, nil)
// 	mockService.On("StartSERPWorkflows", ctx, false, false, []string{"stdResult1"}, date).Return(nil, nil)
// 	mockService.On("StartSuggestedKeywordsWorkflows", ctx, date, results).Return(nil, nil)
// 	mockService.On("StartKeywordInfoWorkflows", ctx, date, results).Return(nil, nil)

// 	err := mockService.StartSEOWorkflows(ctx, false, false, false, false, date)
// 	assert.NoError(t, err)

// 	// Verify workflows are triggered correctly
// 	mockService.AssertCalled(t, "StartSERPWorkflows", ctx, false, false, []string{"stdResult1"}, date)
// 	mockService.AssertCalled(t, "StartSuggestedKeywordsWorkflows", ctx, date, results)
// 	mockService.AssertCalled(t, "StartKeywordInfoWorkflows", ctx, date, results)
// }
// func TestStartSEOWorkflows_ForceAll(t *testing.T) {
// 	ctx := context.Background()
// 	date := time.Date(2024, 11, 15, 0, 0, 0, 0, time.UTC) // Random non-edge date

// 	mockService := new(MockService)
// 	mockService.On("getQueryResults", ctx, date).Return([]string{"result1", "result2"}, nil)

// 	mockService.On("StartSEOWorkflows", ctx, true, true, true, false, date).Return(nil)

// 	err := mockService.StartSEOWorkflows(ctx, true, true, true, false, date)
// 	assert.NoError(t, err)

// 	mockService.AssertExpectations(t)
// }
