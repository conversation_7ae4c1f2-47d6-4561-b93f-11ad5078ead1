{"version": "0.1.20250526", "status_code": 20000, "status_message": "Ok.", "time": "0.1105 sec.", "cost": 0, "tasks_count": 1, "tasks_error": 0, "tasks": [{"id": "07021714-8599-0216-0000-4efb9a15bcce", "status_code": 20000, "status_message": "Ok.", "time": "0.0300 sec.", "cost": 0, "result_count": 1, "path": ["v3", "on_page", "summary", "07021714-8599-0216-0000-4efb9a15bcce"], "data": {"api": "on_page", "function": "summary", "target": "https://www.rabbitgroomers.com/", "max_crawl_pages": 1000, "load_resources": true, "enable_javascript": true, "enable_content_parsing": true, "calculate_keyword_density": true, "store_raw_html": true, "force_sitewide_checks": true, "custom_user_agent": "SEO-Crawler-Bot/1.0", "tag": "full-site-audit"}, "result": [{"crawl_progress": "finished", "crawl_status": {"max_crawl_pages": 1000, "pages_in_queue": 0, "pages_crawled": 8}, "crawl_gateway_address": "***************", "crawl_stop_reason": "empty_queue", "domain_info": {"name": "www.rabbitgroomers.com", "cms": "wordpress 6.8.1", "ip": "*************", "server": "website-pro/9.1.15", "crawl_start": "2025-07-02 14:14:43 +00:00", "crawl_end": "2025-07-02 14:15:15 +00:00", "extended_crawl_status": "no_errors", "ssl_info": {"valid_certificate": true, "certificate_issuer": "R10", "certificate_subject": "www.rabbitgroomers.com", "certificate_version": 3, "certificate_hash": "D361C05CB7C9DE2721520EDAC3D4D629A1313FA0", "certificate_expiration_date": "2025-09-15 07:48:44 +00:00"}, "checks": {"sitemap": false, "robots_txt": true, "start_page_deny_flag": false, "ssl": true, "http2": true, "test_canonicalization": false, "test_hidden_server_signature": true, "test_page_not_found": false, "test_directory_browsing": true, "test_https_redirect": true}, "total_pages": 8, "page_not_found_status_code": 200, "canonicalization_status_code": 404, "directory_browsing_status_code": 403, "www_redirect_status_code": null, "main_domain": "rabbitgroomers.com"}, "page_metrics": {"links_external": 7, "links_internal": 86, "duplicate_title": 0, "duplicate_description": 0, "duplicate_content": 2, "broken_links": 0, "broken_resources": 0, "links_relation_conflict": 0, "redirect_loop": 0, "onpage_score": 93.72, "non_indexable": 2, "checks": {"canonical": 7, "duplicate_meta_tags": 7, "no_description": 6, "frame": 0, "large_page_size": 0, "irrelevant_description": 0, "irrelevant_meta_keywords": 0, "is_https": 8, "is_http": 0, "title_too_long": 0, "low_content_rate": 7, "small_page_size": 0, "no_h1_tag": 2, "recursive_canonical": 0, "no_favicon": 0, "no_image_alt": 2, "no_image_title": 7, "seo_friendly_url": 7, "seo_friendly_url_characters_check": 7, "seo_friendly_url_dynamic_check": 7, "seo_friendly_url_keywords_check": 7, "seo_friendly_url_relative_length_check": 7, "title_too_short": 1, "no_content_encoding": 1, "high_waiting_time": 0, "high_loading_time": 0, "is_redirect": 1, "is_broken": 0, "is_4xx_code": 0, "is_5xx_code": 0, "is_www": 8, "no_doctype": 0, "no_encoding_meta_tag": 0, "high_content_rate": 0, "low_character_count": 3, "high_character_count": 0, "low_readability_rate": 0, "irrelevant_title": 2, "deprecated_html_tags": 0, "duplicate_title_tag": 0, "no_title": 0, "flash": 0, "lorem_ipsum": 0, "has_misspelling": null, "canonical_to_broken": 0, "canonical_to_redirect": 0, "has_links_to_redirects": 1, "is_orphan_page": 0, "has_meta_refresh_redirect": 0, "meta_charset_consistency": 7, "size_greater_than_3mb": 0, "has_html_doctype": 7, "https_to_http_links": 0, "has_render_blocking_resources": 7, "redirect_chain": 0, "canonical_chain": 0, "is_link_relation_conflict": 0}}}]}]}