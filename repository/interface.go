// Code generated by the Vendasta codegen tool (github.com/vendasta/codegen). DO NOT EDIT.
// This means that any changes you make to this file may be overwritten by a subsequent run of the codegen tool, so be cautious.
package aioauditresultsrepository

import (
	"context"

	"time"

	model "github.com/vendasta/listing-products/internal/seo/aioauditresults"
)

// MutateFunc is a function that mutates an existing AIOAuditResults.
// This function is executed inside of a transaction, so it should not make any RPCs or
// perform non-idempotent operations that affect something other than the AIOAuditResults.
//
// For example, sending a datadog tick or incrementing a counter in an instance cache inside of a
// MutateFunc is a bad idea, but incrementing a counter on the actual AIOAuditResults is ok,
// because the AIOAuditResults will be reset if the transaction needs to retry.
type MutateFunc func(ctx context.Context, aIOAuditResults *model.AIOAuditResults) error

// Repository reads from and writes to a storage device(s)
//
//go:generate mockgen -destination mock.go -source=interface.go -package=aioauditresultsrepository
type Repository interface {
	Reader
	Writer
}

// Reader reads AIOAuditResultss from storage
type Reader interface {
	// Get returns the AIOAuditResults specified by key, or a verrors.NotFound error if the AIOAuditResults is missing
	Get(ctx context.Context, key model.Key) (*model.AIOAuditResults, error)
	// GetMulti returns AIOAuditResultss or nils for missing entities in the same order that the keys are passed
	//
	// This method does not return a verrors.NotFound even if all the AIOAuditResultss are missing,
	// you need to handle the list of results.
	GetMulti(ctx context.Context, keys []model.Key) ([]*model.AIOAuditResults, error)

	// List returns a list of AIOAuditResultss that match the given column values.
	// Deleted AIOAuditResults are not included.
	// Key filters work like this: https://github.com/vendasta/vstore/blob/cdf5080ff/vstore/sdks/go/v1/api.go#L244-L253
	// An empty key filter means don't filter by key columns.
	// Non-key columns must be in a composite index to be filterable.
	List(ctx context.Context, keyFilter model.Key, filterByColumns FilterByIndexedValues, cursor string, pageSize int64) (results []*model.AIOAuditResults, nextCursor string, hasMore bool, err error)

	// ListByDate returns a list of AIOAuditResultss that match the given businessID and date range.
	ListByDate(ctx context.Context, businessID string, startDate, endDate time.Time) (results []*model.AIOAuditResults, err error)

	// ListByModel returns a list of AIOAuditResultss that match the given businessID and model.
	ListByModel(ctx context.Context, businessID, model string) (results []*model.AIOAuditResults, err error)
}

// Writer writes AIOAuditResultss to storage
type Writer interface {
	// Create will transactionally insert a AIOAuditResults into storage.
	// This method will return a verrors.AlreadyExists if the AIOAuditResults already exists.
	Create(ctx context.Context, aIOAuditResults *model.AIOAuditResults) error
	// Mutate will apply the given MutateFuncs to an existing AIOAuditResults specified by the given key.
	// Mutations are applied in order inside a transaction that can automatically retry on transient failures.
	Mutate(ctx context.Context, key model.Key, mutations ...MutateFunc) error
	// Upsert will apply the given MutateFuncs to a AIOAuditResults specified by the given key.
	//
	// If the AIOAuditResults exists, then this function works similarly to Mutate, but if the AIOAuditResults doesn't exist,
	// then this function will create it and apply the mutations to a zero valued AIOAuditResults.
	// This can be used when the distinction between inserting a new entity and updating an existing entity is unimportant.
	//
	// Mutations are applied in order inside a transaction that can automatically retry on transient failures.
	Upsert(ctx context.Context, key model.Key, mutations ...MutateFunc) error
}
