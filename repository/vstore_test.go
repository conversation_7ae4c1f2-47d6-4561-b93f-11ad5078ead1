// Code generated by the Vendasta codegen tool (github.com/vendasta/codegen). DO NOT EDIT.
// This means that any changes you make to this file may be overwritten by a subsequent run of the codegen tool, so be cautious.
package aioauditresultsrepository

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/vendasta/gosdks/verrors"
	model "github.com/vendasta/listing-products/internal/seo/aioauditresults"
	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
)

func initializeVStoreRepositoryStub(ctx context.Context) (Repository, vstore.Closer, error) {
	stub, closer, err := vstore.New("test", vstore.UseFake(true))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to initialize vstore stub: %s", err.Error())
	}
	err = stub.RegisterNamespace(ctx, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to register namespace in vstore stub: %s", err.Error())
	}
	err = model.RegisterKind(ctx, stub)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to register kind: %s", err.Error())
	}
	return New(stub), closer, nil
}

// TODO: It is intended that these fail when you first generate your code. To get them to pass, fix the validator!
func Test_AIOAuditResults_Repository_Create(t *testing.T) {
	type testCase struct {
		// The name of the test.
		// This should describe what should happen when the function being tested is invoked given some precondition
		name string

		// setup is run before each testCase
		// this function can be used to create a precondition in the repository that the case will test
		setup func(ctx context.Context, r Repository) error

		// The AIOAuditResults to create
		aIOAuditResults *model.AIOAuditResults
		// The error we expect Create to return
		expectedError error
	}
	cases := []*testCase{
		{
			name: "Create should save the AIOAuditResults to storage",
			aIOAuditResults: &model.AIOAuditResults{
				AuditID:    "auditID",
				BusinessID: "businessID",
			},
			expectedError: nil,
		},
		{
			name: "Create should raise an already exists error if a AIOAuditResults with the same key has already been created",
			setup: func(ctx context.Context, r Repository) error {
				return r.Create(ctx, &model.AIOAuditResults{
					AuditID:    "auditID",
					BusinessID: "businessID",
				})
			},
			aIOAuditResults: &model.AIOAuditResults{
				AuditID:    "auditID",
				BusinessID: "businessID",
			},
			expectedError: verrors.New(verrors.AlreadyExists, "AIOAuditResults already exists"),
		},
		{
			name: "Create should succeed if an existing AIOAuditResults with the same key has been marked deleted",
			setup: func(ctx context.Context, r Repository) error {
				return r.Create(ctx, &model.AIOAuditResults{
					AuditID:    "auditID",
					BusinessID: "businessID",
					Deleted:    time.Now().UTC(),
				})
			},
			aIOAuditResults: &model.AIOAuditResults{
				AuditID:    "auditID",
				BusinessID: "businessID",
			},
			expectedError: nil,
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			repo, closer, err := initializeVStoreRepositoryStub(ctx)
			if err != nil {
				t.Fatalf("error initializing repository: %s", err.Error())
			}
			defer closer()

			if c.setup != nil {
				err = c.setup(ctx, repo)
				if err != nil {
					t.Fatalf("failed to setup test: %s", err.Error())
				}
			}

			err = repo.Create(ctx, c.aIOAuditResults)
			assert.Equal(t, c.expectedError, err)
		})
	}
}

func Test_AIOAuditResults_Repository_Get(t *testing.T) {
	type testCase struct {
		// The name of the test.
		// This should describe what should happen when the function being tested is invoked given some precondition
		name string
		// The key to pass to Repository.Get
		key model.Key

		// setup is run before each testCase
		// this function can be used to create a precondition in the repository that the case will test
		setup func(ctx context.Context, r Repository) error
		// The BusinessID of the AIOAuditResults item we expect Get to return, empty string if no item is expected to be returned
		expectedBusinessID string
		// The AuditID of the AIOAuditResults item we expect Get to return, empty string if no item is expected to be returned
		expectedAuditID string
		// The error we expect Get to return
		expectedError error
	}
	cases := []*testCase{
		{
			name: "get should retrieve a AIOAuditResults from the repository",
			key:  model.NewKey("businessID", "auditID"),
			setup: func(ctx context.Context, r Repository) error {
				return r.Create(ctx, &model.AIOAuditResults{
					AuditID:    "auditID",
					BusinessID: "businessID",
				})
			},
			expectedBusinessID: "businessID",
			expectedAuditID:    "auditID",
			expectedError:      nil,
		},
		{
			name:          "get should return a not found error if the AIOAuditResults was not found",
			key:           model.NewKey("businessID", "auditID"),
			expectedError: verrors.New(verrors.NotFound, "AIOAuditResults not found"),
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			repo, closer, err := initializeVStoreRepositoryStub(ctx)
			if err != nil {
				t.Fatalf("error initializing repository: %s", err.Error())
			}
			defer closer()

			if c.setup != nil {
				err = c.setup(ctx, repo)
				if err != nil {
					t.Fatalf("failed to setup test: %s", err.Error())
				}
			}

			out, err := repo.Get(ctx, c.key)
			assert.Equal(t, c.expectedError, err)
			if err == nil {
				assert.Equal(t, c.expectedBusinessID, out.BusinessID)
				assert.Equal(t, c.expectedAuditID, out.AuditID)
			}
		})
	}
}

// TODO: It is intended that these fail when you first generate your code. To get them to pass, fix the validator!
func Test_AIOAuditResultsMutate(t *testing.T) {
	type testCase struct {
		// The name of the test.
		// This should describe what should happen when the function being tested is invoked given some precondition
		name string
		// The key to pass to Repository.Mutate
		key model.Key

		// setup is run before each testCase
		// this function can be used to create a precondition in the repository that the case will test
		setup func(ctx context.Context, r Repository) error

		// The mutation that will be executed on the entity during the test
		mutator MutateFunc

		// The error we expect Mutate to return
		expectedError error
	}
	cases := []*testCase{
		{
			name:          "should return an error if the AIOAuditResults does not exist",
			key:           model.NewKey("businessID", "auditID"),
			expectedError: verrors.New(verrors.NotFound, "AIOAuditResults not found"),
		},
		{
			name: "should return an error if the mutator returns an error",
			key:  model.NewKey("businessID", "auditID"),
			setup: func(ctx context.Context, r Repository) error {
				return r.Create(ctx, &model.AIOAuditResults{
					AuditID:    "auditID",
					BusinessID: "businessID",
				})
			},
			mutator: func(ctx context.Context, aIOAuditResults *model.AIOAuditResults) error {
				return verrors.New(verrors.Internal, "error applying mutation")
			},
			expectedError: verrors.New(verrors.Internal, "error applying mutation"),
		},
		{
			name: "should not return an error if the mutation is successfully applied",
			key:  model.NewKey("businessID", "auditID"),
			setup: func(ctx context.Context, r Repository) error {
				return r.Create(ctx, &model.AIOAuditResults{
					AuditID:    "auditID",
					BusinessID: "businessID",
				})
			},
			mutator: func(ctx context.Context, e *model.AIOAuditResults) error {
				e.Deleted = time.Date(2017, 12, 21, 0, 0, 0, 0, time.UTC)
				e.Created = time.Date(2017, 12, 21, 0, 0, 0, 0, time.UTC)
				e.Updated = time.Date(2017, 12, 21, 0, 0, 0, 0, time.UTC)
				return nil
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			repo, closer, err := initializeVStoreRepositoryStub(ctx)
			if err != nil {
				t.Fatalf("error initializing repository: %s", err.Error())
			}
			defer closer()

			if c.setup != nil {
				err = c.setup(ctx, repo)
				if err != nil {
					t.Fatalf("failed to setup test: %s", err.Error())
				}
			}

			actualErr := repo.Mutate(ctx, c.key, c.mutator)
			assert.Equal(t, c.expectedError, actualErr)
		})
	}
}

// TODO: It is intended that these fail when you first generate your code. To get them to pass, fix the validator!
func Test_AIOAuditResultsUpsert(t *testing.T) {
	type testCase struct {
		// The name of the test.
		// This should describe what should happen when the function being tested is invoked given some precondition
		name string
		// The key to pass to Repository.Upsert
		key model.Key
		// A mutation to pass to Repository.Upsert
		mutator MutateFunc

		// setup is run before each testCase
		// this function can be used to create a precondition in the repository that the case will test
		setup func(ctx context.Context, r Repository) error

		// The error we expect Upsert to return
		expectedError error
	}
	cases := []*testCase{
		{
			name: "should not return an error if the AIOAuditResults does not exist",
			key:  model.NewKey("businessID", "auditID"),
			mutator: func(ctx context.Context, m *model.AIOAuditResults) error {
				m.AuditID = "auditID"
				m.BusinessID = "businessID"
				return nil
			},
		},
		{
			name: "should return an error if the mutator returns an error while inserting an entity",
			key:  model.NewKey("businessID", "auditID"),
			mutator: func(ctx context.Context, aIOAuditResults *model.AIOAuditResults) error {
				return verrors.New(verrors.Internal, "error applying mutation")
			},
			expectedError: verrors.New(verrors.Internal, "error applying mutation"),
		},
		{
			name: "should return an error if the mutator returns an error while modifying an existing entity",
			key:  model.NewKey("businessID", "auditID"),
			setup: func(ctx context.Context, r Repository) error {
				return r.Create(ctx, &model.AIOAuditResults{
					AuditID:    "auditID",
					BusinessID: "businessID",
				})
			},
			mutator: func(ctx context.Context, aIOAuditResults *model.AIOAuditResults) error {
				return verrors.New(verrors.Internal, "error applying mutation")
			},
			expectedError: verrors.New(verrors.Internal, "error applying mutation"),
		},
		{
			name: "should not return an error if the mutation is successfully applied to an existing entity",
			key:  model.NewKey("businessID", "auditID"),
			setup: func(ctx context.Context, r Repository) error {
				return r.Create(ctx, &model.AIOAuditResults{
					AuditID:    "auditID",
					BusinessID: "businessID",
				})
			},
			mutator: func(ctx context.Context, e *model.AIOAuditResults) error {
				e.Deleted = time.Date(2017, 12, 21, 0, 0, 0, 0, time.UTC)
				e.Created = time.Date(2017, 12, 21, 0, 0, 0, 0, time.UTC)
				e.Updated = time.Date(2017, 12, 21, 0, 0, 0, 0, time.UTC)
				return nil
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			repo, closer, err := initializeVStoreRepositoryStub(ctx)
			if err != nil {
				t.Fatalf("error initializing repository: %s", err.Error())
			}
			defer closer()

			if c.setup != nil {
				err = c.setup(ctx, repo)
				if err != nil {
					t.Fatalf("failed to setup test: %s", err.Error())
				}
			}

			var mutators []MutateFunc
			if c.mutator != nil {
				mutators = append(mutators, c.mutator)
			}

			actualErr := repo.Upsert(ctx, c.key, mutators...)
			assert.Equal(t, c.expectedError, actualErr)
		})
	}
}

func Test_AIOAuditResults_Repository_ListByModel(t *testing.T) {
	type testCase struct {
		// The name of the test.
		// This should describe what should happen when the function being tested is invoked given some precondition
		name string

		// setup is run before each testCase
		// this function can be used to create a precondition in the repository that the case will test
		setup func(ctx context.Context, r Repository) error

		// The business ID to filter by
		businessID string
		// The AI model to filter by
		aiModel string

		// The expected number of results
		expectedCount int
		// The expected error
		expectedError error
	}

	cases := []*testCase{
		{
			name:          "ListByModel should return empty results when no records exist",
			businessID:    "business1",
			aiModel:       "gpt-4",
			expectedCount: 0,
			expectedError: nil,
		},
		{
			name:       "ListByModel should return results filtered by business ID and AI model",
			businessID: "business1",
			aiModel:    "gpt-4",
			setup: func(ctx context.Context, r Repository) error {
				// Create records with matching business ID and AI model
				err := r.Create(ctx, &model.AIOAuditResults{
					BusinessID: "business1",
					AuditID:    "audit1",
					AIModel:    "gpt-4",
					AuditDate:  "2024-01-15",
				})
				if err != nil {
					return err
				}
				err = r.Create(ctx, &model.AIOAuditResults{
					BusinessID: "business1",
					AuditID:    "audit2",
					AIModel:    "gpt-4",
					AuditDate:  "2024-01-16",
				})
				if err != nil {
					return err
				}
				// Create a record with different AI model (should not be returned)
				err = r.Create(ctx, &model.AIOAuditResults{
					BusinessID: "business1",
					AuditID:    "audit3",
					AIModel:    "claude-3",
					AuditDate:  "2024-01-17",
				})
				if err != nil {
					return err
				}
				// Create a record with different business ID (should not be returned)
				err = r.Create(ctx, &model.AIOAuditResults{
					BusinessID: "business2",
					AuditID:    "audit4",
					AIModel:    "gpt-4",
					AuditDate:  "2024-01-18",
				})
				if err != nil {
					return err
				}
				return nil
			},
			expectedCount: 2,
			expectedError: nil,
		},
		{
			name:       "ListByModel should not return deleted records",
			businessID: "business1",
			aiModel:    "gpt-4",
			setup: func(ctx context.Context, r Repository) error {
				// Create a record and then mark it as deleted
				err := r.Create(ctx, &model.AIOAuditResults{
					BusinessID: "business1",
					AuditID:    "audit1",
					AIModel:    "gpt-4",
					AuditDate:  "2024-01-15",
				})
				if err != nil {
					return err
				}
				// Mark it as deleted
				err = r.Mutate(ctx, model.NewKey("business1", "audit1"), func(ctx context.Context, m *model.AIOAuditResults) error {
					m.Deleted = time.Now().UTC()
					return nil
				})
				return err
			},
			expectedCount: 0,
			expectedError: nil,
		},
		{
			name:          "ListByModel should handle empty business ID",
			businessID:    "",
			aiModel:       "gpt-4",
			expectedCount: 0,
			expectedError: nil,
		},
		{
			name:       "ListByModel should handle empty AI model",
			businessID: "business1",
			aiModel:    "",
			setup: func(ctx context.Context, r Repository) error {
				// Create a record with empty AI model
				return r.Create(ctx, &model.AIOAuditResults{
					BusinessID: "business1",
					AuditID:    "audit1",
					AIModel:    "",
					AuditDate:  "2024-01-15",
				})
			},
			expectedCount: 1,
			expectedError: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			repo, closer, err := initializeVStoreRepositoryStub(ctx)
			if err != nil {
				t.Fatalf("error initializing repository: %s", err.Error())
			}
			defer closer()

			if c.setup != nil {
				err = c.setup(ctx, repo)
				if err != nil {
					t.Fatalf("failed to setup test: %s", err.Error())
				}
			}

			results, err := repo.ListByModel(ctx, c.businessID, c.aiModel)

			// Check error
			assert.Equal(t, c.expectedError, err)

			// Check result count
			assert.Equal(t, c.expectedCount, len(results))

			// If we expect results, verify they match the filter criteria
			if err == nil && len(results) > 0 {
				for _, result := range results {
					assert.Equal(t, c.businessID, result.BusinessID)
					assert.Equal(t, c.aiModel, result.AIModel)
					assert.True(t, result.Deleted.IsZero(), "Result should not be deleted")
				}
			}
		})
	}
}

func Test_AIOAuditResults_Repository_ListByDate(t *testing.T) {
	type testCase struct {
		// The name of the test.
		// This should describe what should happen when the function being tested is invoked given some precondition
		name string

		// setup is run before each testCase
		// this function can be used to create a precondition in the repository that the case will test
		setup func(ctx context.Context, r Repository) error

		// The business ID to filter by
		businessID string
		// The start date for the range
		startDate time.Time
		// The end date for the range
		endDate time.Time

		// The expected number of results
		expectedCount int
		// The expected error
		expectedError error
	}

	cases := []*testCase{
		{
			name:          "ListByDate should return empty results when no records exist",
			businessID:    "business1",
			startDate:     time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
			endDate:       time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC),
			expectedCount: 0,
			expectedError: nil,
		},
		{
			name:       "ListByDate should not return deleted records",
			businessID: "business1",
			startDate:  time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
			endDate:    time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC),
			setup: func(ctx context.Context, r Repository) error {
				// Create a record and then mark it as deleted
				err := r.Create(ctx, &model.AIOAuditResults{
					BusinessID: "business1",
					AuditID:    "audit1",
					AuditDate:  "2024-01-16",
				})
				if err != nil {
					return err
				}
				// Mark it as deleted
				err = r.Mutate(ctx, model.NewKey("business1", "audit1"), func(ctx context.Context, m *model.AIOAuditResults) error {
					m.Deleted = time.Now().UTC()
					return nil
				})
				return err
			},
			expectedCount: 0,
			expectedError: nil,
		},
		{
			name:          "ListByDate should handle empty business ID",
			businessID:    "",
			startDate:     time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
			endDate:       time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC),
			expectedCount: 0,
			expectedError: nil,
		},
		{
			name:       "ListByDate should handle edge case where start date equals end date",
			businessID: "business1",
			startDate:  time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
			endDate:    time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
			setup: func(ctx context.Context, r Repository) error {
				// Create a record for the specific date
				return r.Create(ctx, &model.AIOAuditResults{
					BusinessID: "business1",
					AuditID:    "audit1",
					AuditDate:  "2024-01-15",
				})
			},
			expectedCount: 0, // No results because start == end (exclusive range)
			expectedError: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			repo, closer, err := initializeVStoreRepositoryStub(ctx)
			if err != nil {
				t.Fatalf("error initializing repository: %s", err.Error())
			}
			defer closer()

			if c.setup != nil {
				err = c.setup(ctx, repo)
				if err != nil {
					t.Fatalf("failed to setup test: %s", err.Error())
				}
			}

			results, err := repo.ListByDate(ctx, c.businessID, c.startDate, c.endDate)

			// Check error
			assert.Equal(t, c.expectedError, err)

			// Check result count
			assert.Equal(t, c.expectedCount, len(results))

			// If we expect results, verify they match the filter criteria
			if err == nil && len(results) > 0 {
				for _, result := range results {
					assert.Equal(t, c.businessID, result.BusinessID)
					assert.True(t, result.Deleted.IsZero(), "Result should not be deleted")

					// Verify the audit date is within the expected range
					auditDate, parseErr := time.Parse("2006-01-02", result.AuditDate)
					assert.NoError(t, parseErr, "Audit date should be parseable")
					assert.True(t, auditDate.After(c.startDate) || auditDate.Equal(c.startDate),
						"Audit date should be >= start date")
					assert.True(t, auditDate.Before(c.endDate),
						"Audit date should be < end date")
				}
			}
		})
	}
}
