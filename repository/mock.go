// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go
//
// Generated by this command:
//
//	mockgen -destination mock.go -source=interface.go -package=aioauditresultsrepository
//

// Package aioauditresultsrepository is a generated GoMock package.
package aioauditresultsrepository

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	aioauditresults "github.com/vendasta/listing-products/internal/seo/aioauditresults"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, aIOAuditResults *aioauditresults.AIOAuditResults) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, aIOAuditResults)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, aIOAuditResults any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, aIOAuditResults)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, key aioauditresults.Key) (*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, key)
	ret0, _ := ret[0].(*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, key)
}

// GetMulti mocks base method.
func (m *MockRepository) GetMulti(ctx context.Context, keys []aioauditresults.Key) ([]*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMulti", ctx, keys)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMulti indicates an expected call of GetMulti.
func (mr *MockRepositoryMockRecorder) GetMulti(ctx, keys any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMulti", reflect.TypeOf((*MockRepository)(nil).GetMulti), ctx, keys)
}

// List mocks base method.
func (m *MockRepository) List(ctx context.Context, keyFilter aioauditresults.Key, filterByColumns FilterByIndexedValues, cursor string, pageSize int64) ([]*aioauditresults.AIOAuditResults, string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, keyFilter, filterByColumns, cursor, pageSize)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// List indicates an expected call of List.
func (mr *MockRepositoryMockRecorder) List(ctx, keyFilter, filterByColumns, cursor, pageSize any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockRepository)(nil).List), ctx, keyFilter, filterByColumns, cursor, pageSize)
}

// ListByDate mocks base method.
func (m *MockRepository) ListByDate(ctx context.Context, businessID string, startDate, endDate time.Time) ([]*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByDate", ctx, businessID, startDate, endDate)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByDate indicates an expected call of ListByDate.
func (mr *MockRepositoryMockRecorder) ListByDate(ctx, businessID, startDate, endDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByDate", reflect.TypeOf((*MockRepository)(nil).ListByDate), ctx, businessID, startDate, endDate)
}

// ListByModel mocks base method.
func (m *MockRepository) ListByModel(ctx context.Context, businessID, model string) ([]*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByModel", ctx, businessID, model)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByModel indicates an expected call of ListByModel.
func (mr *MockRepositoryMockRecorder) ListByModel(ctx, businessID, model any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByModel", reflect.TypeOf((*MockRepository)(nil).ListByModel), ctx, businessID, model)
}

// Mutate mocks base method.
func (m *MockRepository) Mutate(ctx context.Context, key aioauditresults.Key, mutations ...MutateFunc) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, key}
	for _, a := range mutations {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Mutate", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Mutate indicates an expected call of Mutate.
func (mr *MockRepositoryMockRecorder) Mutate(ctx, key any, mutations ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, key}, mutations...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Mutate", reflect.TypeOf((*MockRepository)(nil).Mutate), varargs...)
}

// Upsert mocks base method.
func (m *MockRepository) Upsert(ctx context.Context, key aioauditresults.Key, mutations ...MutateFunc) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, key}
	for _, a := range mutations {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Upsert", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockRepositoryMockRecorder) Upsert(ctx, key any, mutations ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, key}, mutations...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockRepository)(nil).Upsert), varargs...)
}

// MockReader is a mock of Reader interface.
type MockReader struct {
	ctrl     *gomock.Controller
	recorder *MockReaderMockRecorder
	isgomock struct{}
}

// MockReaderMockRecorder is the mock recorder for MockReader.
type MockReaderMockRecorder struct {
	mock *MockReader
}

// NewMockReader creates a new mock instance.
func NewMockReader(ctrl *gomock.Controller) *MockReader {
	mock := &MockReader{ctrl: ctrl}
	mock.recorder = &MockReaderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReader) EXPECT() *MockReaderMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockReader) Get(ctx context.Context, key aioauditresults.Key) (*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, key)
	ret0, _ := ret[0].(*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockReaderMockRecorder) Get(ctx, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockReader)(nil).Get), ctx, key)
}

// GetMulti mocks base method.
func (m *MockReader) GetMulti(ctx context.Context, keys []aioauditresults.Key) ([]*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMulti", ctx, keys)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMulti indicates an expected call of GetMulti.
func (mr *MockReaderMockRecorder) GetMulti(ctx, keys any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMulti", reflect.TypeOf((*MockReader)(nil).GetMulti), ctx, keys)
}

// List mocks base method.
func (m *MockReader) List(ctx context.Context, keyFilter aioauditresults.Key, filterByColumns FilterByIndexedValues, cursor string, pageSize int64) ([]*aioauditresults.AIOAuditResults, string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, keyFilter, filterByColumns, cursor, pageSize)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// List indicates an expected call of List.
func (mr *MockReaderMockRecorder) List(ctx, keyFilter, filterByColumns, cursor, pageSize any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReader)(nil).List), ctx, keyFilter, filterByColumns, cursor, pageSize)
}

// ListByDate mocks base method.
func (m *MockReader) ListByDate(ctx context.Context, businessID string, startDate, endDate time.Time) ([]*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByDate", ctx, businessID, startDate, endDate)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByDate indicates an expected call of ListByDate.
func (mr *MockReaderMockRecorder) ListByDate(ctx, businessID, startDate, endDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByDate", reflect.TypeOf((*MockReader)(nil).ListByDate), ctx, businessID, startDate, endDate)
}

// ListByModel mocks base method.
func (m *MockReader) ListByModel(ctx context.Context, businessID, model string) ([]*aioauditresults.AIOAuditResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByModel", ctx, businessID, model)
	ret0, _ := ret[0].([]*aioauditresults.AIOAuditResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByModel indicates an expected call of ListByModel.
func (mr *MockReaderMockRecorder) ListByModel(ctx, businessID, model any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByModel", reflect.TypeOf((*MockReader)(nil).ListByModel), ctx, businessID, model)
}

// MockWriter is a mock of Writer interface.
type MockWriter struct {
	ctrl     *gomock.Controller
	recorder *MockWriterMockRecorder
	isgomock struct{}
}

// MockWriterMockRecorder is the mock recorder for MockWriter.
type MockWriterMockRecorder struct {
	mock *MockWriter
}

// NewMockWriter creates a new mock instance.
func NewMockWriter(ctrl *gomock.Controller) *MockWriter {
	mock := &MockWriter{ctrl: ctrl}
	mock.recorder = &MockWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWriter) EXPECT() *MockWriterMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockWriter) Create(ctx context.Context, aIOAuditResults *aioauditresults.AIOAuditResults) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, aIOAuditResults)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockWriterMockRecorder) Create(ctx, aIOAuditResults any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockWriter)(nil).Create), ctx, aIOAuditResults)
}

// Mutate mocks base method.
func (m *MockWriter) Mutate(ctx context.Context, key aioauditresults.Key, mutations ...MutateFunc) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, key}
	for _, a := range mutations {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Mutate", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Mutate indicates an expected call of Mutate.
func (mr *MockWriterMockRecorder) Mutate(ctx, key any, mutations ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, key}, mutations...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Mutate", reflect.TypeOf((*MockWriter)(nil).Mutate), varargs...)
}

// Upsert mocks base method.
func (m *MockWriter) Upsert(ctx context.Context, key aioauditresults.Key, mutations ...MutateFunc) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, key}
	for _, a := range mutations {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Upsert", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockWriterMockRecorder) Upsert(ctx, key any, mutations ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, key}, mutations...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockWriter)(nil).Upsert), varargs...)
}
