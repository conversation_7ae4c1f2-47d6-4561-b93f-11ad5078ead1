// Code generated by the Vendasta codegen tool (github.com/vendasta/codegen). DO NOT EDIT.
// This means that any changes you make to this file may be overwritten by a subsequent run of the codegen tool, so be cautious.
package aioauditresultsrepository

import (
	"context"
	"time"

	"github.com/vendasta/gosdks/verrors"
	model "github.com/vendasta/listing-products/internal/seo/aioauditresults"
)

// SetAiModel sets the aiModel equal to the value passed in
func SetAiModel(aiModel string) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		e.AIModel = aiModel
		return nil
	}
}

// SetBrandName sets the brandName equal to the value passed in
func SetBrandName(brandName string) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		e.BrandName = brandName
		return nil
	}
}

// SetWebsiteURL sets the websiteURL equal to the value passed in
func SetWebsiteURL(websiteURL string) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		e.WebsiteURL = websiteURL
		return nil
	}
}

// SetAuditDate sets the auditDate equal to the value passed in
func SetAuditDate(auditDate string) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		e.AuditDate = auditDate
		return nil
	}
}

// SetStartDate sets the startDate equal to the value passed in
func SetStartDate(startDate time.Time) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		e.StartDate = startDate
		return nil
	}
}

// SetTotalPages sets the totalPages equal to the value passed in
func SetTotalPages(totalPages int64) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		e.TotalPages = totalPages
		return nil
	}
}

// SetAuditStatus sets the auditStatus equal to the value passed in
func SetAuditStatus(auditStatus string) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		e.AuditStatus = auditStatus
		return nil
	}
}

// SetAuditSummary sets the auditSummary equal to the value passed in
func SetAuditSummary(auditSummary string) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		e.AuditSummary = auditSummary
		return nil
	}
}

// SetAuditPages sets the auditPages equal to the value passed in
func SetAuditPages(auditPages []*model.AuditPageData) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		e.AuditPages = auditPages
		return nil
	}
}

// SetAuditScoreResults sets the auditScoreResults equal to the value passed in
func SetAuditScoreResults(auditScoreResults []*model.AuditScoreResults) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		e.AuditScoreResults = auditScoreResults
		return nil
	}
}

// SetDeleted marks the AIOAuditResults deleted. An error is raised if the AIOAuditResults is already deleted.
func SetDeleted(deleted time.Time) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		if !e.Deleted.IsZero() {
			return verrors.New(verrors.NotFound, "AIOAuditResults not found")
		}
		e.Deleted = deleted
		return nil
	}
}

func SetAIOAuditResults(aioAuditResults *model.AIOAuditResults) MutateFunc {
	return func(ctx context.Context, e *model.AIOAuditResults) error {
		*e = *aioAuditResults
		return nil
	}
}
