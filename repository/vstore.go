// Code generated by the Vendasta codegen tool (github.com/vendasta/codegen). DO NOT EDIT.
// This means that any changes you make to this file may be overwritten by a subsequent run of the codegen tool, so be cautious.
package aioauditresultsrepository

import (
	"context"
	"time"

	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/validation"
	"github.com/vendasta/gosdks/verrors"
	model "github.com/vendasta/listing-products/internal/seo/aioauditresults"
	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
)

// New returns a new VStoreRepository object
func New(client vstore.Interface) *VStoreRepository {
	return &VStoreRepository{client: client}
}

// VStoreRepository implements Repository using VStore as a storage device
type VStoreRepository struct {
	client vstore.Interface
}

// Create will transactionally insert a AIOAuditResults into storage.
// <PERSON>reate sets the Created time on the AIOAuditResults automatically.
// Create sets the Updated time on the AIOAuditResults automatically.
// This method will return a verrors.AlreadyExists if the AIOAuditResults already exists.
func (r *VStoreRepository) Create(ctx context.Context, x *model.AIOAuditResults) error {
	err := r.client.ReadWriteTransaction(ctx, func(ctx context.Context, t vstore.Transaction) error {
		o := &model.AIOAuditResults{}
		err := t.Get(ctx, model.NewKey(x.BusinessID, x.AuditID).KeySet(), o)
		if err == vstore.ErrNoSuchEntity {
			x.Created = time.Now().UTC()
			x.Updated = x.Created
			err = validation.NewValidator().Rule(x).Validate()
			if err != nil {
				logging.Debugf(ctx, "Validation error during creation of AIOAuditResults:%s", err.Error())
				return err
			}
			return t.Insert(model.Kind, x)
		}
		if err != nil {
			return err
		}
		// overwriting a AIOAuditResults that isn't marked deleted is not allowed
		if o.Deleted.IsZero() {
			return vstore.ErrEntityAlreadyExists
		}
		x.Created = time.Now().UTC()
		x.Updated = x.Created
		err = validation.NewValidator().Rule(x).Validate()
		if err != nil {
			logging.Debugf(ctx, "Validation error during creation of AIOAuditResults:%s", err.Error())
			return err
		}
		return t.Replace(model.Kind, x)
	})
	if err == vstore.ErrEntityAlreadyExists {
		return verrors.New(verrors.AlreadyExists, "AIOAuditResults already exists")
	}
	return err
}

// Get returns the AIOAuditResults specified by key, or a verrors.NotFound error if the AIOAuditResults is missing
func (r *VStoreRepository) Get(ctx context.Context, key model.Key) (*model.AIOAuditResults, error) {
	e := &model.AIOAuditResults{}
	err := r.client.Get(ctx, key.KeySet(), e)
	if err == vstore.ErrNoSuchEntity {
		return nil, verrors.New(verrors.NotFound, "AIOAuditResults not found")
	}
	if err != nil {
		return nil, err
	}

	if !e.Deleted.IsZero() {
		return nil, verrors.New(verrors.NotFound, "AIOAuditResults not found")
	}

	return e, nil
}

// GetMulti returns AIOAuditResultss or nils for missing entities in the same order that the keys are passed
//
// This method does not return a verrors.NotFound even if all the AIOAuditResultss are missing,
// you need to handle the list of results.
func (r *VStoreRepository) GetMulti(ctx context.Context, keys []model.Key) ([]*model.AIOAuditResults, error) {
	ks := make([]vstore.KeySet, len(keys))
	for i := range keys {
		ks[i] = keys[i].KeySet()
	}
	e := make([]*model.AIOAuditResults, len(keys))
	err := r.client.GetMulti(ctx, ks, e)
	if err != nil {
		return nil, err
	}

	// remove any AIOAuditResultss marked deleted from the results
	for i, o := range e {
		if o == nil {
			continue
		}
		if !o.Deleted.IsZero() {
			e[i] = nil
		}
	}

	return e, nil
}

// FilterByIndexedValues lets you filter by values in each index, without copy-pasta.
type FilterByIndexedValues interface {
	addToQuery(*vstore.Query) *vstore.Query
}

type filterByValue struct {
	addToQuery bool
	value      interface{}
}

// FilterByNothing is a placeholder so your freshly-generated code passes lint,
// and gives you an example to change in your service's List()
func FilterByNothing() FilterByIndexedValues {
	return &filterByNothing{}
}

type filterByNothing struct{}

func (a *filterByNothing) addToQuery(q *vstore.Query) *vstore.Query {
	return q
}

// FilterByBusinessIDAuditDateDeletedIndex lets you filter by this index with auto-complete in your IDE
func FilterByBusinessIDAuditDateDeletedIndex() *AIOAuditResultsFilterByBusinessIDAuditDateDeletedIndex {
	return &AIOAuditResultsFilterByBusinessIDAuditDateDeletedIndex{}
}

// AIOAuditResultsFilterByBusinessIDAuditDateDeletedIndex see FilterByBusinessIDAuditDateDeletedIndex
type AIOAuditResultsFilterByBusinessIDAuditDateDeletedIndex struct {
	business_id filterByValue
	audit_date  filterByValue
}

// BusinessID filter by this indexed column with auto-complete in your IDE
func (a *AIOAuditResultsFilterByBusinessIDAuditDateDeletedIndex) BusinessID(value interface{}) *AIOAuditResultsFilterByBusinessIDAuditDateDeletedIndex {
	a.business_id.addToQuery = true
	a.business_id.value = value
	return a
}

// AuditDate filter by this indexed column with auto-complete in your IDE
func (a *AIOAuditResultsFilterByBusinessIDAuditDateDeletedIndex) AuditDate(value interface{}) *AIOAuditResultsFilterByBusinessIDAuditDateDeletedIndex {
	a.audit_date.addToQuery = true
	a.audit_date.value = value
	return a
}

func (a *AIOAuditResultsFilterByBusinessIDAuditDateDeletedIndex) addToQuery(q *vstore.Query) *vstore.Query {
	q.Index("business_id_audit_date_deleted_index")

	if a.business_id.addToQuery {
		q.Where("business_id", a.business_id.value)
	}

	if a.audit_date.addToQuery {
		q.Where("audit_date", a.audit_date.value)
	}

	return q
}

// FilterByBusinessIDDeletedIndex lets you filter by this index with auto-complete in your IDE
func FilterByBusinessIDDeletedIndex() *AIOAuditResultsFilterByBusinessIDDeletedIndex {
	return &AIOAuditResultsFilterByBusinessIDDeletedIndex{}
}

// AIOAuditResultsFilterByBusinessIDDeletedIndex see FilterByBusinessIDDeletedIndex
type AIOAuditResultsFilterByBusinessIDDeletedIndex struct {
	business_id filterByValue
}

// BusinessID filter by this indexed column with auto-complete in your IDE
func (a *AIOAuditResultsFilterByBusinessIDDeletedIndex) BusinessID(value interface{}) *AIOAuditResultsFilterByBusinessIDDeletedIndex {
	a.business_id.addToQuery = true
	a.business_id.value = value
	return a
}

func (a *AIOAuditResultsFilterByBusinessIDDeletedIndex) addToQuery(q *vstore.Query) *vstore.Query {
	q.Index("business_id_deleted_index")

	if a.business_id.addToQuery {
		q.Where("business_id", a.business_id.value)
	}

	return q
}

// List see repository.Reader interface.
func (r *VStoreRepository) List(ctx context.Context, keyFilter model.Key, filterByColumns FilterByIndexedValues, cursor string, pageSize int64) (results []*model.AIOAuditResults, nextCursor string, hasMore bool, err error) {
	query := vstore.NewQuery(model.Kind)
	if pageSize < 1 {
		return nil, "", false,
			verrors.New(verrors.InvalidArgument, "must give page-size for %s.List", model.Kind)
	}
	query.Limit(pageSize)

	if keyFilter != (model.Key{}) {
		query.KeyFilter(keyFilter.KeySet().Keys())
	}
	filterByColumns.addToQuery(query)
	query.Where("deleted", nil)

	if cursor != "" {
		query.Cursor(cursor)
	}

	resultsIterator, err := r.client.Query(ctx, query)
	if err != nil {
		return nil, "", false, err
	}
	err = resultsIterator.Do(func(result vstore.Result) error {
		m := &model.AIOAuditResults{}
		err := result.ToStruct(m)
		if err != nil {
			return err
		}
		results = append(results, m)
		return nil
	})
	if err != nil {
		return nil, "", false, err
	}

	return results, resultsIterator.Cursor(), resultsIterator.HasMore(), nil
}

// Upsert will apply the given MutateFuncs to a AIOAuditResults specified by the given key.
//
// If the AIOAuditResults exists, then this function works similarly to Mutate, but if the AIOAuditResults doesn't exist,
// then this function will create it and apply the mutations to a zero valued AIOAuditResults.
// This can be used when the distinction between inserting a new entity and updating an existing entity is unimportant.
// Upsert sets the Created time on the AIOAuditResults automatically.
// Upsert sets the Updated time on the AIOAuditResults automatically.
// Mutations are applied in order inside a transaction that can automatically retry on transient failures.
func (r *VStoreRepository) Upsert(ctx context.Context, key model.Key, mutations ...MutateFunc) error {
	return r.client.ReadWriteTransaction(ctx, func(ctx context.Context, t vstore.Transaction) error {
		e := &model.AIOAuditResults{}
		err := t.Get(ctx, key.KeySet(), e)
		if err != nil && err != vstore.ErrNoSuchEntity {
			return err
		}

		now := time.Now().UTC()

		if err == vstore.ErrNoSuchEntity {
			e.Created = now
		}

		// Upsert should not mutate a AIOAuditResults that is marked deleted
		// Instead we mutate a zero value struct, resetting the values of the deleted row
		if !e.Deleted.IsZero() {
			e = &model.AIOAuditResults{}
			e.Created = now
		}

		e.Updated = now
		e.BusinessID = key.BusinessID
		e.AuditID = key.AuditID
		for _, m := range mutations {
			err = m(ctx, e)
			if err != nil {
				return err
			}
		}

		err = validation.NewValidator().
			Rule(e).
			Validate()
		if err != nil {
			logging.Debugf(ctx, "Validation error during creation of AIOAuditResults:%s", err.Error())
			return err
		}

		return t.Replace(model.Kind, e)
	})
}

// Mutate will apply the given MutateFuncs to an existing AIOAuditResults specified by the given key.
// Mutations are applied in order inside a transaction that can automatically retry on transient failures.
// Mutate sets the Updated time on the AIOAuditResults automatically.
// Mutate will return a NotFound error if the entity is marked deleted.
func (r *VStoreRepository) Mutate(ctx context.Context, key model.Key, mutations ...MutateFunc) error {
	return r.client.ReadWriteTransaction(ctx, func(ctx context.Context, t vstore.Transaction) error {
		e := &model.AIOAuditResults{}
		err := t.Get(ctx, key.KeySet(), e)
		if err == vstore.ErrNoSuchEntity {
			return verrors.New(verrors.NotFound, "AIOAuditResults not found")
		}
		if err != nil {
			return err
		}

		// mutating a AIOAuditResults that is marked deleted is not allowed
		if !e.Deleted.IsZero() {
			return verrors.New(verrors.NotFound, "AIOAuditResults not found")
		}

		e.Updated = time.Now().UTC()
		for _, m := range mutations {
			err = m(ctx, e)
			if err != nil {
				return err
			}
		}

		err = validation.NewValidator().
			Rule(e).
			Validate()
		if err != nil {
			logging.Debugf(ctx, "Validation error during creation of AIOAuditResults:%s", err.Error())
			return err
		}

		return t.Update(model.Kind, e)
	})
}

func (r *VStoreRepository) ListByDate(ctx context.Context, businessID string, startDate, endDate time.Time) (results []*model.AIOAuditResults, err error) {
	// Use the standard List method with the appropriate filter
	// This approach is more reliable than trying to use WhereColumnInList with composite indexes

	// Create a filter that matches the index structure - use the simpler filter that only requires business_id
	filter := FilterByBusinessIDDeletedIndex().
		BusinessID(businessID)

	// Use the List method with a large page size to get all results
	allResults, _, _, err := r.List(ctx, model.NewKey(businessID, ""), filter, "", 1000)
	if err != nil {
		return nil, err
	}

	// Filter results by date range on the client side
	var filteredResults []*model.AIOAuditResults
	for _, result := range allResults {
		auditDate, parseErr := time.Parse("2006-01-02", result.AuditDate)
		if parseErr != nil {
			continue // Skip records with invalid dates
		}

		// Check if the date is within the range (inclusive start, exclusive end)
		if (auditDate.After(startDate) || auditDate.Equal(startDate)) && (auditDate.Before(endDate) || auditDate.Equal(endDate)) {
			filteredResults = append(filteredResults, result)
		}
	}

	return filteredResults, nil
}

func (r *VStoreRepository) ListByModel(ctx context.Context, businessID, aiModel string) (results []*model.AIOAuditResults, err error) {
	query := vstore.NewQuery(model.Kind).Index(model.BusinessIDDeletedIndex)
	query.Limit(400)

	query.Where("business_id", businessID)
	query.Where("ai_model", aiModel)
	query.Where("deleted", nil)

	resultsIterator, err := r.client.Query(ctx, query)
	if err != nil {
		return nil, err
	}
	err = resultsIterator.Do(func(result vstore.Result) error {
		m := &model.AIOAuditResults{}
		err := result.ToStruct(m)
		if err != nil {
			return err
		}
		results = append(results, m)
		return nil
	})
	if err != nil {
		return nil, err
	}

	return results, nil
}
