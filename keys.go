// Code generated by the Vendasta codegen tool (github.com/vendasta/codegen). DO NOT EDIT.
// This means that any changes you make to this file may be overwritten by a subsequent run of the codegen tool, so be cautious.

package aioauditresults

import (
	"errors"
	"strings"

	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
)

const keySeparator = string(rune(31))

// Key targets a single AIOAuditResults entity
type Key struct {
	BusinessID string
	AuditID    string
}

// KeySet returns the vstore.KeySet representation of this key for use with the VStore SDK
func (k Key) KeySet() vstore.KeySet {
	return vstore.NewKeySet(Kind, []string{k.BusinessID, k.AuditID})
}

// String returns the key formatted as a string. This format is not namespaced by kind - see KeySet.
// Keys serialized this way can be deserialized with NewKeyFromString
func (k Key) String() string {
	return strings.Join([]string{k.BusinessID, k.AuditID}, keySeparator)
}

// NewKey returns a new Key
func NewKey(businessID string, auditID string) Key {
	return Key{
		BusinessID: businessID,
		AuditID:    auditID,
	}
}

var ErrInvalidStringForKey = errors.New("the provided string had the wrong number of parts and could not form a valid key")

// NewKeyFromString will deserialize a string that has been serialized by Key.String() back into a Key struct
func NewKeyFromString(s string) (Key, error) {
	parts := strings.Split(s, keySeparator)
	if len(parts) != 2 {
		return Key{}, ErrInvalidStringForKey
	}
	return Key{
		BusinessID: parts[0],
		AuditID:    parts[1],
	}, nil
}
