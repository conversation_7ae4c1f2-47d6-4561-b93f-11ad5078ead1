{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "declaration": true, "types": [], "lib": ["dom", "es2018"]}, "angularCompilerOptions": {"skipTemplateCodegen": true, "strictMetadataEmit": true, "fullTemplateTypeCheck": true, "strictInjectionParameters": true, "flatModuleId": "AUTOGENERATED", "flatModuleOutFile": "AUTOGENERATED", "enableResourceInlining": true}, "exclude": ["src/test-setup.ts", "**/*.spec.ts", "jest.config.ts"], "include": ["**/*.ts"]}