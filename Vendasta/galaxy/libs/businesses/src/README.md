# Business Profile Component

This provides a component for managing business profile information.

## Requirements

To setup the component, you will need to: 

Install the business-profile package to your project

```bash
npm install @vendasta/business-profile --save
```

You must also install the angular google maps library in order to provide the google maps credentials -- see https://angular-maps.com/api-docs/ for the configuration options.

```bash
npm install @agm/core--save
```

Next, you will need to import and setup your navigation module (generally in `app.module.ts`). Also

```typescript
import { BusinessProfileModule } from "@vendasta/business-profile";
import { AgmCoreModule } from "@agm/core";
…
@NgModule({
  …
  imports: [
    …, BusinessProfileModule, AgmCoreModule.forRoot({clientId: '<YOUR CLIENT ID>'}), …
  ]
})
```

### External Requirements

The business profile requires the following vendasta packages to be implemented by the project:

- `AccountGroupSDK` (https://www.npmjs.com/package/@vendasta/account-group-sdk)
- `CountryStateService` (https://www.npmjs.com/package/@vendasta/country-state-service)
- `TaxonomyService` (https://www.npmjs.com/package/@vendasta/taxonomy-service)

Please follow their instructions on how to set those packages up.


## Usage:

Simply pass through the accountGroupId to the component.

**IMPORTANT: The component temporarily also requires whitelabel data be provided to it. This should become inferred in future iterations.** 

```html
<business-profile [accountGroupId]="accountGroupId" 
    [productNames]="{'RM': 'Rep Man'}" 
    [editDisabled]="true" 
    [maxCompetitors]="4" 
    [maxShareOfVoiceKeywords]="2"
    (saved)="handleOnSave()"
></business-profile>
```
