/* eslint-disable no-undef, @typescript-eslint/no-unused-vars, no-extra-boolean-cast */
import { __decorate } from 'tslib';
import { Injectable } from '@angular/core';
import { of } from 'rxjs';
import {
  DurationPeriod,
  ListTagsResponse,
  Order,
  Origin,
  PagedResponse,
  Status,
  ActivationStatus,
  ProductActivation,
  LineItem,
  RevenuePeriod,
} from '@vendasta/sales-orders';
import { throwError } from 'rxjs';
import { GetMultiOrderFormsResponse, MarketplaceApp, OrderFormField } from '@vendasta/marketplace-apps/v1';
import { delay } from 'rxjs/operators';
import { AccountGroup } from '@galaxy/account-group';
import { Addon, ListPackagesResponse, Package, Pricing } from '@vendasta/marketplace-packages';
import {
  ActivationInformation,
  App,
  AppKey,
  Edition,
  EditionInformation,
  RequiredParent,
  RequiredParentDetails,
  SharedMarketingInformation,
  ListPartnerEnabledAppsResponse,
  AppType,
} from '@vendasta/marketplace-apps';
import { Task, TaskIdentity, DomainKeyValue, KeyValueTypes } from '@vendasta/task';
import { HttpResponse } from '@angular/common/http';
import { Opportunity } from '@vendasta/sales-opportunities';
import { Salesperson } from '@vendasta/sales';
/* tslint:disable:max-line-length */
let AccountGroupServiceStub = class AccountGroupServiceStub {
  get(accountGroupId, projectionFilter) {
    if (accountGroupId === 'AG-123') {
      return of(
        new AccountGroup({
          accountGroupId: 'AG-123',
          napData: {
            companyName: 'Johns Apples',
            address: '123 Fake St',
            city: 'Saskatoon',
            state: 'SK',
            website: 'https://www.example.com',
            workNumber: ['555-9999'],
          },
          externalIdentifiers: {
            partnerId: 'ABC',
            marketId: 'default',
            salesPersonId: 'SP-1',
          },
          accounts: [
            {
              isTrial: true,
              tags: [],
              accountId: '',
              marketplaceAppId: 'MP-4',
              expiry: null,
              editionId: '',
            },
          ],
          richData: {
            shortDescription: 'here is a SHORT description of my business',
            description: 'here is a LONG description of my business',
            servicesOffered: ['Service 1', 'Service 2'],
            brandsCarried: ['Brand 1', 'Brand 2'],
          },
          hoursOfOperation: [
            {
              dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
              opens: '08:00',
              closes: '17:30',
              description: 'Open for business',
            },
            { dayOfWeek: ['Saturday', 'Sunday'], description: 'Call us!' },
            { dayOfWeek: ['PublicHolidays'], description: 'Closed on public holidays' },
          ],
        }),
      );
    }
    if (accountGroupId === 'AG-456') {
      return of(
        new AccountGroup({
          accountGroupId: 'AG-456',
          napData: {
            companyName: "Sally's Seashells",
            address: '999 9th Ave',
            city: 'Winnipeg',
            state: 'MB',
            website: 'https://www.google.com',
            workNumber: ['555-1111'],
          },
          externalIdentifiers: {
            partnerId: 'ABC',
            marketId: 'default',
            salesPersonId: 'SP-2',
          },
          accounts: [],
        }),
      );
    }
    return throwError({ errorMessage: 'Business not found' });
  }
  bulkUpdate(accountGroupId, updateOperations) {
    if (accountGroupId === 'AG-123' || accountGroupId === '456') {
      return of(
        new HttpResponse({
          status: 200,
          statusText: 'ok',
        }),
      );
    }
    return throwError({ errorMessage: 'Business not found' });
  }
};
AccountGroupServiceStub = __decorate([Injectable()], AccountGroupServiceStub);
export { AccountGroupServiceStub };
let SalesServiceStub = class SalesServiceStub {
  getSalesperson(partnerId, salespersonId) {
    if (salespersonId === 'SP-1') {
      return of(
        new Salesperson({
          salespersonId: 'SP-1',
          firstName: 'Susan',
          lastName: 'Saleswoman',
          email: '<EMAIL>',
          phoneNumber: ['**********'],
        }),
      );
    }
    if (salespersonId === 'SP-2') {
      return of(
        new Salesperson({
          salespersonId: 'SP-1',
          firstName: 'Johnny',
          lastName: 'Sales',
          email: '<EMAIL>',
          phoneNumber: ['**********'],
        }),
      );
    }
    return throwError({ errorMessage: 'Salesperson not found' });
  }
};
SalesServiceStub = __decorate([Injectable()], SalesServiceStub);
export { SalesServiceStub };
const order = new Order({
  orderId: 'ORD-123',
  salespersonId: 'SP-1',
  partnerId: 'ABC',
  businessId: 'AG-123',
  marketId: 'mk1',
  status: Status.APPROVED,
  lineItems: [
    new LineItem({
      appKey: {
        appId: 'MP-1',
        editionId: 'regular',
      },
      currency: 6,
      currencyCode: 'ZAR',
      currentRevenue: {
        revenueComponents: [
          {
            value: 100000,
            isStartingRevenue: false,
          },
          {
            value: 50000,
            isStartingRevenue: false,
            period: RevenuePeriod.WEEKLY,
          },
        ],
      },
      quantity: 1,
    }),
    new LineItem({
      appKey: {
        appId: 'MP-2',
        editionId: '',
      },
      currency: 6,
      currencyCode: 'ZAR',
      currentRevenue: {
        revenueComponents: [
          {
            isStartingRevenue: true,
            value: 1000,
            period: RevenuePeriod.WEEKLY,
          },
        ],
      },
      quantity: 1,
    }),
    new LineItem({
      packageId: 'P-1',
      currency: 0,
      currencyCode: 'DZD',
      currentRevenue: {
        revenueComponents: [
          {
            value: 400,
          },
        ],
      },
      quantity: 2,
    }),
  ],
  customFields: [
    {
      fields: [
        {
          fieldId: 'ID1',
          answer: '"Answer from Order"',
        },
        {
          fieldId: 'ID2',
          answer: 'This is a text area with\nmultiple\nlines of text that has an \n so the textarea resizes\ncorrectly',
        },
        // Commented out answers below show that dropdown/user answers should support the fully saved answers as well as the legacy value only
        {
          fieldId: 'ID4',
          answer: JSON.stringify({ label: 'Renamed Option 2', value: '2' }),
          // answer: JSON.stringify('2')
        },
        {
          fieldId: 'ID4a',
          answer: JSON.stringify([
            { label: 'Threeeeeee', value: '3' },
            { label: 'Two', value: '2' },
          ]),
          // answer: JSON.stringify(['2', '3'])
        },
        {
          fieldId: 'ID5',
          answer: JSON.stringify([
            {
              name: 'Vendasta Logo',
              url: 'https://storage.googleapis.com/wordpress-www-vendasta/vw-wordpress/vendasta-icon-only-stroke-300x300.png',
            },
          ]),
        },
        {
          fieldId: 'ID6',
          answer: JSON.stringify({
            name: 'Johns Doe (<EMAIL>)',
            value: '{"firstName":"John","lastName":"Doe","email":"<EMAIL>","id":"ID-enabled"}',
          }),
          // answer: '"{\\"firstName\\":\\"John\\",\\"lastName\\":\\"Doe\\",\\"email\\":\\"<EMAIL>\\",\\"id\\":\\"ID-enabled\\"}"'
        },
      ],
      productId: 'MP-1',
    },
    {
      fields: [
        {
          fieldId: 'ID2',
          answer: '"9000"',
        },
      ],
      addonKey: {
        addonId: 'A-1',
        appId: 'MP-1',
      },
    },
    {
      fields: [
        {
          fieldId: 'ID1',
          answer: '"Mister"',
        },
      ],
      productId: 'MP-2',
    },
  ],
  commonFields: [
    {
      field: {
        fieldId: 'business_account_group_id',
        answer: '"AG-123"',
      },
    },
    {
      field: {
        fieldId: 'business_name',
        answer: '"Business Inc"',
      },
    },
    {
      field: {
        fieldId: 'business_address',
        answer: '"321 Real Rd, Regina, SK"',
      },
    },
    {
      field: {
        fieldId: 'contact_name',
        answer: '"Jill Smith"',
      },
    },
    {
      field: {
        fieldId: 'contact_email',
        answer: '"<EMAIL>"',
      },
    },
    {
      field: {
        fieldId: 'contact_phone_number',
        answer: '"**********"',
      },
    },
    {
      field: {
        fieldId: 'salesperson_name',
        answer: '"Jim Salesman"',
      },
    },
    {
      field: {
        fieldId: 'salesperson_email',
        answer: '"<EMAIL>"',
      },
    },
    {
      field: {
        fieldId: 'salesperson_phone_number',
        answer: '"**********"',
      },
    },
  ],
  extraFields: [
    {
      fieldId: 'ID2',
      answer: '{"label":"Hello","value":"h"}',
    },
  ],
  productActivations: [
    {
      productId: 'MP-1',
      editionId: 'regular',
      activationStatus: ActivationStatus.ACTIVATED,
    },
    {
      productId: 'MP-2',
      editionId: '',
      activationStatus: ActivationStatus.ALREADY_ACTIVATED,
    },
    {
      productId: 'A-1',
      editionId: '',
      activationStatus: ActivationStatus.ACTIVATED,
    },
    {
      productId: 'A-2',
      editionId: '',
      activationStatus: ActivationStatus.ALREADY_ACTIVATED,
    },
  ],
});
const orderWithProductActivations = JSON.parse(JSON.stringify(order));
orderWithProductActivations.orderId = 'ORD-WITH_PRODUCT_ACTIVATIONS';
// MP-2 does not have unique id's, this is on purpose to simulate legacy support
orderWithProductActivations.productActivations = [
  new ProductActivation({
    productId: 'MP-1',
    editionId: '',
    activationStatus: ActivationStatus.IGNORED_ERRORS,
    uniqueId: 'mp1-one',
  }),
  new ProductActivation({
    productId: 'MP-2',
    editionId: '',
    activationStatus: ActivationStatus.ALREADY_ACTIVATED,
    previousEditionId: 'E-1',
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    activationStatus: ActivationStatus.ACTIVATED,
    uniqueId: 'a1-one',
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    activationStatus: ActivationStatus.ERRORED,
    uniqueId: 'a1-two',
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    activationStatus: ActivationStatus.ALREADY_ACTIVATED,
    uniqueId: 'a1-three',
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    activationStatus: ActivationStatus.ACTIVATED,
    uniqueId: 'a1-four',
  }),
  new ProductActivation({
    productId: 'MP-1',
    editionId: '',
    activationStatus: ActivationStatus.ALREADY_ACTIVATED,
    uniqueId: 'mp1-two',
  }),
  new ProductActivation({
    productId: 'MP-2',
    editionId: '',
    activationStatus: ActivationStatus.ALREADY_ACTIVATED,
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    activationStatus: ActivationStatus.ERRORED,
    uniqueId: 'a1-five',
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    activationStatus: ActivationStatus.ERRORED,
    uniqueId: 'a1-six',
  }),
  new ProductActivation({
    productId: 'MP-1',
    editionId: '',
    activationStatus: ActivationStatus.ALREADY_ACTIVATED,
    uniqueId: 'mp1-three',
  }),
  new ProductActivation({
    productId: 'MP-1',
    editionId: '',
    activationStatus: ActivationStatus.ACTIVATED,
    uniqueId: 'mp1-four',
  }),
  new ProductActivation({
    productId: 'MP-1',
    editionId: '',
    activationStatus: ActivationStatus.ACTIVATED,
    uniqueId: 'mp1-five',
  }),
  new ProductActivation({
    productId: 'MP-2',
    editionId: '',
    activationStatus: ActivationStatus.ERRORED,
  }),
  new ProductActivation({
    productId: 'MP-2',
    editionId: '',
    activationStatus: ActivationStatus.ERRORED,
    previousEditionId: 'E-1',
  }),
  new ProductActivation({
    productId: 'MP-2',
    editionId: '',
    activationStatus: ActivationStatus.ACTIVATED,
  }),
  new ProductActivation({
    productId: 'MP-3',
    editionId: '',
    activationStatus: ActivationStatus.ERRORED,
    uniqueId: 'threeeeee',
  }),
];
const orderWithProductAndNoActivations = JSON.parse(JSON.stringify(order));
orderWithProductAndNoActivations.orderId = 'ORD-WITH_PRODUCT_AND_NO_ACTIVATIONS';
// MP-2 does not have unique id's, this is on purpose to simulate legacy support
orderWithProductAndNoActivations.productActivations = [
  new ProductActivation({
    productId: 'MP-1',
    editionId: '',
    uniqueId: 'mp1-one',
  }),
  new ProductActivation({
    productId: 'MP-2',
    editionId: '',
    previousEditionId: 'E-1',
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    uniqueId: 'a1-one',
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    uniqueId: 'a1-two',
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    uniqueId: 'a1-three',
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    uniqueId: 'a1-four',
  }),
  new ProductActivation({
    productId: 'MP-1',
    editionId: '',
    activationStatus: ActivationStatus.ALREADY_ACTIVATED,
    uniqueId: 'mp1-two',
  }),
  new ProductActivation({
    productId: 'MP-2',
    editionId: '',
    activationStatus: ActivationStatus.ALREADY_ACTIVATED,
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    activationStatus: ActivationStatus.ERRORED,
    uniqueId: 'a1-five',
  }),
  new ProductActivation({
    productId: 'A-1',
    editionId: '',
    activationStatus: ActivationStatus.ERRORED,
    uniqueId: 'a1-six',
  }),
  new ProductActivation({
    productId: 'MP-1',
    editionId: '',
    activationStatus: ActivationStatus.ALREADY_ACTIVATED,
    uniqueId: 'mp1-three',
  }),
  new ProductActivation({
    productId: 'MP-1',
    editionId: '',
    activationStatus: ActivationStatus.ACTIVATED,
    uniqueId: 'mp1-four',
  }),
  new ProductActivation({
    productId: 'MP-1',
    editionId: '',
    activationStatus: ActivationStatus.ACTIVATED,
    uniqueId: 'mp1-five',
  }),
  new ProductActivation({
    productId: 'MP-2',
    editionId: '',
    activationStatus: ActivationStatus.ERRORED,
  }),
  new ProductActivation({
    productId: 'MP-2',
    editionId: '',
    previousEditionId: 'E-1',
  }),
  new ProductActivation({
    productId: 'MP-2',
    editionId: '',
  }),
  new ProductActivation({
    productId: 'MP-3',
    editionId: '',
    uniqueId: 'threeeeee',
  }),
];
const orderWithNoActivations = JSON.parse(JSON.stringify(order));
orderWithNoActivations.orderId = 'ORD-WITH_NO_ACTIVATIONS';
orderWithNoActivations.productActivations = [];
orderWithNoActivations.addonActivations = [];
let CustomerSalesOrdersStub = class CustomerSalesOrdersStub {
  get(orderId, businessId) {
    return of(order).pipe(delay(200));
  }
  list(partnerId, filters, sortOption, cursor, pageSize) {
    const orders = [
      new Order({
        orderId: 'ORD-123',
        businessId: 'AG-123',
        status: Status.APPROVED,
        requestedActivation: new Date(2018, 4, 12),
        salespersonId: 'SP-1',
        created: new Date(2020, 4, 12),
        origin: Origin.VBC,
        contractDuration: {
          duration: DurationPeriod.YEAR,
          value: 2,
        },
        tags: ['1', '2', '3', '4', '5'],
        expiryDate: new Date(2020, 6, 12),
      }),
      new Order({
        orderId: 'ORD-321',
        businessId: 'AG-456',
        status: Status.FULFILLED,
        requestedActivation: new Date(2018, 4, 14),
        salespersonId: 'SP-2',
        created: new Date(2018, 4, 13),
        origin: Origin.SSC,
      }),
      new Order({
        orderId: 'ORD-456',
        businessId: 'AG-123',
        status: Status.SUBMITTED_FOR_CUSTOMER_APPROVAL,
        requestedActivation: new Date(2018, 4, 12),
        salespersonId: 'SP-1',
        created: new Date(2018, 4, 12),
        origin: Origin.VBC,
        contractDuration: {
          duration: DurationPeriod.YEAR,
          value: 2,
        },
        expiryDate: new Date(2024, 4, 12),
      }),
      new Order({
        orderId: 'ORD-666',
        businessId: 'AG-123',
        status: Status.SCHEDULED_ACTIVATION,
        requestedActivation: new Date(2018, 4, 12),
        salespersonId: 'SP-1',
        created: new Date(2018, 4, 12),
        origin: Origin.VBC,
        contractDuration: {
          duration: DurationPeriod.YEAR,
          value: 2,
        },
        expiryDate: new Date(2018, 6, 12),
      }),
      new Order({
        orderId: 'ORD-667',
        businessId: 'AG-123',
        requestedActivation: new Date(2018, 4, 12),
        salespersonId: 'SP-1',
        created: new Date(2018, 4, 12),
        origin: Origin.VBC,
        contractDuration: {
          duration: DurationPeriod.YEAR,
          value: 2,
        },
        expiryDate: new Date(2022, 4, 12),
      }),
    ];
    console.log('Filters', filters);
    console.log('Sort Options', sortOption);
    return of(new PagedResponse(orders, '', false, orders.length)).pipe(
      delay(200), // Need this delay so the loading is setup before the observable finishes
    );
  }
  initiateOrderExport(exportRequest) {
    return of({ exportId: '123456789' });
  }
};
CustomerSalesOrdersStub = __decorate([Injectable()], CustomerSalesOrdersStub);
export { CustomerSalesOrdersStub };
let SalesOrdersServiceStub = class SalesOrdersServiceStub {
  decline(businessId, orderId, declinedReason) {
    if (orderId === 'FAKE') {
      return throwError({ errorMessage: 'Order not found' });
    }
    return of(new Order({ orderId: orderId, businessId: businessId, declinedReason: declinedReason }));
  }
  get(orderId, businessId) {
    let orderResult = order;
    if (orderId === 'ORD-WITH_PRODUCT_ACTIVATIONS') {
      orderResult = orderWithProductActivations;
    } else if (orderId === 'ORD-WITH_PRODUCT_AND_NO_ACTIVATIONS') {
      orderResult = orderWithProductAndNoActivations;
    } else if (orderId === 'ORD-WITH_NO_ACTIVATIONS') {
      orderResult = orderWithNoActivations;
    }
    return of(orderResult).pipe(delay(200));
  }
  previewOrderActivations(orderId, businessId) {
    return of([]).pipe();
  }
  getConfig(partnerId, marketId) {
    return of({
      partnerId: 'ABC',
      marketId: 'mk1',
      taxOptions: [
        { label: 'PST', percentageMultiplier: 0.06 },
        { label: 'GST', percentageMultiplier: 0.05 },
      ],
      extraFields: [
        new OrderFormField({
          label: 'An important question',
          id: 'ID1',
          type: 'text',
          required: true,
          forOfficeUseOnly: true,
        }),
        new OrderFormField({
          label: 'A non important question',
          id: 'ID2',
          type: 'dropdown',
          options: ['g', 'h'],
          optionsWithLabels: [
            { label: 'Goodbye', value: 'g' },
            { label: 'Hello', value: 'h' },
          ],
        }),
      ],
    });
  }
  updateNotes(orderId, businessId, notes) {
    return of(true);
  }
  updateCustomerNotes(orderId, businessId, customerNotes) {
    return of(true);
  }
  updateTags(orderId, businessId, tags) {
    return of(
      new Order({
        orderId: 'ORD-123',
        salespersonId: 'SP-1',
        partnerId: 'ABC',
        businessId: 'AG-123',
        marketId: 'mk1',
        status: Status.APPROVED,
        lineItems: [
          {
            packageId: 'P-1',
            quantity: 1,
          },
          {
            appKey: { appId: 'MP-3' },
            quantity: 1,
          },
        ],
        customFields: [
          {
            fields: [
              {
                fieldId: 'ID1',
                answer: '"Answer from Order"',
              },
            ],
            productId: 'MP-1',
          },
          {
            fields: [
              {
                fieldId: 'ID2',
                answer: '"9000"',
              },
            ],
            addonKey: {
              addonId: 'A-1',
              appId: 'MP-1',
            },
          },
          {
            fields: [
              {
                fieldId: 'ID1',
                answer: '"Mister"',
              },
            ],
            productId: 'MP-2',
          },
        ],
        commonFields: [
          {
            field: {
              fieldId: 'business_account_group_id',
              answer: '"AG-123"',
            },
          },
          {
            field: {
              fieldId: 'business_name',
              answer: '"Business Inc"',
            },
          },
          {
            field: {
              fieldId: 'business_address',
              answer: '"321 Real Rd, Regina, SK"',
            },
          },
          {
            field: {
              fieldId: 'contact_name',
              answer: '"Jill Smith"',
            },
          },
          {
            field: {
              fieldId: 'contact_email',
              answer: '"<EMAIL>"',
            },
          },
          {
            field: {
              fieldId: 'contact_phone_number',
              answer: '"**********"',
            },
          },
          {
            field: {
              fieldId: 'salesperson_name',
              answer: '"Jim Salesman"',
            },
          },
          {
            field: {
              fieldId: 'salesperson_email',
              answer: '"<EMAIL>"',
            },
          },
          {
            field: {
              fieldId: 'salesperson_phone_number',
              answer: '"**********"',
            },
          },
        ],
        extraFields: [
          {
            fieldId: 'ID2',
            answer: '"Hello"',
          },
        ],
        tags: tags,
      }),
    );
  }
  listTags(partnerId, filters) {
    return of(
      new ListTagsResponse({
        tags: [
          'tag1',
          'tag2',
          'tag3',
          'tag4',
          'tag5',
          'tag6',
          'tag7',
          'tag8',
          'tag9',
          'tag10',
          'tag11',
          'tag12',
          'tag13',
          'tag14',
          'tag15',
        ],
      }),
    ).pipe(delay(2000));
  }
  list(partnerId, filters, sortOption, cursor, pageSize) {
    const orders = [
      new Order({
        orderId: 'ORD-123',
        businessId: 'AG-123',
        status: Status.APPROVED,
        requestedActivation: new Date(2018, 4, 12),
        salespersonId: 'SP-1',
        created: new Date(2020, 4, 12),
        origin: Origin.VBC,
        contractDuration: {
          duration: DurationPeriod.YEAR,
          value: 2,
        },
        tags: ['1', '2', '3', '4', '5'],
        expiryDate: new Date(2020, 6, 12),
      }),
      new Order({
        orderId: 'ORD-321',
        businessId: 'AG-456',
        status: Status.FULFILLED,
        requestedActivation: new Date(2018, 4, 14),
        salespersonId: 'SP-2',
        created: new Date(2018, 4, 13),
        origin: Origin.SSC,
      }),
      new Order({
        orderId: 'ORD-456',
        businessId: 'AG-123',
        status: Status.SUBMITTED_FOR_CUSTOMER_APPROVAL,
        requestedActivation: new Date(2018, 4, 12),
        salespersonId: 'SP-1',
        created: new Date(2018, 4, 12),
        origin: Origin.VBC,
        contractDuration: {
          duration: DurationPeriod.YEAR,
          value: 2,
        },
        expiryDate: new Date(2024, 4, 12),
      }),
      new Order({
        orderId: 'ORD-666',
        businessId: 'AG-123',
        status: Status.SCHEDULED_ACTIVATION,
        requestedActivation: new Date(2018, 4, 12),
        salespersonId: 'SP-1',
        created: new Date(2018, 4, 12),
        origin: Origin.VBC,
        contractDuration: {
          duration: DurationPeriod.YEAR,
          value: 2,
        },
        expiryDate: new Date(2018, 6, 12),
      }),
      new Order({
        orderId: 'ORD-667',
        businessId: 'AG-123',
        requestedActivation: new Date(2018, 4, 12),
        salespersonId: 'SP-1',
        created: new Date(2018, 4, 12),
        origin: Origin.VBC,
        contractDuration: {
          duration: DurationPeriod.YEAR,
          value: 2,
        },
        expiryDate: new Date(2022, 4, 12),
      }),
    ];
    console.log('Filters', filters);
    console.log('Sort Options', sortOption);
    return of(new PagedResponse(orders, '', false, orders.length)).pipe(
      delay(200), // Need this delay so the loading is setup before the observable finishes
    );
  }
  ignoreProductActivationError(orderId, businessId, productActivationUniqueId) {
    return of(true);
  }
  updateRequestedActivation(orderId, businessId, requestedActivation) {
    return of(true).pipe(delay(2000));
  }
  updateContractDuration(orderId, businessId, contractDuration) {
    if (!contractDuration) {
      return of(true);
    }
    if (!contractDuration.value || !contractDuration.duration) {
      return throwError('This is an error!');
    }
    return of(true).pipe(delay(2000));
  }
  updateCurrentRevenue(orderId, businessId, lineItems) {
    if (!lineItems) {
      return throwError('This is an error!');
    }
    return of(true).pipe(delay(200));
  }
  updateLineItems(orderId, businessId, lineItems) {
    if (!lineItems) {
      return throwError('This is an error!');
    }
    return of(true).pipe(delay(200));
  }
  archive(businessId, orderId) {
    if (!businessId || !orderId) {
      return throwError('This is an error!');
    }
    return of(new Order()).pipe(delay(200));
  }
  approveCancellation(orderId, businessId) {
    if (!businessId || !orderId) {
      return throwError('This is an error!');
    }
    return of(true).pipe(delay(200));
  }
  ignoreAllProductActivationErrors(orderId, businessId) {
    if (!businessId || !orderId) {
      return throwError('This is an error!');
    }
    return of(true).pipe(delay(200));
  }
  updateAnswers(orderId, businessId, customFields, commonFields, extraFields) {
    console.log('updateAnswers');
    if (!orderId || !businessId) {
      return throwError('This is an error!');
    }
    return of(new Order()).pipe(delay(200));
  }
  getStatusCounts(partnerId, filters) {
    console.log('getStatusCounts');
    return of({
      0: 29,
      8: 30,
      9: 6,
    }).pipe(delay(200));
  }
};
SalesOrdersServiceStub = __decorate([Injectable()], SalesOrdersServiceStub);
export { SalesOrdersServiceStub };
const packages = [
  new Package({
    packageId: 'P-1',
    pricing: new Pricing({
      currency: 22,
      prices: [
        {
          price: 100,
          frequency: 0,
          isStartingPrice: false,
        },
        {
          price: 6000,
          frequency: 2,
          isStartingPrice: true,
        },
      ],
    }),
    lineItems: {
      lineItems: [
        {
          id: 'MP-1',
          quantity: 1,
        },
        {
          id: 'MP-2',
          quantity: 1,
        },
        {
          id: 'A-1',
          parentId: 'MP-1',
          quantity: 2,
        },
      ],
    },
    name: 'The Package',
    icon: 'http://icons.iconarchive.com/icons/gpritiranjan/simple-christmas/512/gift-icon.png',
    headerImageUrl:
      'https://lh3.googleusercontent.com/bc8M69wcWSr8I2jTR-95L53F627loGXk86TaWzR41X3-ve4EyTHxwyTaTkMN5lEZEg-rHdGnjOoMuyH-Lqu6ZxX5CJ65wCyXpw',
  }),
  new Package({
    packageId: 'P-2',
    pricing: new Pricing({
      currency: 21,
      prices: [
        {
          price: 100,
          frequency: 0,
          isStartingPrice: false,
        },
        {
          price: 6000,
          frequency: 2,
          isStartingPrice: true,
        },
      ],
    }),
    lineItems: {
      lineItems: [
        {
          id: 'MP-1',
          quantity: 1,
        },
        {
          id: 'MP-2',
          quantity: 1,
        },
        {
          id: 'A-1',
          parentId: 'MP-1',
          quantity: 2,
        },
      ],
    },
    name: 'The 2nd Package',
    icon: 'http://icons.iconarchive.com/icons/gpritiranjan/simple-christmas/512/gift-icon.png',
  }),
  new Package({
    packageId: 'P-3',
    pricing: new Pricing({
      currency: 21,
      prices: [
        {
          price: -1,
          frequency: 0,
          isStartingPrice: false,
        },
      ],
    }),
    lineItems: {
      lineItems: [
        {
          id: 'MP-1',
          quantity: 1,
        },
        {
          id: 'MP-2',
          quantity: 1,
        },
        {
          id: 'A-1',
          parentId: 'MP-1',
          quantity: 2,
        },
      ],
    },
    name: 'The 3rd Package',
    icon: 'http://icons.iconarchive.com/icons/yohproject/cute/256/gift-icon.png',
  }),
  new Package({
    packageId: 'P-4',
    pricing: new Pricing({}),
    lineItems: {
      lineItems: [
        {
          id: 'MP-1',
          quantity: 1,
        },
        {
          id: 'MP-2',
          quantity: 1,
        },
        {
          id: 'A-1',
          parentId: 'MP-1',
          quantity: 2,
        },
      ],
    },
    name: 'The 4th Package',
    icon: 'http://icons.iconarchive.com/icons/yohproject/cute/256/gift-icon.png',
  }),
];
const opportunity = new Opportunity({
  opportunityId: 'OPPORTUNITY-1249f4bc-1461-4965-b0bc-a21ba3b48a9f',
  accountGroupId: 'AG-2SDL3LLLTZ',
  salesPersonId: 'UID-79985caf-df25-49c8-8857-ce65ab0b2411',
  name: "Colin's Package",
  expectedContractDuration: {
    duration: DurationPeriod.YEAR,
    value: 2,
  },
  lineItems: [
    new LineItem({
      appKey: {
        appId: 'MP-1',
        editionId: 'regular',
      },
      currency: 6,
      currencyCode: 'ZAR',
      currentRevenue: {
        revenueComponents: [
          {
            value: 100000,
            isStartingRevenue: false,
          },
          {
            value: 50000,
            isStartingRevenue: false,
            period: RevenuePeriod.WEEKLY,
          },
        ],
      },
      quantity: 1,
    }),
    new LineItem({
      appKey: {
        appId: 'MP-2',
        editionId: '',
      },
      currency: 6,
      currencyCode: 'ZAR',
      currentRevenue: {
        revenueComponents: [
          {
            isStartingRevenue: true,
            value: 1000,
            period: RevenuePeriod.WEEKLY,
          },
        ],
      },
      quantity: 1,
    }),
    new LineItem({
      packageId: 'P-1',
      currency: 0,
      currencyCode: 'DZD',
      currentRevenue: {
        revenueComponents: [
          {
            value: 400,
          },
        ],
      },
      quantity: 2,
    }),
  ],
});
let SalesOpportunitiesStub = class SalesOpportunitiesStub {
  get(opportunityId, accountGroupId) {
    return of(opportunity);
  }
  updateLineItemsForOpportunity(opportunityId, accountGroupId, salesPersonId, lineItems) {
    return of(new Opportunity(Object.assign(Object.assign({}, opportunity), { lineItems })));
  }
};
SalesOpportunitiesStub = __decorate([Injectable()], SalesOpportunitiesStub);
export { SalesOpportunitiesStub };
let PackageServiceStub = class PackageServiceStub {
  list(
    partnerId,
    statuses,
    marketId = 'default',
    pageSize = 300,
    cursor = 'MA==',
    lmiCategory,
    includeTotalResults,
    filterTerm,
  ) {
    return of(
      new ListPackagesResponse({
        packages: packages,
        totalResults: 100,
      }),
    ).pipe(delay(200));
  }
  getMulti(packageIds) {
    return of(packages.filter((p) => packageIds.some((packageId) => packageId === p.packageId)));
  }
};
PackageServiceStub = __decorate([Injectable()], PackageServiceStub);
export { PackageServiceStub };
let AddonServiceStub = class AddonServiceStub {
  getMultiAddonsByApp(partnerId, appIds, whitelabelOverrideKey) {
    const map = new Map();
    if (appIds.some((id) => id === 'MP-2')) {
      map.set('MP-2', [new Addon({ addonId: 'A-2', appId: 'MP-2' })]);
    }
    if (appIds.some((id) => id === 'MP-1')) {
      map.set('MP-1', [new Addon({ addonId: 'A-1', appId: 'MP-1' })]);
    }
    return of(map).pipe(delay(200));
  }
};
AddonServiceStub = __decorate([Injectable()], AddonServiceStub);
export { AddonServiceStub };
let MarketplaceAppsV1Stub = class MarketplaceAppsV1Stub {
  listApps(pageSize = 0, cursor = '', fieldMask, partnerId) {
    return of({
      apps: [
        new MarketplaceApp({ appId: 'MP-1', usesEditions: true, editionIds: ['', 'regular'] }),
        new MarketplaceApp({ appId: 'MP-2', usesEditions: true, editionIds: ['', 'express'] }),
        new MarketplaceApp({ appId: 'MP-3' }),
      ],
      cursor: '20',
      hasMore: true,
    }).pipe(delay(200));
  }
};
MarketplaceAppsV1Stub = __decorate([Injectable()], MarketplaceAppsV1Stub);
export { MarketplaceAppsV1Stub };
const apps = [
  new App({
    key: new AppKey({ appId: 'MP-1' }),
    sharedMarketingInformation: new SharedMarketingInformation({
      name: 'Test Product',
      iconUrl: 'http://icons.iconarchive.com/icons/gpritiranjan/simple-christmas/512/gift-icon.png',
      tagline: 'A product for testing',
      editionName: 'D-E-L-U-X-E Edition',
    }),
    parentRequirements: new RequiredParent({
      enabled: false,
    }),
    activationInformation: new ActivationInformation({
      orderFormEnabled: true,
      multipleActivationsEnabled: true,
      activationSpecificUrlEnabled: true,
    }),
    editionInformation: new EditionInformation({
      editions: [
        new Edition({ appKey: new AppKey({ appId: 'MP-1' }), name: 'D-E-L-U-X-E Edition', billingId: 'MP-1:express' }),
        new Edition({
          appKey: new AppKey({ appId: 'MP-1', editionId: 'regular' }),
          name: 'Regular Edition',
          billingId: 'MP-1:regular',
        }),
      ],
    }),
  }),
  new App({
    key: new AppKey({ appId: 'MP-1', editionId: 'regular' }),
    sharedMarketingInformation: new SharedMarketingInformation({
      name: 'Test Product',
      iconUrl: 'http://icons.iconarchive.com/icons/gpritiranjan/simple-christmas/512/gift-icon.png',
      tagline: 'A product for testing',
      editionName: 'Regular Edition',
    }),
    parentRequirements: new RequiredParent({
      enabled: false,
    }),
    activationInformation: new ActivationInformation({
      orderFormEnabled: true,
      multipleActivationsEnabled: true,
      activationSpecificUrlEnabled: true,
    }),
    editionInformation: new EditionInformation({
      editions: [
        new Edition({ appKey: new AppKey({ appId: 'MP-1' }), name: 'D-E-L-U-X-E Edition', billingId: 'MP-1:express' }),
        new Edition({
          appKey: new AppKey({ appId: 'MP-1', editionId: 'regular' }),
          name: 'Regular Edition',
          billingId: 'MP-1:regular',
        }),
      ],
    }),
  }),
  new App({
    key: new AppKey({ appId: 'MP-2', editionId: '' }),
    sharedMarketingInformation: new SharedMarketingInformation({
      name: 'Another Product',
      iconUrl: 'https://cdn4.iconfinder.com/data/icons/petshop-block/100/petshop-21-512.png',
      tagline: 'A second product for testing',
      editionName: 'Pro',
    }),
    parentRequirements: new RequiredParent({
      enabled: false,
    }),
    activationInformation: new ActivationInformation({
      orderFormEnabled: false,
      multipleActivationsEnabled: false,
      activationSpecificUrlEnabled: false,
    }),
    editionInformation: new EditionInformation({
      editions: [
        new Edition({ appKey: new AppKey({ appId: 'MP-2', editionId: '' }), name: 'Pro', billingId: 'MP-2:pro' }),
        new Edition({
          appKey: new AppKey({ appId: 'MP-2', editionId: 'E-1' }),
          name: 'Express',
          billingId: 'MP-2:express',
        }),
      ],
    }),
  }),
  new App({
    key: new AppKey({ appId: 'MP-2', editionId: 'E-1' }),
    sharedMarketingInformation: new SharedMarketingInformation({
      name: 'Another Product',
      iconUrl: 'https://cdn4.iconfinder.com/data/icons/petshop-block/100/petshop-21-512.png',
      tagline: 'A second product for testing',
      editionName: 'Express',
    }),
    parentRequirements: new RequiredParent({
      enabled: false,
    }),
    activationInformation: new ActivationInformation({
      orderFormEnabled: false,
      multipleActivationsEnabled: false,
      activationSpecificUrlEnabled: false,
    }),
    editionInformation: new EditionInformation({
      editions: [
        new Edition({ appKey: new AppKey({ appId: 'MP-2', editionId: '' }), name: 'Pro', billingId: 'MP-2:pro' }),
        new Edition({
          appKey: new AppKey({ appId: 'MP-2', editionId: 'E-1' }),
          name: 'Express',
          billingId: 'MP-2:express',
        }),
      ],
    }),
  }),
  new App({
    key: new AppKey({ appId: 'MP-3' }),
    sharedMarketingInformation: new SharedMarketingInformation({
      name: 'A 3rd Product',
      iconUrl: 'http://icons.iconarchive.com/icons/icons8/ios7/256/Hands-Three-Fingers-icon.png',
      tagline: 'A third product for testing',
    }),
    parentRequirements: new RequiredParent({
      enabled: false,
    }),
    activationInformation: new ActivationInformation({
      orderFormEnabled: true,
      multipleActivationsEnabled: true,
      activationSpecificUrlEnabled: false,
    }),
  }),
  new App({
    key: new AppKey({ appId: 'MP-4' }),
    sharedMarketingInformation: new SharedMarketingInformation({
      name: 'A 4th Product',
      iconUrl: 'http://icons.iconarchive.com/icons/icons8/ios7/256/Hands-Three-Fingers-icon.png',
      tagline: 'A fifth product for testing',
    }),
    parentRequirements: new RequiredParent({
      enabled: false,
    }),
    activationInformation: new ActivationInformation({
      orderFormEnabled: true,
      multipleActivationsEnabled: false,
      activationSpecificUrlEnabled: false,
    }),
  }),
  new App({
    key: new AppKey({ appId: 'A-1' }),
    sharedMarketingInformation: new SharedMarketingInformation({
      name: 'Addon Tester',
      iconUrl: 'http://icons.iconarchive.com/icons/custom-icon-design/flatastic-8/128/Addons-icon.png',
      bannerImageUrl:
        'https://lh3.googleusercontent.com/SKZpraX3sx4hSjZgLhpIUcHclJkREW6FGRi0TcKk8X1SIW1GvnRKwBQpyTwOn9ZOEbsvg2Sp2txUIokcOCqnT83sthU',
      tagline: 'A nice addon',
    }),
    parentRequirements: new RequiredParent({
      enabled: true,
      parentDetails: new RequiredParentDetails({
        key: new AppKey({ appId: 'MP-1' }),
        iconUrl: 'http://icons.iconarchive.com/icons/gpritiranjan/simple-christmas/512/gift-icon.png',
        name: 'Test Product',
        tagline: 'A product for testing',
      }),
    }),
    activationInformation: new ActivationInformation({
      orderFormEnabled: true,
      multipleActivationsEnabled: true,
      activationSpecificUrlEnabled: false,
      separateOrderForms: true,
    }),
  }),
  new App({
    key: new AppKey({ appId: 'A-2', editionId: '' }),
    sharedMarketingInformation: new SharedMarketingInformation({
      name: 'Addon 2',
      editionName: '',
      iconUrl:
        'https://lh3.googleusercontent.com/o5E0IXaWz1VgSobUqjhw7-JbDNOQydvSO2iRzl8_o2SIazOirESblF_8UxsXSoOS-_i145G6Pzt25g6TGJY3tgo9XF_oSUlf',
    }),
    parentRequirements: new RequiredParent({
      enabled: true,
      parentDetails: new RequiredParentDetails({
        key: new AppKey({ appId: 'MP-2', editionId: '' }),
        name: 'Product 1',
        iconUrl:
          'https://lh3.googleusercontent.com/JfawpCJ7miWXMptuAnxpkqNSsVCUxNFXtg9TFF17k9o-R1u9bvKf1472DfIZgQ36G3dwN-vkrEEvwbTpYkNtkL2D8Lg',
      }),
    }),
  }),
];
let MarketplaceAppsV2Stub = class MarketplaceAppsV2Stub {
  getMulti(appKeys, partnerId, marketId, projectionFilter, includeNotEnabled) {
    const copyApps = JSON.parse(JSON.stringify(apps));
    const appsToReturn = copyApps.filter((a) =>
      appKeys.find((appKey) => a.key.appId === appKey.appId && (a.key.editionId || '') === (appKey.editionId || '')),
    );
    return of(appsToReturn).pipe(delay(200));
  }
  listPartnerEnabledApps(partnerId, pageSize = 300, cursor = 'MA==', marketId = '', filters = null) {
    const copyApps = JSON.parse(JSON.stringify(apps));
    let appsToReturn = copyApps;
    if (!!(filters === null || filters === void 0 ? void 0 : filters.appType)) {
      if (filters.appType === AppType.APP_TYPE_APP) {
        appsToReturn = copyApps.filter((a) => {
          var _a;
          return !((_a = a.parentRequirements) === null || _a === void 0 ? void 0 : _a.enabled) && !a.key.editionId;
        });
      } else if (filters.appType === AppType.APP_TYPE_ADDON) {
        appsToReturn = copyApps.filter((a) => {
          var _a;
          return ((_a = a.parentRequirements) === null || _a === void 0 ? void 0 : _a.enabled) && !a.key.editionId;
        });
      }
    }
    return of(
      new ListPartnerEnabledAppsResponse({
        apps: appsToReturn,
        cursor: '20',
        hasMore: true,
      }),
    ).pipe(delay(200));
  }
};
MarketplaceAppsV2Stub = __decorate([Injectable()], MarketplaceAppsV2Stub);
export { MarketplaceAppsV2Stub };
let AccountsServiceStub = class AccountsServiceStub {
  listAppsAndAddonsActivationStatusesForBusiness(businessId, filters) {
    const statuses = [{ appId: 'MP-3' }, { appId: 'A-1' }];
    return of(statuses).pipe(delay(200));
  }
};
AccountsServiceStub = __decorate([Injectable()], AccountsServiceStub);
export { AccountsServiceStub };
let MarketplaceAppsApiServiceStub = class MarketplaceAppsApiServiceStub {
  getMultiOrderForms(r) {
    return of(
      new GetMultiOrderFormsResponse({
        orderFormContainer: [
          {
            appId: 'MP-1',
            orderForm: {
              orderForm: [
                new OrderFormField({
                  label: 'this is a textbox',
                  id: 'ID1',
                  type: 'text',
                  description: 'This is a description',
                  required: true,
                  forOfficeUseOnly: true,
                }),
                new OrderFormField({
                  label: 'this is a textarea',
                  id: 'ID2',
                  type: 'textarea',
                  description: 'This is another description',
                }),
                new OrderFormField({
                  label: 'this is a checkbox',
                  id: 'ID3',
                  type: 'checkbox',
                  description: 'This is a checkbox description',
                }),
                new OrderFormField({
                  label: 'this is a dropdown',
                  id: 'ID4',
                  type: 'dropdown',
                  description: 'This is a dropdown description',
                  required: true,
                  options: ['1', '2'],
                  optionsWithLabels: [
                    { label: 'Option 1', value: '1' },
                    { label: 'Option 2', value: '2' },
                  ],
                }),
                new OrderFormField({
                  label: 'this is a multiselect dropdown',
                  id: 'ID4a',
                  type: 'dropdown',
                  description: 'This is a multiselect dropdown description',
                  required: true,
                  options: ['1', '2', '3', '4'],
                  optionsWithLabels: [
                    { label: 'Option 1', value: '1' },
                    { label: 'Two', value: '2' },
                    { label: 'Three', value: '3' },
                    { label: '4', value: '4' },
                  ],
                  allowMultiples: true,
                  allowDuplicates: false,
                  maxChoices: 3,
                }),
                new OrderFormField({
                  label: 'this is a file uploader',
                  id: 'ID5',
                  type: 'file',
                  description: 'This is a file uploader description',
                  uploadUrl: '',
                }),
                new OrderFormField({
                  label: 'this is a Business Center User selector',
                  id: 'ID6',
                  type: 'vbcuser',
                  description: 'This is a Business Center User selector description',
                }),
              ],
              commonFormRequiredFields: {
                businessName: true,
                salespersonName: true,
              },
            },
          },
          {
            appId: 'A-1',
            orderForm: {
              orderForm: [
                new OrderFormField({
                  label: 'Name',
                  id: 'ID1',
                  type: 'textbox',
                  description: 'This is a name description',
                  required: true,
                }),
                new OrderFormField({
                  label: 'Number',
                  id: 'ID2',
                  type: 'textbox',
                  description: 'This is a number description',
                  required: true,
                }),
                new OrderFormField({
                  label: 'Email',
                  id: 'ID3',
                  type: 'textbox',
                  description: 'This is an email description',
                  suffix: '@yourdomain.com',
                }),
                new OrderFormField({
                  label: 'Show me the money',
                  id: 'ID4',
                  type: 'textbox',
                  description: 'This is a money description',
                  prefix: '$',
                }),
              ],
              commonFormRequiredFields: {
                businessName: true,
                contactName: true,
                contactPhoneNumber: true,
              },
            },
          },
          {
            appId: 'MP-2',
            orderForm: {
              orderForm: [
                new OrderFormField({
                  label: 'Title',
                  id: 'ID1',
                  type: 'text',
                  description: 'This is a title description',
                  required: true,
                  forOfficeUseOnly: true,
                }),
                new OrderFormField({
                  label: 'Count',
                  id: 'ID2',
                  type: 'text',
                  description: 'This is a count description',
                  required: true,
                  forOfficeUseOnly: true,
                }),
              ],
              commonFormRequiredFields: {
                businessName: true,
                contactName: true,
                contactEmail: true,
              },
            },
          },
          {
            appId: 'MP-3',
            orderForm: {
              orderForm: [
                new OrderFormField({
                  label: 'Number',
                  id: 'ID1',
                  type: 'text',
                  description: 'This is a number description',
                }),
              ],
              commonFormRequiredFields: {},
            },
          },
          {
            appId: 'MP-4',
            orderForm: {
              orderForm: [
                new OrderFormField({
                  label: 'Title',
                  id: 'ID1',
                  type: 'text',
                  description: 'This is a title description',
                  required: true,
                  forOfficeUseOnly: true,
                }),
                new OrderFormField({
                  label: 'Count',
                  id: 'ID2',
                  type: 'text',
                  description: 'This is a count description',
                  required: true,
                  forOfficeUseOnly: true,
                }),
              ],
              commonFormRequiredFields: {
                businessName: true,
                contactName: true,
                contactEmail: true,
              },
            },
          },
        ],
      }),
    );
  }
};
MarketplaceAppsApiServiceStub = __decorate([Injectable()], MarketplaceAppsApiServiceStub);
export { MarketplaceAppsApiServiceStub };
const subtasks = [
  new Task({
    identity: new TaskIdentity({
      namespace: 'partner/ABC/account-group/AG-123',
      parentPath: '/TK-1/',
      taskId: 'TK-11',
    }),
    title: 'Subtask One',
    dueDate: new Date(2099, 1, 1, 0, 0, 0),
    status: 'In Progress',
  }),
  new Task({
    identity: new TaskIdentity({
      namespace: 'partner/ABC/account-group/AG-123',
      parentPath: '/TK-1/',
      taskId: 'TK-12',
    }),
    title: 'Subtask Two',
    dueDate: new Date(2099, 1, 1, 0, 0, 0),
    completionDate: new Date(2019, 12, 1, 0, 0, 0),
    status: 'Completed',
  }),
  new Task({
    identity: new TaskIdentity({
      namespace: 'partner/ABC/account-group/AG-123',
      parentPath: '/TK-2/',
      taskId: 'TK-21',
    }),
    title: 'Easy Task',
    dueDate: new Date(2020, 2, 2, 9, 0, 0),
    completionDate: new Date(2020, 2, 2, 5, 0, 0),
    status: 'Completed',
  }),
];
let TaskSdkServiceStub = class TaskSdkServiceStub {
  multiProductSearch(req) {
    return of({
      tasks: [
        new Task({
          identity: new TaskIdentity({
            namespace: 'partner/ABC/account-group/AG-123',
            parentPath: '',
            taskId: 'TK-1',
          }),
          title: 'First Project',
          dueDate: new Date(2099, 1, 1, 0, 0, 0),
          status: 'In Progress',
          metadata: new DomainKeyValue().set('ProductId', ['MP-1'], KeyValueTypes.STRING).toKeyValue(),
          subtasks: [
            new TaskIdentity({
              namespace: 'partner/ABC/account-group/AG-123',
              parentPath: '/TK-1/',
              taskId: 'TK-11',
            }),
            new TaskIdentity({
              namespace: 'partner/ABC/account-group/AG-123',
              parentPath: '/TK-1/',
              taskId: 'TK-12',
            }),
          ],
        }),
        new Task({
          identity: new TaskIdentity({
            namespace: 'partner/ABC/account-group/AG-123',
            parentPath: '',
            taskId: 'TK-2',
          }),
          title: 'Another Project',
          dueDate: new Date(2020, 2, 2, 9, 0, 0),
          status: 'Completed',
          metadata: new DomainKeyValue().set('ProductId', ['A-1'], KeyValueTypes.STRING).toKeyValue(),
          subtasks: [
            new TaskIdentity({
              namespace: 'partner/ABC/account-group/AG-123',
              parentPath: '/TK-2/',
              taskId: 'TK-21',
            }),
          ],
        }),
      ],
    });
  }
  getMulti(request) {
    const requestedTasks = subtasks.filter((t) => request.identity.some((i) => i.taskId === t.identity.taskId));
    return of({
      tasks: requestedTasks,
    });
  }
};
TaskSdkServiceStub = __decorate([Injectable()], TaskSdkServiceStub);
export { TaskSdkServiceStub };
let BillingServiceStub = class BillingServiceStub {
  getMultiRetailPricing(merchantId, currency, skus, groupId) {
    console.log('getMultiretailPricing');
    console.log(merchantId);
    console.log(groupId);
    console.log(skus);
    return of({
      'MP-3': {
        strategy: 'INSTANTLY',
        pricingType: 'STANDARD',
        currency: 'GBP',
        frequency: 'MONTHLY',
        pricingRules: [
          {
            price: 100000,
            minUnits: 0,
            maxUnit: 0,
          },
        ],
        commitment: null,
        volumeCommitment: 0,
        isStartingPrice: false,
      },
      'MP-4': {
        strategy: 'INSTANTLY',
        pricingType: 'STANDARD',
        currency: 'GBP',
        frequency: 'YEARLY',
        pricingRules: [
          {
            price: 50000,
            minUnits: 0,
            maxUnit: 0,
          },
        ],
        commitment: null,
        volumeCommitment: 0,
        isStartingPrice: false,
      },
      'MP-1:express': {
        strategy: 'INSTANTLY',
        pricingType: 'STANDARD',
        currency: 'GBP',
        frequency: 'MONTHLY',
        pricingRules: [
          {
            price: 10,
            minUnits: 0,
            maxUnit: 0,
          },
        ],
        commitment: null,
        volumeCommitment: 0,
        isStartingPrice: false,
      },
      'MP-1:regular': {
        strategy: 'INSTANTLY',
        pricingType: 'STANDARD',
        currency: 'GBP',
        frequency: 'MONTHLY',
        pricingRules: [
          {
            price: 40000,
            minUnits: 0,
            maxUnit: 0,
          },
        ],
        commitment: null,
        volumeCommitment: 0,
        isStartingPrice: false,
      },
      'A-1': {
        strategy: 'INSTANTLY',
        pricingType: 'STANDARD',
        currency: 'GBP',
        frequency: 'YEARLY',
        pricingRules: [
          {
            price: 40000,
            minUnits: 0,
            maxUnit: 0,
          },
        ],
        commitment: null,
        volumeCommitment: 0,
        isStartingPrice: false,
      },
    }).pipe(delay(100));
  }
};
BillingServiceStub = __decorate([Injectable()], BillingServiceStub);
export { BillingServiceStub };
//# sourceMappingURL=sales-ui.stub.js.map
