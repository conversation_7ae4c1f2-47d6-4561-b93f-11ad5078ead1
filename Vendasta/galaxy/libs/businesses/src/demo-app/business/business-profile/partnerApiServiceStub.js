import { __decorate } from 'tslib';
import { Injectable } from '@angular/core';
import { of } from 'rxjs';
import { ActivationInformation, SharedMarketingInformation } from '@vendasta/marketplace-apps';
let PartnerApiServiceStub = class PartnerApiServiceStub {
  getMultiApp(req) {
    const resp = { apps: [] };
    req.appKeys.forEach((key) => {
      if (key.appId === 'Test-ProductId-with-restrictions') {
        const app = {
          key: key,
          sharedMarketingInformation: new SharedMarketingInformation({
            name: 'stubbed app',
          }),
          activationInformation: new ActivationInformation({
            requiredBusinessData: {
              hours: true,
              descriptionLong: true,
              descriptionShort: true,
              services: true,
              brands: true,
            },
          }),
        };
        resp.apps.push(app);
      }
      if (key.appId === 'Test-ProductId-with-some-restrictions') {
        const app = {
          key: key,
          sharedMarketingInformation: new SharedMarketingInformation({
            name: 'stubbed app',
          }),
          activationInformation: new ActivationInformation({
            requiredBusinessData: {
              hours: false,
              descriptionLong: true,
              descriptionShort: true,
              services: false,
              brands: true,
            },
          }),
        };
        resp.apps.push(app);
      }
      if (key.appId === 'Test-ProductId-with-no-restrictions') {
        const app = {
          key: key,
          sharedMarketingInformation: new SharedMarketingInformation({
            name: 'stubbed app',
          }),
          activationInformation: new ActivationInformation({}),
        };
        resp.apps.push(app);
      }
    });
    return of(resp);
  }
};
PartnerApiServiceStub = __decorate([Injectable()], PartnerApiServiceStub);
export { PartnerApiServiceStub };
//# sourceMappingURL=partnerApiServiceStub.js.map
