import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { FeatureFlagService as PartnerSDKFeatureFlagService } from '@galaxy/partner';

export class FeatureFlagMultiResponse {
  [key: string]: boolean;
}

@Injectable({ providedIn: 'root' })
export class FeatureFlagService {
  constructor(private featureFlagService: PartnerSDKFeatureFlagService) {}

  checkFeatureFlagsMulti(partnerId: string, marketId = '', featureIds: string[]): Observable<FeatureFlagMultiResponse> {
    return this.featureFlagService.batchGetStatus(partnerId, marketId, featureIds);
  }

  checkFeatureFlag(partnerId: string, marketId = '', featureId: string): Observable<boolean> {
    return this.checkFeatureFlagsMulti(partnerId, marketId, [featureId]).pipe(
      map((val: FeatureFlagMultiResponse): boolean => {
        return featureId in val && val[featureId];
      }),
    );
  }
}
