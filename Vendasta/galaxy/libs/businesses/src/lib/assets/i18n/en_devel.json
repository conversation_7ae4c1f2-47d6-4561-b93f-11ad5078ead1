{"LOGO": "Logo", "PRIMARY": "Primary", "FRONTEND": {"BUSINESSES": {"ADDRESS_FORM": {"PREVIEW": "Preview", "ADDRESS_2": "Additional address line", "COUNTRY_REGION": "Country/Region", "INVALID_COUNTRY_NAME": "Please select a valid country name", "REQUIRED_FIELD": "This field is required", "INVALID_SELECTION": "Invalid selection", "INVALID": "Invalid"}, "YES": "Yes", "NO": "No", "CANCEL": "Cancel", "SAVE": "Save", "DELETE": "Delete", "PRIMARY_INFO": "Primary info", "LOCATION": "Location", "BASIC_INFORMATION": "Basic information", "BASICS": "Basics", "BUSINESS_NAME": "Business name", "COMPETITORS": "Competitors", "COMPETITOR_NAME": "Competitor name", "COMPETITOR_URL": "Competitor URL", "COMPETITOR_DELETE_MESSAGE": "This competitor will be removed from business competitors.", "COMPETITOR_SAVE_SUCCESS": "Competitor saved successfully", "COMPETITOR_SAVE_ERROR": "Error saving Competitor", "COMPETITOR_DELETE_SUCCESS": "Competitor deleted successfully", "COMPETITOR_DELETE_ERROR": "Error deleting Competitor", "COMPETITOR_NAME_REQUIRED": "Name is required", "COMPETITOR_DELETE": "Delete {{name}}?", "CONTACT": "Contact", "ADD_COMMON_BUSINESS_NAME": "+ Add common business name", "COMMON_BUSINESS_NAME": "Common business name", "SERVICE_AREA_BUSINESS": "Service area business", "SERVICE_AREA_BUSINESS_HINT": "A business that visits or delivers to customers directly", "CUSTOMER_LOCATION_ONLY": "Hide my address and only show service areas", "CUSTOMER_AND_BUSINESS_LOCATION": "Show my address in addition to service areas", "SERVICE_AREAS": "Service areas", "SERVICE_AREAS_ERROR": "At least 1 service area is required", "WEBSITE": "Website", "BUSINESS_EMAIL": "Business email", "PRIMARY_CATEGORY": "Primary category", "ADDITIONAL_CATEGORIES": "Additional categories", "BUSINESS_CATEGORIES_WHATS_THIS": "What's this?", "BUSINESS_CATEGORIES_HINT": "Start typing to search business categories", "BUSINESS_CATEGORIES_ERROR": "At least 1 category is required", "BUSINESS_CATEGORIES_INVALID": "Some of your categories are no longer supported. Please consider choosing new categories.", "OPERATIONAL_INFORMATION": "Operational information", "REOPENING_DATE": "Reopening date", "COVID_INFORMATION": "COVID information", "COMMERCE_ONLY": "Commerce only", "BUSINESS_OPEN_CLOSE": "Business open/closed", "SELLING_METHODS": "Selling methods", "ADD_SERVICE": "+ Add service", "SERVICES_OFFERED": "Services offered", "ADD_BRAND": "+ Add brand", "BRANDS_CARRIED": "Brands carried", "LANDMARK": "Landmark", "BOOKING_URL": "Booking Link", "CONDITIONAL_FIELDS": {"ICO": "ICO"}, "MAP_GEOLOCATION": "Map (GeoLocation)", "GEOCONTROL_TOOLTIP_NO_VALUE": "Click the map to place your marker", "GEOCONTROL_TOOLTIP_HAS_VALUE": "Drag the marker to edit your location", "PHONE": "Phone", "PHONE_NUMBERS": "Phone numbers", "ADD_PHONE_NUMBER": "+ Add phone number", "PHONE_NUMBERS_ALLOWED_FORMATS": "Phone numbers must be formatted as +246123456789", "FAX_NUMBER": "Fax number", "CELL_NUMBER": "Cell number", "ADD_CALL_TRACKING_NUMBER": "+ Add call tracking number", "CALL_TRACKING_NUMBERS": "Call tracking numbers", "TOLL_FREE_NUMBER": "Toll-free number", "HOURS": "Hours", "MORE_HOURS": "More hours", "MORE_HOURS_EMPTY": "More hours aren't applicable to your business category", "ACTIVE": "Active", "ADD": "Add", "SOCIAL": "Social", "BUSINESS_PAGES": "Business pages", "FACEBOOK_URL": "Facebook URL", "FOURSQUARE_URL": "Foursquare URL", "LINKEDIN_URL": "LinkedIn URL", "TWITTER_URL": "Twitter URL", "PINTEREST_URL": "Pinterest URL", "INSTAGRAM_URL": "Instagram URL", "YOUTUBE_URL": "YouTube URL", "BLOG": "Blog", "BLOG_URL": "Blog / RSS Feed URL", "PROFESSIONAL_RECORDS": "Professional records", "PERSONAL_INFORMATION": "Personal information", "FIRST_NAME": "First name", "MIDDLE_INITIAL": "Middle initial(s)", "LAST_NAME": "Last name", "PROFESSIONAL_CREDENTIALS": "Professional credentials", "GENDER": "Gender", "GENDERS": {"SELECT_OPTION": "Select option", "MALE": "Male", "FEMALE": "Female", "OTHER": "Other"}, "IS_PROVIDER": "Provider Type", "IS_PROVIDER_ENUM": {"NOT_SPECIFIED": "Select Option", "PRACTITIONER": "Practitioner", "CLINIC": "Clinic"}, "PRACTICE_NAME": "Practice Name", "EMAIL_ADDRESS": "Email address", "ACCEPTING_PATIENTS": "Accepting patients", "INSURANCES_ACCEPTED": "Insurances accepted", "ADD_INSURANCE": "'+ Add insurance", "HOSPITAL_AFFILIATIONS": "Hospital affiliations", "ADD_AFFILIATIONS": "+ Add affiliations", "QUALIFICATIONS_EXPERIENCE": {"DOCTORDOTCOM_CATEGORIES": "Doctor.com Categories", "QUALIFICATIONS_EXPERIENCE": "Qualifications & experience", "EDUCATION": "Education", "SPECIALTIES": "Specialties", "RESIDENCIES": "Residencies", "FELLOWSHIPS": "Fellowships", "ADD_EDUCATION": "+ Add education", "ADD_SPECIALTY": "+ Add specialty", "ADD_RESIDENCY": "+ Add residency", "ADD_FELLOWSHIP": "+ Add fellowship", "NPI": "NPI (National Provider Identifier)", "MEDICAL_LICENSE_NUMBER": "Medical license number", "STATE_LICENSE": "State license"}, "PRACTICE_INFORMATION": "Practice information", "OFFICE_NAME": "Firm/Clinic/Office name", "MARKETING": "Marketing", "SEARCH_ENGINE_INFORMATION": "Search engine information", "SEO_KEYWORDS": "SEO keywords", "ADD_SEO_KEYWORD": "+ Add SEO keyword", "SHARE_OF_VOICE_KEYWORDS": "Share of voice keywords", "ADD_SHARE_OF_VOICE_KEYWORD": "+ Add share of voice keyword", "COMPETITOR_NAMES": "Competitor names", "ADD_COMPETITOR": "+ Add competitor", "BUSINESS_DESCRIPTIONS": "Business descriptions", "TAGLINE": "Tagline", "SHORT_DESCRIPTION": "Short description", "DESCRIPTION": "Description", "LONG_DESCRIPTION": "Long description", "MEDIA": "Media", "ATTRIBUTES": "Custom fields", "IMAGES": "Images", "DESCRIPTIONS": "Descriptions", "ATTRIBUTES_TAB": "Attributes", "CUSTOM_FIELDS": "Custom fields", "ADMINISTRATION": "Administration", "SALES": "Sales", "MARKET": "Market", "PRIMARY_SALESPERSON": "Primary salesperson", "ADD_ADDITIONAL_SALESPEOPLE": "+ Add additional salespeople", "PRIMARY_SALESPERSON_MUST_BE_SELECTED": "Primary salesperson must be selected", "ADDITIONAL_SALESPEOPLE": "Additional salespeople", "CUSTOMER_IDENTIFIER": "Customer identifier", "TAGS": "Tags", "NOTES": "Notes", "PHOTO": "Photo", "GALLERY": "Gallery", "MEDIA_TAB": {"PHOTO_IMAGE_NOUN": "Photo", "PHOTOS": "Gallery", "ADD_PHOTOS": "Add photos", "UPLOAD_PHOTO": "Upload photo", "UNABLE_TO_LOAD_IMAGES": "Unable to load images.", "LOAD_MORE": "Load more", "SHOWING_X_OF_Y": "Showing {{count}} of {{total}}", "ADDING_IMAGES_BOLSTERS": "Adding images bolsters your profile.", "IMAGE_MENU": {"SET_LOGO_QUESTION": "Set logo?", "SET_LOGO": "Set logo", "SET_LOGO_BODY": "Would you like to set this photo as your logo? The photo will automatically be cropped to a square format and replace any existing logo.", "SET_PRIMARY": "Set primary", "SET_PRIMARY_PHOTO": "Set primary photo?", "SET_PRIMARY_BODY": "Would you like to set this photo as your primary photo? This will replace any existing primary photo.", "DELETE_PHOTO_QUESTION": "Are you sure you want to delete this photo", "DELETE_PHOTO": "Delete photo", "DELETE_PHOTO_BODY": "This action cannot be undone", "CROP_PHOTO": "Crop photo", "EDIT": "Edit", "DELETE": "Delete", "UPLOAD_NEW": "Upload New", "GALLERY_SECOND_TITLE": "These photos will be shared on listings that accept images", "GALLERY_ALERT": "Add up to 10 images at a time. 5 MB max file size.", "ADD_GALLARY_PHOTO": "Add gallery photo", "MAIN_LOGO_TITLE": "Main logo", "MAIN_LOGO_HOVER_TEXT": "Your primary business logo. Used for syncing listings and should have a 1:1 aspect ratio.", "MAIN_LOGO_BUTTON_TEXT": "Add main logo", "PRIMARY_TITLE": "Cover photo ", "PRIMARY_HOVER_TEXT": "Showcases your business, products, or style. Used for syncing listings with a 16:9 aspect ratio.", "PRIMARY_BUTTON_TEXT": "Add cover photo", "IMAGE_UPDATE_TITLE": "Upload New Image", "IMAGE_UPDATE_BODY": " Select a new image to replace the picture. Ensure it meets the required size and dimensions."}, "LOGO": "Logo", "PRIMARY": "Primary", "PHOTO_UPLOADED_SUCCESSFULLY": "Photo uploaded successfully!", "LOGO_SET_SUCCESSFULLY": "Logo set successfully", "ERROR_SETTING_PHOTO_AS_LOGO": "Error setting photo as logo", "MAIN_LOGO_SET_SUCCESSFULLY": "Main logo uploaded successfully!", "COVER_PHOTO_SET_SUCCESSFULLY": "Cover photo uploaded successfully!", "ERROR_SETTING_PHOTO": "Error while uploading photo", "PHOTO_DELETED": "Photo deleted", "ERROR_DELETING_PHOTO": "Error deleting photo"}, "TOOLTIPS": {"SELLING_METHOD_TOOLTIP": "The primary method that your business selling stuff.", "LANDMARK_TOOLTIP": "The general area in which your business is physically located. Typical landmark addresses include mall and office names (for example, Oakview Mall or Clocktower Plaza). Only enter a landmark if your street address does not accurately pinpoint your business's location.", "SERVICES_OFFERED_TOOLTIP": "The primary services that your business offers (for example, personal tax filing or root canals).", "BRANDS_CARRIED_TOOLTIP": "The primary brands that your business carries (for example, Ford, Apple,  Firestone, or Allstate).", "SHARE_OF_VOICE_TOOLTIP": "Enter products, services or other keywords to compare the business' search engine share of voice to their competition.", "COMPETITORS_TOOLTIP": "Enter competitor names to benchmark the search engine prominence of those competitors against the business.", "ADDITIONAL_SALESPERSON_TOOLTIP": "The primary salesperson's information will be displayed in the store and in campaign emails for this account. However, you can assign up to 5 additional salespeople to this account.", "CUSTOMER_ID_TOOLTIP": "Changing this ID may result in unintended consequences, particularly if your company uses an SSO integration. If you are unsure if you use SSO, please reach out to your administrator before proceeding.", "VTAX_TOOLTIP": "Business categories help to ensure that relevant listing sources are turned on within {{rmProductName}} (Ex. Selecting 'Restaurants' will turn Urbanspoon to ON and turn Cars.com to OFF). Business categories will also help retrieve the appropriate RSS Feeds in {{smProductName}} and industry averages throughout the product suite.", "CALL_TRACKING_TOOLTIP": "A call tracking number is a phone number that gathers analytics for inbound calls. Call tracking is commonly used as a method of performance assessment for marketing campaigns. This number will be used to find business listings and assess their accuracy in the Listings tab of {{rmProductName}}.", "TAGLINE_TOOLTIP": "Your company slogan or a brief description of the business. The tagline will appear directly below the company name at the top of the {{msProductName}} My Listing.", "COMMON_BUSINESS_NAME_TOOLTIP": "Other names that your business is commonly called (Ex. <PERSON>'s Irish Pub referred to as Patty's or <PERSON>'s Pub). Common business names will become mention searches within {{rmProductName}}.", "SEO_CATEGORY_TOOLTIP": "SEO (Search Engine Optimization) keywords are key words or phrases that help customers find your business via search engines. These keywords will be displayed on the {{msProductName}} My Listing to enhance SEO."}, "LENGTH_OF_REPEATED_FIELD_ERROR": "Must be less than {{maxLength}} characters across all {{fieldName}}, when joined with commas.", "PAYMENT_METHODS": {"PAYMENT_METHODS": "Payment methods", "AMERICAN_EXPRESS": "American Express", "ANDROID_PAY": "Google Pay", "APPLE_PAY": "Apple Pay", "CASH": "Cash", "CHECK": "Check", "DEBIT": "Debit", "DINERS_CLUB": "Diners Club", "DISCOVER": "Discover", "MASTERCARD": "MasterCard", "PAYPAL": "PayPal", "SAMSUNG_PAY": "Samsung Pay", "STORE_CARD": "Store card", "TRAVELERS_CHECK": "Travelers check", "VISA": "Visa", "CCS": "CCS", "SODEXO": "Sodexo", "GOPAY": "GoPay", "V_PAY": "V PAY", "FINANCING": "Financing", "INVOICE": "Invoice", "PAYSEC": "PaySec", "BITCOIN": "Bitcoin", "INVALID_PAYMENT_METHODS": "Selected payment method \"{{paymentMethods}}\" is not valid for your country"}, "STANDARDIZED_TITLES": {"ACCOUNTING": "ACCOUNTING", "ADMINISTRATOR": "ADMINISTRATOR", "BOARD_MEMBER": "BOARD MEMBER", "CHAIRMAN": "CHAIRMAN", "CEO": "CEO", "CIO_CTO": "CIO / CTO", "CONTROLLER": "CONTROLLER", "CORPORATE_TAX": "CORPORATE TAX", "DIRECTOR": "DIRECTOR", "DIVISION_HEAD": "DIVISION HEAD", "E_COMMERCE": "E COMMERCE", "EDUCATOR": "EDUCATOR", "ENGINEERING": "ENGINEERING", "ENVIRONMENT": "ENVIRONMENT", "EVP": "EVP", "EXECUTIVE": "EXECUTIVE", "FACILITIES": "FACILITIES", "FINANCE": "FINANCE", "INSURANCE": "INSURANCE", "INTERNAL_AUDIT": "INTERNAL AUDIT", "INTERNATIONAL": "INTERNATIONAL", "IT_EXECUTIVE": "IT EXECUTIVE", "LEGAL": "LEGAL", "LEGAL_COUNSEL": "LEGAL COUNSEL", "MANAGER": "MANAGER", "MANUFACTURING": "MANUFACTURING", "MARKETING": "MARKETING", "MINISTER": "MINISTER", "OFFICE_MANAGER": "OFFICE MANAGER", "OPERATIONS": "OPERATIONS", "OWNER": "OWNER", "PARTNER": "PARTNER", "PRESIDENT": "PRESIDENT", "PRINCIPAL": "PRINCIPAL", "PUBLISHER": "PUBLISHER", "PURCHASING": "PURCHASING", "QUALITY": "QUALITY", "REAL_ESTATE": "REAL ESTATE", "REGION_MANAGER": "REGION MANAGER", "SALES": "SALES", "SITE_MANAGER": "SITE MANAGER", "SVP": "SVP", "TRAINING": "TRAINING", "TREASURER": "TREASURER", "VICE_CHAIRMAN": "VICE CHAIRMAN", "VICE_PRESIDENT": "VICE PRESIDENT", "VP": "VP", "WEBMASTER": "WEBMASTER"}, "INVALID_FIELD": {"BUSINESS_INFO": {"BUSINESS_TYPE": "Your <i>Service Area Business</> type must be selected", "SERVICE_AREAS": "Please select at least one service area", "ADDRESS": {"COUNTRY": "Please select a valid Country", "CITY": "Please enter a City", "PROVINCE": "Please select a valid State/Province", "ZIP": "Please enter a valid ZIP code", "LINE1": "Please enter a Street Address"}, "EMAIL": "Please enter a valid Email Address", "WEBSITE": "Please enter a valid Website", "BUSINESS_NAME": "Please enter a Business Name", "PRIMARY_BUSINESS_CATEGORY": "Please select a Primary Business Category", "PHONE_NUMBERS": "Please enter a valid Phone Number", "CALL_TRACKING_NUMBERS": "Please enter a valid Call Tracking Number", "CELL_NUMBER": "Please enter a valid Cell Number", "TOLL_FREE_NUMBER": "Please enter a valid Cell Number", "SERVICS_OFFERED_LENGTH": "You may only set upto 15 Services Offered", "SERVICS_OFFERED_CHAR_COUNT": "Services have a character limit of 256, please shorten your entry", "BRANDS_CARRIED_LENGTH": "You may only set upto 15 Brands Carried", "BRANDS_CARRIED_CHAR_COUNT": "Brands have a character limit of 256, please shorten your entry", "PAYMENT_METHODS": "Payment methods must be valid for the specified country"}, "SOCIAL_URL": {"FACEBOOK": "Please enter a valid Facebook URL", "FOURSQUARE": "Please enter a valid Foursquare URL", "LINKEDIN": "Please enter a valid LinkedIn URL", "TWITTER": "Please enter a valid Twitter URL", "PINTEREST": "Please enter a valid Pinterest URL", "INSTAGRAM": "Please enter a valid Instagram URL", "YOUTUBE": "Please enter a valid YouTube URL"}, "DESCRIPTION": {"SHORT": "The short description must be less than 200 characters", "LONG": "The long description must be less than 750 characters"}, "PROFESSIONAL_RECORDS": {"EDUCATIONS": "You may only set upto 15 Educational Degrees", "EDUCATIONS_CHAR_COUNT": "Education entries have a character limit of 256, please shorten your entry", "SPECIALTIES": "You may only set upto 15 Specialties", "SPECIALTIES_CHAR_COUNT": "Specialties have a character limit of 256, please shorten your entry", "RESIDENCIES": "You may only set upto 15 Residencies", "RESIDENCIES_CHAR_COUNT": "Residencies have a character limit of 256, please shorten your entry", "FELLOWSHIPS": "You may only set upto 15 Fellowships", "FELLOWSHIPS_CHAR_COUNT": "Fellowships have a character limit of 256, please shorten your entry", "HOSPITAL_AFFILIATIONS": "You may only set upto 15 Hospital Affiliations", "HOSPITAL_AFFILIATIONS_CHAR_COUNT": "Hospital Affiliations have a character limit of 256, please shorten your entry"}, "ADDITIONAL_PROFESSIONAL_INFO": {"INSURANCES_ACCEPTED": "You may only set upto 15 Insurances Accepted", "INSURANCES_ACCEPTED_CHAR_COUNT": "Insurances Accepted have a character limit of 256, please shorten your entry", "HOSPITAL_AFFILIATIONS": "You may only set upto 15 Hospital Affiliations", "HOSPITAL_AFFILIATIONS_CHAR_COUNT": "Hospital Affiliations have a character limit of 256, please shorten your entry"}, "ADMIN": {"MARKET": "Please select a valid Market", "PRIMARY_SALESPERSON": "Please select a Primary Salesperson", "ADDITIONAL_SALESPERSON": "Please select an Additional Salesperson", "PRIMARY_NOT_IN_ADDITIONAL_SALESPERSON": "Salesperson is already set as Primary Salesperson"}}, "NO_CHANGES_TO_SAVE": "No changes to save", "INVALID_SUBMISSION": "Invalid submission", "INVALID_SESSION": "Invalid Session. Please try again.", "FAILED_TO_UPDATE": "Failed to update", "HOURS_OF_OPERATION_SAVED": "Hours of operation saved", "CUSTOM_FIELDS_SAVED": "New custom field values have been saved", "ACCOUNT_WAS_SAVED": "Account was saved", "BUSINESS_HOURS_SAVED": "Business hours saved", "ACCOUNT_ATTRIBUTES_SAVED": "Custom fields saved", "ACCOUNT_ATTRIBUTES_ERROR": "Error saving custom fields", "ACCOUNT_ATTRIBUTES_DISMISS": "<PERSON><PERSON><PERSON>", "ALREADY_THE_PRIMARY_SALESPERSON": "{{name}} is already the primary salesperson.", "BUSINESS_OPERATION_METHODS": {"SELECT_OPTION": "Select option", "UNSPECIFIED": "Unspecified", "OPEN": "Open", "LIMITED": "Limited", "TEMPORARY": "Temporarily closed", "PERMANENT": "Permanent closed"}, "SELLING_METHODS_AVAILABILITY": {"UNSET": "-", "YES": "Yes", "NO": "No"}, "SERVICE_AVAILABILITY": {"ECOMMERCE_ONLY": "Online store only"}, "PRODUCT_ACTIVATION_FORM": {"FULL_BUSINESS_PROFILE": "Business profile", "FULL_BUSINESS_PROFILE_OPTIONAL": "Optional", "FULL_BUSINESS_PROFILE_REQUIRED": "Required", "FULL_BUSINESS_PROFILE_DESCRIPTION": "Review your business profile to submit this order", "FULL_BUSINESS_PROFILE_READ_ONLY_DESCRIPTION": "Account information was previously updated", "SUCCESS_MESSAGE": "The business profile was updated", "ERROR_MESSAGE": "Something went wrong. Refresh the page and try again.", "ACCOUNT_INFORMATION": "Account information", "ACCOUNT_INFORMATION_SUBTITLE": "This order requires certain fields to be saved to the account", "ACCOUNT_INFORMATION_PANEL": "Update the account before continuing, products require accurate business information", "HOURS_OF_OPERATION_REQUIRED": "Hours of operation are required", "INVALID_SUBMISSION_MESSAGE": "Some required fields haven’t been filled out or are incorrect", "INVALID_SERVICES_OFFERED_FIELD": "Services offered can only be 256 characters in length total", "INVALID_BRANDS_CARRIED_FIELD": "Brands carried can only be 256 characters in length total"}, "WRONG_MARKET": "Wrong market", "UNSAVED_CHANGES": "There are unsaved changes", "TIMEZONE": "Timezone", "SELECTOR_DIALOG": {"ACCOUNT": "Search for a company", "ACCOUNTS": "Accounts", "CHOOSE_ACCOUNT": "Choose account", "CREATE_ACCOUNT": "Create account", "ACCOUNTS_NOT_FOUND": "No accounts found", "SEARCH_ACCOUNT": "Search account"}, "ADD_ACCOUNTS_TO_LISTS": {"TITLE": "Add to list", "SELECT_ACCOUNTS": {"LABEL": "Which accounts would you like to add to a list?", "OPTIONS": {"VISIBLE_ACCOUNTS": "Visible", "SELECTED_ACCOUNTS": "Selected", "ALL_ACCOUNTS": "All"}, "ACCOUNTS": "accounts", "SUCCESS": "Added {{accounts}} accounts to list {{list}}"}, "SELECT_LIST": "Add to list", "NAME_NEW_LIST": "Name of new list", "CREATE_AND_ADD": "Create and add to list", "SUCCESS": "Added {{accounts}} accounts to list {{list}}", "DESCRIPTION": "Export results to an Account List"}, "MORE_TAB_LABEL": "More"}}}