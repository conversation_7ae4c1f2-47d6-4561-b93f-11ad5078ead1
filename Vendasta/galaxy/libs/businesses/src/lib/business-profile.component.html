<form
  [class]="(loading$ | async) === true ? 'loading' : ''"
  [formGroup]="accountGroupForm"
  #accountGroupNgForm="ngForm"
  (keydown.enter)="$event.preventDefault()"
  (ngSubmit)="onSubmit($event)"
  novalidate
>
  <mat-tab-group
    mat-stretch-tabs="false"
    mat-align-tabs="start"
    [selectedIndex]="activeTab"
    (selectedIndexChange)="onTabSwitch($event)"
    [ngClass]="{ 'background-lighter-grey': tabBackground === 'lighter-grey' }"
  >
    <mat-tab [disabled]="(loading$ | async) === true" *ngIf="isTabEnabled(businessProfileTab.Primary)">
      <ng-template mat-tab-label>
        <span
          [ngClass]="{
            'tab-invalid': tabInvalid(businessProfileTab.Primary),
            'tab-changed': tabChanged(businessProfileTab.Primary) && !tabInvalid(businessProfileTab.Primary),
          }"
          class="tab"
        >
          {{ 'FRONTEND.BUSINESSES.PRIMARY_INFO' | translate }}
        </span>
      </ng-template>
      <div class="business-profile__tab-container">
        <div class="business-profile__card">
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && basicInfoForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.BASICS' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div formGroupName="basicInfoForm">
                <forms-input
                  [control]="accountGroupForm.get('basicInfoForm.businessName')"
                  [placeholder]="'FRONTEND.BUSINESSES.BUSINESS_NAME' | translate"
                  [required]="true"
                  bottomSpacing="small"
                ></forms-input>

                <forms-va-input-repeated
                  [controlArray]="accountGroupForm.get('basicInfoForm.commonBusinessNames')"
                  [tooltip]="
                    'FRONTEND.BUSINESSES.TOOLTIPS.COMMON_BUSINESS_NAME_TOOLTIP'
                      | translate: { rmProductName: rmProductName }
                  "
                  [addText]="'FRONTEND.BUSINESSES.ADD_COMMON_BUSINESS_NAME' | translate"
                  [placeholder]="'FRONTEND.BUSINESSES.COMMON_BUSINESS_NAME' | translate"
                  [label]="'FRONTEND.BUSINESSES.COMMON_BUSINESS_NAME' | translate"
                  [maxFields]="3"
                  [disableAdd]="editDisabled"
                  bottomSpacing="small"
                ></forms-va-input-repeated>

                <forms-input
                  [control]="accountGroupForm.get('basicInfoForm.website')"
                  [placeholder]="'FRONTEND.BUSINESSES.WEBSITE' | translate"
                  bottomSpacing="small"
                ></forms-input>

                <glxy-form-field
                  *ngIf="accountGroupForm.get('basicInfoForm.primaryBusinessCategory') as primaryBusinessCategoryCtrl"
                >
                  <glxy-label>{{ 'FRONTEND.BUSINESSES.PRIMARY_CATEGORY' | translate }}</glxy-label>
                  <category-select
                    [formControl]="primaryBusinessCategoryCtrl"
                    maxCategories="1"
                    [required]="true"
                    (onCategoriesChange)="handleCategoriesChange($event)"
                    glxyPrefix
                  ></category-select>
                  <glxy-hint class="business-category-hint">
                    {{ 'FRONTEND.BUSINESSES.BUSINESS_CATEGORIES_HINT' | translate }}
                  </glxy-hint>
                  <glxy-hint align="right">{{ primaryBusinessCategoryCtrl.value.length }}/1</glxy-hint>
                  <glxy-error *ngIf="primaryBusinessCategoryCtrl.value.length < 1">
                    {{ 'FRONTEND.BUSINESSES.BUSINESS_CATEGORIES_ERROR' | translate }}
                  </glxy-error>
                </glxy-form-field>

                <glxy-form-field
                  *ngIf="accountGroupForm.get('basicInfoForm.businessCategories') as businessCategoriesCtrl"
                >
                  <glxy-label>{{ 'FRONTEND.BUSINESSES.ADDITIONAL_CATEGORIES' | translate }}</glxy-label>
                  <category-select
                    [formControl]="businessCategoriesCtrl"
                    maxCategories="9"
                    glxyPrefix
                  ></category-select>
                  <glxy-hint align="right">{{ businessCategoriesCtrl.value.length }}/9</glxy-hint>
                </glxy-form-field>
              </div>
            </mat-card-content>
          </mat-card>
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && phoneForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.PHONE' | translate }}
              </mat-card-title>
              <mat-card-subtitle>
                {{ 'FRONTEND.BUSINESSES.PHONE_NUMBERS_ALLOWED_FORMATS' | translate }}
              </mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <forms-va-input-repeated
                [controlArray]="accountGroupForm.get('phoneForm.workNumbers')"
                [placeholder]="'FRONTEND.BUSINESSES.PHONE_NUMBERS' | translate"
                [addText]="'FRONTEND.BUSINESSES.ADD_PHONE_NUMBER' | translate"
                [label]="'FRONTEND.BUSINESSES.PHONE_NUMBERS' | translate"
                [maxFields]="3"
                [disableAdd]="editDisabled"
                [itemFactory]="phoneNumberItemFactory.bind(this)"
                bottomSpacing="small"
              ></forms-va-input-repeated>
              <forms-input
                [control]="accountGroupForm.get('basicInfoForm.email')"
                [placeholder]="'FRONTEND.BUSINESSES.BUSINESS_EMAIL' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('phoneForm.faxNumber')"
                [placeholder]="'FRONTEND.BUSINESSES.FAX_NUMBER' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('phoneForm.cellNumber')"
                [placeholder]="'FRONTEND.BUSINESSES.CELL_NUMBER' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-va-input-repeated
                [controlArray]="accountGroupForm.get('phoneForm.callTrackingNumbers')"
                [tooltip]="
                  'FRONTEND.BUSINESSES.TOOLTIPS.CALL_TRACKING_TOOLTIP' | translate: { rmProductName: rmProductName }
                "
                [addText]="'FRONTEND.BUSINESSES.ADD_CALL_TRACKING_NUMBER' | translate"
                [placeholder]="'FRONTEND.BUSINESSES.CALL_TRACKING_NUMBERS' | translate"
                [label]="'FRONTEND.BUSINESSES.CALL_TRACKING_NUMBERS' | translate"
                [maxFields]="3"
                [disableAdd]="editDisabled"
                [itemFactory]="phoneNumberItemFactory.bind(this)"
                bottomSpacing="none"
              ></forms-va-input-repeated>
              <forms-input
                [control]="accountGroupForm.get('phoneForm.tollFreeNumber')"
                [placeholder]="'FRONTEND.BUSINESSES.TOLL_FREE_NUMBER' | translate"
                bottomSpacing="small"
              ></forms-input>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="business-profile__card">
          <mat-card appearance="outlined" class="business-profile__card__container">
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.LOCATION' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <forms-geo-input
                *ngIf="showMap()"
                [control]="geoControl"
                [zoom]="mapZoomLevel"
                [bounds]="inferredMapBounds"
                [tooltip]="
                  (geoControl.value
                    ? 'FRONTEND.BUSINESSES.GEOCONTROL_TOOLTIP_NO_VALUE'
                    : 'FRONTEND.BUSINESSES.GEOCONTROL_TOOLTIP_HAS_VALUE'
                  ) | translate
                "
              ></forms-geo-input>
              <glxy-checkbox
                class="service-area"
                (click)="updateServiceArea($event.target.checked)"
                [formControl]="accountGroupForm.get('basicInfoForm.serviceAreaBusiness')"
              >
                {{ 'FRONTEND.BUSINESSES.SERVICE_AREA_BUSINESS' | translate }}
              </glxy-checkbox>
              <glxy-hint class="sab-hint" [matTooltip]="'FRONTEND.BUSINESSES.SERVICE_AREA_BUSINESS_HINT' | translate">
                <a (click)="(null)" class="link">
                  {{ 'FRONTEND.FORMS.INPUT.WHATS_THIS' | translate }}
                </a>
              </glxy-hint>

              <mat-expansion-panel
                class="mat-elevation-z0"
                hideToggle
                [expanded]="accountGroupForm.get('basicInfoForm.serviceAreaBusiness').value"
              >
                <glxy-form-field bottomSpacing="none">
                  <mat-radio-group
                    (change)="markServiceAreaDirty()"
                    [formControl]="accountGroupForm.get('serviceAreaForm.businessType')"
                  >
                    <mat-radio-button [value]="1">
                      {{ 'FRONTEND.BUSINESSES.CUSTOMER_LOCATION_ONLY' | translate }}
                    </mat-radio-button>
                    <mat-radio-button [value]="2">
                      {{ 'FRONTEND.BUSINESSES.CUSTOMER_AND_BUSINESS_LOCATION' | translate }}
                    </mat-radio-button>
                  </mat-radio-group>
                </glxy-form-field>
                <glxy-form-field required="true" bottomSpacing="none">
                  <glxy-label>{{ 'FRONTEND.BUSINESSES.SERVICE_AREAS' | translate }}</glxy-label>

                  <business-place-autocomplete
                    [editDisabled]="editDisabled"
                    (selected)="addPlace($event)"
                    [geoLocation]="listingProfile()?.napData?.location"
                  ></business-place-autocomplete>
                  <glxy-hint
                    align="end"
                    [style.color]="
                      accountGroupForm.get('serviceAreaForm.places')?.value?.length > 0
                        ? 'rgba(0, 0, 0, 0.6)'
                        : '#c62828'
                    "
                  >
                    {{ accountGroupForm.get('serviceAreaForm.places')?.value?.length }}/20
                  </glxy-hint>
                  <glxy-error *ngIf="accountGroupForm.get('serviceAreaForm.places')?.value?.length < 1"></glxy-error>
                </glxy-form-field>
                <mat-chip-listbox>
                  <mat-chip-option
                    *ngFor="let place of accountGroupForm.get('serviceAreaForm.places')?.value"
                    (removed)="removePlace(place.placeId)"
                  >
                    {{ place.placeName }}
                    <mat-icon matChipRemove>cancel</mat-icon>
                  </mat-chip-option>
                </mat-chip-listbox>
              </mat-expansion-panel>
              <business-address-form-v2
                [addressData]="addressData"
                [parentForm]="basicInfoForm"
                (geoLocationChanged)="updateGeoLocation($event)"
              ></business-address-form-v2>

              <glxy-timezone-selector
                [control]="accountGroupForm.get('timeZoneControl')"
                (timezoneChanged)="handleTimeZoneChange($event)"
                [initialTimezone]="listingProfile()?.napData?.timezone"
                bottomSpacing="small"
              ></glxy-timezone-selector>
            </mat-card-content>
          </mat-card>
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && additionalInfoForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.OPERATIONAL_INFORMATION' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <!--TODO will rewrite all `formControl` and Observable value after backend update-->
              <glxy-form-field bottomSpacing="small">
                <glxy-label>
                  {{ 'FRONTEND.BUSINESSES.BUSINESS_OPEN_CLOSE' | translate }}
                </glxy-label>
                <mat-select
                  [placeholder]="'FRONTEND.BUSINESSES.BUSINESS_OPEN_CLOSE' | translate"
                  [formControl]="accountGroupForm.get('serviceAvailabilityForm.closedStatus')"
                >
                  <mat-option
                    *ngFor="let businessOperation of businessOperationMethods$ | async"
                    [value]="businessOperation.code"
                  >
                    {{ businessOperation.name }}
                  </mat-option>
                </mat-select>
                <glxy-error
                  *ngIf="
                    accountGroupForm
                      .get('serviceAvailabilityForm.closedStatus')
                      .getError('closedStatus') as businessOperationError
                  "
                >
                  {{ businessOperationError }}
                </glxy-error>
              </glxy-form-field>

              <ng-container *ngIf="accountGroupForm.get('serviceAvailabilityForm.closedStatus').value as closedStatus">
                <glxy-form-field
                  *ngIf="closedStatus !== undefined && closedStatuses.includes(closedStatus)"
                  bottomSpacing="small"
                >
                  <glxy-label>{{ 'FRONTEND.BUSINESSES.REOPENING_DATE' | translate }}</glxy-label>
                  <input
                    matInput
                    [matDatepicker]="reopeningDate"
                    placeholder="{{ 'FRONTEND.BUSINESSES.REOPENING_DATE' | translate }}"
                    [formControl]="accountGroupForm.get('serviceAvailabilityForm.reopeningDate')"
                  />
                  <mat-datepicker-toggle matSuffix [for]="reopeningDate"></mat-datepicker-toggle>
                  <mat-datepicker #reopeningDate></mat-datepicker>
                  <glxy-error
                    *ngIf="
                      accountGroupForm
                        .get('serviceAvailabilityForm.reopeningDate')
                        .getError('reopeningDate') as reopeningDateError
                    "
                  >
                    {{ reopeningDateError }}
                  </glxy-error>
                </glxy-form-field>
              </ng-container>

              <forms-input
                [control]="accountGroupForm.get('additionalInfoForm.bookingUrl')"
                [placeholder]="'FRONTEND.BUSINESSES.BOOKING_URL' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                *ngIf="showICOField$ | async"
                [control]="accountGroupForm.get('additionalInfoForm.ico')"
                [placeholder]="'FRONTEND.BUSINESSES.CONDITIONAL_FIELDS.ICO' | translate"
                bottomSpacing="small"
              ></forms-input>

              <glxy-form-field bottomSpacing="small">
                <glxy-label>
                  {{ 'FRONTEND.BUSINESSES.SERVICE_AVAILABILITY.ECOMMERCE_ONLY' | translate }}
                </glxy-label>
                <mat-select
                  [placeholder]="'FRONTEND.BUSINESSES.SERVICE_AVAILABILITY.ECOMMERCE_ONLY' | translate"
                  [formControl]="accountGroupForm.get('serviceAvailabilityForm.ecommerceOnly')"
                  [multiple]="false"
                >
                  <mat-option *ngFor="let ecommerce of ecommerceOnly$ | async" [value]="ecommerce.code">
                    {{ ecommerce.name }}
                  </mat-option>
                </mat-select>
              </glxy-form-field>
              <glxy-form-field bottomSpacing="small">
                <glxy-label>
                  {{ 'FRONTEND.BUSINESSES.PAYMENT_METHODS.PAYMENT_METHODS' | translate }}
                </glxy-label>
                <mat-select
                  [placeholder]="'FRONTEND.BUSINESSES.PAYMENT_METHODS.PAYMENT_METHODS' | translate"
                  [formControl]="accountGroupForm.get('additionalInfoForm.paymentMethods')"
                  [multiple]="true"
                >
                  <mat-option *ngFor="let paymentMethod of paymentMethods$ | async" [value]="paymentMethod.code">
                    {{ paymentMethod.name }}
                  </mat-option>
                </mat-select>
                <glxy-error
                  *ngIf="
                    accountGroupForm
                      .get('additionalInfoForm.paymentMethods')
                      .getError('paymentMethods') as paymentMethodError
                  "
                >
                  {{ paymentMethodError }}
                </glxy-error>
              </glxy-form-field>
              <forms-va-input-repeated
                [controlArray]="accountGroupForm.get('additionalInfoForm.servicesOffered')"
                [tooltip]="'FRONTEND.BUSINESSES.TOOLTIPS.SERVICES_OFFERED_TOOLTIP' | translate"
                [addText]="'FRONTEND.BUSINESSES.ADD_SERVICE' | translate"
                [placeholder]="'FRONTEND.BUSINESSES.SERVICES_OFFERED' | translate"
                [label]="'FRONTEND.BUSINESSES.SERVICES_OFFERED' | translate"
                [maxFields]="15"
                [disableAdd]="editDisabled"
                [errorMessages]="CUSTOM_ERROR_MESSAGES"
                bottomSpacing="none"
              ></forms-va-input-repeated>
              <forms-va-input-repeated
                [controlArray]="accountGroupForm.get('additionalInfoForm.brandsCarried')"
                [tooltip]="'FRONTEND.BUSINESSES.TOOLTIPS.BRANDS_CARRIED_TOOLTIP' | translate"
                [addText]="'FRONTEND.BUSINESSES.ADD_BRAND' | translate"
                [placeholder]="'FRONTEND.BUSINESSES.BRANDS_CARRIED' | translate"
                [label]="'FRONTEND.BUSINESSES.BRANDS_CARRIED' | translate"
                [maxFields]="15"
                [disableAdd]="editDisabled"
                [errorMessages]="CUSTOM_ERROR_MESSAGES"
                bottomSpacing="none"
              ></forms-va-input-repeated>
              <forms-input
                [control]="accountGroupForm.get('additionalInfoForm.landmark')"
                [placeholder]="'FRONTEND.BUSINESSES.LANDMARK' | translate"
                [tooltip]="'FRONTEND.BUSINESSES.TOOLTIPS.LANDMARK_TOOLTIP' | translate"
                bottomSpacing="none"
              ></forms-input>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>
    <mat-tab [disabled]="(loading$ | async) === true" *ngIf="isTabEnabled(businessProfileTab.Hours)">
      <ng-template mat-tab-label>
        <span
          [ngClass]="{
            'tab-invalid': tabInvalid(businessProfileTab.Hours),
            'tab-changed': tabChanged(businessProfileTab.Hours),
          }"
          class="tab"
        >
          {{ 'FRONTEND.BUSINESSES.HOURS' | translate }}
        </span>
      </ng-template>
      <div class="tab-container">
        <div class="row row-gutters">
          <div class="col col-sm-12 col-md-6">
            <forms-business-hours
              [loading$]="loading$"
              [control]="businessHoursForm"
              (changes)="onBusinessHoursChange()"
            ></forms-business-hours>
          </div>
          <div class="col col-sm-12 col-md-6">
            <forms-special-hours
              [loading$]="loading$"
              [control]="specialHoursForm"
              (changes)="onBusinessHoursChange()"
            ></forms-special-hours>
          </div>
        </div>
        <div class="row row-gutters">
          <div class="col col-sm-12 col-md-6">
            <business-profile-more-hours
              *ngIf="moreHoursTypes$ | async as moreHoursTypes"
              [moreHoursTypes]="moreHoursTypes"
              [parentFormGroup]="moreHoursForm"
              (changed)="onMoreHoursChange()"
              [loading$]="loading$"
              [editDisabled]="editDisabled"
            ></business-profile-more-hours>
          </div>
        </div>
      </div>
    </mat-tab>

    <mat-tab [disabled]="(loading$ | async) === true" *ngIf="isTabEnabled(businessProfileTab.Descriptions)">
      <ng-template mat-tab-label>
        <span
          [ngClass]="{
            'tab-invalid': tabInvalid(businessProfileTab.Descriptions),
            'tab-changed':
              tabChanged(businessProfileTab.Descriptions) &&
              !(accountGroupNgForm.submitted && tabInvalid(businessProfileTab.Descriptions)),
          }"
          class="tab"
        >
          {{ 'FRONTEND.BUSINESSES.DESCRIPTIONS' | translate }}
        </span>
      </ng-template>
      <div class="business-profile__tab-container">
        <div class="business-profile__card">
          <mat-card
            appearance="outlined"
            class="business-profile__full-card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && businessDescriptionsForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.BUSINESS_DESCRIPTIONS' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <ng-container>
                <forms-input-textarea-ai
                  [control]="accountGroupForm.get('businessDescriptionsForm.shortDescription')"
                  [placeholder]="'FRONTEND.BUSINESSES.SHORT_DESCRIPTION' | translate"
                  [fieldName]="'FRONTEND.BUSINESSES.DESCRIPTION' | translate"
                  [fieldKey]="'shortDescription'"
                  [maxLength]="200"
                  [fetchSuggestion]="getSuggestion"
                  [fetchUpdateSuggestion]="suggestFieldUpdate"
                ></forms-input-textarea-ai>
                <forms-input-textarea-ai
                  [control]="accountGroupForm.get('businessDescriptionsForm.longDescription')"
                  [placeholder]="'FRONTEND.BUSINESSES.LONG_DESCRIPTION' | translate"
                  [fieldName]="'FRONTEND.BUSINESSES.DESCRIPTION' | translate"
                  [fieldKey]="'longDescription'"
                  [maxLength]="longDescriptionLength"
                  [fetchSuggestion]="getSuggestion"
                  [fetchUpdateSuggestion]="suggestFieldUpdate"
                ></forms-input-textarea-ai>
              </ng-container>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>
    <mat-tab [disabled]="(loading$ | async) === true" *ngIf="isTabEnabled(businessProfileTab.Images)">
      <ng-template mat-tab-label>
        <span
          [ngClass]="{
            'tab-changed': tabChanged(businessProfileTab.Images),
          }"
          class="tab"
        >
          {{ 'FRONTEND.BUSINESSES.IMAGES' | translate }}
        </span>
      </ng-template>
      <div class="business-profile__tab-container">
        <div class="business-profile__card">
          <business-media-tab
            [accountGroupId]="accountGroupId()"
            [editDisabled]="editDisabled"
            (imageClicked)="openLightbox($event)"
          ></business-media-tab>
        </div>
      </div>
    </mat-tab>

    <mat-tab [disabled]="(loading$ | async) === true" *ngIf="isTabEnabled(businessProfileTab.Social)">
      <ng-template mat-tab-label>
        <span
          [ngClass]="{
            'tab-invalid': tabInvalid(businessProfileTab.Social),
            'tab-changed':
              tabChanged(businessProfileTab.Social) &&
              !(accountGroupNgForm.submitted && tabInvalid(businessProfileTab.Social)),
          }"
          class="tab"
        >
          {{ 'FRONTEND.BUSINESSES.SOCIAL' | translate }}
        </span>
      </ng-template>
      <div class="business-profile__tab-container">
        <div class="business-profile__card">
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && businessPagesForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.BUSINESS_PAGES' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <forms-input
                [control]="accountGroupForm.get('businessPagesForm.facebookUrl')"
                [placeholder]="'FRONTEND.BUSINESSES.FACEBOOK_URL' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('businessPagesForm.linkedInUrl')"
                [placeholder]="'FRONTEND.BUSINESSES.LINKEDIN_URL' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('businessPagesForm.twitterUrl')"
                [placeholder]="'FRONTEND.BUSINESSES.TWITTER_URL' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('businessPagesForm.pinterestUrl')"
                [placeholder]="'FRONTEND.BUSINESSES.PINTEREST_URL' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('businessPagesForm.instagramUrl')"
                [placeholder]="'FRONTEND.BUSINESSES.INSTAGRAM_URL' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('businessPagesForm.youTubeUrl')"
                [placeholder]="'FRONTEND.BUSINESSES.YOUTUBE_URL' | translate"
                bottomSpacing="small"
              ></forms-input>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="business-profile__card">
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && blogForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.BLOG' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <forms-input
                [control]="accountGroupForm.get('blogForm.blogOrRssFeedUrl')"
                [placeholder]="'FRONTEND.BUSINESSES.BLOG_URL' | translate"
                bottomSpacing="small"
              ></forms-input>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>

    <mat-tab
      [disabled]="(loading$ | async) === true"
      *ngIf="hasProfessionalCategory && isTabEnabled(businessProfileTab.Professional)"
    >
      <ng-template mat-tab-label>
        <span
          [ngClass]="{
            'tab-invalid': tabInvalid(businessProfileTab.Professional),
            'tab-changed':
              tabChanged(businessProfileTab.Professional) &&
              !(accountGroupNgForm.submitted && tabInvalid(businessProfileTab.Professional)),
          }"
          class="tab"
        >
          {{ 'FRONTEND.BUSINESSES.PROFESSIONAL_RECORDS' | translate }}
        </span>
      </ng-template>
      <div class="business-profile__tab-container">
        <div class="business-profile__card">
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && personalInformationForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.PERSONAL_INFORMATION' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <glxy-form-field>
                <glxy-label>Standardized Title</glxy-label>
                <mat-select
                  placeholder="Standardized Title"
                  [formControl]="accountGroupForm.get('personalInformationForm.standardizedTitle')"
                >
                  <mat-option
                    *ngFor="let standardizedTitle of standardizedTitles$ | async"
                    [value]="standardizedTitle.code"
                  >
                    {{ standardizedTitle.name }}
                  </mat-option>
                </mat-select>
              </glxy-form-field>
              <forms-input
                [control]="accountGroupForm.get('personalInformationForm.firstName')"
                [placeholder]="'FRONTEND.BUSINESSES.FIRST_NAME' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('personalInformationForm.middleInitials')"
                [placeholder]="'FRONTEND.BUSINESSES.MIDDLE_INITIAL' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('personalInformationForm.lastName')"
                [placeholder]="'FRONTEND.BUSINESSES.LAST_NAME' | translate"
                bottomSpacing="small"
              ></forms-input>
              <glxy-form-field>
                <glxy-label>{{ 'FRONTEND.BUSINESSES.PROFESSIONAL_CREDENTIALS' | translate }}</glxy-label>
                <mat-select
                  [formControl]="accountGroupForm.get('personalInformationForm.professionalCredentials')"
                  [placeholder]="'FRONTEND.BUSINESSES.PROFESSIONAL_CREDENTIALS' | translate"
                  [multiple]="true"
                >
                  <mat-option
                    *ngFor="let professionalCredential of professionalCredentials"
                    [value]="professionalCredential"
                  >
                    {{ professionalCredential }}
                  </mat-option>
                </mat-select>
              </glxy-form-field>
              <glxy-form-field>
                <glxy-label>{{ 'FRONTEND.BUSINESSES.GENDER' | translate }}</glxy-label>
                <mat-select
                  [formControl]="accountGroupForm.get('personalInformationForm.gender')"
                  [placeholder]="'FRONTEND.BUSINESSES.GENDER' | translate"
                >
                  <mat-option *ngFor="let gender of genders$ | async" [value]="gender.code">
                    {{ gender.name }}
                  </mat-option>
                </mat-select>
              </glxy-form-field>
              <forms-input
                [control]="accountGroupForm.get('personalInformationForm.emailAddress')"
                [placeholder]="'FRONTEND.BUSINESSES.EMAIL_ADDRESS' | translate"
              ></forms-input>
            </mat-card-content>
          </mat-card>
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && additionalProfessionalInfoForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.OPERATIONAL_INFORMATION' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <glxy-form-field bottomSpacing="small">
                <glxy-label>{{ 'FRONTEND.BUSINESSES.ACCEPTING_PATIENTS' | translate }}</glxy-label>
                <mat-select
                  [formControl]="accountGroupForm.get('additionalProfessionalInfoForm.acceptingPatients')"
                  [placeholder]="'FRONTEND.BUSINESSES.ACCEPTING_PATIENTS' | translate"
                >
                  <mat-option [value]="true">
                    {{ 'FRONTEND.BUSINESSES.YES' | translate }}
                  </mat-option>
                  <mat-option [value]="false">
                    {{ 'FRONTEND.BUSINESSES.NO' | translate }}
                  </mat-option>
                </mat-select>
              </glxy-form-field>
              <forms-va-input-repeated
                [controlArray]="accountGroupForm.get('additionalProfessionalInfoForm.insurancesAccepted')"
                [placeholder]="'FRONTEND.BUSINESSES.INSURANCES_ACCEPTED' | translate"
                [label]="'FRONTEND.BUSINESSES.INSURANCES_ACCEPTED' | translate"
                [addText]="'FRONTEND.BUSINESSES.ADD_INSURANCE' | translate"
                [maxFields]="15"
                [disableAdd]="editDisabled"
                [errorMessages]="CUSTOM_ERROR_MESSAGES"
                bottomSpacing="none"
              ></forms-va-input-repeated>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="business-profile__card">
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && qualificationsAndExperienceForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.QUALIFICATIONS_EXPERIENCE' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="doctordotcom-categories">
                <glxy-form-field>
                  <glxy-label>
                    {{ 'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.DOCTORDOTCOM_CATEGORIES' | translate }}
                  </glxy-label>
                  <mat-select
                    [formControl]="accountGroupForm.get('qualificationsAndExperienceForm.doctorDotComCategories')"
                    [placeholder]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.DOCTORDOTCOM_CATEGORIES' | translate"
                    [multiple]="true"
                    [compareWith]="compareDoctorDotComCategories"
                  >
                    <mat-option
                      *ngFor="let doctordotcomCategory of availableDoctorDotComCategories | async"
                      [value]="doctordotcomCategory"
                    >
                      {{ doctordotcomCategory.fullName }}
                    </mat-option>
                  </mat-select>
                </glxy-form-field>
              </div>
              <forms-va-input-repeated
                [controlArray]="accountGroupForm.get('qualificationsAndExperienceForm.educations')"
                [placeholder]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.EDUCATION' | translate"
                [label]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.EDUCATION' | translate"
                [addText]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.ADD_EDUCATION' | translate"
                [maxFields]="15"
                [disableAdd]="editDisabled"
                [errorMessages]="CUSTOM_ERROR_MESSAGES"
                bottomSpacing="small"
              ></forms-va-input-repeated>
              <forms-va-input-repeated
                [controlArray]="accountGroupForm.get('qualificationsAndExperienceForm.specialties')"
                [placeholder]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.SPECIALTIES' | translate"
                [label]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.SPECIALTIES' | translate"
                [addText]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.ADD_SPECIALTY' | translate"
                [maxFields]="15"
                [disableAdd]="editDisabled"
                [errorMessages]="CUSTOM_ERROR_MESSAGES"
                bottomSpacing="small"
              ></forms-va-input-repeated>
              <forms-va-input-repeated
                [controlArray]="accountGroupForm.get('qualificationsAndExperienceForm.residencies')"
                [placeholder]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.RESIDENCIES' | translate"
                [label]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.RESIDENCIES' | translate"
                [addText]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.ADD_RESIDENCY' | translate"
                [maxFields]="15"
                [disableAdd]="editDisabled"
                [errorMessages]="CUSTOM_ERROR_MESSAGES"
                bottomSpacing="small"
              ></forms-va-input-repeated>
              <forms-va-input-repeated
                [controlArray]="accountGroupForm.get('qualificationsAndExperienceForm.fellowships')"
                [placeholder]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.FELLOWSHIPS' | translate"
                [label]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.FELLOWSHIPS' | translate"
                [addText]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.ADD_FELLOWSHIP' | translate"
                [maxFields]="15"
                [disableAdd]="editDisabled"
                [errorMessages]="CUSTOM_ERROR_MESSAGES"
                bottomSpacing="small"
              ></forms-va-input-repeated>
              <forms-input
                [control]="accountGroupForm.get('qualificationsAndExperienceForm.nationalProviderIdentifier')"
                [placeholder]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.NPI' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('qualificationsAndExperienceForm.medicalLicenseNumber')"
                [placeholder]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.MEDICAL_LICENSE_NUMBER' | translate"
                bottomSpacing="small"
              ></forms-input>
              <forms-input
                [control]="accountGroupForm.get('qualificationsAndExperienceForm.stateLicense')"
                [placeholder]="'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.STATE_LICENSE' | translate"
                bottomSpacing="small"
              ></forms-input>
            </mat-card-content>
          </mat-card>
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && practiceInformationForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.PRACTICE_INFORMATION' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <forms-input
                [control]="accountGroupForm.get('practiceInformationForm.firmClinicOfficeName')"
                [placeholder]="'FRONTEND.BUSINESSES.OFFICE_NAME' | translate"
                bottomSpacing="small"
              ></forms-input>
              <glxy-form-field>
                <glxy-label>{{ 'FRONTEND.BUSINESSES.IS_PROVIDER' | translate }}</glxy-label>
                <mat-select
                  [formControl]="accountGroupForm.get('practiceInformationForm.isProvider')"
                  [placeholder]="'FRONTEND.BUSINESSES.IS_PROVIDER' | translate"
                >
                  <mat-option *ngFor="let enumVal of isProviderOptions" [value]="enumVal.code">
                    {{ enumVal.name | translate }}
                  </mat-option>
                </mat-select>
              </glxy-form-field>
              <forms-va-input-repeated
                [controlArray]="accountGroupForm.get('practiceInformationForm.hospitalAffiliations')"
                [placeholder]="'FRONTEND.BUSINESSES.HOSPITAL_AFFILIATIONS' | translate"
                [label]="'FRONTEND.BUSINESSES.HOSPITAL_AFFILIATIONS' | translate"
                [addText]="'FRONTEND.BUSINESSES.ADD_AFFILIATIONS' | translate"
                [maxFields]="15"
                [disableAdd]="editDisabled"
                [errorMessages]="CUSTOM_ERROR_MESSAGES"
                bottomSpacing="small"
              ></forms-va-input-repeated>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>
    <mat-tab [disabled]="(loading$ | async) === true" *ngIf="isTabEnabled(businessProfileTab.Attributes)">
      <ng-template mat-tab-label>
        <span
          [ngClass]="{
            'tab-invalid': tabInvalid(businessProfileTab.Attributes),
            'tab-changed': tabChanged(businessProfileTab.Attributes),
          }"
          class="tab"
        >
          {{ 'FRONTEND.BUSINESSES.ATTRIBUTES_TAB' | translate }}
        </span>
      </ng-template>
      <div class="business-profile__tab-container">
        <div class="business-profile__card">
          <forms-google-attributes
            [form]="googleAttributesForm"
            [metadata]="googleAttributesMetadata()"
            [attributes]="googleAttributes()"
            [categoryLength]="getValidCategoryLength()"
            (navigateToPrimaryInfoTab)="activeTab = businessProfileTab.Primary"
            [editDisabled]="editDisabled"
          ></forms-google-attributes>
        </div>
        <div class="business-profile__card">
          <forms-bing-attributes
            [form]="bingAttributesForm"
            [metadata]="bingAttributesMetadata()"
            [attributes]="bingAttributes()"
            [categoryLength]="getValidCategoryLength()"
            (navigateToPrimaryInfoTab)="activeTab = businessProfileTab.Primary"
            [editDisabled]="editDisabled"
          ></forms-bing-attributes>
        </div>
      </div>
    </mat-tab>
    <mat-tab [disabled]="(loading$ | async) === true" *ngIf="isTabEnabled(businessProfileTab.CustomFields)">
      <ng-template mat-tab-label>
        <span
          data-action="clicked-custom-field-tab-business-component"
          [ngClass]="{
            'tab-changed': tabChanged(businessProfileTab.CustomFields),
          }"
          class="tab"
        >
          {{ 'FRONTEND.BUSINESSES.CUSTOM_FIELDS' | translate }}
        </span>
      </ng-template>
      <div class="business-profile__tab-container">
        <div class="business-profile__card custom-fields-tab">
          <aux-auxiliary-data-table
            *ngIf="partnerId()"
            #auxiliarydatatable
            [partnerId]="partnerId()"
            [objectType]="auxiliaryDataObjectType"
            [objectId]="accountGroupId()"
            [auxiliaryDataAdminURL]="auxiliaryDataAdminURL"
          ></aux-auxiliary-data-table>
        </div>
      </div>
    </mat-tab>
    <mat-tab [disabled]="(loading$ | async) === true" *ngIf="isTabEnabled(businessProfileTab.Administration)">
      <ng-template mat-tab-label>
        <span
          [ngClass]="{
            'tab-invalid': tabInvalid(businessProfileTab.Administration),
            'tab-changed':
              tabChanged(businessProfileTab.Administration) &&
              !(accountGroupNgForm.submitted && tabInvalid(businessProfileTab.Administration)),
          }"
          class="tab"
        >
          {{ 'FRONTEND.BUSINESSES.ADMINISTRATION' | translate }}
        </span>
      </ng-template>
      <div class="business-profile__tab-container">
        <div class="business-profile__card" *ngIf="hasAccessToMarkets() || hasAccessToSales()">
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && salesForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.SALES' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <glxy-form-field *ngIf="hasAccessToMarkets()" bottomSpacing="small">
                <glxy-label>{{ 'FRONTEND.BUSINESSES.MARKET' | translate }}</glxy-label>
                <mat-select
                  [placeholder]="'FRONTEND.BUSINESSES.MARKET' | translate"
                  [formControl]="accountGroupForm.get('salesForm.market')"
                  [required]="true"
                >
                  <mat-option *ngFor="let market of markets" [value]="market.value">
                    {{ market.name }}
                  </mat-option>
                </mat-select>
              </glxy-form-field>
              <glxy-form-field *ngIf="hasAccessToSales()">
                <glxy-label>{{ 'FRONTEND.BUSINESSES.PRIMARY_SALESPERSON' | translate }}</glxy-label>
                <mat-select
                  [placeholder]="'FRONTEND.BUSINESSES.PRIMARY_SALESPERSON' | translate"
                  [formControl]="accountGroupForm.get('salesForm.salespersonId')"
                >
                  <mat-option value=""></mat-option>
                  <mat-option *ngFor="let salesperson of salespeople" [value]="salesperson.salespersonId">
                    {{ salesperson.fullName }}
                  </mat-option>
                </mat-select>
              </glxy-form-field>
              <div class="salespeople-hint add" *ngIf="!isAdditionalSalespeopleInputVisible && salespeople?.length > 0">
                <a (click)="showAdditionalSalespeopleInput()">
                  {{ 'FRONTEND.BUSINESSES.ADD_ADDITIONAL_SALESPEOPLE' | translate }}
                </a>
              </div>
              <glxy-error class="salespeople-hint" *ngIf="accountGroupForm.get('salesForm.salespersonId').invalid">
                {{ 'FRONTEND.BUSINESSES.PRIMARY_SALESPERSON_MUST_BE_SELECTED' | translate }}
              </glxy-error>
              <forms-va-input-tags
                *ngIf="isAdditionalSalespeopleInputVisible && salespeople?.length > 0"
                [formControl]="accountGroupForm.get('salesForm.additionalSalespersonIds')"
                [tooltip]="'FRONTEND.BUSINESSES.TOOLTIPS.ADDITIONAL_SALESPERSON_TOOLTIP' | translate"
                [placeholder]="'FRONTEND.BUSINESSES.ADDITIONAL_SALESPEOPLE' | translate"
                [options]="additionalSalespeople"
                [allowCustomTags]="false"
                [allowDuplicates]="false"
                [maxFields]="5"
              ></forms-va-input-tags>
              <glxy-error
                class="salespeople-hint error"
                *ngIf="accountGroupForm.get('salesForm.additionalSalespersonIds').invalid"
              >
                {{ getPrimarySalespersonInAdditionalSalespeopleErrorMsg() }}
              </glxy-error>
            </mat-card-content>
          </mat-card>
        </div>
        <div class="business-profile__card">
          <mat-card
            appearance="outlined"
            class="business-profile__card__container"
            [ngClass]="{
              'card-invalid': accountGroupNgForm.submitted && adminForm.invalid,
            }"
          >
            <mat-card-header>
              <mat-card-title>
                {{ 'FRONTEND.BUSINESSES.ADMINISTRATION' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <forms-input
                [control]="accountGroupForm.get('adminForm.customerIdentifier')"
                [placeholder]="'FRONTEND.BUSINESSES.CUSTOMER_IDENTIFIER' | translate"
                *ngIf="showCustomerIdentifier()"
                [tooltip]="'FRONTEND.BUSINESSES.TOOLTIPS.CUSTOMER_ID_TOOLTIP' | translate"
                bottomSpacing="none"
              ></forms-input>
              <forms-va-input-tags
                [formControl]="accountGroupForm.get('adminForm.tags')"
                [placeholder]="'FRONTEND.BUSINESSES.TAGS' | translate"
                [options]="tags"
                [allowCustomTags]="true"
                [customTagLength]="50"
                [allowDuplicates]="false"
                [maxFields]="25"
                bottomSpacing="none"
              ></forms-va-input-tags>
              <glxy-form-field bottomSpacing="small">
                <glxy-label>
                  {{ 'FRONTEND.BUSINESSES.NOTES' | translate }}
                </glxy-label>
                <textarea
                  matInput
                  matTextareaAutosize="true"
                  [formControl]="accountGroupForm.get('adminForm.notes')"
                  class="large-textarea"
                ></textarea>
              </glxy-form-field>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
  <mat-card class="save-footer" *ngIf="!editDisabled && !hideSaveButton()">
    <mat-card-content>
      <button mat-raised-button color="primary" type="submit">
        {{ 'FRONTEND.BUSINESSES.SAVE' | translate }}
      </button>
    </mat-card-content>
  </mat-card>
</form>

<ng-template #loading>
  <glxy-loading-spinner></glxy-loading-spinner>
</ng-template>
