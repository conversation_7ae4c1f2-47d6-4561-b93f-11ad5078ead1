import { DestroyRef } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { DayOfWeek, RegularHoursPeriod, TimeOfDay } from '@vendasta/listing-products';
import { of } from 'rxjs';
import { COUNTRY_OPTIONS_TOKEN, defaultCountryControlProvider } from './address';
import { BusinessProfileComponent } from './business-profile.component';
import { MergeHours, SplitHours } from '@vendasta/listing-profile-common';
class FakeTranslate {
  instant(s: string): string {
    return s;
  }
}

describe('BusinessProfileComponent', () => {
  let component: BusinessProfileComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        defaultCountryControlProvider,
        {
          provide: COUNTRY_OPTIONS_TOKEN,
          useValue: of([]),
        },
      ],
    });
    TestBed.runInInjectionContext(() => {
      component = new BusinessProfileComponent(
        null,
        null,
        new FormBuilder(),
        null,
        null,
        null,
        null,
        FakeTranslate as TranslateService,
        null,
        null,
        null,
        null,
        null,
        TestBed.inject(DestroyRef),
        null,
      );
    });
  });
  describe('phone number validation', () => {
    let isValid: (num: string) => boolean;
    const getPhoneValidatorForCountry = (c: string): ((num: string) => boolean) => {
      component['createForm']();
      const country = component['basicInfoForm']['controls']['country'];
      country.setValue(c);

      return function (phone: string): boolean {
        const cell = component.phoneForm.controls.cellNumber;
        cell.setValue(phone);
        cell.markAsDirty();
        cell.markAllAsTouched();
        cell.updateValueAndValidity();
        return cell.valid;
      };
    };

    describe('invalid phone numbers fail validation', () => {
      beforeEach(() => {
        const country = 'US';
        isValid = getPhoneValidatorForCountry(country);
      });

      it('12346', async () => {
        expect(isValid('12346')).toBeFalsy();
      });
      it('18005551234567890', async () => {
        expect(isValid('12345')).toBeFalsy();
      });
    });

    describe('United States', () => {
      beforeEach(() => {
        const country = 'US';
        isValid = getPhoneValidatorForCountry(country);
      });
      it('no country code', async () => {
        expect(isValid('(*************')).toBeTruthy();
      });
      it('has country code', async () => {
        expect(isValid('1 (*************')).toBeTruthy();
      });
    });

    describe('Canada', () => {
      beforeEach(() => {
        const country = 'CA';
        isValid = getPhoneValidatorForCountry(country);
      });
      it('dashes format', async () => {
        expect(isValid('************')).toBeTruthy();
      });
      it('dashes format, with country code', async () => {
        expect(isValid('1-************')).toBeTruthy();
      });
      it('parentheses on area code', async () => {
        expect(isValid('(*************')).toBeTruthy();
      });
      it('parentheses on area code, with country code', async () => {
        expect(isValid('1 (*************')).toBeTruthy();
      });
    });

    describe('North America numbers from AG-849', () => {
      beforeEach(() => {
        const country = 'US';
        isValid = getPhoneValidatorForCountry(country);
      });
      it('1(517)5744863', async () => {
        expect(isValid('1(517)5744863')).toBeTruthy();
      });
      it('1(574)2647581', async () => {
        expect(isValid('1(574)2647581')).toBeTruthy();
      });
      it('******-983-7550', async () => {
        expect(isValid('******-983-7550')).toBeTruthy();
      });
      it('1(205)4037474unite', async () => {
        expect(isValid('1(205)4037474unite')).toBeTruthy();
      });
      it('1(205)9955151', async () => {
        expect(isValid('1(205)9955151')).toBeTruthy();
      });
      it('1(205)4099922', async () => {
        expect(isValid('1(205)4099922')).toBeTruthy();
      });
      it('19515520015', async () => {
        expect(isValid('19515520015')).toBeTruthy();
      });
    });

    describe('Czech Republic from AG-849', () => {
      beforeEach(() => {
        const country = 'CZ';
        isValid = getPhoneValidatorForCountry(country);
      });
      it('+420 221 807 347', async () => {
        expect(isValid('+420 221 807 347')).toBeTruthy();
      });
      it('210320086', async () => {
        expect(isValid('210320086')).toBeTruthy();
      });
      it('915 749 037', async () => {
        expect(isValid('915 749 037')).toBeTruthy();
      });
      it('+420233 322 594', async () => {
        expect(isValid('+420233 322 594')).toBeTruthy();
      });
      it('420234704901', async () => {
        expect(isValid('420234704901')).toBeTruthy();
      });
    });

    describe('South Africa from AG-849', () => {
      beforeEach(() => {
        const country = 'ZA';
        isValid = getPhoneValidatorForCountry(country);
      });

      it('0633213313', async () => {
        expect(isValid('0633213313')).toBeTruthy();
      });
    });

    describe("non-USA numbers work when no country given and it defaults to USA (e.g. partner sign-up page doesn't ask for this AG-818)", () => {
      beforeEach(() => {
        const country = 'US';
        isValid = getPhoneValidatorForCountry(country);
      });
      it('Singapore', async () => {
        expect(isValid('+6567331268')).toBeTruthy();
      });
      it('Malaysia', async () => {
        expect(isValid('+60122751258')).toBeTruthy();
      });
    });

    describe('Singapore from AG-818', () => {
      beforeEach(() => {
        const country = 'SG';
        isValid = getPhoneValidatorForCountry(country);
      });
      it('67331268', async () => {
        expect(isValid('67331268')).toBeTruthy();
      });
      it('6567331268', async () => {
        expect(isValid('6567331268')).toBeTruthy();
      });
      it('+6567331268', async () => {
        expect(isValid('+6567331268')).toBeTruthy();
      });
      it('6733-1268', async () => {
        expect(isValid('6733-1268')).toBeTruthy();
      });
    });

    describe('Malaysia from AG-818', () => {
      beforeEach(() => {
        const country = 'MY';
        isValid = getPhoneValidatorForCountry(country);
      });
      it('0122751258', async () => {
        expect(isValid('0122751258')).toBeTruthy();
      });
      it('60122751258', async () => {
        expect(isValid('60122751258')).toBeTruthy();
      });
      it('6012-2751-258', async () => {
        expect(isValid('6012-2751-258')).toBeTruthy();
      });
      it('+60122751258', async () => {
        expect(isValid('+60122751258')).toBeTruthy();
      });
    });
  });

  describe('legacyProductDetails array validation', () => {
    it('returns false for undefined', () => {
      expect(component.isValidArray(undefined)).toBeFalsy();
    });

    it('returns false for null', () => {
      expect(component.isValidArray(null)).toBeFalsy();
    });

    it('returns false for empty array', () => {
      expect(component.isValidArray([])).toBeFalsy();
    });

    it('returns non-empty array', () => {
      expect(component.isValidArray([1, 2, 3])).toStrictEqual([1, 2, 3]);
    });
  });
});

describe('MergeHours', () => {
  test('empty hours set does not implode', () => {
    const input: RegularHoursPeriod[] = [];
    const expected: RegularHoursPeriod[] = [];
    const actual = MergeHours(input);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });
  test('valid hours not ending or starting at midnight are unchanged', () => {
    const input = [
      {
        openDay: 1,
        openTime: {
          hours: 7,
          minutes: 0,
        },
        closeDay: 1,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 2,
        openTime: {
          hours: 8,
          minutes: 0,
        },
        closeDay: 2,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 3,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 3,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 4,
        openTime: {
          hours: 10,
          minutes: 0,
        },
        closeDay: 4,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 5,
        openTime: {
          hours: 11,
          minutes: 0,
        },
        closeDay: 5,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
    ];
    const expected = [
      {
        openDay: 1,
        openTime: {
          hours: 7,
          minutes: 0,
        },
        closeDay: 1,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 2,
        openTime: {
          hours: 8,
          minutes: 0,
        },
        closeDay: 2,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 3,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 3,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 4,
        openTime: {
          hours: 10,
          minutes: 0,
        },
        closeDay: 4,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 5,
        openTime: {
          hours: 11,
          minutes: 0,
        },
        closeDay: 5,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
    ];
    const actual = MergeHours(input as RegularHoursPeriod[]);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });
  test('hours which would create a span > 24 hours are not merged', () => {
    const input = [
      {
        openDay: 3,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 3,
        closeTime: {
          hours: 24,
          minutes: 0,
        },
      },
      {
        openDay: 4,
        openTime: {
          hours: 0,
          minutes: 0,
        },
        closeDay: 4,
        closeTime: {
          hours: 12,
          minutes: 0,
        },
      },
    ];
    const expected = [
      {
        openDay: 3,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 3,
        closeTime: {
          hours: 24,
          minutes: 0,
        },
      },
      {
        openDay: 4,
        openTime: {
          hours: 0,
          minutes: 0,
        },
        closeDay: 4,
        closeTime: {
          hours: 12,
          minutes: 0,
        },
      },
    ];
    const actual = MergeHours(input as RegularHoursPeriod[]);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });

  test('hours ending at midnight followed by hours starting at midnight the next day are merged', () => {
    const input = [
      {
        openDay: 2,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 2,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 3,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 3,
        closeTime: {
          hours: 24,
          minutes: 0,
        },
      },
      {
        openDay: 4,
        openTime: {
          hours: 0,
          minutes: 0,
        },
        closeDay: 4,
        closeTime: {
          hours: 3,
          minutes: 0,
        },
      },
      {
        openDay: 5,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 5,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
    ];
    const expected = [
      {
        openDay: 2,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 2,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 3,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 4,
        closeTime: {
          hours: 3,
          minutes: 0,
        },
      },
      {
        openDay: 5,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 5,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
    ];
    const actual = MergeHours(input as RegularHoursPeriod[]);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });

  test('hours ending at midnight followed by hours not starting the next day are not merged', () => {
    const input = [
      {
        openDay: 3,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 3,
        closeTime: {
          hours: 24,
          minutes: 0,
        },
      },
      {
        openDay: 5,
        openTime: {
          hours: 0,
          minutes: 0,
        },
        closeDay: 5,
        closeTime: {
          hours: 3,
          minutes: 0,
        },
      },
    ];
    const expected = [
      {
        openDay: 3,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 3,
        closeTime: {
          hours: 24,
          minutes: 0,
        },
      },
      {
        openDay: 5,
        openTime: {
          hours: 0,
          minutes: 0,
        },
        closeDay: 5,
        closeTime: {
          hours: 3,
          minutes: 0,
        },
      },
    ];
    const actual = MergeHours(input as RegularHoursPeriod[]);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });
  test('hours at the end of the week (sun) are merged with the start of the week (mon)', () => {
    const input = [
      {
        openDay: DayOfWeek.MONDAY,
        openTime: {
          hours: 0,
          minutes: 0,
        },
        closeDay: DayOfWeek.MONDAY,
        closeTime: {
          hours: 3,
          minutes: 0,
        },
      },
      {
        openDay: DayOfWeek.SUNDAY,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: DayOfWeek.MONDAY,
        closeTime: {
          hours: 0,
          minutes: 0,
        },
      },
    ];
    const expected = [
      {
        openDay: 7,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 1,
        closeTime: {
          hours: 3,
          minutes: 0,
        },
      },
    ];
    const actual = MergeHours(input as RegularHoursPeriod[]);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });
  test('hours ending at midnight the following day are merged to hours starting at midnight the following day', () => {
    const input = [
      {
        openDay: 1,
        openTime: {
          hours: 8,
          minutes: 30,
        },
        closeDay: 1,
        closeTime: {
          hours: 16,
          minutes: 30,
        },
      },
      {
        openDay: 2,
        openTime: {
          hours: 8,
          minutes: 30,
        },
        closeDay: 2,
        closeTime: {
          hours: 2,
        },
      },
      {
        openDay: 3,
        openTime: {
          hours: 14,
        },
        closeDay: 4,
        closeTime: {},
      },
      {
        openDay: 4,
        openTime: {},
        closeDay: 4,
        closeTime: {
          hours: 9,
        },
      },
      {
        openDay: 5,
        openTime: {
          hours: 8,
          minutes: 30,
        },
        closeDay: 5,
        closeTime: {
          hours: 16,
          minutes: 30,
        },
      },
    ];
    const expected = [
      {
        openDay: 1,
        openTime: {
          hours: 8,
          minutes: 30,
        },
        closeDay: 1,
        closeTime: {
          hours: 16,
          minutes: 30,
        },
      },
      {
        openDay: 2,
        openTime: {
          hours: 8,
          minutes: 30,
        },
        closeDay: 2,
        closeTime: {
          hours: 2,
          minutes: 0,
        },
      },
      {
        openDay: 3,
        openTime: {
          hours: 14,
          minutes: 0,
        },
        closeDay: 4,
        closeTime: {
          hours: 9,
          minutes: 0,
        },
      },
      {
        openDay: 5,
        openTime: {
          hours: 8,
          minutes: 30,
        },
        closeDay: 5,
        closeTime: {
          hours: 16,
          minutes: 30,
        },
      },
    ];
    const actual = MergeHours(input as RegularHoursPeriod[]);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });

  test('split hours that span multiple days into multiple hours', () => {
    const input: RegularHoursPeriod[] = [
      new RegularHoursPeriod({
        openDay: 7,
        openTime: new TimeOfDay({
          hours: 12,
        }),
        closeDay: 1,
        closeTime: new TimeOfDay({
          hours: 12,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 1,
        openTime: new TimeOfDay({
          hours: 2,
        }),
        closeDay: 2,
        closeTime: new TimeOfDay({
          hours: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 2,
        openTime: new TimeOfDay({
          hours: 14,
        }),
        closeDay: 3,
        closeTime: new TimeOfDay({
          hours: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 3,
        openTime: new TimeOfDay({
          hours: 14,
        }),
        closeDay: 4,
        closeTime: new TimeOfDay({
          hours: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 4,
        openTime: new TimeOfDay({
          hours: 14,
        }),
        closeDay: 5,
        closeTime: new TimeOfDay({
          hours: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 5,
        openTime: new TimeOfDay({
          hours: 12,
        }),
        closeDay: 6,
        closeTime: new TimeOfDay({
          hours: 2,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 6,
        openTime: new TimeOfDay({
          hours: 12,
        }),
        closeDay: 7,
        closeTime: new TimeOfDay({
          hours: 2,
        }),
      }),
    ];

    // empty this for now
    const expected: RegularHoursPeriod[] = [
      new RegularHoursPeriod({
        openDay: 7,
        openTime: new TimeOfDay({
          hours: 12,
        }),
        closeDay: 1,
        closeTime: new TimeOfDay({
          hours: 24,
          minutes: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 2,
        openTime: new TimeOfDay({
          hours: 0,
          minutes: 0,
        }),
        closeDay: 2,
        closeTime: new TimeOfDay({
          hours: 12,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 1,
        openTime: new TimeOfDay({
          hours: 2,
        }),
        closeDay: 2,
        closeTime: new TimeOfDay({
          hours: 24,
          minutes: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 2,
        openTime: new TimeOfDay({
          hours: 14,
        }),
        closeDay: 3,
        closeTime: new TimeOfDay({
          hours: 24,
          minutes: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 3,
        openTime: new TimeOfDay({
          hours: 14,
        }),
        closeDay: 4,
        closeTime: new TimeOfDay({
          hours: 24,
          minutes: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 4,
        openTime: new TimeOfDay({
          hours: 14,
        }),
        closeDay: 5,
        closeTime: new TimeOfDay({
          hours: 24,
          minutes: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 5,
        openTime: new TimeOfDay({
          hours: 12,
        }),
        closeDay: 6,
        closeTime: new TimeOfDay({
          hours: 24,
          minutes: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 7,
        openTime: new TimeOfDay({
          hours: 0,
          minutes: 0,
        }),
        closeDay: 7,
        closeTime: new TimeOfDay({
          hours: 2,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 6,
        openTime: new TimeOfDay({
          hours: 12,
        }),
        closeDay: 7,
        closeTime: new TimeOfDay({
          hours: 24,
          minutes: 0,
        }),
      }),
      new RegularHoursPeriod({
        openDay: 1,
        openTime: new TimeOfDay({
          hours: 0,
          minutes: 0,
        }),
        closeDay: 1,
        closeTime: new TimeOfDay({
          hours: 2,
        }),
      }),
    ];

    const actual = SplitHours(input as RegularHoursPeriod[]);
    console.debug(actual);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }

    expect(actual.length).toStrictEqual(expected.length);
  });
});
