import { FormatPhoneNumberPipe } from './format-phone-number.pipe';

describe('FormatPhoneNumberPipe', () => {
  const pipe = new FormatPhoneNumberPipe();
  it('should format US numbers correctly', () => {
    const number = '**********';
    const countryCode = 'US';
    const result = pipe.transform(number, countryCode);
    expect(result).toEqual('(*************');
  });
  it('should format Australian numbers correctly', () => {
    const number = '*********';
    const countryCode = 'AU';
    const result = pipe.transform(number, countryCode);
    expect(result).toEqual('(02) 1111 0000');
  });
  it('should return the number unchanged when country code is invalid', () => {
    const number = '**********';
    const countryCode = 'USAAA';
    const result = pipe.transform(number, countryCode);
    expect(result).toEqual('**********');
  });
  it('should return the number unchanged when format of number and country code conflict', () => {
    const number = '**********';
    const countryCode = 'AU';
    const result = pipe.transform(number, countryCode);
    expect(result).toEqual('**********');
  });
  it('should return an empty string when an empty string is given as a number', () => {
    const number = '';
    const countryCode = 'US';
    const result = pipe.transform(number, countryCode);
    expect(result).toEqual('');
  });
});

describe('transformForBusinessProfile', () => {
  const pipe = new FormatPhoneNumberPipe();
  it('should format US numbers correctly', () => {
    const number = '+***********';
    const countryCode = 'CN';
    const result = pipe.transformForBusinessProfile(number, countryCode);
    expect(result).toEqual('****** 851 2498');
  });
  it('should format local numbers to have country code of account ', () => {
    const number = '**********';
    const countryCode = 'CN';
    const result = pipe.transformForBusinessProfile(number, countryCode);
    expect(result).toEqual('+86 20 4851 2498');
  });
  it('international number, account in USA', () => {
    const number = '+52**********';
    const countryCode = 'US';
    const result = pipe.transformForBusinessProfile(number, countryCode);
    expect(result).toEqual('+52 ************');
  });
  it('should return an empty string when an empty string is given as a number', () => {
    const number = '';
    const countryCode = 'US';
    const result = pipe.transformForBusinessProfile(number, countryCode);
    expect(result).toEqual('');
  });
  it('should format Malaysian numbers correctly, on non-Malaysia account', () => {
    const number = '+6**********';
    const countryCode = 'CN';
    const result = pipe.transformForBusinessProfile(number, countryCode);
    expect(result).toEqual('+60 12 275 1258');
  });
  it('should format Singapore numbers correctly on non-Singapore account', () => {
    const number = '+65********';
    const countryCode = 'CN';
    const result = pipe.transformForBusinessProfile(number, countryCode);
    expect(result).toEqual('+65 6733 1268');
  });
  it('should format Malaysian numbers correctly, on Malaysia account', () => {
    const number = '**********';
    const countryCode = 'MY';
    const result = pipe.transformForBusinessProfile(number, countryCode);
    expect(result).toEqual('+60 12 275 1258');
  });
  it('should format Singapore numbers correctly on Singapore account', () => {
    const number = '********';
    const countryCode = 'SG';
    const result = pipe.transformForBusinessProfile(number, countryCode);
    expect(result).toEqual('+65 6733 1268');
  });
});
