import { Pipe, PipeTransform } from '@angular/core';
import { format, CountryCode, parsePhoneNumber } from 'libphonenumber-js';

/** @deprecated Use FormatPhoneNumberPipe from uikit */
@Pipe({
  name: 'formatPhoneNumber',
  standalone: false,
})
export class FormatPhoneNumberPipe implements PipeTransform {
  transform(phoneNumber: string, countryCode: string): string {
    if (countryCode as CountryCode) {
      const newCountryCode = <CountryCode>countryCode;
      try {
        return format(phoneNumber, newCountryCode, 'NATIONAL');
      } catch {
        return phoneNumber;
      }
    } else {
      return phoneNumber;
    }
  }

  transformForBusinessProfile(phoneNumber: string, countryCode: string): string {
    if (countryCode as CountryCode) {
      const newCountryCode = <CountryCode>countryCode;
      try {
        const parsedNumber = parsePhoneNumber(phoneNumber, newCountryCode);
        if (!parsedNumber || !parsedNumber.isValid()) {
          return phoneNumber;
        }
        return parsedNumber.formatInternational();
      } catch {
        return phoneNumber;
      }
    } else {
      return phoneNumber;
    }
  }
}
