import { AbstractControl, FormControl, UntypedFormGroup, ValidationErrors, ValidatorFn } from '@angular/forms';

export function NotOnlyWhitespaceValidator(control: FormControl) {
  const inputVal = typeof control?.value === 'string' ? control.value : 'notstring';
  return inputVal.trim().length ? null : { whitespace: true };
}
export function totalLengthOfRepeatedFieldValidator(maxLength: number, friendlyName: string): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } => {
    if (!control.value) {
      return null;
    }
    if (control.value.length === 0) {
      return null;
    }

    const value = control.value.filter((val: string) => !!val).join(',');
    return value.length > maxLength
      ? {
          lengthOfRepeatedField: {
            maxLength: maxLength,
            friendlyName: friendlyName,
          },
        }
      : null;
  };
}

export function primaryIsNotInAdditionalSalespeople(salespersonNameFromValue: (value: string) => string): ValidatorFn {
  return (fg: UntypedFormGroup): ValidationErrors => {
    const primaryId = fg.controls['salespersonId'];
    const additionalIds = fg.controls['additionalSalespersonIds'];
    if (!additionalIds.value || additionalIds.value.indexOf(primaryId.value) === -1) {
      additionalIds.setErrors(null);
      return null;
    }

    const error: ValidationErrors = { primaryName: salespersonNameFromValue(primaryId.value) };
    additionalIds.markAsTouched();
    additionalIds.setErrors(error);
    return error;
  };
}

export function primaryIsSelectedWhenHavingAdditionalSalespeople(): ValidatorFn {
  return (fg: UntypedFormGroup): ValidationErrors => {
    const primaryId = fg.controls['salespersonId'];
    const additionalIds = fg.controls['additionalSalespersonIds'];
    if (additionalIds.value && additionalIds.value.length > 0 && !primaryId.value) {
      const error: ValidationErrors = {
        primaryNotSelected: true,
      };
      primaryId.markAsTouched();
      primaryId.setErrors(error);
      return error;
    }

    primaryId.setErrors(null);
    return null;
  };
}
