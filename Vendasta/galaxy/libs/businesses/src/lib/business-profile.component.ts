import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import {
  Component,
  computed,
  DestroyRef,
  effect,
  Inject,
  inject,
  input,
  Input,
  NgZone,
  OnInit,
  output,
  Signal,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { MapGeocoder, MapGeocoderResponse } from '@angular/google-maps';
import { MatSnackBar } from '@angular/material/snack-bar';
import { isHealthCategory } from '@galaxy/business-category';
import { TranslateService } from '@ngx-translate/core';
import { GooglePlace, RichDataPaymentMethods } from '@vendasta/account-group';
import { CountryOption } from '@vendasta/address';
import { AuxiliaryDataTableComponent, ObjectType } from '@vendasta/auxiliary-data-components';
import { CategoryInterface } from '@vendasta/category';
import {
  emailValidator,
  fixControlsForFormArray,
  getChangedValues,
  ItemFactory,
  nameCharacterValidator,
  Option,
  phoneValidator,
  urlValidator,
  whitespaceValidator,
} from '@vendasta/forms';
import { buildTimezoneDisplay } from '@vendasta/galaxy/utility/timezone';
import {
  ValueType as AttributesValueType,
  BingAttribute,
  BingAttributeMetaData,
  BingAttributeMetaDataInterface,
  BusinessHours,
  ClosedStatus,
  DoctorDotComCategory,
  Geo,
  GetDoctorDotComCategoriesRequest,
  GetSuggestionRequest,
  GetSuggestionResponse,
  GoogleAttribute,
  GoogleAttributeMetaData,
  GoogleAttributeMetaDataInterface,
  HealthCareProfessionalInformation,
  HealthCareProfessionalInformationGender,
  HealthCareProfessionalInformationIsProvider,
  IsAvailable,
  LegacyProductDetails,
  ListingProductsApiService,
  ListingProfile,
  ListingProfileApiService,
  MoreHoursType,
  ProjectionFilter,
  RichData,
  ServiceArea,
  ServiceAvailability,
  SuggestFieldUpdateRequest,
  SuggestionApiService,
  UpdateOption,
  VendorAttributeMetaData,
} from '@vendasta/listing-products';
import {
  BingAttributesUpdateOperation,
  BusinessHoursUpdateOperation,
  ExternalIdentifiersUpdateOperation,
  GoogleAttributesUpdateOperation,
  LegacyProductDetailsUpdateOperation,
  ListingProfileService,
  MergeHours,
  NapUpdateOperation,
  RichDataUpdateOperation,
  SocialURLsUpdateOperation,
  SplitHours,
  UpdateOperations,
} from '@vendasta/listing-profile-common';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { ListSalespersonsRequestListSalespersonsFiltersInterface, SalespersonService, SalesSdk } from '@vendasta/sales';
import { Salesperson as SalesSalesperson } from '@vendasta/sales/lib/_internal/objects/salesperson';
import { Salesperson } from '@vendasta/salesperson';
import { VaFullscreenLightboxService } from '@vendasta/uikit';
import {
  catchError,
  combineLatest,
  EMPTY,
  filter,
  map,
  Observable,
  of,
  ReplaySubject,
  shareReplay,
  startWith,
  switchMap,
  tap,
} from 'rxjs';
import { retry } from 'rxjs/operators';
import { Address, COUNTRY_CONTROL_TOKEN } from './address';
import {
  AvailabilityOption,
  BusinessOperationMethods,
  CountryPaymentMethods,
  FormUpdateParameters,
  GenderKeys,
  GenderOption,
  GenderSelection,
  IsProviderOption,
  ProfesstionalCredentials,
  SellingMethodsAvailability,
  ValidationErrorMessages,
} from './business-profile.consts';
import { BusinessProfileTab, HiddenField, InvalidFormField, LightboxData, Market } from './business-profile.interface';
import {
  primaryIsNotInAdditionalSalespeople,
  primaryIsSelectedWhenHavingAdditionalSalespeople,
  totalLengthOfRepeatedFieldValidator,
} from './custom-validators';
import { FormatPhoneNumberPipe } from './format-phone-number.pipe';

declare let google: any;

const formTabs: Record<string, BusinessProfileTab> = {
  basicInfoForm: BusinessProfileTab.Primary,
  serviceAreaForm: BusinessProfileTab.Primary,
  phoneForm: BusinessProfileTab.Primary,
  serviceAvailabilityForm: BusinessProfileTab.Primary,
  geoControl: BusinessProfileTab.Primary,
  additionalInfoForm: BusinessProfileTab.Primary,
  hoursForm: BusinessProfileTab.Hours,
  businessHoursForm: BusinessProfileTab.Hours,
  businessPagesForm: BusinessProfileTab.Social,
  blogForm: BusinessProfileTab.Social,
  businessDescriptionsForm: BusinessProfileTab.Descriptions,
  personalInformationForm: BusinessProfileTab.Professional,
  practiceInformationForm: BusinessProfileTab.Professional,
  qualificationsAndExperienceForm: BusinessProfileTab.Professional,
  additionalProfessionalInfoForm: BusinessProfileTab.Professional,
  timeZoneControl: BusinessProfileTab.Primary,
  googleAttributesForm: BusinessProfileTab.Attributes,
  bingAttributesForm: BusinessProfileTab.Attributes,
};

@Component({
  selector: 'business-profile',
  templateUrl: './business-profile.component.html',
  styleUrls: ['./business-profile.component.scss'],
  providers: [FormatPhoneNumberPipe],
  standalone: false,
})
export class BusinessProfileComponent implements OnInit {
  readonly accountGroupId = input.required<string>();
  @Input() productNames: any;
  @Input() editDisabled = false;
  @Input() maxShareOfVoiceKeywords = 3;
  @Input() maxCompetitors = 3;
  @Input() enabledTabs: BusinessProfileTab[] = [
    BusinessProfileTab.Primary,
    BusinessProfileTab.Hours,
    BusinessProfileTab.Social,
    BusinessProfileTab.Professional,
    BusinessProfileTab.Descriptions,
    BusinessProfileTab.Images,
    BusinessProfileTab.Attributes,
  ];
  @Input() markets: Market[];
  @Input() tags: Option[];
  @Input() hiddenFields?: HiddenField[];
  @Input() auxiliaryDataAdminURL = '';
  @Input() tabBackground: 'default' | 'lighter-grey' = 'default';
  readonly saved = output();

  @ViewChild('auxiliarydatatable')
  private readonly auxiliaryDataTable: AuxiliaryDataTableComponent;
  protected auxiliaryDataObjectType: ObjectType = 'business';
  protected visibleConditionalFields: string[] = [];

  protected CUSTOM_ERROR_MESSAGES = {
    lengthOfRepeatedField: function (error: { maxLength: number; friendlyName: string }): string {
      return this.translateService.instant('FRONTEND.BUSINESSES.LENGTH_OF_REPEATED_FIELD_ERROR', {
        maxLength: error.maxLength,
        fieldName: this.translateService.instant(error.friendlyName),
      });
    }.bind(this),
  };

  private readonly profileLoading$$ = new ReplaySubject<boolean>();
  protected loading$ = this.profileLoading$$.asObservable();

  protected mapZoomLevel: number;
  protected inferredMapBounds: google.maps.LatLngBoundsLiteral;

  protected businessProfileTab = BusinessProfileTab;
  protected activeTab: BusinessProfileTab;

  public salespeople: Salesperson[];
  public additionalSalespeople: Option[];
  public addressData: Address;
  public addressFormDirty = false;

  // TODO set up those method once backend update
  // !-- Service Availability
  public businessOperationMethods$: Observable<any[]>;
  public ecommerceOnly$: Observable<AvailabilityOption[]>;

  // TODO: The following choices will result in eventual duplication of list between here and microservice
  // TODO: Perhaps these could be requested from the microservice, or be accessed from SDK?
  public genders$: Observable<GenderOption[]>;
  public paymentMethods$: Observable<any[]>;
  public standardizedTitles$: Observable<any[]>;
  public readonly professionalCredentials = ProfesstionalCredentials;
  public moreHoursTypes$: Observable<MoreHoursType[]>;
  public isProviderOptions: IsProviderOption[];

  public isAdditionalSalespeopleInputVisible = false;

  public readonly attributesMetadata: Signal<VendorAttributeMetaData[]>;
  private readonly attributesMetadata$: Observable<VendorAttributeMetaData[]>;

  public readonly listingProfile: Signal<ListingProfile>;
  private readonly listingProfile$: Observable<ListingProfile>;

  protected readonly googleAttributesMetadata: Signal<GoogleAttributeMetaData[]>;
  protected readonly googleAttributes: Signal<GoogleAttribute[]>;
  protected readonly bingAttributesMetadata: Signal<BingAttributeMetaData[]>;
  protected readonly bingAttributes: Signal<BingAttribute[]>;
  protected readonly partnerId: Signal<string>;

  // the subset of operational statuses implying a closure or limitation of services
  public closedStatuses = [ClosedStatus.LIMITED, ClosedStatus.TEMPORARY, ClosedStatus.PERMANENT];

  public partnersLongDescriptionLength = {
    MTCZ: 5000,
    BLS: 1000,
  };
  public longDescriptionLength = 750;

  public basicInfoForm: FormGroup;
  public serviceAreaForm: FormGroup;
  public phoneForm: FormGroup;
  public additionalInfoForm: FormGroup;
  public businessHoursForm: FormControl;
  public specialHoursForm: FormControl;
  public moreHoursForm: FormGroup;
  public businessPagesForm: FormGroup;
  public blogForm: FormGroup;
  public businessDescriptionsForm: FormGroup;
  public personalInformationForm: FormGroup;
  public serviceAvailabilityForm: FormGroup;
  public qualificationsAndExperienceForm: FormGroup;
  public additionalProfessionalInfoForm: FormGroup;
  public practiceInformationForm: FormGroup;
  public geoControl: FormControl;
  public adminForm: FormGroup;
  public salesForm: FormGroup;
  public timezoneControl: FormControl;
  public googleAttributesForm: FormGroup;
  public bingAttributesForm: FormGroup;
  public accountGroupForm: FormGroup;
  public availableDoctorDotComCategories: Observable<DoctorDotComCategory[]>;
  public currentDoctorDotComCategories: DoctorDotComCategory[];
  public showICOField$: Observable<boolean>;

  public hasProfessionalCategory = false;
  public phoneNumberItemFactory: ItemFactory = (): FormControl =>
    this.fb.control(this.formStateWithDisabled(''), phoneValidator(this.getCountryCode.bind(this), false));

  private readonly countryChanges = inject(COUNTRY_CONTROL_TOKEN).valueChanges.pipe(
    filter((country): country is CountryOption => typeof country === 'object'),
    map((country) => country.code),
  );
  private mapsLoaded$: Observable<boolean>;
  accountGroupId$: Observable<string>;
  private readonly mapsURL =
    'https://maps.googleapis.com/maps/api/js?key=AIzaSyDxrR1bk6d2TRa4orIDF05H3UlhvIXRRC0&libraries=places';

  constructor(
    private listingProfileService: ListingProfileService,
    private listingProfileApiService: ListingProfileApiService,
    private fb: FormBuilder,
    private snackbar: MatSnackBar,
    @Inject(SalespersonService) private readonly salespersonService: SalesSdk,
    private zone: NgZone,
    private formatPhoneNumber: FormatPhoneNumberPipe,
    private translateService: TranslateService,
    private geocoder: MapGeocoder,
    private productAnalyticsService: ProductAnalyticsService,
    private lightbox: VaFullscreenLightboxService,
    private suggestionService: SuggestionApiService,
    private listingProductsApiService: ListingProductsApiService,
    private readonly destroyRef: DestroyRef,
    private readonly httpClient: HttpClient,
  ) {
    this.accountGroupId$ = toObservable(this.accountGroupId);

    effect(() => {
      this.accountGroupId();
      this.bingAttributesForm.reset();
      this.googleAttributesForm.reset();
    });

    this.moreHoursTypes$ = this.accountGroupId$.pipe(
      switchMap((accountGroupId: string) =>
        this.listingProfileApiService.getMoreHoursTypes({
          businessId: accountGroupId,
          languageCode: this.translateService.currentLang || this.translateService.defaultLang,
        }),
      ),
      map((response) => response.moreHoursTypes),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.attributesMetadata$ = this.accountGroupId$.pipe(
      switchMap((accountGroupId: string) =>
        this.listingProfileApiService.getAttributeMetadata({
          businessId: accountGroupId,
          languageCode: this.translateService.currentLang || this.translateService.defaultLang,
        }),
      ),
      map((response) => response.attributeMedata),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.attributesMetadata = toSignal(this.attributesMetadata$);

    this.listingProfile$ = this.accountGroupId$.pipe(
      tap(() => this.profileLoading$$.next(true)),
      switchMap((accountGroupId: string) =>
        this.listingProfileService.get(
          accountGroupId,
          new ProjectionFilter({
            externalIdentifiers: true,
            socialUrls: true,
            richData: true,
            napData: true,
            businessHours: true,
            googleAttributes: true,
            googleAttributesMetadata: true,
            bingAttributes: true,
            bingAttributesMetadata: true,
            legacyProductDetails: true,
          }),
        ),
      ),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.listingProfile = toSignal(this.listingProfile$);

    this.googleAttributesMetadata = computed(() => {
      const am = this.attributesMetadata();
      if (am) {
        const gAttributeMetadata = am.find((b) => b.vendorId === 'google');
        return gAttributeMetadata?.googleAttributeMetadata?.googleAttributeMetadata || [];
      } else {
        return [];
      }
    });

    this.googleAttributes = computed(() => this.listingProfile()?.googleAttributes || []);

    this.bingAttributesMetadata = computed(() => {
      const am = this.attributesMetadata();
      if (am) {
        const bAttributeMetadata = am.find((a) => a.vendorId === 'bing');
        return bAttributeMetadata?.bingAttributeMetadata?.bingAttributeMetadata || [];
      } else {
        return [];
      }
    });
    this.bingAttributes = computed(() => this.listingProfile()?.bingAttributes || []);
    this.partnerId = computed(() => this.listingProfile()?.externalIdentifiers?.partnerId || '');

    this.mapsLoaded$ = this.httpClient?.jsonp(this.mapsURL, 'callback').pipe(
      map(() => true),
      retry(1),
      catchError(() => of(false)),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  get rmProductName(): string {
    return (this.productNames || {}).RM || 'Reputation Management';
  }

  private createForm(): void {
    this.geoControl = this.fb.control(this.formStateWithDisabled());
    this.serviceAreaForm = this.fb.group(
      {
        businessType: [this.formStateWithDisabled(0), Validators.min(1)],
        places: [this.formStateWithDisabled([]), Validators.required],
      },
      this.formStateWithDisabled(undefined),
    );
    this.basicInfoForm = this.fb.group({
      businessName: [
        this.formStateWithDisabled(''),
        [Validators.required, whitespaceValidator(), nameCharacterValidator()],
      ],
      commonBusinessNames: this.fb.array([[this.formStateWithDisabled('')]], Validators.maxLength(3)),
      country: [this.formStateWithDisabled(''), Validators.required],
      website: [this.formStateWithDisabled(''), urlValidator],
      email: [this.formStateWithDisabled(''), emailValidator],
      primaryBusinessCategory: [this.formStateWithDisabled([]), Validators.required],
      businessCategories: [this.formStateWithDisabled([])],
      serviceAreaBusiness: [this.formStateWithDisabled(false)],
    });

    this.phoneForm = this.fb.group({
      workNumbers: this.fb.array(
        [this.phoneNumberItemFactory()],
        // We don't require phone number on the apis so requiring a phone number on edit forces them to add
        // one after the fact which they can't do
        Validators.maxLength(6),
      ),
      faxNumber: [this.formStateWithDisabled('')],
      cellNumber: [this.formStateWithDisabled(''), phoneValidator(this.getCountryCode.bind(this), false)],
      callTrackingNumbers: this.fb.array([this.phoneNumberItemFactory()], Validators.maxLength(3)),
      tollFreeNumber: [this.formStateWithDisabled(''), phoneValidator(this.getCountryCode.bind(this), false)],
    });

    this.additionalInfoForm = this.fb.group({
      paymentMethods: [
        this.formStateWithDisabled([]),
        paymentMethodValidator(this.getCountryCode.bind(this), this.translateService),
      ],
      servicesOffered: this.fb.array(
        [[this.formStateWithDisabled('')]],
        Validators.compose([
          Validators.maxLength(15),
          totalLengthOfRepeatedFieldValidator(256, 'FRONTEND.BUSINESSES.SERVICES_OFFERED'),
        ]),
      ),
      brandsCarried: this.fb.array(
        [[this.formStateWithDisabled('')]],
        Validators.compose([
          Validators.maxLength(15),
          totalLengthOfRepeatedFieldValidator(256, 'FRONTEND.BUSINESSES.BRANDS_CARRIED'),
        ]),
      ),
      landmark: [this.formStateWithDisabled('')],
      bookingUrl: [this.formStateWithDisabled('')],
      ico: [this.formStateWithDisabled('')],
    });

    this.businessHoursForm = this.fb.control(this.formStateWithDisabled([]));
    this.moreHoursForm = this.fb.group([]);
    this.specialHoursForm = this.fb.control(this.formStateWithDisabled());

    this.businessPagesForm = this.fb.group({
      facebookUrl: [this.formStateWithDisabled(''), urlValidator],
      linkedInUrl: [this.formStateWithDisabled(''), urlValidator],
      twitterUrl: [this.formStateWithDisabled(''), urlValidator],
      pinterestUrl: [this.formStateWithDisabled(''), urlValidator],
      instagramUrl: [this.formStateWithDisabled(''), urlValidator],
      youTubeUrl: [this.formStateWithDisabled(''), urlValidator],
    });
    this.blogForm = this.fb.group({
      blogOrRssFeedUrl: [this.formStateWithDisabled('')],
    });

    this.businessDescriptionsForm = this.fb.group({
      shortDescription: [this.formStateWithDisabled(''), Validators.maxLength(200)],
      longDescription: [this.formStateWithDisabled(''), Validators.maxLength(this.longDescriptionLength)],
    });

    this.personalInformationForm = this.fb.group({
      standardizedTitle: [this.formStateWithDisabled()],
      firstName: [this.formStateWithDisabled('')],
      middleInitials: [this.formStateWithDisabled('')],
      lastName: [this.formStateWithDisabled('')],
      professionalCredentials: [this.formStateWithDisabled([])],
      gender: [this.formStateWithDisabled()],
      emailAddress: [this.formStateWithDisabled(''), emailValidator],
    });

    this.serviceAvailabilityForm = this.fb.group({
      ecommerceOnly: [this.formStateWithDisabled()],
      closedStatus: [this.formStateWithDisabled()],
      reopeningDate: [this.formStateWithDisabled()],
    });

    this.qualificationsAndExperienceForm = this.fb.group({
      doctorDotComCategories: [this.formStateWithDisabled([])],
      educations: this.fb.array(
        [[this.formStateWithDisabled('')]],
        Validators.compose([
          Validators.maxLength(15),
          totalLengthOfRepeatedFieldValidator(256, 'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.EDUCATION'),
        ]),
      ),
      specialties: this.fb.array(
        [[this.formStateWithDisabled('')]],
        Validators.compose([
          Validators.maxLength(15),
          totalLengthOfRepeatedFieldValidator(256, 'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.SPECIALTIES'),
        ]),
      ),
      residencies: this.fb.array(
        [[this.formStateWithDisabled('')]],
        Validators.compose([
          Validators.maxLength(15),
          totalLengthOfRepeatedFieldValidator(256, 'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.RESIDENCIES'),
        ]),
      ),
      fellowships: this.fb.array(
        [[this.formStateWithDisabled('')]],
        Validators.compose([
          Validators.maxLength(15),
          totalLengthOfRepeatedFieldValidator(256, 'FRONTEND.BUSINESSES.QUALIFICATIONS_EXPERIENCE.FELLOWSHIPS'),
        ]),
      ),
      nationalProviderIdentifier: [this.formStateWithDisabled('')],
      medicalLicenseNumber: [this.formStateWithDisabled('')],
      stateLicense: [this.formStateWithDisabled('')],
    });

    this.additionalProfessionalInfoForm = this.fb.group({
      acceptingPatients: [this.formStateWithDisabled(null)],
      insurancesAccepted: this.fb.array(
        [[this.formStateWithDisabled('')]],
        Validators.compose([
          Validators.maxLength(15),
          totalLengthOfRepeatedFieldValidator(256, 'FRONTEND.BUSINESSES.INSURANCES_ACCEPTED'),
        ]),
      ),
    });
    this.practiceInformationForm = this.fb.group({
      firmClinicOfficeName: [this.formStateWithDisabled('')],
      isProvider: [this.formStateWithDisabled()],
      hospitalAffiliations: this.fb.array(
        [[this.formStateWithDisabled('')]],
        Validators.compose([
          Validators.maxLength(15),
          totalLengthOfRepeatedFieldValidator(256, 'FRONTEND.BUSINESSES.HOSPITAL_AFFILIATIONS'),
        ]),
      ),
    });

    this.googleAttributesForm = this.fb.group({});

    this.bingAttributesForm = this.fb.group({});

    this.salesForm = this.fb.group({});
    this.adminForm = this.fb.group({});

    if (this.isTabEnabled(BusinessProfileTab.Administration)) {
      this.salesForm = this.fb.group(
        {
          market: [this.formStateWithDisabled(''), Validators.required],
          salespersonId: [this.formStateWithDisabled('')],
          additionalSalespersonIds: [this.formStateWithDisabled([])],
        },
        {
          validators: Validators.compose([
            primaryIsNotInAdditionalSalespeople(
              (value) => this.salespeople?.find((salesperson) => salesperson.salespersonId === value).fullName,
            ),
            primaryIsSelectedWhenHavingAdditionalSalespeople(),
          ]),
        },
      );
      this.adminForm = this.fb.group({
        customerIdentifier: [{ value: '' }],
        tags: [this.formStateWithDisabled([])],
        notes: [this.formStateWithDisabled('')],
      });
    }

    this.timezoneControl = this.fb.control(this.formStateWithDisabled({}));

    this.accountGroupForm = this.fb.group({
      basicInfoForm: this.basicInfoForm,
      serviceAreaForm: this.serviceAreaForm,
      phoneForm: this.phoneForm,
      additionalInfoForm: this.additionalInfoForm,
      businessHoursForm: this.businessHoursForm,
      businessPagesForm: this.businessPagesForm,
      blogForm: this.blogForm,
      businessDescriptionsForm: this.businessDescriptionsForm,
      personalInformationForm: this.personalInformationForm,
      serviceAvailabilityForm: this.serviceAvailabilityForm,
      qualificationsAndExperienceForm: this.qualificationsAndExperienceForm,
      additionalProfessionalInfoForm: this.additionalProfessionalInfoForm,
      practiceInformationForm: this.practiceInformationForm,
      geoControl: this.geoControl,
      adminForm: this.adminForm,
      timeZoneControl: this.timezoneControl,
      googleAttributesForm: this.googleAttributesForm,
      bingAttributesForm: this.bingAttributesForm,
      salesForm: this.salesForm,
    });

    this.countryChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((e) => {
      const paymentMethodsField = this.additionalInfoForm.get('paymentMethods');
      // for some reason, the value from getCountry is not updated
      // when the country changes, so we need to update it manually
      this.basicInfoForm.get('country').setValue(e);
      paymentMethodsField.updateValueAndValidity();
    });
  }

  private loadFormValues(listingProfile: ListingProfile): void {
    if (!listingProfile.legacyProductDetails) {
      listingProfile.legacyProductDetails = createDefaultLegacyProductDetails();
    }

    const commonBusinessNames = listingProfile.legacyProductDetails.commonName || [''];
    this.setControlsForFormArray(
      this.basicInfoForm.get('commonBusinessNames') as FormArray,
      commonBusinessNames.length,
    );
    const ids = listingProfile.externalIdentifiers?.vCategoryIds || [];
    const primaryCategory = ids.length > 0 ? [ids[0]] : [];
    const businessCategories = ids.length > 0 ? ids.slice(1) : [];

    this.basicInfoForm.patchValue({
      businessName: listingProfile.napData.companyName,
      commonBusinessNames: commonBusinessNames,
      country: listingProfile.napData.country,
      website: listingProfile.napData.website,
      email: listingProfile.richData.email,
      businessCategories: businessCategories,
      primaryBusinessCategory: primaryCategory,
      serviceAreaBusiness: listingProfile.napData?.serviceAreaBusiness,
    });

    this.setControlsForFormArray(
      this.serviceAreaForm.get('places') as FormArray,
      listingProfile.napData.serviceArea?.places?.length || 0,
    );

    this.basicInfoForm.get('serviceAreaBusiness').markAsPristine();

    this.serviceAreaForm.setValue({
      businessType: listingProfile.napData.serviceArea?.businessType || 0,
      places: listingProfile.napData.serviceArea?.places || [],
    });
    if (!listingProfile.napData.serviceArea) {
      this.serviceAreaForm.disable();
    }

    this.addressData = {
      company: listingProfile.napData.companyName,
      address1: listingProfile.napData.address,
      address2: listingProfile.napData.address2,
      city: listingProfile.napData.city,
      country: listingProfile.napData.country,
      province: listingProfile.napData.state,
      zip: listingProfile.napData.zip,
    };
    this.geoControl.setValue(listingProfile.napData.location);
    let workNumbers: string[] = listingProfile.napData.workNumber || [''];
    workNumbers = workNumbers.map((number) =>
      this.formatPhoneNumber.transformForBusinessProfile(number, listingProfile.napData.country),
    );
    const callTrackingNumbers = listingProfile.napData.callTrackingNumber || [''];

    this.setControlsForFormArray(
      this.phoneForm.get('workNumbers') as FormArray,
      workNumbers.length,
      this.phoneNumberItemFactory.bind(this),
    );
    this.setControlsForFormArray(this.phoneForm.get('callTrackingNumbers') as FormArray, callTrackingNumbers.length);
    this.phoneForm.patchValue({
      workNumbers: workNumbers,
      faxNumber: listingProfile.richData.faxNumber,
      cellNumber: this.formatPhoneNumber.transformForBusinessProfile(
        listingProfile.richData.cellNumber || '',
        listingProfile.napData.country,
      ),
      callTrackingNumbers: callTrackingNumbers,
      tollFreeNumber: listingProfile.richData.tollFreeNumber,
    });

    const servicesOffered = listingProfile.richData.servicesOffered || [''];
    const brandsCarried = listingProfile.richData.brandsCarried || [''];

    this.setControlsForFormArray(this.additionalInfoForm.get('servicesOffered') as FormArray, servicesOffered.length);
    this.setControlsForFormArray(this.additionalInfoForm.get('brandsCarried') as FormArray, brandsCarried.length);
    this.additionalInfoForm.patchValue({
      paymentMethods: listingProfile.richData.paymentMethods,
      servicesOffered: servicesOffered,
      brandsCarried: brandsCarried,
      landmark: listingProfile.richData.landmark,
      bookingUrl: listingProfile.richData.bookingUrl,
    });

    if (!!listingProfile.richData.conditionalFields && listingProfile.richData.conditionalFields?.length > 0) {
      const richDataCf = listingProfile.richData.conditionalFields;
      const valuesToPatch = {};
      richDataCf.forEach((cf) => {
        valuesToPatch[cf.id] = cf.value;
        this.visibleConditionalFields.push(cf.id);
      });

      this.additionalInfoForm.patchValue(valuesToPatch);
    }

    this.showICOField$ = this.countryChanges.pipe(
      map((country) => {
        if (this.addressData.country !== country) {
          return country === 'CZ';
        }

        return this.isConditionalFieldVisible('ico');
      }),
    );

    const generalHours = listingProfile.businessHours?.find((hours) => hours.hoursTypeId === 'GENERAL');
    const mergedHours = MergeHours(generalHours?.regularHours || []);

    this.businessHoursForm.setValue(mergedHours);
    this.specialHoursForm.setValue(generalHours?.specialHours || []);

    listingProfile.businessHours
      ?.filter((hours) => hours.hoursTypeId !== 'GENERAL')
      .forEach((hours) => {
        this.moreHoursForm.addControl(
          hours.hoursTypeId,
          this.fb.control(this.formStateWithDisabled(hours.regularHours || [])),
        );
      });

    if (listingProfile.socialUrls) {
      this.businessPagesForm.patchValue({
        facebookUrl: listingProfile.socialUrls.facebookUrl ? encodeURI(listingProfile.socialUrls.facebookUrl) : '',
        linkedInUrl: listingProfile.socialUrls.linkedinUrl ? encodeURI(listingProfile.socialUrls.linkedinUrl) : '',
        twitterUrl: listingProfile.socialUrls.twitterUrl ? encodeURI(listingProfile.socialUrls.twitterUrl) : '',
        pinterestUrl: listingProfile.socialUrls.pinterestUrl ? encodeURI(listingProfile.socialUrls.pinterestUrl) : '',
        instagramUrl: listingProfile.socialUrls.instagramUrl ? encodeURI(listingProfile.socialUrls.instagramUrl) : '',
        youTubeUrl: listingProfile.socialUrls.youtubeUrl ? encodeURI(listingProfile.socialUrls.youtubeUrl) : '',
      });
      this.blogForm.patchValue({
        blogOrRssFeedUrl: listingProfile.socialUrls.rssUrl,
      });
    }

    const partnerId = listingProfile.externalIdentifiers.partnerId;
    if (partnerId in this.partnersLongDescriptionLength) {
      this.longDescriptionLength = this.partnersLongDescriptionLength[partnerId];
    }
    this.businessDescriptionsForm.controls.longDescription.setValidators(
      Validators.maxLength(this.longDescriptionLength),
    );

    this.businessDescriptionsForm.patchValue({
      shortDescription: listingProfile.richData.shortDescription,
      longDescription: listingProfile.richData.description,
    });

    if (listingProfile.richData?.serviceAvailability) {
      this.serviceAvailabilityForm.patchValue({
        ecommerceOnly: listingProfile.richData?.serviceAvailability?.ecommerceOnly || null,
        closedStatus: listingProfile.richData?.serviceAvailability?.closedStatus || null,
        reopeningDate: listingProfile.richData?.serviceAvailability?.reopeningDate || null,
      });
    }

    if (listingProfile.richData.healthCareProfessionalInformation) {
      this.personalInformationForm.patchValue({
        standardizedTitle: listingProfile.richData?.healthCareProfessionalInformation?.standardizedTitle || '',
        firstName: listingProfile.richData?.healthCareProfessionalInformation?.firstName || '',
        middleInitials: listingProfile.richData?.healthCareProfessionalInformation?.initials || '',
        lastName: listingProfile.richData?.healthCareProfessionalInformation?.lastName || '',
        professionalCredentials: listingProfile.richData?.healthCareProfessionalInformation?.professionalCredential || [
          '',
        ],
        gender:
          listingProfile.richData?.healthCareProfessionalInformation?.gender ||
          HealthCareProfessionalInformationGender.NotSpecified,
        emailAddress: listingProfile.richData?.healthCareProfessionalInformation?.email || '',
      });

      const educations = listingProfile.richData?.healthCareProfessionalInformation?.school || [''];
      const specialties = listingProfile.richData?.healthCareProfessionalInformation?.specialty || [''];
      const residencies = listingProfile.richData?.healthCareProfessionalInformation?.residency || [''];
      const fellowships = listingProfile.richData?.healthCareProfessionalInformation?.fellowship || [''];

      this.setControlsForFormArray(
        this.qualificationsAndExperienceForm.get('educations') as FormArray,
        educations.length,
      );
      this.setControlsForFormArray(
        this.qualificationsAndExperienceForm.get('specialties') as FormArray,
        specialties.length,
      );
      this.setControlsForFormArray(
        this.qualificationsAndExperienceForm.get('residencies') as FormArray,
        residencies.length,
      );
      this.setControlsForFormArray(
        this.qualificationsAndExperienceForm.get('fellowships') as FormArray,
        fellowships.length,
      );
      this.setControlsForFormArray(
        this.practiceInformationForm.get('hospitalAffiliations') as FormArray,
        listingProfile.richData?.healthCareProfessionalInformation?.hospitalAffiliations?.length,
      );
      this.setControlsForFormArray(
        this.qualificationsAndExperienceForm.get('doctorDotComCategories') as FormArray,
        listingProfile.richData?.healthCareProfessionalInformation?.doctorDotComCategories?.length,
      );

      this.qualificationsAndExperienceForm.patchValue({
        educations: educations,
        specialties: specialties,
        residencies: residencies,
        fellowships: fellowships,
        nationalProviderIdentifier:
          listingProfile.richData?.healthCareProfessionalInformation?.nationalProviderIdentifier || '',
        medicalLicenseNumber: listingProfile.richData?.healthCareProfessionalInformation?.medicalLicenseNumber || '',
        stateLicense: listingProfile.richData?.healthCareProfessionalInformation?.stateLicense || '',
        doctorDotComCategories:
          listingProfile.richData?.healthCareProfessionalInformation?.doctorDotComCategories || [],
      });
      const insurancesAccepted = listingProfile.richData?.healthCareProfessionalInformation?.insurancesAccepted || [''];
      this.setControlsForFormArray(
        this.additionalProfessionalInfoForm.get('insurancesAccepted') as FormArray,
        insurancesAccepted.length,
      );
      this.additionalProfessionalInfoForm.patchValue({
        acceptingPatients: listingProfile.richData?.healthCareProfessionalInformation?.isTakingPatients,
        insurancesAccepted: insurancesAccepted,
      });
      this.practiceInformationForm.patchValue({
        firmClinicOfficeName: listingProfile.richData?.healthCareProfessionalInformation?.office || '',
        isProvider:
          listingProfile.richData?.healthCareProfessionalInformation?.isProvider ||
          HealthCareProfessionalInformationIsProvider.IsProviderNotSpecified,
        hospitalAffiliations: listingProfile.richData?.healthCareProfessionalInformation?.hospitalAffiliations || [''],
      });
    }
    if (this.isTabEnabled(BusinessProfileTab.Administration)) {
      this.salesForm.patchValue({
        market: listingProfile.externalIdentifiers.marketId,
        salespersonId: listingProfile.externalIdentifiers.salesPersonId,
        additionalSalespersonIds: listingProfile.externalIdentifiers.additionalSalesPersonIds,
      });
      if (
        listingProfile.externalIdentifiers.additionalSalesPersonIds &&
        listingProfile.externalIdentifiers.additionalSalesPersonIds.length > 0
      ) {
        this.isAdditionalSalespeopleInputVisible = true;
      }
      this.adminForm.patchValue({
        customerIdentifier: listingProfile.externalIdentifiers.customerIdentifier,
        tags: listingProfile.externalIdentifiers.tags,
        notes: listingProfile.legacyProductDetails.adminNotes,
      });
    }

    this.profileLoading$$.next(false);
  }

  getGoogleCheckboxValues(formGroup: FormGroup, metaAttribute: GoogleAttributeMetaDataInterface): string[] {
    const values: string[] = [];

    Object.keys(formGroup.controls).forEach((key) => {
      if (formGroup.controls[key].getRawValue() === true) {
        const indexOfKeyInValueDisplayName = metaAttribute?.valueDisplayNames.findIndex((d) => d === key);
        values.push(metaAttribute?.values[indexOfKeyInValueDisplayName]);
      }
    });

    return values;
  }

  updateGeoLocation(location: Geo): void {
    this.geoControl.patchValue(location);
    this.geoControl.markAsDirty();
  }

  getBingCheckboxValues(formGroup: FormGroup, metaAttribute: BingAttributeMetaDataInterface): string[] {
    const values: string[] = [];

    Object.keys(formGroup.controls).forEach((key) => {
      if (formGroup.controls[key].getRawValue() === true) {
        const indexOfKeyInValueDisplayName = metaAttribute?.valueDisplayNames.findIndex((d) => d === key);
        values.push(metaAttribute?.values[indexOfKeyInValueDisplayName]);
      }
    });

    return values;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getDoctorDotComCategories(accountGroup: ListingProfile) {
    return this.listingProductsApiService.getDoctorDotComCategories(new GetDoctorDotComCategoriesRequest({}));
  }

  getValidCategoryLength(): number {
    return (
      this.accountGroupForm?.get('basicInfoForm.primaryBusinessCategory')?.value?.filter((c) => c !== 'not_found')
        ?.length || 0
    );
  }

  getBingAttributesToSubmit(
    duHastMe: FormGroup,
    bingAttributeMetaData: BingAttributeMetaDataInterface[],
  ): BingAttribute[] {
    if (!bingAttributeMetaData) {
      bingAttributeMetaData = [];
    }
    const attribs: BingAttribute[] = [];

    bingAttributeMetaData.sort((a, b) => a.displayName.localeCompare(b.displayName));
    const sortOrder = bingAttributeMetaData.map((md) => md.parent);

    Object.keys(duHastMe.controls).forEach((key) => {
      let value = duHastMe.controls[key].value;
      const metaAttribute = bingAttributeMetaData[bingAttributeMetaData.findIndex((d) => d.parent === key)];

      switch (metaAttribute.valueType) {
        case AttributesValueType.REPEATED_ENUM: {
          const form = duHastMe.controls[key] as FormGroup;
          value = this.getGoogleCheckboxValues(form, metaAttribute);
          break;
        }
        case AttributesValueType.ENUM: {
          const ctrl = duHastMe.controls[key] as FormControl;
          value = metaAttribute?.values[metaAttribute.valueDisplayNames.findIndex((v) => v === ctrl.value)];
          break;
        }
      }

      const ba: BingAttribute = {
        name: key,
        value: value,
        toApiJson: () => ({}) as object,
      };
      attribs.push(ba);
    });

    const sortedAttribs = sortOrder.map((i) => attribs.find((j) => j.name === i)).filter(Boolean);

    return sortedAttribs;
  }

  isConditionalFieldVisible(cfId: string): boolean {
    return this.visibleConditionalFields.includes(cfId);
  }

  getGoogleAttributesToSubmit(
    duHastMe: FormGroup,
    googleAttributeMetaData: GoogleAttributeMetaDataInterface[],
  ): GoogleAttribute[] {
    if (!googleAttributeMetaData) {
      googleAttributeMetaData = [];
    }
    const attribs: GoogleAttribute[] = [];

    googleAttributeMetaData.sort((a, b) => a.displayName.localeCompare(b.displayName));
    const sortOrder = googleAttributeMetaData.map((md) => md.parent);

    Object.keys(duHastMe.controls).forEach((key) => {
      let value = duHastMe.controls[key].value;
      const metaAttribute = googleAttributeMetaData[googleAttributeMetaData.findIndex((d) => d.parent === key)];

      switch (metaAttribute.valueType) {
        case AttributesValueType.REPEATED_ENUM: {
          const form = duHastMe.controls[key] as FormGroup;
          value = this.getGoogleCheckboxValues(form, metaAttribute);
          break;
        }
        case AttributesValueType.ENUM: {
          const ctrl = duHastMe.controls[key] as FormControl;
          value = metaAttribute?.values[metaAttribute.valueDisplayNames.findIndex((v) => v === ctrl.value)];
          break;
        }
        default:
      }

      const ga: GoogleAttribute = {
        name: key,
        value: value,
        toApiJson: () => ({}) as object,
      };
      attribs.push(ga);
    });

    const sortedAttribs = sortOrder.map((i) => attribs.find((j) => j.name === i)).filter(Boolean);

    return sortedAttribs;
  }

  compareDoctorDotComCategories(category1: DoctorDotComCategory, category2: DoctorDotComCategory): boolean {
    return category1 && category2 ? category1.id === category2.id : category1 === category2;
  }

  getFirstErrorFromForm(): string {
    const error = this.getFirstInvalidFormControl({ key: 'accountGroupForm', form: this.accountGroupForm });
    if (error) {
      const formErrorsForField = Object.keys(error.form.errors)[0];
      return ValidationErrorMessages[error.key][formErrorsForField];
    }

    return '';
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async onSubmit(arg): Promise<void> {
    if (
      this.accountGroupForm.pristine &&
      !this.addressFormDirty &&
      (!this.auxiliaryDataTable || this.auxiliaryDataTable?.formGroup.pristine)
    ) {
      this.snackbar.open(this.translateService.instant('FRONTEND.BUSINESSES.NO_CHANGES_TO_SAVE'), null, {
        duration: 3000,
      });
      return;
    }

    if (!this.accountGroupForm.valid || this.editDisabled) {
      this.activeTab = this.getFirstInvalidTab();
      const message = this.getFirstErrorFromForm() ?? 'FRONTEND.BUSINESSES.INVALID_SUBMISSION';
      this.snackbar.open(this.translateService.instant(message), null, {
        duration: 3000,
      });
      return;
    }
    const accountGroup: any = {};
    Object.keys(this.accountGroupForm.controls).forEach((controlKey) => {
      const c = this.accountGroupForm.controls[controlKey];
      accountGroup[controlKey] = getChangedValues(c);
    });
    this.trackChangedForms(accountGroup);

    const operations = new UpdateOperations();

    if (this.googleAttributesForm.dirty) {
      const googleAttributeMetaData = this.googleAttributesMetadata();
      operations.addUpdateOperation(
        new GoogleAttributesUpdateOperation({
          googleAttribute: this.getGoogleAttributesToSubmit(this.googleAttributesForm, googleAttributeMetaData),
        }),
      );
    }

    if (this.bingAttributesForm.dirty) {
      const bingAttributeMetaData = this.bingAttributesMetadata();
      operations.addUpdateOperation(
        new BingAttributesUpdateOperation({
          bingAttribute: this.getBingAttributesToSubmit(this.bingAttributesForm, bingAttributeMetaData),
        }),
      );
    }

    const phones = accountGroup.phoneForm || {};
    const basicInfo = accountGroup.basicInfoForm || {};
    const address = accountGroup.basicInfoForm?.addressForm;
    if (basicInfo || address || accountGroup.serviceAreaForm || accountGroup.timeZoneControl || phones) {
      operations.addUpdateOperation(
        new NapUpdateOperation({
          companyName: basicInfo.businessName,
          address: address?.address1,
          address2: address?.address2,
          city: address?.city,
          state: address?.province?.code || undefined,
          zip: address?.zip,
          country: address?.country?.code,
          serviceAreaBusiness: basicInfo.serviceAreaBusiness,
          serviceArea: accountGroup.serviceAreaForm ? new ServiceArea(accountGroup.serviceAreaForm) : undefined,
          website: basicInfo.website,
          workNumber: phones.workNumbers,
          callTrackingNumber: phones.callTrackingNumbers,
          location: accountGroup.geoControl ? new Geo(accountGroup.geoControl) : undefined,
          timezone: accountGroup.timeZoneControl ? this.listingProfile().napData.timezone : undefined,
        }),
      );
    }

    const businessDescriptions = accountGroup.businessDescriptionsForm || {};

    const businessPages = accountGroup.businessPagesForm || {};
    const blog = accountGroup.blogForm || {};
    operations.addUpdateOperation(
      new SocialURLsUpdateOperation({
        linkedinUrl: businessPages.linkedInUrl,
        twitterUrl: businessPages.twitterUrl,
        facebookUrl: businessPages.facebookUrl,
        rssUrl: blog.blogOrRssFeedUrl,
        youtubeUrl: businessPages.youTubeUrl,
        instagramUrl: businessPages.instagramUrl,
        pinterestUrl: businessPages.pinterestUrl,
      }),
    );

    // Healthcare data is a bit different -- if any of the fields changed we need to send everything due to
    // current field mask limitations
    let healthCareProfessionalInfo;
    const healthCareHasChanges =
      accountGroup.practiceInformationForm ||
      accountGroup.additionalProfessionalInfoForm ||
      accountGroup.qualificationsAndExperienceForm ||
      accountGroup.personalInformationForm;
    if (this.hasProfessionalCategory && healthCareHasChanges) {
      const practiceInformation = this.practiceInformationForm.value;
      const additionalProfessionalInfo = this.additionalProfessionalInfoForm.value;
      const qualificationsAndExperience = this.qualificationsAndExperienceForm.value;
      const personalInformation = this.personalInformationForm.value;
      healthCareProfessionalInfo = new HealthCareProfessionalInformation({
        email: personalInformation.emailAddress,
        fellowship: qualificationsAndExperience.fellowships.filter((v: any) => !!v),
        firstName: personalInformation.firstName,
        gender: personalInformation.gender,
        initials: personalInformation.middleInitials,
        insurancesAccepted: additionalProfessionalInfo.insurancesAccepted,
        isTakingPatients: additionalProfessionalInfo.acceptingPatients,
        lastName: personalInformation.lastName,
        medicalLicenseNumber: qualificationsAndExperience.medicalLicenseNumber,
        nationalProviderIdentifier: qualificationsAndExperience.nationalProviderIdentifier,
        office: practiceInformation.firmClinicOfficeName,
        professionalCredential: personalInformation.professionalCredentials,
        residency: qualificationsAndExperience.residencies.filter((v: any) => !!v),
        school: qualificationsAndExperience.educations.filter((v: any) => !!v),
        specialty: qualificationsAndExperience.specialties.filter((v: any) => !!v),
        standardizedTitle: personalInformation.standardizedTitle,
        stateLicense: qualificationsAndExperience.stateLicense,
        doctorDotComCategories: qualificationsAndExperience.doctorDotComCategories.filter((v: any) => !!v),
        hospitalAffiliations: practiceInformation.hospitalAffiliations.filter((v: string | null | undefined) => !!v),
        isProvider: practiceInformation.isProvider,
      });
    } else if (!this.hasProfessionalCategory && (basicInfo.businessCategories || basicInfo.primaryBusinessCategory)) {
      // Categories changed and doesnt have professional category
      healthCareProfessionalInfo = new HealthCareProfessionalInformation({});
    }

    const serviceAvailabilityChanges = this.serviceAvailabilityForm.value || {};

    const serviceAvailabilityInfo = new ServiceAvailability({
      ecommerceOnly: serviceAvailabilityChanges.ecommerceOnly,
      closedStatus: serviceAvailabilityChanges.closedStatus,
      reopeningDate: serviceAvailabilityChanges.reopeningDate,
    });

    if (
      serviceAvailabilityChanges.closedStatus !== undefined &&
      this.closedStatuses.includes(serviceAvailabilityChanges.closedStatus)
    ) {
      serviceAvailabilityInfo.closedStatusDate = new Date();
    } else {
      serviceAvailabilityInfo.closedStatusDate = null;
      serviceAvailabilityInfo.reopeningDate = null;
    }

    const additionalInfo = accountGroup.additionalInfoForm || {};

    const richDataUpdateOperations = new RichData({
      tollFreeNumber: phones.tollFreeNumber,
      cellNumber: phones.cellNumber,
      faxNumber: phones.faxNumber,
      email: basicInfo.email,
      description: businessDescriptions.longDescription,
      shortDescription: businessDescriptions.shortDescription,
      servicesOffered: additionalInfo.servicesOffered,
      brandsCarried: additionalInfo.brandsCarried,
      landmark: additionalInfo.landmark,
      paymentMethods: additionalInfo.paymentMethods,
      serviceAvailability: serviceAvailabilityInfo,
      healthCareProfessionalInformation: healthCareProfessionalInfo,
      bookingUrl: additionalInfo.bookingUrl,
    });

    // only fetch values from visible conditional Fields
    if (this.visibleConditionalFields.length > 0) {
      const cfValues = [];
      this.visibleConditionalFields.forEach((field) => {
        const value = additionalInfo[field];
        if (value != undefined) {
          cfValues.push({
            id: field,
            value: value,
          });
        }
      });
      if (cfValues.length > 0) {
        richDataUpdateOperations.conditionalFields = cfValues;
      }
    }

    operations.addUpdateOperation(new RichDataUpdateOperation(richDataUpdateOperations));

    const externalIdentifiersUpdateOperation = new ExternalIdentifiersUpdateOperation({
      partnerId: this.listingProfile()?.externalIdentifiers.partnerId,
      updateOrigin: 'business-profile',
    });

    if (
      typeof basicInfo.businessCategories !== 'undefined' ||
      typeof basicInfo.primaryBusinessCategory !== 'undefined'
    ) {
      const primary = this.accountGroupForm.get('basicInfoForm.primaryBusinessCategory').value;
      const additional = this.accountGroupForm.get('basicInfoForm.businessCategories').value;
      externalIdentifiersUpdateOperation.vCategoryIds = Array.from(new Set([...primary, ...additional])).filter(
        (id) => !!id,
      );
    }

    if (this.isTabEnabled(BusinessProfileTab.Administration)) {
      if (accountGroup.adminForm) {
        externalIdentifiersUpdateOperation.tags = accountGroup.adminForm.tags;
        externalIdentifiersUpdateOperation.customerIdentifier = accountGroup.adminForm.customerIdentifier;
      }
      if (accountGroup.salesForm) {
        externalIdentifiersUpdateOperation.marketId = accountGroup.salesForm.market;
        externalIdentifiersUpdateOperation.salesPersonId = accountGroup.salesForm.salespersonId;
        externalIdentifiersUpdateOperation.additionalSalesPersonIds = accountGroup.salesForm.additionalSalespersonIds;
      }
    }
    operations.addUpdateOperation(externalIdentifiersUpdateOperation);

    if (
      (this.isTabEnabled(BusinessProfileTab.Administration) && accountGroup?.adminForm?.notes !== undefined) ||
      basicInfo.commonBusinessNames !== undefined
    ) {
      operations.addUpdateOperation(
        new LegacyProductDetailsUpdateOperation({
          adminNotes: accountGroup?.adminForm?.notes,
          commonName: basicInfo?.commonBusinessNames,
        }),
      );
    }

    this.doUpdate({ operations: operations }).pipe(takeUntilDestroyed(this.destroyRef)).subscribe();
    return;
  }

  getFirstInvalidTab(): BusinessProfileTab {
    const tabKeys = Object.keys(BusinessProfileTab);
    for (let i = 0; i < tabKeys.length; i++) {
      if (this.tabInvalid(BusinessProfileTab[tabKeys[i]])) {
        return BusinessProfileTab[tabKeys[i]];
      }
    }
  }

  getFirstInvalidFormControl(invalidForm: InvalidFormField): InvalidFormField | null {
    if (invalidForm.form instanceof FormGroup) {
      const controls = invalidForm.form.controls;
      for (const controlKey in controls) {
        const control = this.getFirstInvalidFormControl({ key: controlKey, form: controls[controlKey] });
        if (!control) {
          continue;
        }
        return control;
      }

      return null;
    }

    if (invalidForm.form instanceof FormArray) {
      if (invalidForm.form.invalid && invalidForm.form.errors) {
        return invalidForm;
      }
      for (let i = 0; i < invalidForm.form.controls.length; i++) {
        const control = this.getFirstInvalidFormControl({ key: invalidForm.key, form: invalidForm.form.controls[i] });
        if (!control) {
          continue;
        }
        return {
          key: invalidForm.key,
          form: control.form,
        };
      }

      return null;
    }
    if (invalidForm.form?.invalid) {
      return invalidForm;
    }
  }

  onBusinessHoursChange(): void {
    const operations: UpdateOperations = new UpdateOperations();

    const sh = SplitHours(this.businessHoursForm.value || []);

    const businessHours = new BusinessHours({
      hoursTypeId: 'GENERAL',
      regularHours: sh,
      specialHours: this.specialHoursForm.value || [],
    });

    operations.addUpdateOperation(new BusinessHoursUpdateOperation(businessHours));
    this.doUpdate({ operations: operations, onlyBusinessHoursSave: true })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  onMoreHoursChange(): void {
    if (this.moreHoursForm.pristine) {
      this.snackbar.open(this.translateService.instant('FRONTEND.BUSINESSES.NO_CHANGES_TO_SAVE'), null, {
        duration: 3000,
      });
      return;
    }
    if (!this.moreHoursForm.valid || this.editDisabled) {
      this.snackbar.open(this.translateService.instant('FRONTEND.BUSINESSES.INVALID_SUBMISSION'), null, {
        duration: 3000,
      });
      return;
    }

    const operations: UpdateOperations = new UpdateOperations();
    Object.keys(this.moreHoursForm.controls).forEach((key) => {
      const values = getChangedValues(this.moreHoursForm.controls[key]);
      if (values) {
        operations.addUpdateOperation(
          new BusinessHoursUpdateOperation(
            new BusinessHours({
              hoursTypeId: key,
              regularHours: values || [],
            }),
          ),
        );
      }
    });

    this.doUpdate({ operations: operations, onlyBusinessHoursSave: true })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  private doUpdate(updateParams: FormUpdateParameters): Observable<any> {
    return this.listingProfileService.bulkUpdate(this.accountGroupId(), updateParams.operations).pipe(
      // TODO: Use injectable alert service?
      catchError((err) => {
        const snackBarRef = this.snackbar.open(err.message, err.retryable ? 'retry' : null, { duration: 3000 });
        snackBarRef
          .onAction()
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe(() => this.doUpdate(updateParams));
        return EMPTY;
      }),
      switchMap(() => this.auxiliaryDataTable?.saveChanges() || of(false)),
      catchError(() => {
        const snackBarRef = this.snackbar.open(
          this.translateService.instant('FRONTEND.BUSINESSES.ACCOUNT_ATTRIBUTES_ERROR'),
          this.translateService.instant('FRONTEND.BUSINESSES.ACCOUNT_ATTRIBUTES_DISMISS'),
          { duration: 3000 },
        );
        this.productAnalyticsService.trackEvent(
          'CustomFields:edit-custom-field-data-fail',
          'CustomFields',
          'edit-custom-field-data-fail',
        );
        snackBarRef.onAction().subscribe(() => snackBarRef.dismiss());
        return snackBarRef.afterDismissed().pipe(map(() => null));
      }),
      tap((auxiliaryDataSaved) => {
        if (updateParams.onlyBusinessHoursSave) {
          this.snackbar.open(this.translateService.instant('FRONTEND.BUSINESSES.BUSINESS_HOURS_SAVED'), null, {
            duration: 3000,
          });
        } else if (updateParams.onlyHoursSave) {
          this.snackbar.open(this.translateService.instant('FRONTEND.BUSINESSES.HOURS_OF_OPERATION_SAVED'), null, {
            duration: 3000,
          });
        } else {
          this.markFormsPristine();
          if (auxiliaryDataSaved) {
            const accountSaved = this.translateService.instant('FRONTEND.BUSINESSES.ACCOUNT_WAS_SAVED');
            const customFieldsSaved = this.translateService.instant('FRONTEND.BUSINESSES.CUSTOM_FIELDS_SAVED');
            this.snackbar.open(`${accountSaved}. ${customFieldsSaved}.`, null, {
              duration: 3000,
            });
            this.productAnalyticsService.trackEvent(
              'CustomFields:edit-custom-field-data-success',
              'CustomFields',
              'edit-custom-field-data-success',
            );
          } else {
            this.snackbar.open(this.translateService.instant('FRONTEND.BUSINESSES.ACCOUNT_WAS_SAVED'), null, {
              duration: 3000,
            });
          }
        }
      }),
      tap(() => {
        this.saved.emit();
      }),
    );
  }

  ngOnInit(): void {
    this.activeTab = BusinessProfileTab.Primary;
    this.createForm();
    if (this.isTabEnabled(BusinessProfileTab.Administration)) {
      this.watchMarketForSalespeople();
    }

    this.availableDoctorDotComCategories = this.listingProfile$.pipe(
      switchMap((accountGroup) =>
        this.getDoctorDotComCategories(accountGroup).pipe(
          map((categories) => {
            const allCategories = categories.categories;
            if (accountGroup.richData?.healthCareProfessionalInformation?.doctorDotComCategories?.length > 0) {
              this.currentDoctorDotComCategories =
                accountGroup.richData.healthCareProfessionalInformation.doctorDotComCategories;
            }
            return allCategories;
          }),
          catchError((error) => {
            console.error('Error fetching doctor.com categories:', error);
            return of(null);
          }),
        ),
      ),
    );

    this.listingProfile$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((listingProfile) => {
      this.loadFormValues(listingProfile);
      this.mapZoomLevel = this.getMapZoomLevel();
      if (!listingProfile.napData.location) {
        this.inferMapBounds();
      }
      this.timezoneControl.setValue(buildTimezoneDisplay(listingProfile?.napData?.timezone));
      return listingProfile;
    });

    const bomTranslateString = 'FRONTEND.BUSINESSES.BUSINESS_OPERATION_METHODS.';
    const bomTranslateKeys = Object.keys(ClosedStatus).filter((k) => isNaN(Number(k)));
    this.businessOperationMethods$ = combineLatest([
      this.translateService.stream(bomTranslateKeys.map((key) => bomTranslateString + key)),
      this.serviceAvailabilityForm.get('closedStatus').valueChanges.pipe(startWith(0)),
    ]).pipe(
      map(([translations]) => {
        const businessOperationMethods = BusinessOperationMethods.slice();
        return businessOperationMethods.map((method) => {
          return {
            code: method,
            name: translations[bomTranslateString + ClosedStatus[method]],
          };
        });
      }),
    );

    const ecommerceValueChanges$ = this.serviceAvailabilityForm.get('ecommerceOnly').valueChanges.pipe(startWith(0));
    this.ecommerceOnly$ = ecommerceValueChanges$.pipe(switchMap(() => this.availabilityOptions()));

    const paymentMethodKeys = Object.keys(RichDataPaymentMethods).filter((k) => isNaN(Number(k)));
    const paymentMethodsString = 'FRONTEND.BUSINESSES.PAYMENT_METHODS';
    this.paymentMethods$ = combineLatest([
      this.countryChanges,
      this.translateService.stream(paymentMethodKeys.map((key) => `${paymentMethodsString}.${key}`)),
      this.additionalInfoForm.get('paymentMethods').valueChanges.pipe(startWith([])),
    ]).pipe(
      map(([countryCode, paymentMethodsTranslations, valueChanges]) =>
        this.setPaymentMethods(countryCode, paymentMethodsTranslations, valueChanges),
      ),
    );

    const genderTranslateString = 'FRONTEND.BUSINESSES.GENDERS.';
    const genderTranslateKeys = Object.keys(GenderKeys).filter((k) => isNaN(Number(k)));
    this.genders$ = this.translateService.stream(genderTranslateKeys.map((key) => genderTranslateString + key)).pipe(
      map((genders) => {
        const selections = GenderSelection.slice();
        return selections.map((selection) => {
          return {
            code: selection,
            name: genders[`${genderTranslateString}${GenderKeys[selection]}`],
          };
        });
      }),
    );

    const isProviderTranslateKeys = new Map<HealthCareProfessionalInformationIsProvider, string>([
      [HealthCareProfessionalInformationIsProvider.IsProviderTrue, 'FRONTEND.BUSINESSES.IS_PROVIDER_ENUM.PRACTITIONER'],
      [HealthCareProfessionalInformationIsProvider.IsProviderFalse, 'FRONTEND.BUSINESSES.IS_PROVIDER_ENUM.CLINIC'],
    ]);

    this.isProviderOptions = Array.from(isProviderTranslateKeys.keys()).map((key) => {
      return {
        code: key,
        name: isProviderTranslateKeys.get(key),
      };
    });

    this.standardizedTitles$ = this.translateService.stream(getAllStandardizedTitles()).pipe(
      map((standardizedTitles) => {
        return [
          { code: null, name: null },
          ...Object.keys(standardizedTitles).map((key) => {
            return { code: key, name: standardizedTitles[key] };
          }),
        ];
      }),
    );
  }

  private async availabilityOptions(): Promise<AvailabilityOption[]> {
    const availabilities = SellingMethodsAvailability.slice();
    const availabilityTranslateKeys = Object.keys(IsAvailable).filter((k) => isNaN(Number(k)));
    const availabilityTranslateStrings = 'FRONTEND.BUSINESSES.SELLING_METHODS_AVAILABILITY';
    const availabilityStrings = availabilityTranslateKeys.map((key) => `${availabilityTranslateStrings}.${key}`);

    const translation = this.translateService.instant(availabilityStrings);

    return availabilities.map((method) => {
      return <AvailabilityOption>{
        code: method,
        name: translation[`${availabilityTranslateStrings}.${IsAvailable[method]}`],
      };
    });
  }

  isTabEnabled(tab: BusinessProfileTab): boolean {
    return this.enabledTabs.includes(tab);
  }

  isValidArray(array: any): any {
    return array === null || array === undefined || array.length === 0 ? false : array;
  }

  handleCategoriesChange(categories: CategoryInterface[]): void {
    this.hasProfessionalCategory = categories?.some((category) => isHealthCategory(category));
  }

  showAdditionalSalespeopleInput(): void {
    this.isAdditionalSalespeopleInputVisible = true;
  }

  getPrimarySalespersonInAdditionalSalespeopleErrorMsg(): string {
    const errors: { [key: string]: any } = this.salesForm.get('additionalSalespersonIds').errors;
    return this.translateService.instant('FRONTEND.BUSINESSES.ALREADY_THE_PRIMARY_SALESPERSON', {
      name: errors.primaryName,
    });
  }

  private watchMarketForSalespeople(): void {
    this.salesForm.get('market').valueChanges.forEach((marketId) => {
      if (this.listingProfile()) {
        const partnerId = this.listingProfile().externalIdentifiers.partnerId;
        const req: ListSalespersonsRequestListSalespersonsFiltersInterface = {
          partnerId: partnerId,
          marketId: marketId,
        };
        this.salespersonService
          .listSalespersons(req, '', 500)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe((result) => {
            this.salespeople = result.results.map((sp) => convertFromSales(partnerId, marketId, sp));
            this.additionalSalespeople = this.salespeople.map((sp) => ({ name: sp.fullName, value: sp.salespersonId }));
          }),
          this.salesForm.patchValue({ salespersonId: '' });
        this.salesForm.patchValue({ additionalSalespersonIds: [] });
      }
    });
  }

  private formStateWithDisabled<T>(value?: T): object {
    return { value: value, disabled: this.editDisabled };
  }

  private setControlsForFormArray(formArray: FormArray, length: number, controlFactory?: ItemFactory): void {
    // Wrapping helper to provided default control to reduce duplication
    const defaultControlFactory = () => this.fb.control(this.formStateWithDisabled(''));
    fixControlsForFormArray(formArray, controlFactory || defaultControlFactory, length);
  }

  getCountryCode(): string {
    return this.basicInfoForm.get('country').value || 'US';
  }

  private getMapZoomLevel(): number {
    let zoom = 6;
    if (this.listingProfile().napData.state) {
      zoom = 7;
    }
    if (this.listingProfile().napData.city) {
      zoom = 13;
    }
    if (this.listingProfile().napData.address || this.listingProfile().napData.zip || this.geoControl.value) {
      zoom = 15;
    }
    return zoom;
  }

  get maxCompetitorsFields(): number {
    return this.maxCompetitors || 3;
  }

  get maxShareOfVoiceKeywordsFields(): number {
    return this.maxShareOfVoiceKeywords || 3;
  }

  // inferMapBounds uses the NAP data to infer the boundaries to use for the geolocation control
  private inferMapBounds(): void {
    this.mapsLoaded$.pipe(
      map((didLoad) => {
        if (didLoad) {
          const fullAddress = [
            this.listingProfile().napData.address || '',
            this.listingProfile().napData.city || '',
            this.listingProfile().napData.state || '',
            this.listingProfile().napData.zip || '',
            this.listingProfile().napData.country || '',
          ].join(',');

          this.geocoder.geocode({ address: fullAddress }).pipe(
            map((result: MapGeocoderResponse) => {
              if (result.status === google.maps.GeocoderStatus.OK && result.results.length > 0) {
                const viewport = result.results[0].geometry.viewport;
                this.zone.run(() => {
                  this.inferredMapBounds = {
                    east: viewport.getNorthEast().lng(),
                    north: viewport.getNorthEast().lat(),
                    west: viewport.getSouthWest().lng(),
                    south: viewport.getSouthWest().lat(),
                  };
                });
              }
            }),
          );
        }
      }),
    );
  }

  openLightbox(data: LightboxData): void {
    this.lightbox.open({ imageUrls: data.imageUrls, index: data.index });
  }

  onTabSwitch(index: BusinessProfileTab): void {
    this.activeTab = index;
  }

  tabInvalid(tab: BusinessProfileTab): boolean {
    switch (tab) {
      case BusinessProfileTab.Primary:
        return (
          this.basicInfoForm.invalid ||
          this.additionalInfoForm.invalid ||
          this.phoneForm.invalid ||
          this.serviceAvailabilityForm.invalid ||
          this.serviceAreaForm.invalid
        );
      case BusinessProfileTab.Social:
        return this.businessPagesForm.invalid || this.blogForm.invalid;
      case BusinessProfileTab.Professional:
        return (
          this.personalInformationForm.invalid ||
          this.practiceInformationForm.invalid ||
          this.qualificationsAndExperienceForm.invalid ||
          this.additionalProfessionalInfoForm.invalid
        );
      case BusinessProfileTab.Descriptions:
        return this.businessDescriptionsForm.invalid;
      case BusinessProfileTab.Administration:
        return this.salesForm.invalid || this.adminForm.invalid;
    }
  }

  tabChanged(tab: BusinessProfileTab): boolean {
    switch (tab) {
      case BusinessProfileTab.Primary:
        return (
          this.basicInfoForm.dirty ||
          this.additionalInfoForm.dirty ||
          this.phoneForm.dirty ||
          this.addressFormDirty ||
          this.serviceAvailabilityForm.dirty ||
          this.serviceAreaForm.dirty
        );
      case BusinessProfileTab.Social:
        return this.businessPagesForm.dirty || this.blogForm.dirty;
      case BusinessProfileTab.Professional:
        return (
          this.personalInformationForm.dirty ||
          this.practiceInformationForm.dirty ||
          this.qualificationsAndExperienceForm.dirty ||
          this.additionalProfessionalInfoForm.dirty
        );
      case BusinessProfileTab.Descriptions:
        return this.businessDescriptionsForm.dirty;
      case BusinessProfileTab.Administration:
        return this.salesForm.dirty || this.adminForm.dirty;
    }
  }

  hideSaveButton(): boolean {
    const tabsToHideSaveButton = [];
    return tabsToHideSaveButton.indexOf(this.activeTab) > -1;
  }

  markFormsPristine(): void {
    this.basicInfoForm.markAsPristine();
    this.additionalInfoForm.markAsPristine();
    this.serviceAvailabilityForm.markAsPristine();
    this.phoneForm.markAsPristine();
    this.businessPagesForm.markAsPristine();
    this.blogForm.markAsPristine();
    this.personalInformationForm.markAsPristine();
    this.practiceInformationForm.markAsPristine();
    this.qualificationsAndExperienceForm.markAsPristine();
    this.additionalProfessionalInfoForm.markAsPristine();
    this.businessDescriptionsForm.markAsPristine();
    this.googleAttributesForm.markAsPristine();
    this.bingAttributesForm.markAsPristine();
    this.serviceAreaForm.markAsPristine();
    this.auxiliaryDataTable?.formGroup.markAsPristine();
    this.salesForm.markAsPristine();
    this.adminForm.markAsPristine();
    this.businessHoursForm.markAsPristine();
    this.specialHoursForm.markAsPristine();
    this.moreHoursForm.markAsPristine();
    this.geoControl.markAsPristine();
    this.timezoneControl.markAsPristine();
  }

  showMap(): boolean {
    if (this.hiddenFields) {
      return this.hiddenFields.indexOf(HiddenField.Map) < 0;
    }
    return true;
  }

  hasAccessToMarkets(): boolean {
    if (this.hiddenFields) {
      return this.hiddenFields.indexOf(HiddenField.Markets) < 0;
    }
    return true;
  }

  hasAccessToSales(): boolean {
    if (this.hiddenFields) {
      return this.hiddenFields.indexOf(HiddenField.Sales) < 0;
    }
    return true;
  }

  showCustomerIdentifier(): boolean {
    if (this.hiddenFields) {
      return this.hiddenFields.indexOf(HiddenField.CustomerIdentifier) < 0;
    }
    return true;
  }

  get showDateTemporaryClose(): boolean {
    const closedValue = this.serviceAvailabilityForm.get('closedStatus').value;
    return (
      closedValue === ClosedStatus.TEMPORARY ||
      closedValue === ClosedStatus.LIMITED ||
      closedValue === ClosedStatus.PERMANENT
    );
  }

  handleTimeZoneChange(timeZone: string): void {
    this.listingProfile().napData.timezone = timeZone;
  }

  trackChangedForms(accountGroup: any): void {
    const changedForms = Object.keys(accountGroup)?.filter((key) => {
      return !!accountGroup[key];
    });
    const changedTabs = new Set(changedForms.map((form) => formTabs[form]));
    changedTabs.forEach((tab) => {
      this.productAnalyticsService.trackEvent(`listing-profile-tab-update`, 'listing-profile', 'change', 1, {
        tab: BusinessProfileTab[tab],
      });
    });
    changedForms.forEach((form) => {
      this.productAnalyticsService.trackEvent(`listing-profile-form-update`, 'listing-profile', 'change', 1, {
        form: form,
      });
    });
  }

  removePlace(placeId: string): void {
    const places: GooglePlace[] = this.accountGroupForm.get('serviceAreaForm.places')?.value || [];
    this.accountGroupForm.get('serviceAreaForm.places').setValue(places.filter((p) => p.placeId !== placeId));
    this.markServiceAreaDirty();
  }

  addPlace(place: GooglePlace): void {
    const places: GooglePlace[] = this.accountGroupForm.get('serviceAreaForm.places').value || [];
    if (places.filter((p) => p.placeId === place.placeId)?.length > 0) {
      return;
    }
    places.push(place);
    this.accountGroupForm.get('serviceAreaForm.places').setValue(places);
    this.markServiceAreaDirty();
  }

  markServiceAreaDirty(): void {
    this.accountGroupForm.get('serviceAreaForm').markAsDirty();
    this.accountGroupForm.get('serviceAreaForm.places').markAsDirty();
    this.accountGroupForm.get('serviceAreaForm.businessType').markAsDirty();
    this.accountGroupForm.get('basicInfoForm.serviceAreaBusiness').markAsDirty();
  }

  updateServiceArea(checked: boolean): void {
    if (checked) {
      this.accountGroupForm.get('serviceAreaForm').enable();
      this.accountGroupForm.get('serviceAreaForm').setValue({ businessType: 1, places: [] });
      this.markServiceAreaDirty();
    } else {
      this.accountGroupForm.get('serviceAreaForm').disable();
    }
  }

  getSuggestion = (fieldType: string): Observable<string> => {
    return this.suggestionService
      .getSuggestion(
        new GetSuggestionRequest({
          businessId: this.accountGroupId(),
          fieldType: fieldType,
          languageCode: this.translateService.currentLang || this.translateService.defaultLang,
        }),
      )
      .pipe(
        catchError((err: HttpErrorResponse) => {
          const message = err.error?.message ? err.error.message : 'Unknown error';
          this.snackbar.open(message, '', { politeness: 'assertive', duration: 5000 });
          return EMPTY;
        }),
        map((resp: GetSuggestionResponse) => resp?.suggestion),
      );
  };

  suggestFieldUpdate = (
    fieldType: string,
    existing: string,
    option: UpdateOption,
    tone?: string,
  ): Observable<string> => {
    return this.suggestionService
      .suggestFieldUpdate(
        new SuggestFieldUpdateRequest({
          businessId: this.accountGroupId(),
          fieldType: fieldType,
          existingValue: existing,
          option: option,
          tone: tone,
          languageCode: this.translateService.currentLang || this.translateService.defaultLang,
        }),
      )
      .pipe(
        catchError((err: HttpErrorResponse) => {
          const message = err.error?.message ? err.error.message : 'Unknown error';
          this.snackbar.open(message, '', { politeness: 'assertive', duration: 5000 });
          return EMPTY;
        }),
        map((resp: GetSuggestionResponse) => resp?.suggestion),
      );
  };

  private setPaymentMethods(countryCode: string, paymentMethodsTranslations, valueChanges) {
    const paymentMethodsString = 'FRONTEND.BUSINESSES.PAYMENT_METHODS';
    let paymentMethods = CountryPaymentMethods['DEFAULT'].slice();
    if (countryCode && CountryPaymentMethods[countryCode]) {
      paymentMethods = CountryPaymentMethods[countryCode].slice();
    }
    if (valueChanges) {
      (valueChanges as number[]).forEach((selectedMethod) => {
        if (paymentMethods.indexOf(selectedMethod) < 0) {
          paymentMethods.push(selectedMethod);
        }
      });
    }
    return paymentMethods.map((method) => ({
      code: method,
      name: paymentMethodsTranslations[`${paymentMethodsString}.${RichDataPaymentMethods[method]}`],
    }));
  }
}

function convertFromSales(partnerId: string, marketId: string, sp: SalesSalesperson): Salesperson {
  return {
    partnerId: partnerId,
    marketId: marketId,
    salespersonId: sp.salespersonId,
    fullName: `${sp.firstName} ${sp.lastName}`,
    firstName: sp.firstName,
    lastName: sp.lastName,
    email: sp.email,
  };
}

function createDefaultLegacyProductDetails(): LegacyProductDetails {
  return new LegacyProductDetails({
    commonName: [],
    adminNotes: '',
  });
}

function paymentMethodValidator(getCountryCode: () => string | string, translateService: any): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } => {
    if (!control.value) {
      return null;
    }
    const countryCode = getCountryCode();
    let paymentMethods = CountryPaymentMethods['DEFAULT'].slice();
    if (countryCode && CountryPaymentMethods[countryCode]) {
      paymentMethods = CountryPaymentMethods[countryCode].slice();
    }
    const invalidPaymentMethods = (control.value as number[]).filter((selectedPaymentMethod) => {
      return paymentMethods.indexOf(selectedPaymentMethod) < 0;
    });
    if (invalidPaymentMethods.length) {
      const paymentMethodsTranslations = translateService.instant('FRONTEND.BUSINESSES.PAYMENT_METHODS');
      const paymentMethodsString = invalidPaymentMethods
        .map((method) => {
          return paymentMethodsTranslations[RichDataPaymentMethods[method]];
        })
        .join(', ');
      const message = translateService.instant('FRONTEND.BUSINESSES.PAYMENT_METHODS.INVALID_PAYMENT_METHODS', {
        paymentMethods: paymentMethodsString,
      });
      return { paymentMethods: message };
    }
    return null;
  };
}

function getAllStandardizedTitles(): string[] {
  return [
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.ACCOUNTING',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.ADMINISTRATOR',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.BOARD_MEMBER',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.CHAIRMAN',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.CEO',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.CIO_CTO',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.CONTROLLER',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.CORPORATE_TAX',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.DIRECTOR',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.DIVISION_HEAD',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.E_COMMERCE',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.EDUCATOR',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.ENGINEERING',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.ENVIRONMENT',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.EVP',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.EXECUTIVE',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.FACILITIES',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.FINANCE',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.INSURANCE',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.INTERNAL_AUDIT',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.INTERNATIONAL',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.IT_EXECUTIVE',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.LEGAL',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.LEGAL_COUNSEL',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.MANAGER',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.MANUFACTURING',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.MARKETING',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.MINISTER',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.OFFICE_MANAGER',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.OPERATIONS',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.OWNER',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.PARTNER',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.PRESIDENT',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.PRINCIPAL',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.PUBLISHER',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.PURCHASING',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.QUALITY',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.REAL_ESTATE',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.REGION_MANAGER',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.SALES',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.SITE_MANAGER',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.SVP',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.TRAINING',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.VICE_CHAIRMAN',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.VICE_PRESIDENT',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.VP',
    'FRONTEND.BUSINESSES.STANDARDIZED_TITLES.WEBMASTER',
  ];
}
