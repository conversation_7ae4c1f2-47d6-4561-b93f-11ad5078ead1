<div class="row row-gutters">
  @for (imageData of profileImageData; track imageData) {
    <div class="col col-xs-12 col-sm-6 col-md-4">
      <mat-card class="category-card">
        @if (imageData.imageLink) {
          <mat-card-header>
            <mat-card-title class="title-with-button">
              <span>{{ imageData.title | translate }}</span>
              <mat-icon
                class="tab-label-title-tooltip"
                [glxyTooltip]="imageData.hoverText | translate"
                [tooltipTitle]="imageData.title | translate"
                [highContrast]="false"
              >
                info_outline
              </mat-icon>
              <span class="spacer"></span>
              <button mat-stroked-button [matMenuTriggerFor]="menu" type="button">
                <mat-icon>edit</mat-icon>
                {{ 'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.EDIT' | translate }}
              </button>
              <mat-menu #menu="matMenu">
                <button mat-menu-item (click)="deleteMenuClicked(imageData.imageLink, imageData.type)">
                  <mat-icon>delete</mat-icon> {{ 'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.DELETE' | translate }}
                </button>
                <button mat-menu-item (click)="openImageUpdateDialog(imageData)">
                  <mat-icon>file_upload</mat-icon>
                  {{ 'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.UPLOAD_NEW' | translate }}
                </button>
              </mat-menu>
            </mat-card-title>
          </mat-card-header>
          <img [src]="imageData.imageLink" mat-card-image class="loaded-profile-image" />
          <div class="v-spacer"></div>
        } @else {
          <mat-card-header>
            <mat-card-title class="title-with-button"
              >{{ imageData.title | translate }}
              <mat-icon
                class="tab-label-title-tooltip"
                [glxyTooltip]="imageData.hoverText | translate"
                [tooltipTitle]="imageData.title | translate"
                [highContrast]="false"
              >
                info_outline
              </mat-icon>
              <span class="spacer"></span>
            </mat-card-title>
          </mat-card-header>
          <img [src]="imageData.emptyImageLink" class="empty-default-state-image" mat-card-image />
          <div class="business-media-uploader-container">
            <business-media-image-uploader
              [buttonText]="imageData.buttonText | translate"
              [aspectRatio]="imageData.ratio"
              (imageChanged)="onImageChanged($event, imageData.type)"
              (loaderState)="setLoader($event, imageData.title)"
            >
            </business-media-image-uploader>
            <div *ngIf="getLoader(imageData.title)">
              <glxy-loading-spinner
                [size]="'large'"
                [fullWidth]="true"
                [fullHeight]="true"
                [inline]="false"
              ></glxy-loading-spinner>
            </div>
          </div>
        }
      </mat-card>
    </div>
  }
</div>

<mat-card appearance="outlined">
  @let images = images$ | async;
  <mat-card-header class="card-header">
    <mat-card-title>
      <div class="card-title-container">
        <div class="left-section">
          <span class="gallery-title">{{ 'FRONTEND.BUSINESSES.GALLERY' | translate }} </span><br />
          <span class="grey-text">{{
            'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.GALLERY_SECOND_TITLE' | translate
          }}</span>
        </div>
        <div class="right-section">
          @if (!editDisabled() && images.length > 0) {
            <business-gallery-multi-image-uploader
              [buttonText]="'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.ADD_GALLARY_PHOTO' | translate"
              (imageChanged)="onImageChanged($event, MenuOption.Gallery)"
              (loaderState)="setLoader($event, 'gallery')"
            >
            </business-gallery-multi-image-uploader>
          }
        </div>
      </div>
    </mat-card-title>
  </mat-card-header>

  <mat-card-content layout="column" layout-align="center center" class="image-gallery-card">
    @if (getLoader('gallery')) {
      <glxy-loading-spinner
        [size]="'large'"
        [fullWidth]="true"
        [fullHeight]="true"
        [inline]="false"
      ></glxy-loading-spinner>
    }
    @if (images && images.length > 0) {
      @if (galleryInfo$ | async; as info) {
        <business-media-tab-gallery
          [images]="images"
          (imageClicked)="openLightbox($event)"
          (menuOptionSelected)="imageMenuClicked($event)"
        ></business-media-tab-gallery>
        @if (info.loading) {
          <mat-progress-bar class="progress-bar" mode="indeterminate"></mat-progress-bar>
        }
        @if (info.pageFetchError) {
          <div class="connection-error">
            <span>
              {{ 'FRONTEND.BUSINESSES.MEDIA_TAB.UNABLE_TO_LOAD_IMAGES' | translate }}
            </span>
          </div>
        }
        @if (info.hasMore) {
          <div class="load-more-button">
            <button mat-button type="button" [disabled]="info.loading" (click)="loadMore()">
              {{ 'FRONTEND.BUSINESSES.MEDIA_TAB.LOAD_MORE' | translate }}
            </button>
          </div>
        }
        <div class="result-count">
          <span>
            {{
              'FRONTEND.BUSINESSES.MEDIA_TAB.SHOWING_X_OF_Y'
                | translate: { count: images.length, total: info.totalImages }
            }}
          </span>
        </div>
      }
    } @else {
      <span class="no-images-section">
        <img [src]="galleryDefaultImageLink" class="empty-default-state-image" mat-card-image />
        <div class="business-media-uploader-container">
          <business-gallery-multi-image-uploader
            [buttonText]="'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.ADD_GALLARY_PHOTO' | translate"
            (imageChanged)="onImageChanged($event, MenuOption.Gallery)"
            (loaderState)="setLoader($event, 'gallery')"
          >
          </business-gallery-multi-image-uploader>
          <div>
            <div class="center-section">
              <span class="grey-text">{{ 'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.GALLERY_ALERT' | translate }}</span>
            </div>
          </div>
        </div>
      </span>
    }
  </mat-card-content>
</mat-card>
