import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MenuOption } from '../media-tab.interface';

@Component({
  selector: 'business-image-update-dialog',
  templateUrl: './image-update-dialog.component.html', // External HTML template
  styleUrls: ['./image-update-dialog.component.scss'], // Optional: External CSS (if needed)
  standalone: false,
})
export class ImageUpdateDialogComponent {
  buttonText: string;
  menuOption: MenuOption;
  ratio: number;
  constructor(
    private dialogRef: MatDialogRef<ImageUpdateDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any, // Data passed into the dialog
  ) {
    this.buttonText = data.buttonText || 'Upload Image';
    this.menuOption = data.type;
    this.ratio = data.ratio;
  }

  onImageChanged(event: any): void {
    // Handle image change
    this.dialogRef.close(event); // Confirm and close the dialog
  }

  onCancel(): void {
    this.dialogRef.close(false); // Close the dialog without action
  }
}
