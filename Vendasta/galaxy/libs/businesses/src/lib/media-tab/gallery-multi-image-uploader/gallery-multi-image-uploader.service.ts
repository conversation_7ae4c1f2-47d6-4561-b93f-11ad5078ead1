import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FileInfo, GalaxyImageUploaderService } from '@vendasta/galaxy/uploader';
import { Observable, Subject, of } from 'rxjs';
import { finalize, switchMap, shareReplay } from 'rxjs/operators';
import { Environment, EnvironmentService } from '@galaxy/core';

export interface UploadResponse {
  data: {
    url: string;
  };
}
@Injectable({
  providedIn: 'root', // This makes the service a singleton
})
export class UploadedUrlsService {
  private uploadedUrls: string[] = [];
  private uploadedUrlsSubject$ = new Subject<string[]>();

  public getUploadedUrls$(): Observable<string[]> {
    return this.uploadedUrlsSubject$.asObservable().pipe(shareReplay(1));
  }

  public addUploadedUrl(url: string, isFinalUpload: boolean): void {
    this.uploadedUrls.push(url);
    if (isFinalUpload) {
      this.uploadedUrlsSubject$.next(this.uploadedUrls);
      this.uploadedUrls = [];
    }
  }
}

@Injectable()
export class GalleryMultiImageUploaderService extends GalaxyImageUploaderService {
  private uploadQueue: FileInfo[] = [];
  private isUploading = false;
  private uploadSubjects = new Map<FileInfo, Subject<UploadResponse>>(); // Map to hold unique subjects per file

  constructor(
    private httpClient: HttpClient,
    private environmentService: EnvironmentService,
    private uploadedUrlsService: UploadedUrlsService,
  ) {
    super(httpClient);
  }

  public getURL(): string {
    switch (this.environmentService.getEnvironment()) {
      case Environment.DEMO: {
        return 'https://media-demo.apigateway.co/image/upload';
      }
      case Environment.PROD: {
        return 'https://media-prod.apigateway.co/image/upload';
      }
      default: {
        return 'https://media-demo.apigateway.co/image/upload';
      }
    }
  }

  buildRequest(fileInfo: FileInfo): Observable<UploadResponse> {
    const subject = new Subject<UploadResponse>();
    this.uploadSubjects.set(fileInfo, subject);
    this.uploadQueue.push(fileInfo);
    if (!this.isUploading) {
      this.processNextUpload();
    }
    return subject.asObservable();
  }

  private processNextUpload() {
    if (this.uploadQueue.length === 0) {
      this.isUploading = false;
      return;
    }
    this.isUploading = true;

    const fileInfo = this.uploadQueue.shift();
    const subject = this.uploadSubjects.get(fileInfo);

    if (fileInfo && subject) {
      const imageToUpload = fileInfo?.data?.croppedImage || fileInfo.file;
      const formData = new FormData();
      formData.append('image', imageToUpload);
      this.httpClient
        .post<UploadResponse>(this.getURL(), formData, { withCredentials: true })
        .pipe(
          switchMap((response: UploadResponse) => {
            subject.next(response);
            subject.complete();
            this.uploadSubjects.delete(fileInfo);
            const isFinalUpload = this.uploadQueue.length === 0;
            this.uploadedUrlsService.addUploadedUrl(response.data.url, isFinalUpload);
            return of(response);
          }),
          finalize(() => {
            this.processNextUpload();
          }),
        )
        .subscribe({
          error: (error) => {
            subject.error(error);
            this.uploadSubjects.delete(fileInfo);
          },
        });
    }
  }
}
