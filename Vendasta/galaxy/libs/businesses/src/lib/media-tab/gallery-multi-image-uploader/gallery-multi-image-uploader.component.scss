@use 'design-tokens' as *;

.button-container {
  display: flex;
  justify-content: center; /* Horizontal alignment */
  align-items: center; /* Vertical alignment */
}

.flex-container {
  display: flex;

  glxy-image-uploader,
  glxy-image-editor {
    width: 280px;
  }

  .upload-image-button-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    button {
      margin-left: $spacing-3;
      margin-top: $spacing-3;
    }

    button:first-of-type {
      margin-top: 0;
    }
  }
}

.flex-container.row-orientation {
  flex-direction: row;
}

.upload-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 280px;
  max-height: 180px;
  border-radius: $default-border-radius;
  border: 1px solid $border-color;
  background-color: $secondary-background-color;

  img {
    object-fit: contain;
    max-width: 100%;
    max-height: 100%;
  }

  img.extra-padding {
    max-width: calc(100% - #{$spacing-3});
    max-height: calc(100% - #{$spacing-3});
  }

  &.extra-padding {
    padding: $spacing-3;
  }

  &.light-preview {
    background-color: $glxy-grey-50;
  }

  &.dark-preview {
    background-color: $glxy-grey-900;
  }

  &.extra-small {
    width: 64px;
    height: 64px;
  }

  &.small {
    width: 128px;
    height: 128px;
  }
}
