import { Component, EventEmitter, Inject, input, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  FileInfo,
  FileUploadError,
  GalaxyImageUploaderService,
  GALAXY_UPLOADER_SERVICE_TOKEN,
} from '@vendasta/galaxy/uploader';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { GalleryMultiImageUploaderService, UploadedUrlsService } from './gallery-multi-image-uploader.service';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'business-gallery-multi-image-uploader',
  templateUrl: './gallery-multi-image-uploader.component.html',
  styleUrls: ['./gallery-multi-image-uploader.component.scss'],
  providers: [{ provide: GALAXY_UPLOADER_SERVICE_TOKEN, useClass: GalleryMultiImageUploaderService }],
  standalone: false,
})
export class GalleryMultiImageUploaderComponent implements OnInit, OnD<PERSON>roy {
  constructor(
    @Inject(GALAXY_UPLOADER_SERVICE_TOKEN) private uploadService: GalaxyImageUploaderService,
    private uploadedUrlsService: UploadedUrlsService,
    private snackService: SnackbarService,
    private dialog: MatDialog,
  ) {}

  /** list of accepted image file types */
  @Input() acceptedFiles = '.png,.svg,.jpg,.jpeg';

  @Input() buttonText = 'Upload';

  parsedButtonText: [string, string] = ['GALAXY.UPLOADER.CHOOSE_IMAGE', 'GALAXY.UPLOADER.CHOOSE_IMAGE_PL'];
  /** Optional background theme to preview the image in */
  readonly previewTheme = input<'dark' | 'light'>();
  readonly previewSize = input<'xs' | 'sm' | 'lg'>('lg');
  readonly addPadding = input<boolean>(false);

  /** notifies the parent component that the image resource has changed to it can react to it  */
  @Output() imageChanged: EventEmitter<string> = new EventEmitter<string>();

  @Output() loaderState: EventEmitter<boolean> = new EventEmitter<boolean>();

  maxDimensions = { width: 5000, height: 5000 };
  croppingFile: FileInfo;

  subscriptions: Subscription[] = [];

  ngOnInit(): void {
    this.subscriptions.push(
      this.uploadService.fileErrored$$.pipe(filter((error) => !!error)).subscribe({
        next: (error: FileUploadError) => this.onError(error),
      }),
    );

    this.subscriptions.push(
      this.uploadedUrlsService.getUploadedUrls$().subscribe({
        next: (urls: string[]) => {
          this.onUpload(urls);
        },
      }),
    );
    this.parsedButtonText = [this.buttonText, 'GALAXY.UPLOADER.CHOOSE_IMAGE_PL'];
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  onError(fileError: FileUploadError): void {
    this.snackService.errorSnack(fileError.error.message);
  }

  onUpload(urls: string[]): void {
    urls.forEach((url) => {
      this.imageChanged.emit(url);
    });
    this.uploadService.clear();
    this.croppingFile = null;
  }

  onFilesChanged(files: FileInfo[]): void {
    if (files[0]) {
      this.croppingFile = files[0];
      this.loaderState.emit(true);
      this.uploadService.uploadQueuedFiles();
    }
  }

  convertButtonText(inputText: string): [string, string] {
    return [inputText, 'GALAXY.UPLOADER.CHOOSE_IMAGE_PL'];
  }
}
