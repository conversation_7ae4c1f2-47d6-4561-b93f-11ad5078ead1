import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import {
  MediaApiService as AccountGroupMediaApiService,
  AssociateImageRequest,
  DeleteImageRequest,
  ImageType,
  ListImagesRequest,
} from '@vendasta/account-group-media';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { MediaApiService } from '@vendasta/media';
import { BehaviorSubject, EMPTY as empty, firstValueFrom, Observable } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';
import { ClientImage, MenuOption } from './media-tab.interface';

@Injectable()
export class MediaTabService {
  private reloadSubject = new BehaviorSubject<string>(null);
  reload$ = this.reloadSubject.asObservable();
  private accountGroupId: string;
  hasMore = new BehaviorSubject<boolean>(false);
  totalImages$$ = new BehaviorSubject<number>(0);
  loading$$ = new BehaviorSubject<boolean>(false);
  pageFetchError$$ = new BehaviorSubject<boolean>(false);
  imagesSubject$$ = new BehaviorSubject<ClientImage[]>([]);
  private imageCursor: string;

  constructor(
    private accountGroupMediaApiService: AccountGroupMediaApiService,
    public mediaApiService: MediaApiService,
    private snackbarService: SnackbarService,
  ) {}

  setAccountGroupId(accountGroupId: string): void {
    this.clearConfig();
    this.accountGroupId = accountGroupId;
    this.addClientImages();
  }

  clearConfig(): void {
    this.accountGroupId = null;
    this.hasMore.next(false);
    this.totalImages$$.next(0);
    this.loading$$.next(false);
    this.pageFetchError$$.next(false);
    this.imageCursor = null;
    this.imagesSubject$$.next([]);
  }

  private addImageToSubject(image: ClientImage): void {
    const curImages = this.imagesSubject$$.getValue();

    if (!curImages.find((x) => x.url === image.url)) {
      if (image.type) {
        curImages.unshift(image);
        this.imagesSubject$$.next(curImages);
      } else {
        const index = curImages.findIndex((x) => x.date < image.date && x.type === image.type);
        if (index !== -1) {
          curImages.splice(index, 0, image);
          this.imagesSubject$$.next(curImages);
        } else {
          this.imagesSubject$$.next(curImages.concat(image));
        }
      }
    }
  }

  private removeImageUrlFromSubject(imageUrl: string, imageType: string): void {
    const curImages = this.imagesSubject$$.getValue();
    const index = curImages.findIndex((x) => x.url === imageUrl);
    if (index > -1) {
      curImages.splice(index, 1);
      this.imagesSubject$$.next(curImages);
      let total: number = this.totalImages$$.getValue();
      total--;
      this.totalImages$$.next(total);
    }
    this.reloadSubject.next(imageType.trim());
  }

  private removeImageTypeFromSubject(imageType: ImageType): boolean {
    const curImages = this.imagesSubject$$.getValue();
    const index = curImages.findIndex((x) => x.type === this.imageTypeToString(imageType));

    if (index > -1) {
      curImages.splice(index, 1);
      this.imagesSubject$$.next(curImages);
      return true;
    }
    return false;
  }

  createAndAssociate(servingUrl: string, imageType?: ImageType): Observable<string> {
    return this.mediaApiService.create(servingUrl).pipe(
      switchMap((createResponse) => {
        const associateRequest = new AssociateImageRequest({
          accountGroupId: this.accountGroupId,
          imageId: createResponse.imageId,
          imageType: imageType,
        });
        return this.accountGroupMediaApiService
          .associateImage(associateRequest)
          .pipe(map(() => createResponse.imageId));
      }),
    );
  }

  addClientImages(): void {
    this.loading$$.next(true);
    const request = new ListImagesRequest({
      accountGroupId: this.accountGroupId,
      cursor: this.imageCursor,
      pageSize: 8,
    });
    firstValueFrom(this.accountGroupMediaApiService.listImages(request))
      .then((result) => {
        const imageAssociations = result.images;
        if (!imageAssociations || imageAssociations.length === 0) {
          this.reloadSubject.next('addClientImages');
        } else {
          this.imageCursor = result.nextCursor;
          if (this.totalImages$$.getValue() < result.totalResults) {
            this.totalImages$$.next(result.totalResults);
          }
          this.hasMore.next(result.hasMore);
          imageAssociations.forEach((element) => {
            const url = this.mediaApiService.get(element.imageId);
            const type = this.imageTypeToString(element.imageType);
            this.addImageToSubject({
              url: url,
              date: element.created,
              type: type,
            });
          });
          this.pageFetchError$$.next(false);
        }
        this.loading$$.next(false);
      })
      .catch((err) => {
        console.error(err);
        this.pageFetchError$$.next(true);
        this.loading$$.next(false);
      })
      .finally(() => {
        this.loading$$.next(false);
      });
  }

  deleteImage(imageUrl: string, imageType: string, dialogRef: MatDialogRef<any>): void {
    firstValueFrom(
      dialogRef.afterClosed().pipe(
        switchMap((result) => {
          if (result) {
            const imageId = imageUrl.split('/').pop();
            const deleteImageRequest = new DeleteImageRequest({
              accountGroupId: this.accountGroupId,
              imageId: imageId,
            });
            return this.accountGroupMediaApiService.deleteImage(deleteImageRequest).pipe(
              tap(
                () => {
                  this.removeImageUrlFromSubject(imageUrl, imageType);
                  this.snackbarService.openSuccessSnack('FRONTEND.BUSINESSES.MEDIA_TAB.PHOTO_DELETED');
                },
                () => {
                  this.snackbarService.openErrorSnack('FRONTEND.BUSINESSES.MEDIA_TAB.ERROR_DELETING_PHOTO');
                },
              ),
            );
          }
          return empty;
        }),
      ),
    );
  }

  public imageTypeToString(type: number): string {
    if (type === ImageType.LOGO) {
      return 'Logo';
    }
    if (type === ImageType.PRIMARY) {
      return 'Primary';
    }
    if (type === ImageType.NOT_SPECIFIED) {
      return undefined;
    }
  }

  markImageAsPerType(imageUrl: string, type: MenuOption): void {
    const imageType = this.convertMenuOptionToImageType(type);

    this.createAndAssociate(imageUrl, imageType)
      .pipe(
        tap({
          next: (imageId) => {
            const clientType = {
              url: this.mediaApiService.get(imageId),
              date: new Date(),
              type: this.imageTypeToString(imageType),
            };
            if (MenuOption.Gallery == type) {
              let total: number = this.totalImages$$.getValue();
              total++;
              this.totalImages$$.next(total);
            } else {
              this.removeImageTypeFromSubject(imageType);
            }
            this.addImageToSubject(clientType);
            this.snackbarService.openSuccessSnack('FRONTEND.BUSINESSES.MEDIA_TAB.' + this.getSuccessMessage(imageType));
          },
          error: () => {
            this.snackbarService.openErrorSnack('FRONTEND.BUSINESSES.MEDIA_TAB.ERROR_SETTING_PHOTO');
          },
        }),
      )
      .subscribe(); // Add subscribe to ensure API call is made
  }

  convertMenuOptionToImageType(menuOption: MenuOption): ImageType {
    switch (menuOption) {
      case MenuOption.Logo:
        return ImageType.LOGO;
      case MenuOption.Primary:
        return ImageType.PRIMARY;
      default:
        return ImageType.NOT_SPECIFIED; // Default to NOT_SPECIFIED or handle other cases
    }
  }

  getSuccessMessage(imageType?: ImageType): string {
    switch (imageType) {
      case ImageType.LOGO:
        return 'MAIN_LOGO_SET_SUCCESSFULLY';
      case ImageType.PRIMARY:
        return 'COVER_PHOTO_SET_SUCCESSFULLY';
      default:
        return 'PHOTO_UPLOADED_SUCCESSFULLY'; // Default to NOT_SPECIFIED or handle other cases
    }
  }
}
