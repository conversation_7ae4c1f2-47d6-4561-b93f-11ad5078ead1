@use 'design-tokens' as *;

.title-with-button {
  display: flex;
  align-items: center; /* Ensures vertical alignment */
  font-weight: 400;
}

.spacer {
  flex: 1 1 auto; /* This pushes the button to the right */
}

.mat-mdc-card {
  margin: 10px 10px 20px 10px;
}

.card-header {
  justify-content: space-between;
}

.result-count {
  margin-top: 1em;
  text-align: right;
  color: rgba(134, 133, 134, 0.575);
}

.load-more-button {
  margin-top: 0.5em;
  margin-bottom: -1em;
  text-align: center;
}

.material-icons.upload {
  padding-top: 1em;
  font-size: 4em;
}

.no-images-section {
  width: 100%;
  height: 100%;
  color: gray;
  label {
    font-size: 1.8em;
  }
  div {
    padding: 0.75em;
  }
}

.image-gallery-card {
  text-align: center;
  width: 100%;
  position: relative;
}

.progress-bar {
  margin-top: 0.5em;
}

.connection-error {
  color: red;
  font-style: italic;
  font-size: 1.5em;
  margin: 1em;
}

.add-button {
  position: absolute;
  right: $spacing-2;
  top: $spacing-2;
}

:host {
  display: block;
}

button {
  vertical-align: middle;
}

mat-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Ensure space is distributed evenly */
  height: 100%; /* Make all cards the same height */
}

.spacer {
  flex-grow: 1;
}

.row-gutters {
  margin-bottom: $spacing-5; /* Adjust if you want the row to have bottom spacing */
}

mat-card-header {
  height: $spacing-5 + $spacing-4; /* Set a fixed height for the header */
  display: flex;
  align-items: center; /* Vertically center content */
  justify-content: space-between; /* Ensure content is spread out */
  overflow: hidden; /* Handle any overflow content gracefully */
}

.business-media-uploader-container {
  margin-top: 1em; /* Add some spacing above the uploader */
  margin-bottom: $spacing-3; /* Adjust this value as needed for spacing */
}

.loaded-profile-image {
  max-width: 100%; /* Adjust this percentage to control the image size */
  max-height: 150px; /* Set a maximum height for the image */
  margin: $spacing-5; /* Centers the image horizontally */
  display: block; /* Ensures the image respects the margin */
  object-fit: contain; /* Ensures the image maintains its aspect ratio */
  align-self: center; /* Center the image vertically if necessary */
}

.empty-default-state-image {
  max-width: 80%; /* Adjust this percentage to control the image size */
  max-height: 150px; /* Set a maximum height for the image */
  margin: 0 auto; /* Centers the image horizontally */
  display: block; /* Ensures the image respects the margin */
  object-fit: contain; /* Ensures the image maintains its aspect ratio */
  align-self: center; /* Center the image vertically if necessary */
}

.v-spacer {
  height: 30px; /* Adjust this value to increase or decrease the space */
}

.tab-label-title-tooltip {
  color: $grey;
  font-size: $spacing-3;
  height: $spacing-1 + $spacing-3;
  width: $spacing-2 + $spacing-3;
  margin-left: $spacing-1;
}

.card-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-section {
  text-align: left; /* Ensures the text is left-aligned */
}

.center-section {
  text-align: center;
}

.right-section {
  margin-left: auto; /* Keeps the button on the right */
}

.gallery-title {
  font-weight: 400;
  line-height: 22px;
}
.grey-text {
  color: $glxy-grey-700;
  font-weight: 400;
  font-size: $spacing-3;
}
