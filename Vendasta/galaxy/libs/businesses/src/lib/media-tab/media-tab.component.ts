import { ChangeDetectorRef, Component, effect, input, output, OnInit, OnDestroy } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { combineLatest, firstValueFrom, Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { LightboxData } from '../business-profile.interface';
import { ImageMenuClick } from './media-tab-gallery/image-menu-click';
import { ClientImage, MenuOption } from './media-tab.interface';
import { MediaTabService } from './media-tab.service';
import { ImageUpdateDialogComponent } from './image-update-dialog/image-update-dialog.component';

@Component({
  selector: 'business-media-tab',
  templateUrl: './media-tab.component.html',
  styleUrls: ['./media-tab.component.scss'],
  standalone: false,
})
export class MediaTabComponent implements OnInit, OnDestroy {
  accountGroupId = input.required<string>();
  editDisabled = input<boolean>(false);
  imageClicked = output<LightboxData>();
  loader = false;
  protected readonly images$: Observable<ClientImage[]>;
  private reloadSubscription: Subscription;

  profileImageData = [
    {
      type: MenuOption.Logo,
      title: 'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.MAIN_LOGO_TITLE',
      hoverText: 'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.MAIN_LOGO_HOVER_TEXT',
      buttonText: 'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.MAIN_LOGO_BUTTON_TEXT',
      imageType: 'Logo',
      imageLink: '',
      ratio: 1,
      emptyImageLink: this.getImageSrc('main_logo_empty_state_image.svg'),
    },
    {
      type: MenuOption.Primary,
      title: 'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.PRIMARY_TITLE',
      hoverText: 'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.PRIMARY_HOVER_TEXT',
      buttonText: 'FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.PRIMARY_BUTTON_TEXT',
      imageType: 'Primary',
      imageLink: '',
      ratio: 16 / 9,
      emptyImageLink: this.getImageSrc('cover_photo_empty_state_image.svg'),
    },
  ];

  galleryDefaultImageLink = this.getImageSrc('Gallery_empty_state_image.svg');
  loadingStates: { [title: string]: boolean } = {};

  constructor(
    public dialog: MatDialog,
    public tabService: MediaTabService,
    private cdr: ChangeDetectorRef,
  ) {
    this.images$ = this.tabService.imagesSubject$$.pipe(
      map((images: ClientImage[]) => {
        const unmatchedImages: ClientImage[] = [];

        if (images.length > 0) {
          // Clear previous image links
          this.profileImageData.forEach((profileImage) => {
            profileImage.imageLink = '';
          });

          // Iterate over profileImageData and update imageLink if matching image is found
          this.profileImageData.forEach((profileImage) => {
            const matchingImage = images.find((image) => {
              return image.type === profileImage.imageType;
            });

            if (matchingImage) {
              profileImage.imageLink = matchingImage.url;
            }
          });

          // Add images that do not match any profileImageData to unmatchedImages
          images.forEach((image) => {
            const isUnmatched = !this.profileImageData.some((profileImage) => profileImage.imageType === image.type);

            if (isUnmatched) {
              unmatchedImages.push(image);
            }
          });
        }
        this.tabService.totalImages$$.next(unmatchedImages.length);
        return unmatchedImages; // Return only unmatched images
      }),
    );

    this.images$.subscribe(() => {
      this.setLoader(false, '');
    });

    effect(() => {
      this.tabService.setAccountGroupId(this.accountGroupId());
    });
  }

  setLoader(isLoading: boolean, title: string): void {
    this.loadingStates[title] = isLoading;
  }

  getLoader(title: string): boolean {
    return this.loadingStates[title] || false;
  }

  ngOnInit(): void {
    // Ensure loader is hidden during component refresh or reload
    this.loadingStates = {};

    this.reloadSubscription = this.tabService.reload$.subscribe((imageType) => {
      if (imageType === 'addClientImages') {
        this.reloadComponentForAddImage();
      } else {
        this.reloadComponent(imageType);
      }
    });
  }

  reloadComponent(imageType: string): void {
    this.profileImageData.forEach((profileImage) => {
      if (profileImage.imageType === imageType) {
        profileImage.imageLink = profileImage.emptyImageLink;
      }
    });
    this.cdr.detectChanges();
  }
  reloadComponentForAddImage(): void {
    this.profileImageData.forEach((profileImage) => {
      profileImage.imageLink = profileImage.emptyImageLink;
    });

    this.cdr.detectChanges();
  }
  ngOnDestroy(): void {
    if (this.reloadSubscription) {
      this.reloadSubscription.unsubscribe();
    }
  }
  public get galleryInfo$(): Observable<any> {
    return combineLatest([
      this.tabService.totalImages$$,
      this.tabService.hasMore,
      this.tabService.loading$$,
      this.tabService.pageFetchError$$,
    ]).pipe(
      map(([totalImages, hasMore, loading, pageFetchError]) => ({ totalImages, hasMore, loading, pageFetchError })),
    );
  }

  loadMore(): void {
    this.tabService.addClientImages();
  }

  async openLightbox(clickedImage: ClientImage) {
    const imageUrls: string[] = await firstValueFrom(
      this.images$.pipe(map((images) => images.map((image) => image.url))),
    );
    this.imageClicked.emit({ imageUrls: imageUrls, index: imageUrls.indexOf(clickedImage.url) });
  }

  async imageMenuClicked(clickedOption: ImageMenuClick): Promise<void> {
    const getMenuKey = (key) => `FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.${key}`;
    const dialogRef = this.dialog.open(ConfirmationModalComponent);
    dialogRef.componentInstance.title = getMenuKey('DELETE_PHOTO_QUESTION');
    dialogRef.componentInstance.body = getMenuKey('DELETE_PHOTO_BODY');
    dialogRef.componentInstance.confirmButton = getMenuKey('DELETE_PHOTO');
    this.tabService.deleteImage(clickedOption.image.url, MenuOption.Gallery, dialogRef);
  }

  async deleteMenuClicked(imageUrl: string, imageType: string): Promise<void> {
    const getMenuKey = (key) => `FRONTEND.BUSINESSES.MEDIA_TAB.IMAGE_MENU.${key}`;

    const dialogRef = this.dialog.open(ConfirmationModalComponent);
    dialogRef.componentInstance.title = getMenuKey('DELETE_PHOTO_QUESTION');
    dialogRef.componentInstance.body = getMenuKey('DELETE_PHOTO_BODY');
    dialogRef.componentInstance.confirmButton = getMenuKey('DELETE_PHOTO');

    await this.tabService.deleteImage(imageUrl, imageType, dialogRef);
  }

  onImageChanged(imageUrl: string, type: MenuOption): void {
    this.tabService.markImageAsPerType(imageUrl, type);
    this.setLoader(false, 'gallery');
  }

  openImageUpdateDialog(imageData: any): void {
    const dialogRef = this.dialog.open(ImageUpdateDialogComponent, {
      width: '400px',
      data: { buttonText: imageData.buttonText, type: imageData.type, ratio: imageData.ratio },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Handle image update logic after confirmation
        this.tabService.markImageAsPerType(result, imageData.type);
      }
    });
  }
  getImageSrc(imageName: string): string {
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      return `assets/businesses/media-tab-images/${imageName}`;
    }
    return `https://vstatic-prod.apigateway.co/business-center-client/assets/businesses/media-tab-images/${imageName}`;
  }

  protected readonly MenuOption = MenuOption;
}

@Component({
  selector: 'business-confirmation-modal',
  template: `
    <mat-dialog-content class="mat-dialog-content">
      <h2 mat-dialog-title class="mat-dialog-title">{{ title | translate }}</h2>
      <p>{{ body | translate }}</p>
    </mat-dialog-content>
    <mat-dialog-actions class="mat-dialog-actions">
      <button mat-button color="primary" (click)="onCancel()" type="button" mat-dialog-close>
        {{ 'FRONTEND.BUSINESSES.CANCEL' | translate }}
      </button>
      <button mat-raised-button color="primary" (click)="onConfirm()" type="button" mat-dialog-close>
        {{ confirmButton | translate }}
      </button>
    </mat-dialog-actions>
  `,
  standalone: false,
})
export class ConfirmationModalComponent {
  // Translation key for modal title
  public title: string;
  // Translation key for modal body
  public body: string;
  // Translation key for modal confirm button
  public confirmButton: string;
  constructor(private dialogRef: MatDialogRef<ConfirmationModalComponent>) {}

  onConfirm(): void {
    this.dialogRef.close(true);
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
