<mat-grid-list cols="{{ gridColumns }}" gutterSize="0.8em">
  @for (image of images; track image) {
    <mat-grid-tile class="img-tile">
      @if (image.type) {
        <mat-grid-tile-header class="mat-grid-tile-overlay img-overlay">
          <mat-chip-listbox>
            <mat-chip-option color="primary" selected="true" class="img-type-chip">
              {{ image.type | uppercase | translate }}
            </mat-chip-option>
          </mat-chip-listbox>
        </mat-grid-tile-header>
      }
      <mat-grid-tile-footer class="mat-grid-tile-overlay img-overlay">
        <span class="img-upload-date">{{ image.date | date }}</span>
        <va-action-menu
          class="img-secondary-button"
          [icon]="'more_vert'"
          [color]="'rgba(255,255,255,0.87)'"
          [actions]="[MenuOption.Delete]"
          (actionSelected)="menuOptionClicked(image, $event)"
        ></va-action-menu>
      </mat-grid-tile-footer>
      <img [src]="image.url" (click)="onClick(image)" [srcset]="getSrcsetImageUrls(image.url)" />
      <div class="gradient-overlay"></div>
    </mat-grid-tile>
  }
</mat-grid-list>
