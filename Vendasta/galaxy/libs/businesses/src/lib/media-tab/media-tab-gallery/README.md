# MediaTabGallery
Takes in an array of images and creates an image gallery formatted as a grid. Each image is a tile that 
has a date. Clicking on any image will emit the current ClientImage, triggering the business-profile component 
to pop up a va-lightbox for a very pleasing viewing experience.

## Implementation

### Properties:

    [images]: The images that will be displayed, must implement the ClientImage interface.
    (OnImageSelected): The eventEmitter for when a grid tile is clicked.

### Example:

    <media-tab-gallery
      [images]="images: ClientImages[]"
      (onImageSelected)="emit(imageSelectionEvent)"
    ></media-tab-gallery>
