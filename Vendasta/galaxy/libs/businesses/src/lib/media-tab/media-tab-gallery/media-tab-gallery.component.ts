import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ImageTransformation, ImageTransformationService } from '@vendasta/image-transformation';
import { Subscription, fromEvent as observableFromEvent } from 'rxjs';

import { ClientImage, MenuOption } from '../media-tab.interface';
import { ImageMenuClick } from './image-menu-click';

@Component({
  styleUrls: ['./media-tab-gallery.component.scss'],
  selector: 'business-media-tab-gallery',
  templateUrl: './media-tab-gallery.component.html',
  standalone: false,
})
export class MediaTabGalleryComponent implements OnInit, OnDestroy {
  @Input() images: ClientImage[];
  @Output() imageClicked = new EventEmitter<ClientImage>();
  @Input() menuOptions: string[];
  @Output() menuOptionSelected = new EventEmitter<ImageMenuClick>();
  mobileColumns = 2;
  desktopColumns = 4;
  gridColumns = 4;
  private subscriptions: Subscription[] = [];

  constructor(private imageTransformationService: ImageTransformationService) {}

  ngOnInit(): void {
    this.updateColumnTotal();
    this.subscriptions.push(
      observableFromEvent(window, 'resize').subscribe(() => {
        this.updateColumnTotal();
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => {
      sub.unsubscribe();
    });
  }

  onClick(image: ClientImage): void {
    this.imageClicked.emit(image);
  }

  menuOptionClicked(image: ClientImage, option: MenuOption): void {
    const menuClick: ImageMenuClick = {
      image: image,
      option: option,
    };
    this.menuOptionSelected.emit(menuClick);
  }

  getSrcsetImageUrls(imageUrl: string): string {
    const transformation = new ImageTransformation().crop().renderAsJpeg().setJpegQuality(80);
    return this.imageTransformationService.getSrcSetForImage(imageUrl, [256, 320, 512], transformation);
  }

  updateColumnTotal(): void {
    this.gridColumns = window.innerWidth < 800 ? this.mobileColumns : this.desktopColumns;
  }

  protected readonly MenuOption = MenuOption;
}
