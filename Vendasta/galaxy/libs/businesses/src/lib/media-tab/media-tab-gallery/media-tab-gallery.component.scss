.img-upload-date {
  position: absolute;
}

.mat-grid-tile.img-tile {
  cursor: pointer;
  img {
    display: block;
    max-width: unset;
    max-height: 100%;
    min-height: 100%;
  }
}

.mat-grid-tile-overlay.img-overlay {
  z-index: 100;
  pointer-events: none;
  background-color: transparent;
}

.gradient-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60%;
  width: 100%;
  pointer-events: none;
  background: linear-gradient(180deg, transparent, #202020);
  // TODO: change this to a non-linear material scrim gradient
}

.img-secondary-button {
  position: absolute;
  right: 0;
  pointer-events: auto;
}

mat-grid-tile:hover {
  box-shadow: 0.1em 0.1em 0.25em rgba(47, 62, 109, 0.54);
}

.img-type-chip {
  position: absolute;
  right: 0.5em;
  top: 0.5em;
}
