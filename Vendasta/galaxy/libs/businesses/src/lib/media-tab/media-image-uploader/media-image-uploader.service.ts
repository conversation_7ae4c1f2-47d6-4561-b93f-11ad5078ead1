import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { FileInfo, GalaxyImageUploaderService, UploadResponse } from '@vendasta/galaxy/uploader';
import { Observable } from 'rxjs';

@Injectable()
export class MediaImageUploaderService extends GalaxyImageUploaderService {
  constructor(
    private httpClient: HttpClient,
    private environmentService: EnvironmentService,
  ) {
    super(httpClient);
  }

  public getURL(): string {
    switch (this.environmentService.getEnvironment()) {
      case Environment.DEMO: {
        return 'https://media-demo.apigateway.co/image/upload';
      }
      case Environment.PROD: {
        return 'https://media-prod.apigateway.co/image/upload';
      }
      default: {
        return 'https://media-demo.apigateway.co/image/upload';
      }
    }
  }

  buildRequest(fileInfo: FileInfo): Observable<UploadResponse> {
    const imageToUpload = fileInfo?.data?.croppedImage || fileInfo.file;
    const formData = new FormData();

    formData.append('image', imageToUpload);
    return this.httpClient.post<UploadResponse>(this.getURL(), formData, { withCredentials: true });
  }
}
