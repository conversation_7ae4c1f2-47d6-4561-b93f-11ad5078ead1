import { Component, EventEmitter, Inject, input, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  FileInfo,
  FileUploadError,
  GalaxyImageUploaderService,
  GALAXY_UPLOADER_SERVICE_TOKEN,
} from '@vendasta/galaxy/uploader';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { MediaImageUploaderService } from './media-image-uploader.service';
import { MatDialog } from '@angular/material/dialog';

import { ImageCropperDialogComponent } from '../image-cropper-dialog/image-cropper-dialog.component'; // import the dialog component

@Component({
  selector: 'business-media-image-uploader',
  templateUrl: './media-image-uploader.component.html',
  styleUrls: ['./media-image-uploader.component.scss'],
  providers: [{ provide: GALAXY_UPLOADER_SERVICE_TOKEN, useClass: MediaImageUploaderService }],
  standalone: false,
})
export class MediaImageUploaderComponent implements OnInit, OnDestroy {
  constructor(
    @Inject(GALAXY_UPLOADER_SERVICE_TOKEN) private uploadService: GalaxyImageUploaderService,
    private snackService: SnackbarService,
    private dialog: MatDialog,
  ) {}

  /** a url of the image that is currently saved */
  @Input() imageUrl: string;
  /** list of accepted image file types */
  @Input() acceptedFiles = '.png,.svg,.jpg,.jpeg';
  /** disable the editing of the image. View only. */
  @Input() disabled = false;
  /** Text to display in the hint section of the image uploader */
  @Input() hintText = '';
  /** Optional: allowed aspect ratios in image uploader */
  @Input() aspectRatio = 1;

  @Input() buttonText = 'Upload';
  parsedButtonText: [string, string] = ['GALAXY.UPLOADER.CHOOSE_IMAGE', 'GALAXY.UPLOADER.CHOOSE_IMAGE_PL'];
  /** Optional background theme to preview the image in */
  readonly previewTheme = input<'dark' | 'light'>();
  readonly previewSize = input<'xs' | 'sm' | 'lg'>('lg');
  readonly addPadding = input<boolean>(false);

  /** notifies the parent component that the image resource has changed to it can react to it  */
  @Output() imageChanged: EventEmitter<string> = new EventEmitter<string>();

  @Output() loaderState: EventEmitter<boolean> = new EventEmitter<boolean>();

  maxDimensions = { width: 5000, height: 5000 };
  minDimensions = { width: 250, height: 250 };

  croppingFile: FileInfo;
  isEditing = false;

  subscriptions: Subscription[] = [];

  ngOnInit(): void {
    this.subscriptions.push(
      this.uploadService.fileErrored$$.pipe(filter((error) => !!error)).subscribe({
        next: (error: FileUploadError) => this.onError(error),
      }),
    );

    this.subscriptions.push(
      this.uploadService.fileUploaded$.pipe(filter((fileInfo) => !!fileInfo)).subscribe({
        next: (fileInfo: FileInfo) => this.onUpload(fileInfo),
      }),
    );
    this.parsedButtonText = [this.buttonText, 'GALAXY.UPLOADER.CHOOSE_IMAGE_PL'];
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  assignImageEditing(isEditing: boolean): void {
    this.isEditing = isEditing;
  }

  onError(fileError: FileUploadError): void {
    this.snackService.errorSnack(fileError.error.message);
  }

  onUpload(file: FileInfo): void {
    this.imageChanged.emit(file.url);
    this.uploadService.clear();
    this.croppingFile = null;
    this.assignImageEditing(false);
  }

  endEdit(blob?: Blob): void {
    if (blob) {
      this.uploadService.setCroppedImage(this.croppingFile, blob);
      this.uploadService.uploadQueuedFiles();
    } else {
      this.uploadService.clear();
      this.croppingFile = null;
    }
  }

  onFilesChanged(files: FileInfo[]): void {
    if (files[0]) {
      this.croppingFile = files[0];
      this.openEditorDialog();
    }
  }

  convertButtonText(inputText: string): [string, string] {
    return [inputText, 'GALAXY.UPLOADER.CHOOSE_IMAGE_PL'];
  }

  openEditorDialog(): void {
    const dialogRef = this.dialog.open(ImageCropperDialogComponent, {
      width: '700px',
      minWidth: '60%',
      maxWidth: '100%',
      minHeight: '500px',
      data: {
        croppingFile: this.croppingFile,
        aspectRatio: this.aspectRatio,
        maxDimensions: this.maxDimensions,
        minDimensions: this.minDimensions,
      },
    });

    dialogRef.afterClosed().subscribe((blob) => {
      if (blob) {
        this.endEdit(blob); // handle cropped image
        this.loaderState.emit(true);
      } else {
        this.croppingFile = null; // clear if no cropping was done
      }
    });
  }
}
