import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TranslateModule } from '@ngx-translate/core';
import { VaFormsModule } from '@vendasta/forms';
import { ImageCropperDialogComponent } from './image-cropper-dialog.component';
import { GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { <PERSON><PERSON><PERSON>, Mat<PERSON>ardHeader, MatCardTitle } from '@angular/material/card';

@NgModule({
  imports: [
    CommonModule,
    VaFormsModule,
    MatIconModule,
    MatButtonModule,
    MatProgressBarModule,
    ReactiveFormsModule,
    MatDialogModule,
    TranslateModule,
    GalaxyUploaderModule,
    Mat<PERSON>ard,
    MatCardHeader,
    MatCardTitle,
  ],
  declarations: [ImageCropperDialogComponent],
  providers: [],
  exports: [ImageCropperDialogComponent],
})
export class ImageCropperDialogModule {}
