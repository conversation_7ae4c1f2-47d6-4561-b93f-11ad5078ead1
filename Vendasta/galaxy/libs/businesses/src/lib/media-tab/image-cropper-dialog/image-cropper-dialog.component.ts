import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'business-image-cropper-dialog',
  templateUrl: './image-cropper-dialog.component.html',
  styleUrls: ['./image-cropper-dialog.component.scss'],
  standalone: false,
})
export class ImageCropperDialogComponent implements OnInit {
  maxEditorHeight: string;
  aspectRatioLabel: number;

  constructor(
    public dialogRef: MatDialogRef<ImageCropperDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {}

  ngOnInit(): void {
    this.maxEditorHeight = `${window.innerHeight / 2}px`;
    this.aspectRatioLabel = this.data.aspectRatio;
  }

  closeDialog(blob?: Blob): void {
    this.dialogRef.close(blob); // Pass the cropped image blob back to the parent
  }
}
