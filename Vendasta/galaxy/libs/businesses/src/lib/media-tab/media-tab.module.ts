import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TranslateModule } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { MediaTabGalleryModule } from './media-tab-gallery/media-tab-gallery.module';
import { ConfirmationModalComponent, MediaTabComponent } from './media-tab.component';
import { MediaTabService } from './media-tab.service';
import { UploadImageDialogModule } from './upload-image-dialog/upload-image-dialog.module';
import { MediaImageUploaderComponent } from './media-image-uploader/media-image-uploader.component';
import { MatGridTile } from '@angular/material/grid-list';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { GalaxyListItemIconDirective } from 'list';
import { GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';
import { ImageCropperDialogModule } from './image-cropper-dialog/image-cropper-dialog.module';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { ImageUpdateDialogComponent } from './image-update-dialog/image-update-dialog.component';
import { GalleryMultiImageUploaderComponent } from './gallery-multi-image-uploader/gallery-multi-image-uploader.component';
@NgModule({
  declarations: [
    MediaTabComponent,
    ConfirmationModalComponent,
    MediaImageUploaderComponent,
    ImageUpdateDialogComponent,
    GalleryMultiImageUploaderComponent,
  ],
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MediaTabGalleryModule,
    MatProgressBarModule,
    UploadImageDialogModule,
    MediaTabGalleryModule,
    MatIconModule,
    MatDialogModule,
    TranslateModule,
    MatGridTile,
    GalaxyTooltipModule,
    GalaxyListItemIconDirective,
    GalaxyUploaderModule,
    MatMenuTrigger,
    MatMenu,
    MatMenuItem,
    ImageCropperDialogModule,
    GalaxyLoadingSpinnerModule,
  ],
  providers: [MediaTabService, SnackbarService],
  exports: [MediaTabComponent, MediaImageUploaderComponent, GalleryMultiImageUploaderComponent],
})
export class MediaTabModule {}
