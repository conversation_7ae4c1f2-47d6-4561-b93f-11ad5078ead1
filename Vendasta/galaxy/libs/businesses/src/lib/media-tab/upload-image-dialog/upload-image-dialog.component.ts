import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Subscription } from 'rxjs';
import { ClientImage } from '../media-tab.interface';
import { MediaTabService } from '../media-tab.service';

@Component({
  selector: 'business-upload-image-dialog',
  templateUrl: './upload-image-dialog.component.html',
  styleUrls: ['./upload-image-dialog.component.scss'],
  standalone: false,
})
export class UploadImageDialogComponent implements OnDestroy {
  imageUrlControl = new UntypedFormControl();
  private subscriptions: Subscription[] = [];
  maxFileSizeBytes = 5242880; // MiB
  imageService: MediaTabService;
  uploading = false;

  constructor(
    public dialogRef: MatDialogRef<UploadImageDialogComponent>,
    private alertService: SnackbarService,
  ) {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => {
      sub.unsubscribe();
    });
  }

  uploadPhotos(): void {
    this.uploading = true;
    const imageUrl = this.imageUrlControl.value.url;
    this.subscriptions.push(
      this.imageService.createAndAssociate(imageUrl).subscribe(
        (imageId) => {
          this.onSuccess(imageId);
        },
        (err) => {
          this.uploadError(err);
        },
      ),
    );
  }

  private onSuccess(imageId: string): void {
    this.alertService.openSuccessSnack('FRONTEND.BUSINESSES.MEDIA_TAB.PHOTO_UPLOADED_SUCCESSFULLY');
    const image: ClientImage = { url: this.imageService.mediaApiService.get(imageId), date: new Date() };
    this.dialogRef.close(image);
  }

  uploadError(error: Error): void {
    console.error(error.message);
    this.alertService.openErrorSnack(error.message);
    this.dialogRef.close();
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
