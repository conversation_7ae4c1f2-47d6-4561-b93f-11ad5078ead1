<h1 mat-dialog-title>
  {{ 'FRONTEND.BUSINESSES.MEDIA_TAB.UPLOAD_PHOTO' | translate }}
</h1>

<forms-image-uploader
  mat-dialog-content
  class="uploader"
  [customUploader]="imageService.mediaApiService"
  [maxFileSize]="maxFileSizeBytes"
  [imageNoun]="'FRONTEND.BUSINESSES.MEDIA_TAB.PHOTO_IMAGE_NOUN' | translate"
  [formControl]="imageUrlControl"
  (imageUploadError)="uploadError($event)"
></forms-image-uploader>

@if (uploading) {
  <mat-progress-bar class="progress-bar" mode="indeterminate"></mat-progress-bar>
}
<mat-dialog-actions>
  <button mat-button (click)="onCancel()" color="primary">
    {{ 'FRONTEND.BUSINESSES.CANCEL' | translate }}
  </button>
  @if (imageUrlControl.value !== null) {
    <button mat-raised-button [disabled]="uploading" color="primary" type="button" (click)="uploadPhotos()">
      {{ 'FRONTEND.BUSINESSES.SAVE' | translate }}
    </button>
  }
</mat-dialog-actions>
