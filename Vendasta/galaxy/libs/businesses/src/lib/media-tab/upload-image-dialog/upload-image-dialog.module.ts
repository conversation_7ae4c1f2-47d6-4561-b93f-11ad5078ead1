import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TranslateModule } from '@ngx-translate/core';
import { VaFormsModule } from '@vendasta/forms';
import { UploadImageDialogComponent } from './upload-image-dialog.component';

@NgModule({
  imports: [
    CommonModule,
    VaFormsModule,
    MatIconModule,
    MatButtonModule,
    MatProgressBarModule,
    ReactiveFormsModule,
    MatDialogModule,
    TranslateModule,
  ],
  declarations: [UploadImageDialogComponent],
  providers: [],
  exports: [UploadImageDialogComponent],
})
export class UploadImageDialogModule {}
