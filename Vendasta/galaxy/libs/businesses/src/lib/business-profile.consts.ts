import { UpdateOperations } from '@galaxy/listing-profile-common';
import { ClosedStatus, IsAvailable, RichDataPaymentMethods } from '@vendasta/listing-products';

export interface FormUpdateParameters {
  operations: UpdateOperations;
  onlyHoursSave?: boolean;
  onlyBusinessHoursSave?: boolean;
}

export interface AvailabilityOption {
  code: number;
  name: string;
}

export interface GenderOption {
  code: number;
  name: string;
}

export interface IsProviderOption {
  code: number;
  name: string;
}

export enum GenderKeys {
  SELECT_OPTION = 0,
  FEMALE = 1,
  MALE = 2,
  OTHER = 3,
}

export const GenderSelection = [GenderKeys.SELECT_OPTION, GenderKeys.FEMALE, GenderKeys.MALE, GenderKeys.OTHER];

export const CountryPaymentMethods = {
  DEFAULT: [
    RichDataPaymentMethods.AMERICAN_EXPRESS,
    RichDataPaymentMethods.ANDROID_PAY,
    RichDataPaymentMethods.APPLE_PAY,
    RichDataPaymentMethods.CASH,
    RichDataPaymentMethods.CHECK,
    RichDataPaymentMethods.DEBIT,
    RichDataPaymentMethods.DINERS_CLUB,
    RichDataPaymentMethods.DISCOVER,
    RichDataPaymentMethods.FINANCING,
    RichDataPaymentMethods.MASTERCARD,
    RichDataPaymentMethods.PAYPAL,
    RichDataPaymentMethods.SAMSUNG_PAY,
    RichDataPaymentMethods.STORE_CARD,
    RichDataPaymentMethods.TRAVELERS_CHECK,
    RichDataPaymentMethods.VISA,
  ],
  CZ: [
    RichDataPaymentMethods.AMERICAN_EXPRESS,
    RichDataPaymentMethods.ANDROID_PAY,
    RichDataPaymentMethods.CASH,
    RichDataPaymentMethods.CHECK,
    RichDataPaymentMethods.DINERS_CLUB,
    RichDataPaymentMethods.DISCOVER,
    RichDataPaymentMethods.MASTERCARD,
    RichDataPaymentMethods.PAYPAL,
    RichDataPaymentMethods.TRAVELERS_CHECK,
    RichDataPaymentMethods.VISA,
    RichDataPaymentMethods.CCS,
    RichDataPaymentMethods.SODEXO,
    RichDataPaymentMethods.GOPAY,
    RichDataPaymentMethods.V_PAY,
    RichDataPaymentMethods.FINANCING,
    RichDataPaymentMethods.INVOICE,
    RichDataPaymentMethods.PAYSEC,
    RichDataPaymentMethods.BITCOIN,
  ],
};

export const BusinessOperationMethods = [
  ClosedStatus.UNSPECIFIED,
  ClosedStatus.OPEN,
  ClosedStatus.LIMITED,
  ClosedStatus.TEMPORARY,
  ClosedStatus.PERMANENT,
];

export const SellingMethodsAvailability = [IsAvailable.UNSET, IsAvailable.YES, IsAvailable.NO];

export interface TimezoneOption {
  timezone: string;
  display: string;
}

export const ProfesstionalCredentials = [
  'ACSW',
  'AGNP-C',
  'ANP',
  'ANP-C',
  'AP',
  'APN',
  'APR',
  'APRN',
  'ARNP',
  'AUD',
  'BCBA',
  'CCC/SLP',
  'CCSW',
  'CERT MDT',
  'CFNP',
  'CLTC',
  'CMP',
  'CNA',
  'CNM',
  'CNP',
  'CNS',
  'CPNP',
  'CRNA',
  'CRNP',
  'CSW',
  'DC',
  'DCSW',
  'DDS',
  'DMD',
  'DNP',
  'DO',
  'DOT',
  'DOM',
  'DPM',
  'DPT',
  'FAAP',
  'FACC',
  'FACOG',
  'FACOS',
  'FACP',
  'FACS',
  'FCCP',
  'FCOG',
  'FCOS',
  'FICS',
  'FNP',
  'FNPC',
  'FNP-MC',
  'FPN',
  'FPNP',
  'IBCLC',
  'ITDS',
  'LAC',
  'LADC',
  'LCS',
  'LCSW',
  'LGSW',
  'LICSW',
  'LISW',
  'LMBT',
  'LMFT',
  'LMH',
  'LMHC',
  'LMHP',
  'LMSW',
  'LMT',
  'LP',
  'LPC',
  'LPCC',
  'LPN',
  'LPT',
  'LSW',
  'LVN',
  'MBBS',
  'MBCHB',
  'MD',
  'MDT',
  'MFT',
  'MHP',
  'MHS',
  'MHSP',
  'MMFT',
  'MPH',
  'MSN',
  'MSW',
  'MT',
  'NCC',
  'NCTM',
  'NCTMB',
  'ND',
  'NNP',
  'NP',
  'NP-C',
  'OD',
  'OT',
  'OTA',
  'OTR',
  'PA',
  'PAC',
  'PFNP',
  'PHARMD',
  'PHD',
  'PMHN',
  'PMHNP',
  'PNP',
  'PRN',
  'PSY',
  'PSYD',
  'PT',
  'RD',
  'RDH',
  'RDH/DH',
  'RN',
  'RNC',
  'RNP',
  'RPH',
  'RPT',
  'SLP',
  'SPTH',
  'SW',
  'WHNP',
];

export const ValidationErrorMessages = {
  businessName: {
    required: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.BUSINESS_NAME',
  },
  businessType: {
    required: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.BUSINESS_TYPE',
  },
  places: {
    required: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.SERVICE_AREAS',
  },
  website: {
    url: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.WEBSITE',
  },
  email: {
    email: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.EMAIL',
  },
  primaryBusinessCategory: {
    required: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.PRIMARY_BUSINESS_CATEGORY',
  },
  workNumbers: {
    phone: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.PHONE_NUMBERS',
  },
  callTrackingNumbers: {
    phone: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.CALL_TRACKING_NUMBERS',
  },
  cellNumber: {
    phone: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.CELL_NUMBER',
  },
  tollFreeNumber: {
    phone: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.TOLL_FREE_NUMBER',
  },
  country: {
    required: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.ADDRESS.COUNTRY',
    invalidSelection: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.ADDRESS.COUNTRY',
  },
  address1: {
    required: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.ADDRESS.LINE1',
  },
  city: {
    required: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.ADDRESS.CITY',
  },
  province: {
    required: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.ADDRESS.PROVINCE',
    invalidSelection: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.ADDRESS.PROVINCE',
  },
  zip: {
    required: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.ADDRESS.ZIP',
  },
  servicesOffered: {
    maxlength: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.SERVICES_OFFERED_LENGTH',
    lengthOfRepeatedField: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.SERVICES_OFFERED_CHAR_COUNT',
  },
  brandsCarried: {
    maxlength: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.BRANDS_CARRIED_LENGTH',
    lengthOfRepeatedField: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.BRANDS_CARRIED_LENGTH',
  },
  paymentMethods: {
    paymentMethods: 'FRONTEND.BUSINESSES.INVALID_FIELD.BUSINESS_INFO.PAYMENT_METHODS',
  },
  shortDescription: {
    maxlength: 'FRONTEND.BUSINESSES.INVALID_FIELD.DESCRIPTION.SHORT',
  },
  longDescription: {
    maxlength: 'FRONTEND.BUSINESSES.INVALID_FIELD.DESCRIPTION.LONG',
  },
  facebookUrl: {
    url: 'FRONTEND.BUSINESSES.INVALID_FIELD.SOCIAL_URL.FACEBOOK',
  },
  twitterUrl: {
    url: 'FRONTEND.BUSINESSES.INVALID_FIELD.SOCIAL_URL.TWITTER',
  },
  instagramUrl: {
    url: 'FRONTEND.BUSINESSES.INVALID_FIELD.SOCIAL_URL.INSTAGRAM',
  },
  linkedInUrl: {
    url: 'FRONTEND.BUSINESSES.INVALID_FIELD.SOCIAL_URL.LINKEDIN',
  },
  pinterestUrl: {
    url: 'FRONTEND.BUSINESSES.INVALID_FIELD.SOCIAL_URL.PINTEREST',
  },
  youtubeUrl: {
    url: 'FRONTEND.BUSINESSES.INVALID_FIELD.SOCIAL_URL.YOUTUBE',
  },
  educations: {
    maxlength: 'FRONTEND.BUSINESSES.INVALID_FIELD.PROFESSIONAL_RECORDS.EDUCATIONS',
    lengthOfRepeatedField: 'FRONTEND.BUSINESSES.INVALID_FIELD.PROFESSIONAL_RECORDS.EDUCATIONS_CHAR_COUNT',
  },
  fellowships: {
    maxlength: 'FRONTEND.BUSINESSES.INVALID_FIELD.PROFESSIONAL_RECORDS.FELLOWSHIPS',
    lengthOfRepeatedField: 'FRONTEND.BUSINESSES.INVALID_FIELD.PROFESSIONAL_RECORDS.FELLOWSHIPS_CHAR_COUNT',
  },
  specialties: {
    maxlength: 'FRONTEND.BUSINESSES.INVALID_FIELD.PROFESSIONAL_RECORDS.SPECIALTIES',
    lengthOfRepeatedField: 'FRONTEND.BUSINESSES.INVALID_FIELD.PROFESSIONAL_RECORDS.SPECIALTIES_CHAR_COUNT',
  },
  residencies: {
    maxlength: 'FRONTEND.BUSINESSES.INVALID_FIELD.PROFESSIONAL_RECORDS.RESIDENCIES',
    lengthOfRepeatedField: 'FRONTEND.BUSINESSES.INVALID_FIELD.PROFESSIONAL_RECORDS.RESIDENCIES_CHAR_COUNT',
  },
  insuranceAccepted: {
    maxlength: 'FRONTEND.BUSINESSES.INVALID_FIELD.ADDITIONAL_PROFESSIONAL_INFO.INSURANCE_ACCEPTED',
    lengthOfRepeatedField:
      'FRONTEND.BUSINESSES.INVALID_FIELD.ADDITIONAL_PROFESSIONAL_INFO.INSURANCE_ACCEPTED_CHAR_COUNT',
  },
  hospitalAffiliations: {
    maxlength: 'FRONTEND.BUSINESSES.INVALID_FIELD.ADDITIONAL_PROFESSIONAL_INFO.HOSPITAL_AFFILIATIONS',
    lengthOfRepeatedField:
      'FRONTEND.BUSINESSES.INVALID_FIELD.ADDITIONAL_PROFESSIONAL_INFO.HOSPITAL_AFFILIATIONS_CHAR_COUNT',
  },
  market: {
    required: 'FRONTEND.BUSINESSES.INVALID_FIELD.ADMIN.MARKET',
  },
  additionalSalespersonIds: {
    primaryName: 'FRONTEND.BUSINESSES.INVALID_FIELD.ADMIN.PRIMARY_NOT_IN_ADDITIONAL_SALESPERSON',
    primaryNotSelected: 'FRONTEND.BUSINESSES.INVALID_FIELD.ADMIN.PRIMARY_SALESPERSON',
  },
};
