@use 'design-tokens' as *;

$light-yellow: #fff8e1;
$yellow: #ffc107;
$profile-card-container-horizontal-margin: $spacing-2;
$save-button-horizontal-padding: $spacing-4;

.loading {
  display: none;
}
.business-profile__tab-container {
  padding: $spacing-2;
  display: flex;
  flex-wrap: wrap;
}

.tab-container {
  padding: $spacing-4;
}

.business-profile__card {
  width: 100%;
  flex-basis: 50%;
  align-items: stretch;
  flex-grow: 1;
  min-width: 343px;
  padding: $spacing-4;
}
.doctordotcom-categories {
  margin-bottom: $spacing-5;
}

.business-profile__half-card__container {
  width: 50%;
  margin: $spacing-2 $profile-card-container-horizontal-margin $spacing-4 $profile-card-container-horizontal-margin;
}

.business-profile__card__container {
  margin: $spacing-2 $profile-card-container-horizontal-margin $spacing-4 $profile-card-container-horizontal-margin;
}
.custom-fields-tab {
  margin-bottom: 2em;
  // aligns custom field table with save button
  padding: 0 calc($save-button-horizontal-padding - $profile-card-container-horizontal-margin);
}

mat-card {
  margin-bottom: $spacing-4;
}

// Assume we want full width input elements
mat-form-field,
mat-select,
forms-input,
forms-input-repeated,
forms-input-textarea,
forms-input-textarea-ai,
forms-input-tags,
forms-hours-of-operation,
forms-business-hours,
forms-google-attributes {
  width: 100%;
}

business-address-form-v2 {
  margin-top: $spacing-1;
}

// Space out md selects roughly like md inputs (1em margins inside, 15px extra bottom)
mat-select {
  ::ng-deep .mat-select-trigger {
    font-size: $font-preset-4-size;
  }
}

mat-checkbox {
  display: block;
  margin-bottom: $spacing-3;

  ::ng-deep .mat-checkbox-layout {
    white-space: normal;
  }
}

.remove-field-button {
  // Make it similar shape to other material component input suffix controls (select dropdown)
  cursor: pointer;
  font-size: $font-preset-4-size;
  margin: 0 $spacing-1;
}

.card-invalid {
  background-color: rgba(255, 240, 240, 0.3);
}

.tab {
  padding: 0 $spacing-3;
  position: relative;
}

.tab-invalid {
  color: $red;
}

.tab-changed {
  color: $green;
}

.tab-changed::before {
  content: '';
  background: $green;
  position: absolute;
  left: 0;
  display: block;
  height: $spacing-2;
  width: $spacing-2;
  border-radius: 50%;
  top: 50%;
  margin-top: $negative-1;
}

// Use full width and center tabs
mat-tab-group {
  width: 100%;

  ::ng-deep .mat-tab-labels {
    justify-content: space-around;
  }
}

.salespeople-hint {
  font-size: 75%;
  margin-top: -2em;
  height: $spacing-3;
  margin-bottom: 1em;

  &.add {
    text-align: right;
    a {
      cursor: pointer;
    }
  }

  &.error {
    margin-top: -1em;
  }
}

.large-textarea textarea {
  min-height: 200px;
}

.save-footer {
  width: inherit;
  position: sticky;
  bottom: $spacing-3;
  z-index: 1;
  box-shadow:
    0px 3px 3px -2px rgba(0, 0, 0, 0.2),
    0px 3px 4px 0px rgba(0, 0, 0, 0.14),
    0px 1px 8px 0px rgba(0, 0, 0, 0.12);
  margin: $spacing-3;
}

mat-hint {
  font-size: $font-preset-5-size;
}

mat-expansion-panel {
  border: none;
}

.sab-hint {
  width: 120px;
}

mat-expansion-panel.mat-expanded {
  margin-top: 0;
  border: none;
}

::ng-deep .mat-expansion-panel-body.mat-expansion-panel-body {
  border-top: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.service-area {
  margin-bottom: 0;
  margin-top: $spacing-2;
}

.mat-mdc-autocomplete-panel-above {
  width: 100% !important;
}

.service-area-hint {
  margin-bottom: $spacing-3;
}

.field-title {
  @include text-preset-4--bold;
}

.business-category-hint {
  float: left;
}

.glxy-form-field.bottom-spacing--default {
  margin-bottom: $spacing-3 !important;
}

.salespeople-hint {
  font-size: 13px;
  margin-top: $negative-2;
  height: 13px;

  &.add {
    text-align: right;
    a {
      cursor: pointer;
    }
  }

  &.error {
    margin-top: -1em;
  }
}

:host ::ng-deep .background-lighter-grey .mat-mdc-tab-header {
  background: $lighter-grey;
}
