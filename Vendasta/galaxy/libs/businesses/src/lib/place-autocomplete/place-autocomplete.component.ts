import { Component, EventEmitter, Input, Output, ViewChild, AfterViewInit } from '@angular/core';
import { Geo, GooglePlace } from '@vendasta/account-group';

@Component({
  selector: 'business-place-autocomplete',
  templateUrl: './place-autocomplete.component.html',
  styleUrls: ['./place-autocomplete.component.scss'],
  standalone: false,
})
export class PlaceAutocompleteComponent implements AfterViewInit {
  maxBounds: google.maps.LatLngBounds;
  autocompleteInput: string;
  @ViewChild('addresstext') addresstext: any;
  @Input() set geoLocation(b: Geo) {
    if (!!b && b?.latitude !== undefined && b?.longitude !== undefined) {
      const center = new google.maps.LatLng(b.latitude, b.longitude);
      // Set location bias for Google place search to a radius of 300 km from the business location because Google says
      // service areas should not extend farther than about a 2-hour drive away from the business location
      // https://support.google.com/business/answer/3038177#zippy=%2Caddress
      this.maxBounds = new google.maps.Circle({ radius: 300000, center: center }).getBounds();
    }
  }
  @Input() editDisabled = false;
  @Output() selected: EventEmitter<GooglePlace> = new EventEmitter();

  ngAfterViewInit(): void {
    this.getPlaceAutocomplete();
  }

  private getPlaceAutocomplete(): void {
    const options: google.maps.places.AutocompleteOptions = {
      types: ['(regions)'],
    };
    options.bounds = this.maxBounds ? this.maxBounds : undefined;
    options.strictBounds = false;
    options.fields = ['place_id', 'address_components', 'name', 'types'];

    const autocomplete = new google.maps.places.Autocomplete(this.addresstext?.nativeElement, options);
    google.maps.event.addListener(autocomplete, 'place_changed', () => {
      const place = autocomplete.getPlace();
      this.invokeEvent(place);
      this.autocompleteInput = '';
      this.addresstext?.nativeElement.blur();
      this.addresstext?.nativeElement.focus();
    });
  }

  invokeEvent(place: google.maps.places.PlaceResult): void {
    this.selected.emit(
      new GooglePlace({
        placeId: place.place_id,
        placeName: this.formatName(place),
        city: place.address_components.find((component) => component.types.indexOf('locality') !== -1)?.long_name || '',
      }),
    );
  }

  formatName(place: google.maps.places.PlaceResult): string {
    const city =
      place.address_components.find((component) => component.types.indexOf('locality') !== -1)?.long_name || '';
    const province =
      place.address_components.find((component) => component.types.indexOf('administrative_area_level_1') !== -1)
        ?.short_name || '';
    const country =
      place.address_components.find((component) => component.types.indexOf('country') !== -1)?.short_name || '';
    switch (place.types[0]) {
      case 'administrative_area_level_2':
      case 'locality': {
        return joinFirstItems([place.name, province, country], ', ', 2);
      }
      case 'sublocality':
      case 'postal_code': {
        return joinFirstItems([place.name, city, province, country], ', ', 3);
      }
      case 'country': {
        return place.name;
      }
      case 'administrative_area_level_1': {
        return joinFirstItems([place.name, country], ', ', 2);
      }
    }
    return place.name;
  }
}

function joinFirstItems(items: string[], delimiter: string, max: number): string {
  return items.filter(Boolean).slice(0, max).join(delimiter);
}
