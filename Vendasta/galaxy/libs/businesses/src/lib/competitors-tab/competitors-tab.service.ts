import { HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Competitor, CompetitorApiService, CreateCompetitorResponse } from '@vendasta/account-group';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable()
export class CompetitorsTabService {
  constructor(private api: CompetitorApiService) {}

  public getCompetitors(businessId: string, cursor: string, pageSize: number): Observable<Competitor[]> {
    const req = { businessId: businessId, pagingOptions: { cursor: cursor, pageSize: pageSize } };
    return this.api.getCompetitors(req).pipe(
      map((res) => res.competitors),
      catchError(() => of([])),
    );
  }

  public create(businessId: string, competitor: any): Observable<CreateCompetitorResponse> {
    const req = {
      businessId: businessId,
      location: {
        name: competitor.name,
        url: competitor.url,
      },
    };
    return this.api.create(req);
  }

  public update(businessId: string, competitor: any): Observable<HttpResponse<null>> {
    const req = {
      competitor: {
        businessId: businessId,
        competitorId: competitor.competitorId,
        location: {
          name: competitor.name,
          url: competitor.url,
        },
      },
      fieldMask: {
        paths: ['name', 'url'],
      },
    };
    return this.api.update(req);
  }

  public delete(businessId: string, competitorId: string): Observable<HttpResponse<null>> {
    const req = {
      businessId: businessId,
      competitorId: competitorId,
    };
    return this.api.delete(req);
  }
}
