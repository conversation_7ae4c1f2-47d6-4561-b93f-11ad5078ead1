.competitor-card {
  margin: 10px 10px 20px 10px;
}

.competitor-input {
  width: 30%;
  margin-left: 0;
  margin-top: 8px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.name {
  margin-right: 18px;
}

mat-card-title {
  margin-bottom: 8px;
}

::ng-deep {
  .mat-mdc-dialog-container {
    min-height: 150px !important;
  }
}

mat-spinner {
  margin: auto;
}

button {
  span.loading {
    visibility: hidden;
  }
  .spinner-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
  }
}

.form-elements {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
