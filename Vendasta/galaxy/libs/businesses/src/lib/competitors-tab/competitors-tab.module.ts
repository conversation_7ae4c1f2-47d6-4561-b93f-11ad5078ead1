import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { CompetitorsTabComponent } from './competitors-tab.component';
import { CompetitorsTabService } from './competitors-tab.service';

@NgModule({
  declarations: [CompetitorsTabComponent],
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatInputModule,
    MatProgressSpinnerModule,
    TranslateModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
  ],
  providers: [CompetitorsTabService],
  exports: [CompetitorsTabComponent],
})
export class CompetitorsTabModule {}
