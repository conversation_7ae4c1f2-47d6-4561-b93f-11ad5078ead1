import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Competitor } from '@vendasta/account-group';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ConfirmationData, ConfirmationService } from '@vendasta/uikit';
import { finalize, take } from 'rxjs/operators';
import { CompetitorsTabService } from './competitors-tab.service';

const MAX_COMPETITORS = 3;

export interface CompetitorForm {
  value: {
    name: string;
    url: string;
    competitorId: string;
    businessId: string;
  };
}

@Component({
  selector: 'business-competitors-tab',
  templateUrl: './competitors-tab.component.html',
  styleUrls: ['./competitors-tab.component.scss'],
  standalone: false,
})
export class CompetitorsTabComponent implements OnInit {
  @Input() businessId: string;
  @Input() formDisabled = false;

  competitorsForm: UntypedFormGroup;
  maxCompetitors = MAX_COMPETITORS;
  originalCompetitors: any[] = [];
  buttonLoading = false;
  index: number = null;
  cursor = '';

  constructor(
    private formBuilder: UntypedFormBuilder,
    private confirmationService: ConfirmationService,
    private service: CompetitorsTabService,
    private alertService: SnackbarService,
    private translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.competitorsForm = this.formBuilder.group({
      competitors: this.formBuilder.array([]),
    });
    if (this.formDisabled) {
      this.competitorsForm.disable();
    }

    this.service
      .getCompetitors(this.businessId, this.cursor, this.maxCompetitors)
      .pipe(take(1))
      .subscribe((competitors: Competitor[]) => {
        this.initForm(competitors);
      });
  }

  get competitorsArray(): UntypedFormArray {
    return <UntypedFormArray>this.competitorsForm.get('competitors');
  }

  private initForm(competitors: Competitor[]): void {
    if (competitors && competitors.length > 0) {
      competitors.forEach((c: Competitor) => {
        const competitorGroup = this.formBuilder.group({
          name: this.formBuilder.control({ value: c.location.name, disabled: true }, Validators.required),
          url: this.formBuilder.control({ value: c.location.url, disabled: true }),
          competitorId: this.formBuilder.control({ value: c.competitorId, disabled: true }),
          businessId: this.formBuilder.control({ value: c.businessId, disabled: true }),
        });
        this.competitorsArray.push(competitorGroup);
      });
    } else {
      const competitorGroup = this.formBuilder.group({
        name: this.formBuilder.control({ value: '', disabled: this.formDisabled }, Validators.required),
        url: this.formBuilder.control({ value: '', disabled: this.formDisabled }),
        competitorId: this.formBuilder.control({ value: '', disabled: this.formDisabled }),
        businessId: this.formBuilder.control({ value: '', disabled: this.formDisabled }),
      });
      this.competitorsArray.push(competitorGroup);
    }
  }

  addCompetitor(): void {
    if (this.formDisabled) {
      return;
    }
    const competitor = this.formBuilder.group({
      name: this.formBuilder.control({ value: '', disabled: false }, Validators.required),
      url: this.formBuilder.control({ value: '', disabled: false }),
      competitorId: this.formBuilder.control({ value: '', disabled: false }),
      businessId: this.formBuilder.control({ value: '', disabled: false }),
    });
    this.competitorsArray.push(competitor);
  }

  editCompetitor(index: number, competitor: CompetitorForm): void {
    if (this.formDisabled) {
      return;
    }
    // temporarily save competitor to restore later when user clicks cancel
    this.originalCompetitors.push({ key: index, value: competitor.value });
    this.competitorsArray.controls[index].enable();
  }

  cancel(index: number): void {
    // restore competitor when user cancel edit
    const originalCompetitor = this.originalCompetitors.find((c) => c.key === index);
    if (originalCompetitor) {
      this.competitorsArray.controls[index].patchValue(originalCompetitor.value);
      this.competitorsArray.controls[index].disable();
    } else if (index === 0) {
      this.competitorsArray.controls[index].reset();
    } else {
      this.competitorsArray.removeAt(index);
    }
  }

  saveCompetitor(index: number, competitor: CompetitorForm): void {
    if (this.formDisabled) {
      return;
    }
    this.index = index;
    this.buttonLoading = true;
    if (competitor.value.competitorId) {
      this.service
        .update(this.businessId, competitor.value)
        .pipe(
          take(1),
          finalize(() => (this.buttonLoading = false)),
        )
        .subscribe(
          () => {
            // clear competitor saved from edit backup
            const removeIndex = this.originalCompetitors.indexOf(this.originalCompetitors.find((c) => c.key === index));
            this.originalCompetitors.splice(removeIndex);

            this.competitorsArray.controls[index].disable();
            this.alertService.openSuccessSnack('FRONTEND.BUSINESSES.COMPETITOR_SAVE_SUCCESS');
          },
          () => {
            this.alertService.openErrorSnack('FRONTEND.BUSINESSES.COMPETITOR_SAVE_ERROR');
          },
        );
    } else {
      this.service
        .create(this.businessId, competitor.value)
        .pipe(
          take(1),
          finalize(() => (this.buttonLoading = false)),
        )
        .subscribe(
          (res) => {
            // clear competitor saved from edit backup
            const removeIndex = this.originalCompetitors.indexOf(this.originalCompetitors.find((c) => c.key === index));
            this.originalCompetitors.splice(removeIndex);

            this.competitorsArray.controls[index].get('competitorId').setValue(res.competitorId);
            this.competitorsArray.controls[index].get('businessId').setValue(res.businessId);
            this.competitorsArray.controls[index].disable();
            this.alertService.openSuccessSnack('FRONTEND.BUSINESSES.COMPETITOR_SAVE_SUCCESS');
          },
          () => {
            this.alertService.openErrorSnack('FRONTEND.BUSINESSES.COMPETITOR_SAVE_ERROR');
          },
        );
    }
  }

  deleteCompetitor(index: number, competitor: CompetitorForm): void {
    if (this.formDisabled) {
      return;
    }
    const confirmOptions = {
      title: this.translate.instant('FRONTEND.BUSINESSES.COMPETITOR_DELETE', { name: competitor.value.name }),
      text: this.translate.instant('FRONTEND.BUSINESSES.COMPETITOR_DELETE_MESSAGE'),
      closeText: this.translate.instant('FRONTEND.BUSINESSES.CANCEL'),
      confirmText: this.translate.instant('FRONTEND.BUSINESSES.DELETE'),
      severity: 'warn',
    } as ConfirmationData;
    this.confirmationService.confirm(confirmOptions).then((confirmationClicked) => {
      if (confirmationClicked) {
        this.service
          .delete(this.businessId, competitor.value.competitorId)
          .pipe(take(1))
          .subscribe(
            () => {
              if (this.competitorsArray.length === 1) {
                this.competitorsArray.removeAt(index);
                this.addCompetitor();
              } else {
                this.competitorsArray.removeAt(index);
              }
              this.alertService.openSuccessSnack('FRONTEND.BUSINESSES.COMPETITOR_DELETE_SUCCESS');
            },
            () => {
              this.alertService.openErrorSnack('FRONTEND.BUSINESSES.COMPETITOR_DELETE_ERROR');
            },
          );
      }
    });
  }
}
