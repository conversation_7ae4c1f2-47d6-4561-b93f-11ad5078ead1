<mat-card appearance="outlined" class="competitor-card">
  <mat-card-header>
    <mat-card-title>
      {{ 'FRONTEND.BUSINESSES.COMPETITORS' | translate }}
    </mat-card-title>
  </mat-card-header>
  <ng-container>
    <mat-card-content>
      <form [formGroup]="competitorsForm">
        <ng-container
          formArrayName="competitors"
          *ngFor="let c of competitorsArray.controls; let i = index; let last = last"
        >
          <div class="form-elements">
            <glxy-form-field class="competitor-input name" bottomSpacing="none">
              <glxy-label>
                {{ 'FRONTEND.BUSINESSES.COMPETITOR_NAME' | translate }}
              </glxy-label>
              <input matInput [formControl]="c.get('name')" required />
              <glxy-error
                *ngIf="c.get('name').invalid && (c.get('name').dirty || c.get('name').touched)"
                class="alert alert-danger"
              >
                <span *ngIf="c.get('name').errors.required">
                  {{ 'FRONTEND.BUSINESSES.COMPETITOR_NAME_REQUIRED' | translate }}
                </span>
              </glxy-error>
            </glxy-form-field>
            <glxy-form-field class="competitor-input" bottomSpacing="none">
              <glxy-label>
                {{ 'FRONTEND.BUSINESSES.COMPETITOR_URL' | translate }}
              </glxy-label>
              <input matInput [formControl]="c.get('url')" />
            </glxy-form-field>

            <button
              *ngIf="c.disabled"
              mat-button
              color="primary"
              class="mat-icon-button"
              [disabled]="formDisabled"
              (click)="editCompetitor(i, c)"
            >
              <mat-icon>mode_edit</mat-icon>
            </button>
            <button
              data-action="save-competitor"
              *ngIf="c.enabled"
              mat-button
              color="primary"
              (click)="saveCompetitor(i, c)"
              [disabled]="c.get('name').invalid || formDisabled"
            >
              <span [ngClass]="{ loading: index === i && buttonLoading }">
                {{ 'FRONTEND.BUSINESSES.SAVE' | translate }}
              </span>
              <div class="spinner-container" *ngIf="index === i && buttonLoading">
                <mat-spinner [diameter]="20"></mat-spinner>
              </div>
            </button>
            <button *ngIf="c.enabled" mat-button [disabled]="formDisabled" (click)="cancel(i)">
              {{ 'FRONTEND.BUSINESSES.CANCEL' | translate }}
            </button>
            <button
              *ngIf="c.disabled"
              mat-button
              color="primary"
              class="mat-icon-button"
              [disabled]="formDisabled"
              (click)="deleteCompetitor(i, c)"
            >
              <mat-icon>close</mat-icon>
            </button>
          </div>
          <button
            mat-button
            color="primary"
            *ngIf="last && competitorsArray.controls.length < maxCompetitors"
            [disabled]="formDisabled"
            (click)="addCompetitor()"
          >
            {{ 'FRONTEND.BUSINESSES.ADD_COMPETITOR' | translate }}
          </button>
        </ng-container>
      </form>
    </mat-card-content>
  </ng-container>
</mat-card>
