import { Pipe, PipeTransform } from '@angular/core';

interface Address {
  address: string;
  city: string;
  state: string;
}
// formatNAPDataToString formats address, city, and state into a comma separate string
@Pipe({ name: 'formatAddressToString', standalone: true })
export class FormatAddressToStringPipe implements PipeTransform {
  transform(address: Address): string {
    return [address.address?.trim(), address.city?.trim(), address.state?.trim()].filter((v) => !!v).join(', ');
  }
}
