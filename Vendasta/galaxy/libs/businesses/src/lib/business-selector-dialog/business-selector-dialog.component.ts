import { Component, Inject, OnDestroy, OnInit, Signal } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { AccountGroup, Constraint, ProjectionFilter } from '@vendasta/account-group';
import { Observable, Subscription, startWith, tap } from 'rxjs';
import { BusinessSelectorDialogService } from './business-selector-dialog.service';

export interface DialogData {
  partnerId: string;
  marketId: string | string[];
  projectionFilterOverride?: ProjectionFilter;
  hideCreateBusiness?: boolean;
}
@Component({
  templateUrl: './business-selector-dialog.component.html',
  styleUrls: ['./business-selector-dialog.component.scss'],
  standalone: false,
})
export class BusinessSelectorDialogComponent implements OnInit, OnD<PERSON>roy {
  private readonly subscriptions: Subscription[] = [];
  protected readonly searchFormField = new UntypedFormControl('');
  protected loading$: Observable<boolean>;
  protected business$: Observable<AccountGroup[]>;
  protected hasMore$: Observable<boolean>;
  protected loadingFirstPage: Signal<boolean>;

  constructor(
    private readonly dialogRef: MatDialogRef<BusinessSelectorDialogComponent>,
    private readonly router: Router,
    private readonly businessSelectorDialogService: BusinessSelectorDialogService,
    @Inject(MAT_DIALOG_DATA) protected readonly data: DialogData,
  ) {}

  ngOnInit(): void {
    this.loading$ = this.businessSelectorDialogService.loading$;
    this.business$ = this.businessSelectorDialogService.business$;
    this.hasMore$ = this.businessSelectorDialogService.hasMore$;
    this.businessSelectorDialogService.data = this.data;
    this.loadingFirstPage = this.businessSelectorDialogService.loadingFirstPage;

    this.businessSelectorDialogService.initBusinesses();

    this.subscriptions.push(
      this.searchFormField.valueChanges
        .pipe(
          startWith(''),
          tap(() => {
            this.businessSelectorDialogService.searchFormFieldValue = this.searchFormField.value;
          }),
        )
        .subscribe(),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  loadMoreAccounts(inViewportEvent: any): void {
    if (inViewportEvent.visible) {
      this.businessSelectorDialogService.loadMore = true;
    }
  }

  selectBusiness(accountGroup: AccountGroup): void {
    this.dialogRef.close(accountGroup);
  }

  createBusiness(): void {
    this.dialogRef.close();
    this.router.navigateByUrl(`/business/search`);
  }

  getPartnerIdfromConstraints(constraints: Constraint[]): string {
    return constraints.find((element) => element?.foreignKey?.kind === 'PartnerAccountGroup')?.foreignKey?.key;
  }

  close(): void {
    this.dialogRef.close();
  }
}
