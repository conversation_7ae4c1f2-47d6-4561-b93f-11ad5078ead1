import { FormatAddressToStringPipe } from './format-address.pipe';

describe('FormatNAPDataToString', () => {
  const pipe = new FormatAddressToStringPipe();

  it('should return comma separated address, city, state', () => {
    const napData = {
      address: '123 St',
      city: 'Saskatoon',
      state: 'SK',
    };
    expect(pipe.transform(napData)).toBe('123 St, Saskatoon, SK');
  });

  it('should return no commas when only single property has a value', () => {
    const napData = {
      address: '',
      city: 'Saskatoon',
      state: '',
    };
    expect(pipe.transform(napData)).toBe('Saskatoon');
  });

  it('should return empty string when there are no values', () => {
    const napData = {
      address: '',
      city: '',
      state: '',
    };
    expect(pipe.transform(napData)).toBe('');
  });

  it('should return trim whitespace from the beginning and end of values', () => {
    const napData = {
      address: ' 123 st ',
      city: ' stoon',
      state: 'SK ',
    };
    expect(pipe.transform(napData)).toBe('123 st, stoon, SK');
  });

  it('should parsed values when they are undefined', () => {
    const napData = {
      address: undefined,
      city: ' stoon',
      state: 'SK ',
    };
    expect(pipe.transform(napData)).toBe('stoon, SK');
  });
});
