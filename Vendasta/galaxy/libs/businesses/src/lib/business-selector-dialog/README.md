## BusinessSelectorDialogComponent

### Functional behaviour
Inputs: 
- This component takes in partner ID and market ID, with market ID being required if the dialog is being used in a non-partner context in order for the account-group lookup to be filtered properly.
- Example usage:
  ```
  const dialog$ = this.partnerId$.pipe(
    switchMap((partnerId) => {
      return this.dialog
        .open(BusinessSelectorDialogComponent, {
          width: '500px',
          data: {
            partnerId: partnerId,
          },
        })
        .afterClosed();
    })
  );```

Outputs: 
- This component will output an account group (interface from `@vendasta/account-group`) with the following projection filters used to get extra data: accountGroupExternalIdentifiers and napData.
