import { NgModule } from '@angular/core';
import { BusinessSelectorDialogComponent } from './business-selector-dialog.component';
import { CommonModule } from '@angular/common';
import { MatListModule as MatListModule } from '@angular/material/list';
import { MatButtonModule as MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule as MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule as MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule as MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule as MatDialogModule } from '@angular/material/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { InViewportModule } from 'ng-in-viewport';
import { FormatAddressToStringPipe } from './format-address.pipe';
import { LexiconModule } from '@galaxy/lexicon';
import baseTranslation from '../assets/i18n/en_devel.json';
import { MatLineModule } from '@angular/material/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatChipsModule } from '@angular/material/chips';

@NgModule({
  declarations: [BusinessSelectorDialogComponent],
  imports: [
    CommonModule,
    MatListModule,
    MatChipsModule,
    MatButtonModule,
    MatFormFieldModule,
    FormatAddressToStringPipe,
    MatInputModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    ReactiveFormsModule,
    InViewportModule,
    LexiconModule.forChild({
      componentName: 'common/businesses',
      baseTranslation: baseTranslation,
    }),
    MatLineModule,
    GalaxyFormFieldModule,
  ],
  exports: [BusinessSelectorDialogComponent],
  providers: [],
})
export class BusinessSelectorDialogModule {}
