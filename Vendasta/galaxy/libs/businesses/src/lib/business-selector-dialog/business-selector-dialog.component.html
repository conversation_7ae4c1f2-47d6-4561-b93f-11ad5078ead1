<h2 mat-dialog-title>{{ 'FRONTEND.BUSINESSES.SELECTOR_DIALOG.CHOOSE_ACCOUNT' | translate }}</h2>
<div class="search-container">
  <glxy-form-field suffixIcon="search">
    <input
      matInput
      type="text"
      placeholder="{{ 'FRONTEND.BUSINESSES.SELECTOR_DIALOG.ACCOUNT' | translate }}"
      [formControl]="searchFormField"
    />
  </glxy-form-field>
</div>
<mat-divider></mat-divider>

<mat-dialog-content class="options-container">
  <ng-container *ngIf="loadingFirstPage() === false && (business$ | async) as businesses; else loading">
    <div *ngIf="businesses.length > 0; else emptyState" class="options-list">
      <div *ngFor="let ag of businesses; let last = last" (click)="selectBusiness(ag)">
        <div class="option">
          <span class="account-title">
            {{ ag?.napData?.companyName }}
          </span>
          <span>
            {{ ag.napData | formatAddressToString }}
          </span>
          <p matListItemMeta *ngIf="ag.constraints?.length > 0">
            PID: {{ getPartnerIdfromConstraints(ag.constraints) }}
          </p>
        </div>
        <mat-divider *ngIf="!last"></mat-divider>
      </div>
    </div>
    <ng-container
      *ngIf="{
        hasMore: hasMore$ | async,
        loading: loading$ | async
      } as details"
    >
      <div class="load-more-container">
        <div
          *ngIf="!details.loading && details.hasMore"
          class="load-more-check"
          inViewport
          [inViewportOptions]="{ partial: false }"
          (inViewportAction)="loadMoreAccounts($event)"
        ></div>
        <mat-spinner [diameter]="30" *ngIf="details.loading"></mat-spinner>
      </div>
    </ng-container>
  </ng-container>
</mat-dialog-content>

<mat-dialog-actions>
  <button mat-stroked-button (click)="close()">{{ 'FRONTEND.BUSINESSES.CANCEL' | translate }}</button>
</mat-dialog-actions>

<ng-template #emptyState>
  <div class="empty">
    {{ 'FRONTEND.BUSINESSES.SELECTOR_DIALOG.ACCOUNTS_NOT_FOUND' | translate }}
    @if (!data.hideCreateBusiness) {
      <div class="cta-button">
        <button mat-raised-button color="primary" (click)="createBusiness()">
          {{ 'FRONTEND.BUSINESSES.SELECTOR_DIALOG.CREATE_ACCOUNT' | translate }}
        </button>
      </div>
    }
  </div>
</ng-template>

<ng-template #loading>
  <div>
    <div *ngFor="let i of [1, 2, 3, 4, 5]" class="stencil-shimmer loading-shimmer"></div>
  </div>
</ng-template>
