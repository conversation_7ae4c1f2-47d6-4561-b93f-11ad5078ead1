import { Injectable, Signal, signal } from '@angular/core';
import {
  AccountGroup,
  AccountGroupApiService,
  LookupRequestInterface,
  ProjectionFilter,
} from '@vendasta/account-group';
import { BehaviorSubject, combineLatest, debounceTime, distinctUntilChanged, map, shareReplay, switchMap } from 'rxjs';
import { DialogData } from './business-selector-dialog.component';

const PAGE_SIZE = 20;

@Injectable({
  providedIn: 'root',
})
export class BusinessSelectorDialogService {
  private readonly nextCursor$$ = new BehaviorSubject<string>('');
  private readonly searchFormFieldValue$$ = new BehaviorSubject<string>('');
  private readonly loadMore$$ = new BehaviorSubject<boolean>(false);
  private readonly loadMoreTrigger$ = this.loadMore$$.asObservable().pipe(map(() => null));
  private readonly loading$$ = new BehaviorSubject<boolean>(true);
  public readonly loading$ = this.loading$$.asObservable();
  private readonly business$$ = new BehaviorSubject<AccountGroup[]>([]);
  public readonly business$ = this.business$$.asObservable();
  private readonly hasMore$$ = new BehaviorSubject<boolean>(false);
  public readonly hasMore$ = this.hasMore$$.asObservable();
  private _loadingFirstPage = signal(true);
  public loadingFirstPage: Signal<boolean> = this._loadingFirstPage.asReadonly();

  private previousSearchTerm = '';
  public data: DialogData;

  constructor(private readonly accountGroupService: AccountGroupApiService) {}

  // initBusinesses sets up the subscription to load in account groups, responding to changes in search, scroll, and partner ID
  public initBusinesses(): void {
    combineLatest([this.searchFormFieldValue$$, this.loadMoreTrigger$])
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        shareReplay(1),
        switchMap(([searchTerm]) => {
          this.loading$$.next(true);
          let cursor = this.nextCursor$$.value;
          if (this.previousSearchTerm !== searchTerm) {
            // reset cursor and current results when new search terms entered
            cursor = '';
            this._loadingFirstPage.set(true);
            this.business$$.next([]);
          }
          this.previousSearchTerm = searchTerm;
          const projectionFilter =
            this.data.projectionFilterOverride ||
            new ProjectionFilter({
              napData: true,
              accountGroupExternalIdentifiers: true,
              constraints: this.data.partnerId === 'VMF',
            });
          const lookupRequest: LookupRequestInterface = {
            projectionFilter: projectionFilter,
            filters: {
              partnerId: this.data.partnerId,
              includeDeleted: false,
            },
            pageSize: PAGE_SIZE,
            searchTerm: searchTerm,
            cursor: cursor,
            sortOptions: {
              direction: 1,
              field: 1,
            },
          };
          if (this.data.marketId) {
            if (Array.isArray(this.data.marketId)) {
              lookupRequest.filters.marketIds = this.data.marketId;
            } else {
              lookupRequest.filters.marketIds = [this.data.marketId];
            }
          }
          return this.accountGroupService.lookup(lookupRequest);
        }),
      )
      .subscribe({
        next: (pagedAccountGroupResponse) => {
          // append the results to the current list
          const businesses = pagedAccountGroupResponse.accountGroups ?? [];
          this.business$$.next(this.business$$.value.concat(businesses));
          this.hasMore$$.next(pagedAccountGroupResponse.hasMore);
          this.nextCursor$$.next(pagedAccountGroupResponse.nextCursor);
          this.loading$$.next(false);
          this._loadingFirstPage.set(false);
        },
        error: () => {
          this.loading$$.next(false);
          this._loadingFirstPage.set(false);
        },
      });
  }

  set searchFormFieldValue(value: string) {
    this.searchFormFieldValue$$.next(value);
  }

  set loadMore(value: boolean) {
    this.loadMore$$.next(value);
  }
}
