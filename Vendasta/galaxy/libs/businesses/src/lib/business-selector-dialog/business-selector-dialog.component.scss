@use 'design-tokens' as *;

.mat-mdc-dialog-content {
  mat-list {
    mat-list-item {
      padding: 0;
    }
  }
}

.load-more-container {
  padding: 4px;

  .load-more-check {
    width: 100%;
    height: 1px;
  }

  mat-spinner {
    display: block;
    margin: auto;
  }
}

.empty {
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: $gray;
  text-align: center;
  font-size: 14px;

  .cta-button {
    padding-top: 16px;
  }
}

.search-container {
  padding: 0 $spacing-4;
}
.options-container {
  padding: 0 $spacing-4;
  height: 300px;
  .options-list {
    .option {
      display: flex;
      flex-direction: column;
      margin: $spacing-2 0;
      .account-title {
        font-weight: 500;
        color: $primary-text-color;
      }
      &:hover:not(.spinner) {
        cursor: pointer;
        background-color: $lightest-grey;
        border-radius: 8px;
      }
    }
  }
}

.loading-shimmer {
  height: 48px;
  width: 100%;
  margin: $spacing-2 0;
}
