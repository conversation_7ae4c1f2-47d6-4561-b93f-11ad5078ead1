import { Geo } from '@vendasta/listing-products';

export enum FieldName {
  FirstName = 'firstName',
  LastName = 'lastName',
  Country = 'country',
  City = 'city',
  PostalCode = 'zip',
  Province = 'province',
  Address1 = 'address1',
  Address2 = 'address2',
  Phone = 'phone',
  Company = 'company',
}

export interface Address {
  company?: string;
  firstName?: string;
  lastName?: string;
  address1: string;
  address2: string;
  city: string;
  province?: string;
  zip: string;
  // Country code ISO 3166-1 alpha-2
  country: string;
  phone?: string;
  location?: Geo;
}

export interface AddressAutoComplete {
  placeId: string;
  address1: string;
  city: string;
  province?: string;
  zip: string;
  country: string;
  location?: Geo;
}

export interface Province {
  code: string;
  name: string;
}
export interface LoadCountriesResponse {
  data: { countries: Country[] };
}

export interface LoadCountryResponse {
  data: { country: Country };
}
export interface Country {
  name: string;
  code: string;
  continent: string;
  phoneNumberPrefix: number;
  autocompletionField: string;
  labels: {
    address1: string;
    address2: string;
    city: string;
    company: string;
    country: string;
    firstName: string;
    lastName: string;
    phone: string;
    postalCode: string;
    zone: string;
  };
  formatting: {
    edit: string;
    show: string;
  };
  zones: Province[];
}

export interface ResponseError {
  errors: {
    locations: {
      column: number;
      line: number;
    }[];
    message: string;
    problems: {
      explanation: string;
    }[];
    value: any;
  }[];
}
