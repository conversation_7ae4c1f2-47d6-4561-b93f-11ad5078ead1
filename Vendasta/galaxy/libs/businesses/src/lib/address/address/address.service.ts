import { Injectable } from '@angular/core';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { AddressAPIService, CountryConfiguration, CountryOption } from '@vendasta/address';
import { EMPTY, Observable, of } from 'rxjs';
import { catchError, map, shareReplay, startWith, switchMap, tap } from 'rxjs/operators';
import { Address, FieldName, Province } from './interface';

const FIELD_REGEXP = /({\w+})/g;
const FIELDS_MAPPING: {
  [key: string]: FieldName;
} = {
  '{firstName}': FieldName.FirstName,
  '{lastName}': FieldName.LastName,
  '{country}': FieldName.Country,
  '{city}': FieldName.City,
  '{zip}': FieldName.PostalCode,
  '{province}': FieldName.Province,
  '{address1}': FieldName.Address1,
  '{address2}': FieldName.Address2,
  '{phone}': FieldName.Phone,
  '{company}': FieldName.Company,
};
const LINE_DELIMITER = '_';
const DEFAULT_FORM_LAYOUT =
  '{firstName}{lastName}_{company}_{address1}_{address2}_{city}_{country}{province}{zip}_{phone}';
const DEFAULT_SHOW_LAYOUT =
  '{lastName} {firstName}_{company}_{address1} {address2}_{city} {province} {zip}_{country}_{phone}';

@Injectable()
export class AddressService {
  private countryConfigCache: Map<string, CountryConfiguration> = new Map<string, CountryConfiguration>([]);
  private countryOptionCache: Map<string, CountryOption[]> = new Map<string, CountryOption[]>([]);
  private countryConfig$: Observable<CountryConfiguration>;
  private countryOptions$: Observable<CountryOption[]>;

  constructor(
    private addressApiService: AddressAPIService,
    private translateService: TranslateService,
  ) {}

  public mailingAddress$(address: Address): Observable<string> {
    return this.getCountry(address.country).pipe(
      map((country: CountryConfiguration) => this.format(country, address)),
      map((addressLines: string[]) => {
        return addressLines.filter((line) => !!line).join('\n');
      }),
    );
  }

  public shortAddress$(address: Address): Observable<string> {
    return this.getCountry(address.country).pipe(
      map((country: CountryConfiguration) =>
        this.format(country, {
          address1: address.address1,
          address2: address.address2,
          city: address.city,
          province: address.province,
          zip: '',
          country: '',
        } as Address),
      ),
      map((addressLines: string[]) => {
        return addressLines.filter((line) => !!line).join(', ');
      }),
    );
  }

  // Fetch information for rendering a country's input or output
  public getCountry(countryCode: string): Observable<CountryConfiguration> {
    if (!countryCode) {
      return of(null);
    }
    return this.translateService.onLangChange.pipe(
      map((langChangeEvent: LangChangeEvent) => langChangeEvent.lang),
      startWith(this.translateService.currentLang || this.translateService.defaultLang),
      switchMap((locale: string) => {
        const cacheKey = `${locale}:${countryCode}`;
        if (this.countryConfigCache.has(cacheKey)) {
          return of(this.countryConfigCache.get(cacheKey));
        }
        if (this.countryConfig$) {
          return this.countryConfig$;
        }
        this.countryConfig$ = this.addressApiService.getCountryConfiguration(countryCode, locale).pipe(
          tap((countryConfig: CountryConfiguration) => {
            this.countryConfigCache.set(cacheKey, countryConfig);
            this.countryConfig$ = null;
          }),
          catchError(() => {
            this.countryConfig$ = null;
            return EMPTY;
          }),
          shareReplay(),
        );
        return this.countryConfig$;
      }),
    );
  }

  // Fetch information for rendering country options for a form
  public listAllCountryOptions(): Observable<CountryOption[]> {
    return this.translateService.onLangChange.pipe(
      map((langChangeEvent: LangChangeEvent) => langChangeEvent.lang),
      startWith(this.translateService.currentLang),
      switchMap((locale: string) => {
        const cacheKey = locale;
        if (this.countryOptionCache.has(locale)) {
          return of(this.countryOptionCache.get(locale));
        }
        if (this.countryOptions$) {
          return this.countryOptions$;
        }
        this.countryOptions$ = this.addressApiService.listAllCountryOptions(locale).pipe(
          tap((countryOptions: CountryOption[]) => {
            this.countryOptionCache.set(cacheKey, countryOptions);
            this.countryOptions$ = null;
          }),
          shareReplay(),
        );
        return this.countryOptions$;
      }),
    );
  }

  // Fetch an ordered list of fields to use for building address form data
  public getOrderedFields(countryCode: string): Observable<string[][]> {
    return this.getCountry(countryCode).pipe(
      map((country: CountryConfiguration) => {
        const format = country ? country.formatting.edit : DEFAULT_FORM_LAYOUT;
        return format.split(LINE_DELIMITER).map((fields) => {
          const result = fields.match(FIELD_REGEXP);
          if (!result) {
            return [];
          }

          const fieldMapping = result.map((field) => {
            return FIELDS_MAPPING[field].toString();
          });

          // always put city before postal code
          const cityIndex = fieldMapping.indexOf(FieldName.City.toString());
          const postalCodeIndex = fieldMapping.indexOf(FieldName.PostalCode.toString());
          if (cityIndex >= 0 && postalCodeIndex >= 0 && cityIndex > postalCodeIndex) {
            fieldMapping[cityIndex] = FieldName.PostalCode.toString();
            fieldMapping[postalCodeIndex] = FieldName.City.toString();
          }
          return fieldMapping;
        });
      }),
    );
  }

  public getProvince(provinces: Province[], provinceCode: string): Province {
    return (
      provinces?.find((province) => province.code === provinceCode) || {
        name: '',
        code: '',
      }
    );
  }

  /* PRIVATE UTILITY FUNCTIONS */
  private format(country: CountryConfiguration, address: Address): string[] {
    const layout = country ? country.formatting.show || DEFAULT_SHOW_LAYOUT : DEFAULT_SHOW_LAYOUT;
    return layout.split(LINE_DELIMITER).map((fields) => this.renderLineTemplate(country, fields, address).trim());
  }

  private renderLineTemplate(country: CountryConfiguration, template: string, address: Address): string {
    const result = template.match(FIELD_REGEXP);
    if (!result) {
      return '';
    }

    let line = template;
    let lineIsEmpty = true;
    result.forEach((key) => {
      const addressKey: string = key.replace('{', '').replace('}', '');
      if (address[addressKey]) {
        lineIsEmpty = false;
      }

      switch (addressKey) {
        case FieldName.Country.toString():
          line = line.replace(`{${FieldName.Country}}`, address.country ? country.name : '');
          break;
        case FieldName.Province.toString(): {
          let province = address.province || '';
          if (province && country) {
            province = this.getProvince(country.zones, province).name;
          }
          line = line.replace(`{${FieldName.Province}}`, province);
          break;
        }
        default:
          line = line.replace(key, address[addressKey] || '');
          break;
      }
    });
    if (lineIsEmpty) {
      return '';
    } else {
      return line.trim().replace('  ', ' ');
    }
  }
}
