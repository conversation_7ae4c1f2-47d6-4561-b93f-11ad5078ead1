import { AddressService } from './address.service';

describe('getProvince', () => {
  const translateServiceMock = {} as any;
  const addressApiServiceMock = {} as any;
  const addressService = new AddressService(addressApiServiceMock, translateServiceMock);

  const provinces = [
    { name: 'A', code: 'a' },
    { name: 'B', code: 'b' },
    { name: 'C', code: 'c' },
    { name: 'D', code: 'd' },
    { name: 'E', code: 'e' },
  ];

  it('should return empty province object if province code is empty', () => {
    const provinceCode = '';
    const expected = { name: '', code: '' };
    const actual = addressService.getProvince(provinces, provinceCode);
    expect(actual).toEqual(expected);
  });

  it('should return empty province object if province code is not found', () => {
    const provinceCode = 'f';
    const expected = { name: '', code: '' };
    const actual = addressService.getProvince(provinces, provinceCode);
    expect(actual).toEqual(expected);
  });
});
