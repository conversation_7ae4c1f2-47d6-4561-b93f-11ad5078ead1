import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';

import { LexiconModule } from '@galaxy/lexicon';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { AddressService } from './address/address.service';
import baseTranslation from '../assets/i18n/en_devel.json';
import { COUNTRY_OPTIONS_TOKEN, CountryInputComponent } from './inputs/country-input.component';

const MaterialModules = [
  MatAutocompleteModule,
  MatFormFieldModule,
  MatInputModule,
  MatIconModule,
  MatProgressSpinnerModule,
  GalaxyInputModule,
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: 'common/address',
      baseTranslation: baseTranslation,
    }),
    ...MaterialModules,
    GalaxyFormFieldModule,
    CountryInputComponent,
    MatCheckboxModule,
    MatChipsModule,
  ],
  providers: [
    AddressService,
    {
      provide: COUNTRY_OPTIONS_TOKEN,
      useFactory: (a: AddressService) => a.listAllCountryOptions(),
      deps: [AddressService],
    },
  ],
})
export class AddressFormModule {}
