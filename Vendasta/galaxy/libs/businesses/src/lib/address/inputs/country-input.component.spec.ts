import { FormControl, Validators } from '@angular/forms';
import { CountryOption } from '@vendasta/address';
import { of } from 'rxjs';
import {
  COUNTRY_CONTROL_TOKEN,
  COUNTRY_OPTIONS_TOKEN,
  countryValidator,
  defaultCountryControlProvider,
} from './country-input.component';

import { TestBed } from '@angular/core/testing';
import { shareReplay } from 'rxjs/operators';

describe('CountryValidator', () => {
  let validatorFn: ReturnType<typeof countryValidator>;

  beforeEach(() => {
    const countryOptions = [
      { code: 'CA', name: 'Canada' },
      { code: 'US', name: 'United States' },
    ];

    TestBed.configureTestingModule({
      providers: [
        {
          provide: COUNTRY_OPTIONS_TOKEN,
          useValue: of(countryOptions).pipe(shareReplay()),
        },
      ],
    });

    validatorFn = TestBed.runInInjectionContext(countryValidator);
  });

  it('should validate if the value is a valid country', async () => {
    const control = new FormControl<CountryOption | string>(new CountryOption({ code: 'CA', name: 'Canada' }));
    const result = await validatorFn(control);
    expect(result).toBeNull();

    const control2 = new FormControl<CountryOption | string>(new CountryOption({ code: 'US', name: 'United States' }));
    const result2 = await validatorFn(control2);
    expect(result2).toBeNull();
  });

  it('should invalidate if the value is not a valid country', async () => {
    const control = new FormControl<CountryOption | string>('invalid');
    const result = await validatorFn(control);
    expect(result).toEqual({ invalidCountry: true });

    const control2 = new FormControl<CountryOption | string>(' '); // An invalid country code
    const result2 = await validatorFn(control2);
    expect(result2).toEqual({ invalidCountry: true });

    const control3 = new FormControl<CountryOption | string>(
      new CountryOption({ code: 'IC', name: 'Invalid Country' }),
    );
    const result3 = await validatorFn(control3);
    expect(result3).toEqual({ invalidCountry: true });
  });

  it('should validate if the value is empty or null', async () => {
    const control = new FormControl<CountryOption | string>('');
    const result = await validatorFn(control);
    expect(result).toBeNull();

    const control2 = new FormControl<CountryOption | string>(null);
    const result2 = await validatorFn(control2);
    expect(result2).toBeNull();
  });
});

describe('defaultCountryControlProvider', () => {
  let control: FormControl<CountryOption | string>;
  let countryValidatorFn: ReturnType<typeof countryValidator>;

  beforeEach(() => {
    const countryOptions = [
      { code: 'CA', name: 'Canada' },
      { code: 'US', name: 'United States' },
    ];

    TestBed.configureTestingModule({
      providers: [
        defaultCountryControlProvider,
        {
          provide: COUNTRY_OPTIONS_TOKEN,
          useValue: of(countryOptions).pipe(shareReplay()),
        },
      ],
    });

    control = TestBed.inject(COUNTRY_CONTROL_TOKEN);
    countryValidatorFn = TestBed.runInInjectionContext(countryValidator);
  });

  it('should provide a country control with the expected validators', () => {
    expect(control).toBeInstanceOf(FormControl);
    expect(typeof control.validator === typeof countryValidatorFn).toBeTruthy();
    expect(control.hasValidator(Validators.required)).toBeTruthy();
  });
});
