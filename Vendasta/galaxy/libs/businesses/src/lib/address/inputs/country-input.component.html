<glxy-form-field bottomSpacing="none" suffixIcon="arrow_drop_down">
  <glxy-label>
    {{ 'FRONTEND.BUSINESSES.ADDRESS_FORM.COUNTRY_REGION' | translate }}
  </glxy-label>
  <input
    matInput
    type="text"
    id="{{ disableAutoFill }}"
    name="{{ disableAutoFill }}"
    autocomplete="{{ disableAutoFill }}"
    [formControl]="control"
    [matAutocomplete]="auto"
    (keydown.Tab)="tabHandler()"
  />
  <mat-autocomplete
    autoActiveFirstOption
    #auto="matAutocomplete"
    showPanel="true"
    [displayWith]="countryName"
    (optionSelected)="selectCountryOption($event.option?.value)"
    (optionActivated)="activatedOption($event.option?.value)"
  >
    <mat-option *ngFor="let option of filteredCountries$ | async" [value]="option">
      {{ option.name }}
    </mat-option>
  </mat-autocomplete>
  <glxy-error *ngIf="control?.errors !== null">
    {{ 'FRONTEND.BUSINESSES.ADDRESS_FORM.REQUIRED_FIELD' | translate }}
  </glxy-error>
</glxy-form-field>
