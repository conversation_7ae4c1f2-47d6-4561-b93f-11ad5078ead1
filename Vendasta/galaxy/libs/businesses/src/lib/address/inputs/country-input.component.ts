import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>orOf, NgIf } from '@angular/common';
import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  InjectionToken,
  Output,
  Provider,
  inject,
} from '@angular/core';
import {
  AsyncValidatorFn,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { CountryOption } from '@vendasta/address';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { Observable, firstValueFrom, map, shareReplay, switchMap } from 'rxjs';

export const COUNTRY_OPTIONS_TOKEN = new InjectionToken<Observable<CountryOption[]>>('COUNTRY_OPTIONS');
export const COUNTRY_CONTROL_TOKEN = new InjectionToken<FormControl<CountryOption | string>>('COUNTRY_CONTROL_TOKEN');
export const defaultCountryControlProvider: Provider = {
  provide: COUNTRY_CONTROL_TOKEN,
  useFactory: () => new FormControl<CountryOption | string>('', [Validators.required], [countryValidator()]),
};

export function countryValidator(): AsyncValidatorFn {
  const countryOptions = firstValueFrom(inject(COUNTRY_OPTIONS_TOKEN));

  return async (countryControl: FormControl<CountryOption | string>): Promise<ValidationErrors | null> => {
    const country = countryControl.value;
    const countries = await countryOptions;
    if (!country) return null;
    if (typeof country === 'string') return { invalidCountry: true };
    if (countries.find((c) => c.code.toLowerCase() === country.code.toLowerCase())) return null;

    return { invalidCountry: true };
  };
}

@Component({
  selector: 'business-address-country-input',
  templateUrl: './country-input.component.html',
  styleUrls: ['./country-input.component.scss'],
  imports: [
    AsyncPipe,
    FormsModule,
    GalaxyFormFieldModule,
    MatAutocompleteModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    NgForOf,
    NgIf,
    ReactiveFormsModule,
    TranslateModule,
  ],
})
export class CountryInputComponent {
  @Output() readonly countrySelected = new EventEmitter<CountryOption>();
  private readonly countries$ = inject(COUNTRY_OPTIONS_TOKEN);
  readonly filteredCountries$ = this.countries$.pipe(
    switchMap((countries) =>
      this.control.valueChanges.pipe(
        map((country) => this._filter(countries, country)),
        shareReplay({ bufferSize: 1, refCount: true }),
      ),
    ),
  );
  private _preferredCountry: CountryOption = null;

  readonly control = inject(COUNTRY_CONTROL_TOKEN) as FormControl<CountryOption | string>;

  private readonly _elementRef = inject(ElementRef);
  @HostListener('focusout', ['$event.relatedTarget']) async onFocusOut(target: HTMLElement): Promise<void> {
    // if target part of the component, do nothing
    if (this._elementRef.nativeElement.contains(target)) return;

    const countries = await firstValueFrom(this.filteredCountries$);
    const { value } = this.control;
    if (
      typeof value === 'string' &&
      countries.length > 0 &&
      countries?.[0].name.toLowerCase() === value.toLowerCase()
    ) {
      return this.selectCountryOption(countries[0]);
    }
  }

  countryName = (option: CountryOption): string => {
    return option?.name || '';
  };

  disableAutoFill = this.stopChromeAutofill();
  stopChromeAutofill(): string {
    return Math.random().toString(36).substring(2);
  }

  private _filter(countries: CountryOption[], country: string | CountryOption): CountryOption[] {
    return countries.filter((option) => {
      if (typeof country === 'string') {
        return option.name.toLowerCase().includes(country.toLowerCase());
      }
      return country;
    });
  }

  selectCountryOption(option: CountryOption): void {
    this.control.setValue(option);
    this.countrySelected.emit(option);
  }

  activatedOption(option: CountryOption): void {
    this._preferredCountry = option;
  }

  tabHandler(): void {
    if (this._preferredCountry) {
      this.selectCountryOption(this._preferredCountry);
    }
  }
}
