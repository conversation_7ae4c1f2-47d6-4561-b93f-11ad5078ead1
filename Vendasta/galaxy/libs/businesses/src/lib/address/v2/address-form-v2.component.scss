@use 'design-tokens' as dt;

:host {
  display: block;
  justify-content: space-between;
  width: 100%;

  .form-container {
    display: flex;
    flex-direction: column;
    flex: 1 0 100%;
  }
  .address-output {
    flex: 1 1 100%;
  }
}

.form-row {
  flex-wrap: wrap;
  .mat-mdc-form-field {
    flex: 1;
    margin-bottom: dt.$spacing-3;
  }

  .address-form-field-province,
  .address-form-field-zip {
    width: 45%;
  }

  .address-form-field-city {
    width: 45%;
    float: right;
  }
}

.service-area {
  margin-top: dt.$spacing-2;
}

.country-form-container {
  margin-top: dt.$spacing-3;
}

.preview-text {
  display: block;
  font-size: dt.$font-preset-5-size;
  line-height: dt.$spacing-3;
  color: dt.$secondary-text-color;
}
.formatted-address {
  display: block;
  white-space: pre-wrap;
  line-height: dt.$spacing-4;
}

.service-area-hint {
  width: 120px;
}

business-address-autocomplete {
  width: 100%;
}
