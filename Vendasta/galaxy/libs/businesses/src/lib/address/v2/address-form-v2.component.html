<div class="form-container">
  <business-address-country-input (countrySelected)="countryChanged($event)"></business-address-country-input>
  <form class="country-form-container" #form [formGroup]="formGroup" *ngIf="formFields">
    <div *ngFor="let field of formFields; let last = last" class="form-row">
      <!-- General text input -->
      <glxy-form-field
        *ngIf="dynamicControls.indexOf(field.key) > -1"
        bottomSpacing="small"
        class="address-form-field-{{ field.key }}"
      >
        <glxy-label>
          {{ (field.key === 'address2' ? 'FRONTEND.BUSINESSES.ADDRESS_FORM.ADDRESS_2' : field.label) | translate }}
        </glxy-label>
        <input
          matInput
          *ngIf="field.key !== 'address1'; else addressAutoComplete"
          [required]="field.required"
          [formControlName]="field.key"
        />
        <ng-template #addressAutoComplete>
          <business-address-autocomplete
            [control]="formGroup.controls[field.key]"
            [countryRestriction]="formGroup.controls['country'].value"
            [required]="field.required"
            (selected)="updateFormViaAutoComplete($event)"
          ></business-address-autocomplete>
        </ng-template>
        <glxy-error
          *ngIf="
            formGroup.controls[field.key].hasError('required') || formGroup.controls[field.key].hasError('whitespace')
          "
        >
          {{ 'FRONTEND.BUSINESSES.ADDRESS_FORM.REQUIRED_FIELD' | translate }}
        </glxy-error>
        <glxy-error *ngIf="formGroup.controls[field.key].hasError('invalidPostalCode')">
          {{ 'FRONTEND.BUSINESSES.ADDRESS_FORM.INVALID' | translate }} {{ field.label }}
        </glxy-error>
      </glxy-form-field>

      <!-- Dropdown with autocomplete -->
      <glxy-form-field
        *ngIf="field.key === 'province'"
        class="address-form-field-{{ field.key }}"
        suffixIcon="arrow_drop_down"
        [required]="field.required"
      >
        <glxy-label>{{ field.label }}</glxy-label>
        <input
          [formControlName]="field.key"
          [required]="field.required"
          [matAutocomplete]="provinceAutocomplete"
          autocomplete="doNotAutoComplete"
          [value]="field?.name"
          #provinceInput
        />
        <mat-autocomplete
          [autoActiveFirstOption]="provinceInput.value"
          #provinceAutocomplete="matAutocomplete"
          [displayWith]="provinceName"
        >
          <mat-option *ngFor="let option of provinces | filterProvinces: provinceInput.value" [value]="option">
            {{ option.name }}
          </mat-option>
        </mat-autocomplete>
        <glxy-error
          *ngIf="
            formGroup.controls[field.key].hasError('required') || formGroup.controls[field.key].hasError('whitespace')
          "
        >
          {{ 'FRONTEND.BUSINESSES.ADDRESS_FORM.REQUIRED_FIELD' | translate }}
        </glxy-error>
        <glxy-error *ngIf="formGroup.controls[field.key].hasError('invalidPostalCode')">
          {{ 'FRONTEND.BUSINESSES.ADDRESS_FORM.INVALID' | translate }} {{ field.label }}
        </glxy-error>
        <glxy-error *ngIf="formGroup.controls[field.key].hasError('invalidSelection')">
          {{ 'FRONTEND.BUSINESSES.ADDRESS_FORM.INVALID_SELECTION' | translate }}
        </glxy-error>
      </glxy-form-field>
    </div>
  </form>
</div>

<div class="address-output" *ngIf="showAddressPreview && formGroup.value | businessAddressPipe | async as address">
  <span class="preview-text">
    {{ 'FRONTEND.BUSINESSES.ADDRESS_FORM.PREVIEW' | translate }}
  </span>
  <span class="formatted-address">{{ address }}</span>
</div>
