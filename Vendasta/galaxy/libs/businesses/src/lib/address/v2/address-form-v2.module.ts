import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyCheckboxModule } from '@vendasta/galaxy/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatRadioModule } from '@angular/material/radio';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';

import { AddressAutocompleteComponent } from './address-autocomplete/address-autocomplete.component';
import { LexiconModule } from '@galaxy/lexicon';
import { AddressService } from '../address/address.service';
import baseTranslation from '../../assets/i18n/en_devel.json';
import {
  COUNTRY_OPTIONS_TOKEN,
  CountryInputComponent,
  defaultCountryControlProvider,
} from '../inputs/country-input.component';
import { AddressFormV2Component, FilterProvincesPipe, businessAddressPipe } from './address-form-v2.component';

const MaterialModules = [
  MatAutocompleteModule,
  MatFormFieldModule,
  MatInputModule,
  MatIconModule,
  MatProgressSpinnerModule,
  MatTooltipModule,
  MatRadioModule,
  MatExpansionModule,
  MatChipsModule,
];

@NgModule({
  declarations: [AddressFormV2Component, AddressAutocompleteComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: 'common/address',
      baseTranslation: baseTranslation,
    }),
    ...MaterialModules,
    CountryInputComponent,
    GalaxyFormFieldModule,
    businessAddressPipe,
    FilterProvincesPipe,
    GalaxyCheckboxModule,
  ],
  exports: [AddressFormV2Component],
  providers: [
    AddressService,
    {
      provide: COUNTRY_OPTIONS_TOKEN,
      useFactory: (a: AddressService) => a.listAllCountryOptions(),
      deps: [AddressService],
    },
    defaultCountryControlProvider,
  ],
})
export class AddressFormV2Module {}
