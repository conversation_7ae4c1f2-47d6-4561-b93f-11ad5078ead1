import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  Output,
  SimpleChange,
  ViewChild,
  OnInit,
  AfterViewInit,
  OnChanges,
} from '@angular/core';
import { Geo } from '@vendasta/account-group';

import { AddressAutoComplete, Country } from '../../address/interface';
import { FormControl } from '@angular/forms';
@Component({
  selector: 'business-address-autocomplete',
  templateUrl: './address-autocomplete.component.html',
  styleUrls: ['./address-autocomplete.component.scss'],
  standalone: false,
})
export class AddressAutocompleteComponent implements OnInit, AfterViewInit, OnChanges {
  autocompleteInput: string;
  @ViewChild('addresstext') addresstext: any;

  @Input() control: FormControl;
  @Input() countryRestriction: Country;
  @Input() required = false;
  @Output() selected: EventEmitter<AddressAutoComplete> = new EventEmitter();
  @Output() addressChange: EventEmitter<string> = new EventEmitter();

  protected autocomplete: google.maps.places.Autocomplete;
  protected editDisabled: boolean;
  protected disabled: boolean;

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit() {
    this.cdr.detectChanges();
    this.autocompleteInput = this.control.value;
  }

  ngAfterViewInit() {
    this.initAutoComplete();
    this.listenToAddressChange();
  }

  ngOnChanges(changes: { [propName: string]: SimpleChange }) {
    if (
      changes.countryRestriction &&
      changes.countryRestriction.currentValue.Country &&
      !changes.countryRestriction.firstChange
    ) {
      const componentRestrictions = { country: this.countryRestriction.code };
      this.updateAutoCompleteRestrictions(componentRestrictions); // update the autocomplete options if country restriction changes after the initial load
    }
  }

  private initAutoComplete(): void {
    const options: google.maps.places.AutocompleteOptions = {
      types: ['address'],
      fields: ['place_id', 'address_components', 'vicinity', 'types', 'geometry'],
      componentRestrictions: { country: this.countryRestriction.code },
    };
    this.autocomplete = new google.maps.places.Autocomplete(this.addresstext?.nativeElement, options);
  }

  private listenToAddressChange(): void {
    google.maps.event.addListener(this.autocomplete, 'place_changed', () => {
      const place = this.autocomplete.getPlace();

      this.invokeEvent(place);

      this.autocompleteInput = this.formatAddressLine1(place.address_components);
      this.onAddressChange(this.autocompleteInput);
      this.addresstext?.nativeElement.blur();
      this.addresstext?.nativeElement.focus();
    });
  }

  private updateAutoCompleteRestrictions(componentRestriction: google.maps.places.ComponentRestrictions): void {
    this.autocomplete.setComponentRestrictions(componentRestriction);
  }

  onAddressChange(address: string): void {
    this.control.patchValue(address);
    this.control.markAsDirty();
    if (address === '') {
      this.control.setErrors({ required: true });
    }
  }

  invokeEvent(place: google.maps.places.PlaceResult): void {
    this.selected.emit({
      placeId: place.place_id,
      address1: this.formatAddressLine1(place.address_components),
      zip: place.address_components.find((component) => component.types.indexOf('postal_code') !== -1)?.long_name || '',
      province:
        place.address_components.find((component) => component.types.indexOf('administrative_area_level_1') !== -1)
          ?.long_name || '',
      country: place.address_components.find((component) => component.types.indexOf('country') !== -1)?.long_name || '',
      city: place.address_components.find((component) => component.types.indexOf('locality') !== -1)?.long_name || '',
      location: new Geo({
        latitude: place.geometry?.location?.lat(),
        longitude: place.geometry?.location?.lng(),
      }),
    });
  }

  formatAddressLine1(addressComponents: google.maps.GeocoderAddressComponent[]): string {
    if (!addressComponents) {
      return '';
    }

    const streetNumber =
      addressComponents.find((component) => component.types.indexOf('street_number') !== -1)?.long_name || '';
    const route = addressComponents.find((component) => component.types.indexOf('route') !== -1)?.long_name || '';
    const street = joinFirstItems([streetNumber, route], ' ', 2);
    return street;
  }
}

function joinFirstItems(items: string[], delimiter: string, max: number): string {
  return items.filter(Boolean).slice(0, max).join(delimiter);
}
