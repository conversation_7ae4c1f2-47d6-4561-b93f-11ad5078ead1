import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Component, Pipe, PipeTransform } from '@angular/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { Spectator, createComponentFactory } from '@ngneat/spectator/jest';
import { TranslateService } from '@ngx-translate/core';
import { CountryConfiguration, CountryOption } from '@vendasta/address';
import { firstValueFrom, of } from 'rxjs';
import { AddressService } from '../address/address.service';
import { COUNTRY_OPTIONS_TOKEN, defaultCountryControlProvider } from '../inputs/country-input.component';
import { AddressFormV2Component, businessAddressPipe } from './address-form-v2.component';

const mockTranslateService = {
  instant: jest.fn(),
} as unknown as TranslateService;

@Pipe({ name: 'translate', standalone: false })
class MockTranslatePipe implements PipeTransform {
  transform(value: any) {
    return value;
  }
}

@Component({ selector: 'business-address-country-input', template: ``, standalone: false })
class CountryInputMockComponent {}

const canadaCountryConfig = {
  code: 'CA',
  continent: 'North America',
  name: 'Canada',
  phoneNumberPrefix: 1,
  formatting: {
    show: '{firstName} {lastName}_{company}_{address1}_{address2}_{city} {province} {zip}_{country}_{phone}',
    edit: '{firstName}{lastName}_{company}_{address1}_{address2}_{city}_{country}{province}{zip}_{phone}',
    required: ['address1', 'city', 'province', 'zip'],
  },
  labels: {
    address1: 'Address Line 1',
    address2: 'Address Line 2',
    city: 'City',
    company: 'Company',
    country: 'Country',
    firstName: 'First Name',
    lastName: 'Last Name',
    phone: 'Phone',
    postalCode: 'Postal Code',
    zone: 'Province',
  },
  zones: [
    {
      code: 'AB',
      name: 'Alberta',
    },
    {
      code: 'BC',
      name: 'British Columbia',
    },
    {
      code: 'MB',
      name: 'Manitoba',
    },
    {
      code: 'NB',
      name: 'New Brunswick',
    },
    {
      code: 'NL',
      name: 'Newfoundland and Labrador',
    },
    {
      code: 'NS',
      name: 'Nova Scotia',
    },
    {
      code: 'NT',
      name: 'Northwest Territories',
    },
    {
      code: 'NU',
      name: 'Nunavut',
    },
    {
      code: 'ON',
      name: 'Ontario',
    },
    {
      code: 'PE',
      name: 'Prince Edward Island',
    },
    {
      code: 'QC',
      name: 'Quebec',
    },
    {
      code: 'SK',
      name: 'Saskatchewan',
    },
    {
      code: 'YT',
      name: 'Yukon',
    },
  ],
  locale: 'en-CA',
} as CountryConfiguration;

const canadaCountryOptions = { code: 'CA', name: 'Canada' } as CountryOption;

const czechCountryConfig = {
  code: 'CZ',
  continent: 'Europe',
  name: 'Czech Republic',
  phoneNumberPrefix: 420,
  formatting: {
    show: '{firstName} {lastName}_{company}_{address1}_{address2}_{zip} {city}_{country}_{phone}',
    edit: '{firstName}{lastName}_{company}_{address1}_{address2}_{city}_{country}{province}{zip}_{phone}',
    required: ['address1', 'city', 'zip'],
  },
  labels: {
    address1: 'Address Line 1',
    address2: 'Address Line 2',
    city: 'City',
    company: 'Company',
    country: 'Country',
    firstName: 'First Name',
    lastName: 'Last Name',
    phone: 'Phone',
    postalCode: 'Postal Code',
    zone: 'Region',
  },
  zones: [
    {
      code: 'CZ-PR',
      name: 'Praha',
    },
    {
      code: 'CZ-SK',
      name: 'Středočeský kraj',
    },
    {
      code: 'CZ-JC',
      name: 'Jihočeský kraj',
    },
    {
      code: 'CZ-PA',
      name: 'Plzeňský kraj',
    },
    {
      code: 'CZ-KA',
      name: 'Karlovarský kraj',
    },
    {
      code: 'CZ-US',
      name: 'Ústecký kraj',
    },
    {
      code: 'CZ-LI',
      name: 'Liberecký kraj',
    },
    {
      code: 'CZ-HK',
      name: 'Královéhradecký kraj',
    },
    {
      code: 'CZ-PA',
      name: 'Pardubický kraj',
    },
    {
      code: 'CZ-VY',
      name: 'Vysočina',
    },
    {
      code: 'CZ-JM',
      name: 'Jihomoravský kraj',
    },
    {
      code: 'CZ-MO',
      name: 'Moravskoslezský kraj',
    },
    {
      code: 'CZ-OL',
      name: 'Olomoucký kraj',
    },
    {
      code: 'CZ-ZL',
      name: 'Zlínský kraj',
    },
    {
      code: 'CZ-SC',
      name: 'Kraj Vysočina',
    },
  ],
  locale: 'cs-CZ',
} as CountryConfiguration;

const czechCountryOptions = { code: 'CZ', name: 'Czech Republic' } as CountryOption;

describe('AddressFormV2Component', () => {
  let spectator: Spectator<AddressFormV2Component>;
  let addressService: AddressService;

  const createComponent = createComponentFactory<AddressFormV2Component>({
    component: AddressFormV2Component,
    imports: [HttpClientTestingModule, MatAutocompleteModule, businessAddressPipe],
    providers: [
      AddressService,
      { provide: TranslateService, useValue: mockTranslateService },
      { provide: COUNTRY_OPTIONS_TOKEN, useValue: of([canadaCountryOptions, czechCountryOptions]) },
      defaultCountryControlProvider,
    ],
    declarations: [CountryInputMockComponent, MockTranslatePipe],
  });

  beforeEach(() => {
    spectator = createComponent({
      detectChanges: false,
    });

    addressService = spectator.inject(AddressService);
  });

  it('should set form control values correct', async () => {
    jest.spyOn(addressService, 'getCountry').mockReturnValue(of(canadaCountryConfig));

    const address = {
      country: 'CA',
      province: 'SK',
      address1: '123 Main St',
      address2: '',
      city: 'Saskatoon',
      zip: '12345',
      company: null,
      firstName: null,
      lastName: null,
      phone: null,
    };

    const expectFormValues = {
      ...address,
      province: { code: 'SK', name: 'Saskatchewan' },
      country: canadaCountryOptions,
    };

    await spectator.component['setInitialValues'](address);
    expect(spectator.component['formGroup'].value).toEqual(expectFormValues);
  });

  it('should set form control values when values are null and required fields should error', async () => {
    jest.spyOn(addressService, 'getCountry').mockReturnValue(of(canadaCountryConfig));

    const address = {
      country: 'CA',
      province: null,
      address1: null,
      address2: null,
      city: null,
      zip: null,
      company: null,
      firstName: null,
      lastName: null,
      phone: null,
    };

    const expectFormValues = {
      ...address,
      province: null,
      country: canadaCountryOptions,
    };

    await spectator.component['setInitialValues'](address);
    expect(spectator.component['formGroup'].value).toEqual(expectFormValues);
    await firstValueFrom(spectator.component['formGroup'].statusChanges);
    expect(spectator.component['formGroup'].invalid).toEqual(true);
    for (const i in canadaCountryConfig.formatting.required) {
      const control = canadaCountryConfig.formatting.required[i];

      expect(spectator.component['formGroup'].controls[control].status).toEqual('INVALID');
    }
  });

  it('should not error on province if province is not required', async () => {
    jest.spyOn(addressService, 'getCountry').mockReturnValue(of(czechCountryConfig));

    const address = {
      country: 'CZ',
      address1: null,
      address2: null,
      city: null,
      zip: null,
      province: null,
      company: null,
      firstName: null,
      lastName: null,
      phone: null,
    };

    const expectFormValues = {
      ...address,
      country: czechCountryOptions,
    };

    await spectator.component['setInitialValues'](address);
    expect(spectator.component['formGroup'].value).toEqual(expectFormValues);
    await firstValueFrom(spectator.component['formGroup'].statusChanges);
    expect(spectator.component['formGroup'].invalid).toEqual(true);
    for (const i in czechCountryConfig.formatting.required) {
      const control = czechCountryConfig.formatting.required[i];
      expect(spectator.component['formGroup'].controls[control].status).toEqual('INVALID');
    }
    expect(spectator.component['formGroup'].controls['province'].invalid).toEqual(false);
  });
});
