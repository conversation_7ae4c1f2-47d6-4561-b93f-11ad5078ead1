import { Component, EventEmitter, Input, Output, Pipe, PipeTransform, ViewChild, inject } from '@angular/core';
import {
  AbstractControl,
  FormGroup,
  FormControl,
  ValidationErrors,
  ValidatorFn,
  Validators,
  UntypedFormBuilder,
} from '@angular/forms';
import { CountryConfiguration } from '@vendasta/address';
import { Country, State } from '@vendasta/country-state';
import { Observable, catchError, firstValueFrom, of } from 'rxjs';
import { AddressService } from '../address/address.service';
import { Address, AddressAutoComplete } from '../address/interface';
import { COUNTRY_CONTROL_TOKEN, COUNTRY_OPTIONS_TOKEN } from '../inputs/country-input.component';
import { Validator as SharedValidators } from '@vendasta/shared';
import { NotOnlyWhitespaceValidator } from '../../custom-validators';
import { Geo } from '@vendasta/account-group';

interface FormField {
  country: Country | string;
  province: State | string;
  address1: string;
  address2: string;
  city: string;
  zip: string;
}

const labelOverrides: Record<string, string> = {
  zip: 'postalCode',
  province: 'zone',
};
type TemplateFormField = { key: string; label: string; required: boolean };

@Component({
  selector: 'business-address-form-v2',
  templateUrl: './address-form-v2.component.html',
  styleUrls: ['./address-form-v2.component.scss'],
  standalone: false,
})
export class AddressFormV2Component {
  @ViewChild('addresstext') addresstext: any;
  @Input() showAddressPreview = false;
  @Input() set addressData(a: Address) {
    if (a) {
      this.__addressData = a;
      this.setInitialValues(a);
    }
  }
  @Input({ required: true }) set parentForm(form: FormGroup) {
    this.disabled = form.disabled;
    if (this.disabled) {
      this.editDisabled = true;
      this.formGroup.disable();
    }
    form.addControl('addressForm', this.formGroup);
  }
  @Input() requiredFields = true;

  @Output() geoLocationChanged: EventEmitter<Geo> = new EventEmitter();

  protected autoCompleteControls = ['country', 'province'];
  protected dynamicControls = ['address1', 'address2', 'city', 'zip'];
  protected __addressData: Address;
  protected editDisabled: boolean;

  // COUNTRY
  private readonly countryOptions$ = inject(COUNTRY_OPTIONS_TOKEN);
  private readonly addressService = inject(AddressService);
  private currentConfig: CountryConfiguration;
  protected countryRestriction: Country;

  // PROVINCE
  protected selectFirstProvince = false;
  protected provinces = [];

  protected readonly formGroup: FormGroup = new FormGroup({
    country: inject(COUNTRY_CONTROL_TOKEN),
  });

  protected formFields: TemplateFormField[] = [];
  private disabled: boolean;

  constructor(private fb: UntypedFormBuilder) {}

  private async updateForm(a: FormField, config = this.currentConfig) {
    await this.updateFormFields(config);
    removeOrphanedControls(this.formGroup, this.formFields);
    this.formGroup.patchValue(a);
  }

  private async setInitialValues(a: Address | null) {
    const countryOption = (await firstValueFrom(this.countryOptions$)).find((o) => o.code === a.country);
    if (!countryOption) {
      this.formGroup.controls.country.setValue(null, { emitEvent: false });
    }
    const config = await this.getUpdatedCountryConfig(countryOption);
    if (config) {
      const prov = this.addressService.getProvince(config.zones, a.province);
      const province: State | null = prov.code ? { code: prov.code, name: prov.name } : null;
      const formValues = { ...a, province, country: countryOption };
      this.countryRestriction = countryOption;
      return this.updateForm(formValues);
    }
  }

  protected async countryChanged(country: Country): Promise<void> {
    const config = await this.getUpdatedCountryConfig(country);
    const value = { ...this.formGroup.value, province: null };
    this.countryRestriction = country;
    return this.updateForm(value, config);
  }

  protected async getUpdatedCountryConfig(country: Country): Promise<CountryConfiguration> {
    if (this.currentConfig?.code === country.code) return this.currentConfig;
    this.currentConfig = await firstValueFrom(
      this.addressService.getCountry(country.code).pipe(catchError(() => of(null))),
    );

    this.provinces = this.currentConfig?.zones || [];
    return this.currentConfig;
  }

  private async updateFormFields(c: CountryConfiguration): Promise<void> {
    if (!c) return;

    const templates = await firstValueFrom(this.addressService.getOrderedFields(c.code));

    const requiredFields = c.formatting.required || [];
    this.formFields = templates.flat().reduce(
      (acc, field) => [
        ...acc,
        {
          key: field,
          label: c.labels[labelOverrides[field]] || c.labels[field],
          required: !!requiredFields.find((f) => f === field),
        },
      ],
      [],
    );

    this.formFields.forEach((field) => {
      const validators = this.getFieldValidators(field);
      const control = this.formGroup.get(field.key);
      if (control) {
        control.setValidators(validators);
        control.updateValueAndValidity();
      } else {
        this.formGroup.setControl(field.key, new FormControl('', validators));
      }
    });
    if (this.disabled) {
      this.formGroup.disable();
    }
  }

  private getFieldValidators(field: TemplateFormField): ValidatorFn[] {
    const validators = [];
    if (field.required) {
      validators.push(Validators.required);
      validators.push(NotOnlyWhitespaceValidator);
    }
    if (field.key === 'zip') {
      validators.push(SharedValidators.isValidPostalCode(this.currentConfig.code));
    }
    if (this.autoCompleteControls.indexOf(field.key) >= 0) validators.push(autoCompleteValidator);
    return validators;
  }

  provinceName(province: State): string {
    return province ? province.name : '';
  }

  updateFormViaAutoComplete(address: AddressAutoComplete): void {
    const changedValues = {
      ...this.formGroup.value,
      province: this.provinces.find((p) => p.name === address.province),
      city: address.city,
      zip: address.zip,
      address1: address.address1,
    };

    if (address.location?.latitude || address.location?.longitude) {
      this.geoLocationChanged.emit(address.location);
    }

    this.updateForm(changedValues);

    this.formGroup.get('address1').markAsDirty();
    this.formGroup.get('city').markAsDirty();
    this.formGroup.get('zip').markAsDirty();

    if (this.formGroup.get('province')) {
      this.formGroup.get('province').markAsDirty();
    }
  }
}

function formatAddress(formValues: FormField): Address {
  const { country } = formValues;

  const address: Address = {
    country: (country as Country)?.code || null,
    province: (formValues.province as State)?.code || null,
    address1: formValues.address1 || null,
    address2: formValues.address2 || null,
    city: formValues.city || null,
    zip: formValues.zip || null,
  };

  return address;
}

function autoCompleteValidator(control: AbstractControl): ValidationErrors | null {
  return !control.value || control.value?.code ? null : { invalidSelection: true };
}

function removeOrphanedControls(formGroup: FormGroup, fields: TemplateFormField[]) {
  const formFields = fields.map((field) => field.key);
  Object.keys(formGroup.controls).forEach((key) => {
    if (formFields.indexOf(key) < 0) {
      formGroup.removeControl(key);
    }
  });
}

@Pipe({
  name: 'businessAddressPipe',
  standalone: true,
})
export class businessAddressPipe implements PipeTransform {
  private readonly addressService = inject(AddressService);

  transform(formValues: FormField): Observable<string> {
    const { country } = formValues;
    if (!country) return of('');

    const address = formatAddress(formValues);
    return this.addressService.mailingAddress$(address);
  }
}

@Pipe({
  name: 'filterProvinces',
  standalone: true,
})
export class FilterProvincesPipe implements PipeTransform {
  transform(provinces: State[], searchTerm: string): State[] {
    if (!searchTerm) return provinces;
    return provinces.filter((province) => {
      const { name = '' } = province;
      return name.toLocaleLowerCase().indexOf(searchTerm.toLocaleLowerCase()) >= 0;
    });
  }
}
