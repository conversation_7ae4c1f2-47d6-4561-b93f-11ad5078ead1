<div class="title">
  {{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.TITLE' | translate }}
</div>

<form [formGroup]="formGroup">
  <mat-dialog-content>
    <mat-divider></mat-divider>
    <div class="section-title">
      {{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.SELECT_ACCOUNTS.LABEL' | translate }}
    </div>

    <mat-radio-group #rGroup class="choices" formControlName="whichAccounts">
      <mat-radio-button
        [value]="WhichAccountsOption.selected"
        radioGroup="rGroup"
        *ngIf="(selectedRowCount$ | async) > 0"
      >
        <strong>
          {{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.SELECT_ACCOUNTS.OPTIONS.SELECTED_ACCOUNTS' | translate }}
        </strong>
        | {{ this.selectedRowCount$ | async }}
        {{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.SELECT_ACCOUNTS.ACCOUNTS' | translate }}
      </mat-radio-button>

      <mat-radio-button [value]="WhichAccountsOption.visible" radioGroup="rGroup" cdkFocusInitial>
        <strong>
          {{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.SELECT_ACCOUNTS.OPTIONS.VISIBLE_ACCOUNTS' | translate }}
        </strong>
        | {{ this.displayedRowCount$ | async }}
        {{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.SELECT_ACCOUNTS.ACCOUNTS' | translate }}
      </mat-radio-button>

      <mat-radio-button [value]="WhichAccountsOption.all" radioGroup="rGroup">
        <strong>
          {{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.SELECT_ACCOUNTS.OPTIONS.ALL_ACCOUNTS' | translate }}
        </strong>
        | {{ this.totalResults$ | async }}
        {{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.SELECT_ACCOUNTS.ACCOUNTS' | translate }}
      </mat-radio-button>
    </mat-radio-group>

    <mat-divider></mat-divider>
    <div class="section-title">
      {{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.SELECT_LIST' | translate }}
    </div>
    <ng-container *ngIf="existingLists$ | async as list; else loadingSpinner">
      <glxy-form-field size="large">
        <mat-select [formControl]="selectedList">
          <mat-select-trigger>
            {{ selectedList.value === NewListSentinel ? NewListText : selectedList.value }}
          </mat-select-trigger>
          <ng-container *ngFor="let item of list">
            <mat-option [value]="item.value">
              {{ item.label }}
              <br />
              <span>{{ item.description }}</span>
            </mat-option>
          </ng-container>
        </mat-select>
      </glxy-form-field>
    </ng-container>

    <ng-template #loadingSpinner>
      <div class="stencil-shimmer shimmer"></div>
    </ng-template>

    <glxy-form-field size="large" *ngIf="showNewListNameInput$ | async">
      <glxy-label>{{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.NAME_NEW_LIST' | translate }}</glxy-label>
      <input type="text" [formControl]="newListName" matInput />
    </glxy-form-field>
  </mat-dialog-content>

  <mat-dialog-actions>
    <button (click)="close()" mat-button color="primary" type="cancel">
      {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
    </button>
    <button (click)="addToList()" mat-raised-button color="primary" type="submit" [disabled]="formInvalid$ | async">
      <ng-container *ngIf="showNewListNameInput$ | async; else existingList">
        {{ 'FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.CREATE_AND_ADD' | translate }}
      </ng-container>
      <ng-template #existingList>
        {{ 'COMMON.ACTION_LABELS.ADD' | translate }}
      </ng-template>
    </button>
  </mat-dialog-actions>
</form>
