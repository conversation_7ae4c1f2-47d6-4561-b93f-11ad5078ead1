import { Observable } from 'rxjs';

export interface ActionListsDependencies {
  totalPageSize?: number;
  listActionLists(
    partnerId: string,
    marketId: string,
    searchString?: string,
    pageIndex?: number,
    pageSize?: number,
  ): Observable<ActionLists>;
  addAccountsToActionList(
    partnerId: string,
    marketId: string,
    listName: string,
    accountGroupIds: string[],
  ): Observable<any>;
}

export interface ActionLists {
  actionLists: ActionList[];
  nextPage: number;
  totalActionListsCount: number;
}

export interface ActionList {
  id: string;
  name: string;
  accounts: number;
  inProgress: boolean;
}
