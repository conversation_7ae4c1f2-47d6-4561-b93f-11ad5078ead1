import { Inject, Injectable } from '@angular/core';
import { Observable, combineLatest } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { ActionListsInjectionToken, ActionListsServiceModule } from './account-lists-service.module';
import { ActionLists, ActionListsDependencies } from './interface';

@Injectable({
  providedIn: ActionListsServiceModule,
})
export class AccountListsService {
  constructor(
    @Inject('PARTNER_ID') readonly partnerId$: Observable<string>,
    @Inject('MARKET_ID') readonly marketId$: Observable<string>,
    @Inject(ActionListsInjectionToken) private readonly config: ActionListsDependencies,
  ) {}

  public addAccountsToActionList(listName: string, accounts: string[]): Observable<string> {
    return combineLatest([this.partnerId$, this.marketId$]).pipe(
      switchMap(([pid, market]) => this.config.addAccountsToActionList(pid, market, listName, accounts)),
    );
  }

  public listActionLists(searchString?: string, pageIndex = 0, pageSize = 100): Observable<ActionLists> {
    return combineLatest([this.partnerId$, this.marketId$]).pipe(
      switchMap(([pid, market]) => this.config.listActionLists(pid, market, searchString, pageIndex, pageSize)),
      map((resp) => {
        resp.actionLists = resp.actionLists.map((l) => {
          l.accounts = l.accounts || 0;
          return l;
        });
        return resp;
      }),
    );
  }
}
