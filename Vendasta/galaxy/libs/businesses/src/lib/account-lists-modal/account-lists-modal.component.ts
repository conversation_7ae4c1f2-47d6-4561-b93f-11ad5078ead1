import { CommonModule } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { Component, Inject, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { SelectInputOptionInterface } from '@vendasta/galaxy/input';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { PartnerApiService } from '@vendasta/partner';
import { UIKitModule, VaMaterialTableModule, VaStencilsModule } from '@vendasta/uikit';
import {
  DataSourceSettings,
  LoadRequest,
  TableDataService,
  VaFilteredMatTableService,
} from '@vendasta/va-filter2-table';
import assertNever from 'assert-never';
import { BehaviorSubject, EMPTY, Observable, Subject, combineLatest, of } from 'rxjs';
import { catchError, finalize, map, shareReplay, startWith, switchMap, take, tap } from 'rxjs/operators';
import { ActionListsInjectionToken } from './account-lists-service.module';
import { AccountListsService } from './account-lists.service';
import { ActionListsDependencies } from './interface';

export interface ModalTableDataService<T> extends TableDataService<T> {
  loadAllRowIDs(request: LoadRequest): Observable<string[]>;
}

export interface AccountListsDialogData {
  vaFilteredMatTableService: VaFilteredMatTableService<any>;
  tableDataService: ModalTableDataService<any>;
  currentTableDataSettings: DataSourceSettings;
}

enum WhichAccountsOption {
  visible = 'visible',
  selected = 'selected',
  all = 'all',
}

const NewListText = 'Create a new list';
const newListSentinel = new Object();

@Component({
  templateUrl: './account-lists-modal.component.html',
  styleUrls: ['./account-lists-modal.component.scss'],
  standalone: false,
})
export class AccountListsModalComponent {
  selectedRowCount$: Observable<number> = this.dialogData.vaFilteredMatTableService.selectedRows$.pipe(
    map((rows) => rows.length),
  );
  displayedRowCount$: Observable<number> = this.dialogData.vaFilteredMatTableService.displayedRows$.pipe(
    map((rows) => rows.length),
  );
  totalResults$: Observable<number> = this.dialogData.tableDataService.totalResults$;

  WhichAccountsOption = WhichAccountsOption;
  NewListSentinel = newListSentinel;
  NewListText = NewListText;

  existingLists$: Observable<SelectInputOptionInterface[]> = of(null).pipe(
    tap(() => this.actionPending$$.next(true)),
    switchMap(() => this.accountListsService.listActionLists()),
    tap(() => this.actionPending$$.next(false)),
    map((resp): SelectInputOptionInterface[] =>
      [{ value: newListSentinel, label: NewListText }].concat(
        resp.actionLists.map(
          (l): SelectInputOptionInterface => ({
            label: l.name,
            value: l.name, // addAccountsToActionList() needs the name, not the ID
            description: `${l.accounts} accounts`,
          }),
        ),
      ),
    ),
    catchError((err: HttpErrorResponse) => {
      console.error(`error (${err.status}) when listing lists`);
      this.alertService.openErrorSnack('ERRORS.SOMETHING_WENT_WRONG');
      return EMPTY;
    }),
  );

  whichAccounts = new UntypedFormControl(WhichAccountsOption.visible);
  selectedList = new UntypedFormControl('');
  newListName = new UntypedFormControl('');
  formGroup = new UntypedFormGroup({
    whichAccounts: this.whichAccounts,
    selectedList: this.selectedList,
    newListName: this.newListName,
  });
  showNewListNameInput$ = this.selectedList.valueChanges.pipe(
    map((value) => value === newListSentinel),
    startWith(false),
  );

  private readonly actionPending$$: Subject<boolean> = new BehaviorSubject<boolean>(false);
  readonly actionPending$ = this.actionPending$$.asObservable().pipe(shareReplay(1));

  formInvalid$: Observable<boolean> = combineLatest([this.actionPending$, this.formGroup.valueChanges]).pipe(
    map(([pending, { whichAccounts, selectedList, newListName }]) => {
      if (pending || !whichAccounts || !selectedList || (selectedList === newListSentinel && !newListName?.trim())) {
        return true;
      }
      return false;
    }),
    startWith(true),
  );

  constructor(
    public dialogRef: MatDialogRef<AccountListsModalComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: AccountListsDialogData,
    private readonly accountListsService: AccountListsService,
    private readonly alertService: SnackbarService,
    @Inject(ActionListsInjectionToken) private readonly config: ActionListsDependencies,
  ) {}

  getAccounts(which: WhichAccountsOption): Observable<string[]> {
    switch (which) {
      case WhichAccountsOption.all: {
        return this.dialogData.tableDataService.loadAllRowIDs({
          ...this.dialogData.currentTableDataSettings,
          pageIndex: 0,
          pageSize: this.config?.totalPageSize || 1000,
        });
      }
      case WhichAccountsOption.visible: {
        return this.dialogData.vaFilteredMatTableService.displayedRows$.pipe(
          map((rows) => rows.map((r) => r.businessId || r.accountGroupId)),
        );
      }
      case WhichAccountsOption.selected: {
        return this.dialogData.vaFilteredMatTableService.selectedRows$.pipe(
          map((rows) => rows.map((r) => r.businessId)),
        );
      }
      default: {
        return assertNever(which);
      }
    }
  }

  addToList(): void {
    this.actionPending$$.next(true);

    const agids$ = this.getAccounts(this.whichAccounts.value);

    let listName = this.selectedList.value;
    if (listName === newListSentinel) {
      listName = this.newListName.value;
    }

    agids$
      .pipe(
        switchMap((agids) => this.accountListsService.addAccountsToActionList(listName, agids).pipe(map(() => agids))),
        take(1),
        tap((agids) =>
          this.alertService.openSuccessSnack('FRONTEND.BUSINESSES.ADD_ACCOUNTS_TO_LISTS.SUCCESS', {
            interpolateTranslateParams: {
              accounts: agids.length.toString(),
              list: listName,
            },
          }),
        ),
        catchError((err: HttpErrorResponse) => {
          console.error(`error (${err.status}) when adding to list ${listName}`);
          this.alertService.openErrorSnack('ERRORS.SOMETHING_WENT_WRONG');
          return EMPTY;
        }),
        finalize(() => this.actionPending$$.next(false)),
      )
      .subscribe(() => {
        this.dialogRef.close();
      });
  }

  close(): void {
    this.dialogRef.close();
  }
}

@NgModule({
  declarations: [AccountListsModalComponent],
  imports: [
    CommonModule,
    MatDialogModule,
    MatDividerModule,
    MatRadioModule,
    MatSortModule,
    MatTableModule,
    VaMaterialTableModule,
    VaStencilsModule,
    MatIconModule,
    UIKitModule,
    MatToolbarModule,
    GalaxyAlertModule,
    GalaxyPageModule,
    MatButtonModule,
    MatMenuModule,
    MatTooltipModule,
    MatPaginatorModule,
    TranslateModule,
    MatCardModule,
    MatProgressBarModule,
    FormsModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatSelectModule,
  ],
  providers: [PartnerApiService],
})
export class AccountListsModalModule {}
