import { InjectionToken, ModuleWithProviders, NgModule } from '@angular/core';
import { ActionListsDependencies } from './interface';

export const ActionListsInjectionToken = new InjectionToken<ActionListsDependencies>(
  'dependencies for the action-lists',
);

@NgModule()
export class ActionListsServiceModule {
  static forRoot(config: InjectionToken<ActionListsDependencies>): ModuleWithProviders<ActionListsServiceModule> {
    return {
      ngModule: ActionListsServiceModule,
      providers: [
        {
          provide: ActionListsInjectionToken,
          useExisting: config,
        },
      ],
    };
  }
}
