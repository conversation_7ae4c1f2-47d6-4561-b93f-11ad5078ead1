<h1 id="address-component">Product Activation Prerequisites Form</h1>

<p>
  The
  <em>Product Activation Prerequisites form</em>
  component contains the collection of account group properties that can be directly edited from the product order page.
  Any changes to the business profile activation form are persisted to the account group for the given product
  activations.
</p>

<p>
  Account group properties that are set in the
  <em>requiredBusinessData</em>
  struct as part of the
  <em>restrictions</em>
  on MarketPlaceApps are surfaced on this component.
</p>

<p>
  When adding a new account group property to this component, be sure to update the
  <em>isFormValid</em>
  method to include the new control so the Submit action runs proper validation of required fields.
</p>
