import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { VaFormsModule } from '@vendasta/forms';
import { ProductActivationPrereqFormComponent } from './product-activation-prereq-form.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { LexiconModule } from '@galaxy/lexicon';
import baseTranslation from '../assets/i18n/en_devel.json';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';

@NgModule({
  declarations: [ProductActivationPrereqFormComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: 'common/businesses',
      baseTranslation: baseTranslation,
    }),
    MatExpansionModule,
    MatFormFieldModule,
    MatIconModule,
    VaFormsModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    GalaxyFormFieldModule,
  ],
  exports: [ProductActivationPrereqFormComponent],
  providers: [],
})
export class ProductActivationPrereqFormModule {}
