<ng-container *ngIf="loading$ | async; else loaded">
  <div class="stencil-shimmer"></div>
  <div class="stencil-shimmer"></div>
</ng-container>
<ng-template #loaded>
  <ng-container *ngIf="(hasRestrictions$ | async) && businessActivationForm">
    <ng-container *ngIf="sectionConfig$ | async as sectionConfig">
      <mat-card appearance="outlined" class="title-container">
        <mat-card-header>
          <mat-card-title>
            {{ 'FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.ACCOUNT_INFORMATION' | translate }}
          </mat-card-title>
          <mat-card-subtitle>
            {{ 'FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.ACCOUNT_INFORMATION_SUBTITLE' | translate }}
          </mat-card-subtitle>
        </mat-card-header>
      </mat-card>
      <ng-template
        [ngTemplateOutlet]="content"
        [ngTemplateOutletContext]="{ sectionConfig: sectionConfig }"
      ></ng-template>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #content let-sectionConfig="sectionConfig">
  <form [formGroup]="businessActivationForm" (ngSubmit)="onSubmit()" (keydown.enter)="$event.preventDefault()">
    <mat-expansion-panel [expanded]="expanded">
      <mat-expansion-panel-header>
        <mat-panel-title class="expansion-title-container">
          <span class="expansion-title-text">
            <ng-container *ngIf="!isReadOnly; else infoIcon">
              <span class="material-icons warning">warning</span>
            </ng-container>
            <ng-template #infoIcon>
              <span class="material-icons info">info_outline</span>
            </ng-template>
            <span>
              {{ 'FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.FULL_BUSINESS_PROFILE' | translate }}
            </span>
          </span>
        </mat-panel-title>
        <mat-panel-description>
          <ng-container *ngIf="!isReadOnly; else readOnlyPanelMessage">
            <span class="expansion-title-subtext">
              {{ 'FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.FULL_BUSINESS_PROFILE_DESCRIPTION' | translate }}
            </span>
          </ng-container>
          <ng-template #readOnlyPanelMessage>
            <span class="expansion-title-subtext">
              {{
                'FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.FULL_BUSINESS_PROFILE_READ_ONLY_DESCRIPTION' | translate
              }}
            </span>
          </ng-template>
        </mat-panel-description>
      </mat-expansion-panel-header>
      <forms-business-hours
        data-testid="hoo-input"
        *ngIf="sectionConfig.showHoO"
        [loading$]="loading$"
        [control]="businessActivationForm.get('businessHours')"
      ></forms-business-hours>
      <glxy-form-field *ngIf="sectionConfig.showLongDescription" class="print-hide">
        <glxy-label>
          {{ 'FRONTEND.BUSINESSES.LONG_DESCRIPTION' | translate }}
          <span *ngIf="!orderFormOptions?.bypassRequiredQuestions">*</span>
        </glxy-label>
        <textarea
          matInput
          #longDescription
          matTextareaAutosize
          matAutosizeMinRows="3"
          maxlength="750"
          formControlName="description"
          data-testid="description-input"
        ></textarea>
        <glxy-hint align="end">{{ longDescription.value.length }} / 750</glxy-hint>
      </glxy-form-field>
      <div *ngIf="sectionConfig.showLongDescription" class="print">
        <div class="label">
          {{ 'FRONTEND.BUSINESSES.LONG_DESCRIPTION' | translate }}
        </div>
        <div class="answer">
          {{ businessActivationForm.controls.description?.value }}
        </div>
      </div>
      <glxy-form-field *ngIf="sectionConfig.showDescription" class="print-hide">
        <glxy-label>
          {{ 'FRONTEND.BUSINESSES.SHORT_DESCRIPTION' | translate }}
          <span *ngIf="!orderFormOptions?.bypassRequiredQuestions">*</span>
        </glxy-label>
        <textarea
          matInput
          #shortDescription
          matTextareaAutosize
          matAutosizeMinRows="3"
          maxlength="200"
          formControlName="shortDescription"
          data-testid="short-description-input"
        ></textarea>
        <glxy-hint align="end">{{ shortDescription.value.length }} / 200</glxy-hint>
      </glxy-form-field>
      <div *ngIf="sectionConfig.showDescription" class="print">
        <div class="label">
          {{ 'FRONTEND.BUSINESSES.SHORT_DESCRIPTION' | translate }}
        </div>
        <div class="answer">
          {{ businessActivationForm.controls.shortDescription?.value }}
        </div>
      </div>
      <forms-va-input-repeated
        *ngIf="sectionConfig.showServices"
        data-testid="services-input"
        [controlArray]="businessActivationForm.get('servicesOffered')"
        [tooltip]="'FRONTEND.BUSINESSES.TOOLTIPS.SERVICES_OFFERED_TOOLTIP' | translate"
        [addText]="'FRONTEND.BUSINESSES.ADD_SERVICE' | translate"
        [label]="servicesPlaceholder"
        [maxFields]="15"
        [errorMessages]="'FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.INVALID_SERVICES_OFFERED_FIELD' | translate"
        [disableAdd]="isReadOnly"
      ></forms-va-input-repeated>
      <forms-va-input-repeated
        *ngIf="sectionConfig.showBrands"
        [controlArray]="businessActivationForm.get('brandsCarried')"
        data-testid="brands-carried-input"
        [tooltip]="'FRONTEND.BUSINESSES.TOOLTIPS.BRANDS_CARRIED_TOOLTIP' | translate"
        [addText]="'FRONTEND.BUSINESSES.ADD_BRAND' | translate"
        [label]="brandsPlaceholder"
        [maxFields]="15"
        [errorMessages]="'FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.INVALID_BRANDS_CARRIED_FIELD' | translate"
        [disableAdd]="isReadOnly"
      ></forms-va-input-repeated>
      <div class="form-footer">
        <button mat-flat-button color="primary" type="submit" *ngIf="!isReadOnly" [disabled]="submitting$ | async">
          {{ 'FRONTEND.BUSINESSES.SAVE' | translate }}
        </button>
        <span
          *ngIf="(hasUnsavedChanges$ | async) && !isReadOnly"
          class="unsaved-changes"
          data-testid="unsaved-changes-warning"
        >
          {{ 'FRONTEND.BUSINESSES.UNSAVED_CHANGES' | translate }}
        </span>
      </div>
    </mat-expansion-panel>
  </form>
</ng-template>
