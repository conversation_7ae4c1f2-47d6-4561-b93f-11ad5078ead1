import { HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import {
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  AccountGroup,
  AccountGroupService,
  ProjectionFilter,
  RichDataUpdateOperation,
  UpdateOperations,
} from '@galaxy/account-group';
import {
  BusinessHoursUpdateOperation,
  UpdateOperations as LPUpdateOperations,
  ListingProfileService,
  MergeHours,
  SplitHours,
} from '@galaxy/listing-profile-common';
import { PartnerApiService } from '@galaxy/marketplace-apps';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  BusinessHours,
  ListingProfile,
  RegularHoursPeriod,
  ProjectionFilter as LPProjectionFilter,
} from '@vendasta/listing-products';
import { OrderFormOptionsInterface } from '@vendasta/store';
import equal from 'fast-deep-equal';
import { BehaviorSubject, EMPTY, Observable, Subscription, combineLatest, of } from 'rxjs';
import { catchError, debounceTime, distinctUntilChanged, filter, map, startWith, switchMap, tap } from 'rxjs/operators';
import { arrayAllTruthyItemsRequired, totalLengthOfRepeatedFieldValidator } from './validators';

interface BusinessSectionConfig {
  showHoO: boolean;
  showServices: boolean;
  showBrands: boolean;
  showDescription: boolean;
  showLongDescription: boolean;
}

@Component({
  selector: 'business-product-activation-prereq-form',
  templateUrl: './product-activation-prereq-form.component.html',
  styleUrls: ['./product-activation-prereq-form.component.scss'],
  standalone: false,
})
export class ProductActivationPrereqFormComponent implements OnInit, OnDestroy {
  @Input() businessId: string;
  @Input() expanded = false;

  protected isReadOnly = false;
  private _orderFormOptions: OrderFormOptionsInterface = {};
  @Input()
  set orderFormOptions(value: OrderFormOptionsInterface) {
    this._orderFormOptions = value;
    this.isReadOnly = value?.readOnly || false;
  }
  get orderFormOptions(): OrderFormOptionsInterface {
    return this._orderFormOptions;
  }

  @Input()
  set productIds(productIds: string[]) {
    this.productIds$$.next(productIds);
  }

  @Output() hasBusinessProductActivationPrereqFormEvent: EventEmitter<boolean> = new EventEmitter();

  private productIds$$: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);
  private sectionConfig$$: BehaviorSubject<BusinessSectionConfig> = new BehaviorSubject<BusinessSectionConfig>(null);
  private submitting$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  private business$: Observable<AccountGroup>;
  private listingProfile$: Observable<ListingProfile>;
  readonly productIds$: Observable<string[]> = this.productIds$$.asObservable();
  readonly sectionConfig$: Observable<BusinessSectionConfig> = this.sectionConfig$$.asObservable();
  readonly submitting$: Observable<boolean> = this.submitting$$.asObservable();
  readonly loading$: Observable<boolean> = this.loading$$.asObservable();
  hasRestrictions$: Observable<boolean>;

  private subscriptions: Subscription[] = [];

  private businessActivationForm$$ = new BehaviorSubject<UntypedFormGroup | null>(null);
  private set businessActivationForm(form: UntypedFormGroup) {
    this.businessActivationForm$$.next(form);
  }
  public get businessActivationForm(): UntypedFormGroup | null {
    return this.businessActivationForm$$.getValue();
  }

  servicesPlaceholder: string;
  brandsPlaceholder: string;

  currentSubmittedFormValues: any;
  public hasUnsavedChanges$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public hasUnsavedChanges$ = this.hasUnsavedChanges$$.asObservable();
  public isValid$: Observable<boolean>;

  public static checkChipFieldValidity(chipField: any): boolean {
    if (!chipField) {
      return false;
    }

    // the chips component's empty state is a list with one empty string
    if (chipField instanceof Array) {
      if (chipField.length === 1 && chipField[0] === '') {
        return false;
      }
    }
    return true;
  }

  constructor(
    private fb: UntypedFormBuilder,
    private accountGroupService: AccountGroupService,
    private listingProfileService: ListingProfileService,
    private partnerApiService: PartnerApiService,
    private translateService: TranslateService,
    private alertService: SnackbarService,
  ) {}

  ngOnInit(): void {
    this.servicesPlaceholder = this.orderFormOptions?.bypassRequiredQuestions
      ? this.translateService.instant('FRONTEND.BUSINESSES.SERVICES_OFFERED')
      : this.translateService.instant('FRONTEND.BUSINESSES.SERVICES_OFFERED') + ' *';
    this.brandsPlaceholder = this.orderFormOptions?.bypassRequiredQuestions
      ? this.translateService.instant('FRONTEND.BUSINESSES.BRANDS_CARRIED')
      : this.translateService.instant('FRONTEND.BUSINESSES.BRANDS_CARRIED') + ' *';

    this.business$ = this.accountGroupService
      .get(
        this.businessId,
        new ProjectionFilter({
          richData: true,
          accountGroupExternalIdentifiers: true,
        }),
      )
      .pipe(catchError(() => of(new AccountGroup())));

    this.listingProfile$ = this.listingProfileService
      .get(
        this.businessId,
        new LPProjectionFilter({
          businessHours: true,
        }),
      )
      .pipe(catchError(() => of(new ListingProfile())));

    this.getSectionConfig$();

    this.hasRestrictions$ = this.sectionConfig$.pipe(
      map((resp) => {
        if (
          resp &&
          (resp.showDescription || resp.showLongDescription || resp.showHoO || resp.showServices || resp.showBrands)
        ) {
          return true;
        } else {
          this.hasBusinessProductActivationPrereqFormEvent.emit(false);
          return false;
        }
      }),
    );

    this.isValid$ = this.businessActivationForm$$.asObservable().pipe(
      filter((form) => form !== null),
      switchMap((form) => {
        return form.statusChanges.pipe(
          startWith(form.status),
          debounceTime(250), // wait for form to stabilize before emitting
          map((status) => status !== 'INVALID'),
        );
      }),
      map((formValid) => {
        let hasSubmittedRequiredValues = true;
        for (const key of Object.keys(this.currentSubmittedFormValues)) {
          const formValue = this.currentSubmittedFormValues[key];
          if (!ProductActivationPrereqFormComponent.checkChipFieldValidity(formValue)) {
            hasSubmittedRequiredValues = false;
            break;
          }
        }
        return formValid && hasSubmittedRequiredValues;
      }),
      distinctUntilChanged(),
    );

    this.subscriptions.push(
      combineLatest([this.business$, this.listingProfile$, this.sectionConfig$])
        .pipe(
          map(([business, listingProfile, sectionConfig]) => {
            const shortDescription = sectionConfig?.showDescription
              ? (business && business.richData && business.richData.shortDescription) || ''
              : null;
            const longDescription = sectionConfig?.showLongDescription
              ? (business && business.richData && business.richData.description) || ''
              : null;

            let businessHours: RegularHoursPeriod[] = null;
            if (sectionConfig?.showHoO) {
              const generalHours = listingProfile.businessHours?.find((hours) => hours.hoursTypeId === 'GENERAL');
              businessHours = MergeHours(generalHours?.regularHours || []);
            }

            let servicesOffered = null;
            if (sectionConfig?.showServices) {
              servicesOffered = [''];
              if (business?.richData?.servicesOffered && business.richData.servicesOffered.length) {
                servicesOffered = business.richData.servicesOffered;
              }
            }

            let brandsCarried = null;
            if (sectionConfig?.showBrands) {
              brandsCarried = [''];
              if (business?.richData?.brandsCarried && business.richData.brandsCarried.length) {
                brandsCarried = business.richData.brandsCarried;
              }
            }

            const validators: ValidatorFn[] = [];
            const arrayValidators: ValidatorFn[] = [];
            if (!this.orderFormOptions?.bypassRequiredQuestions) {
              validators.push(Validators.required);
              arrayValidators.push(arrayAllTruthyItemsRequired());
            }

            const businessProfileForm = new UntypedFormGroup({});
            if (shortDescription !== null) {
              businessProfileForm.addControl('shortDescription', this.fb.control(shortDescription, validators));
            }
            if (longDescription !== null) {
              businessProfileForm.addControl('description', this.fb.control(longDescription, validators));
            }
            if (businessHours !== null) {
              businessProfileForm.addControl('businessHours', this.fb.control(businessHours, []));
            }
            if (servicesOffered !== null) {
              businessProfileForm.addControl(
                'servicesOffered',
                this.fb.array(servicesOffered, [
                  Validators.compose([
                    Validators.minLength(1),
                    Validators.maxLength(15),
                    ...arrayValidators,
                    totalLengthOfRepeatedFieldValidator(
                      256,
                      this.translateService.instant('FRONTEND.BUSINESSES.SERVICES_OFFERED'),
                    ),
                  ]),
                ]),
              );
            }
            if (brandsCarried !== null) {
              businessProfileForm.addControl(
                'brandsCarried',
                this.fb.array(
                  brandsCarried,
                  Validators.compose([
                    Validators.minLength(1),
                    Validators.maxLength(15),
                    ...arrayValidators,
                    totalLengthOfRepeatedFieldValidator(
                      256,
                      this.translateService.instant('FRONTEND.BUSINESSES.BRANDS_CARRIED'),
                    ),
                  ]),
                ),
              );
            }
            this.currentSubmittedFormValues = JSON.parse(JSON.stringify(businessProfileForm.value));
            if (this.isReadOnly) {
              businessProfileForm.disable();
            }
            return businessProfileForm;
          }),
        )
        .subscribe((form) => {
          this.businessActivationForm = form;
          if (this.orderFormOptions?.readOnly && this.businessActivationForm.controls.businessHours) {
            this.businessActivationForm.controls.businessHours.disable();
          }

          // Track changes
          Object.keys(this.businessActivationForm.controls).map((controlName) => {
            return this.subscriptions.push(
              this.businessActivationForm.controls[controlName].valueChanges
                .pipe(
                  debounceTime(500),
                  tap((changes) =>
                    this.hasUnsavedChanges$$.next(!equal(this.currentSubmittedFormValues[controlName], changes)),
                  ),
                )
                .subscribe(),
            );
          });
        }),
    );
  }

  getSectionConfig$(): void {
    this.loading$$.next(true);
    combineLatest([this.productIds$, this.business$])
      .pipe(
        switchMap(([productIds, business]) => {
          if (!productIds || productIds.length === 0 || business?.deleted || !business.externalIdentifiers) {
            this.loading$$.next(false);
            return of(EMPTY);
          }
          return this.partnerApiService.getMultiApp({
            partnerId: business.externalIdentifiers.partnerId,
            marketId: business.externalIdentifiers.marketId,
            appKeys: productIds.map((appId) => {
              return { appId: appId };
            }),
            includeNotEnabled: true,
          });
        }),
        filter((resp: any) => Boolean(resp?.apps)),
        map((resp) => resp.apps.map((app) => app?.activationInformation?.requiredBusinessData)),
        map((configs) => configs.filter((config) => config)),
        map((configs) =>
          configs.reduce(
            (acc, appConfig) => {
              acc.showHoO = acc.showHoO || appConfig.hours;
              acc.showDescription = acc.showDescription || appConfig.descriptionShort;
              acc.showLongDescription = acc.showLongDescription || appConfig.descriptionLong;
              acc.showBrands = acc.showBrands || appConfig.brands;
              acc.showServices = acc.showServices || appConfig.services;
              return acc;
            },
            {
              showHoO: false,
              showServices: false,
              showBrands: false,
              showDescription: false,
              showLongDescription: false,
            } as BusinessSectionConfig,
          ),
        ),
        catchError((err) => {
          console.error('Error caught: ', err);
          this.loading$$.next(false);
          return EMPTY;
        }),
      )
      .subscribe((config) => {
        this.sectionConfig$$.next(config);
        this.loading$$.next(false);
      });
  }

  private setAsTouched(form: UntypedFormGroup): void {
    form.markAsTouched();
    const controls = form.controls;
    for (const i of Object.keys(controls)) {
      if (controls[i] instanceof UntypedFormControl) {
        controls[i].markAsTouched();
      } else if (controls[i] instanceof UntypedFormGroup) {
        this.setAsTouched(controls[i] as UntypedFormGroup);
      } else if (controls[i] instanceof UntypedFormArray) {
        (controls[i] as UntypedFormArray).controls.forEach((c) => c.markAsTouched());
      }
      controls[i].updateValueAndValidity({ emitEvent: false });
    }
    form.updateValueAndValidity({ emitEvent: false });
  }

  onSubmit(): void {
    if (this.businessActivationForm.pristine) {
      this.alertService.openErrorSnack('FRONTEND.BUSINESSES.NO_CHANGES_TO_SAVE');
      return;
    }

    if (!this.businessActivationForm.valid) {
      let errorMsg = this.translateService.instant('FRONTEND.BUSINESSES.INVALID_SUBMISSION') + ': ';
      if (this.businessActivationForm.get('servicesOffered')?.invalid) {
        errorMsg +=
          this.translateService.instant('FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.INVALID_SERVICES_OFFERED_FIELD') +
          ' ';
      }
      if (this.businessActivationForm.get('brandsCarried')?.invalid) {
        if (this.businessActivationForm.get('servicesOffered')?.invalid) {
          errorMsg += '| ';
        }
        errorMsg += this.translateService.instant(
          'FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.INVALID_BRANDS_CARRIED_FIELD',
        );
      }
      this.alertService.openErrorSnack(errorMsg);
      return;
    }

    const data = this.businessActivationForm.value;
    const updateOperations = new UpdateOperations();
    let listingProfileUpdate$: Observable<HttpResponse<null>> = of(null);

    if (this.businessActivationForm.controls.businessHours?.dirty) {
      const lpUpdateOperations = new LPUpdateOperations();
      lpUpdateOperations.addUpdateOperation(
        new BusinessHoursUpdateOperation(
          new BusinessHours({
            regularHours: SplitHours(data.businessHours),
            hoursTypeId: 'GENERAL',
          }),
        ),
      );
      listingProfileUpdate$ = this.listingProfileService.bulkUpdate(this.businessId, lpUpdateOperations);
    }

    const richDataUpdateOperation: RichDataUpdateOperation = new RichDataUpdateOperation();
    if (this.businessActivationForm.controls.brandsCarried?.dirty) {
      richDataUpdateOperation.brandsCarried = data.brandsCarried;
    }
    if (this.businessActivationForm.controls.servicesOffered?.dirty) {
      richDataUpdateOperation.servicesOffered = data.servicesOffered;
    }
    if (this.businessActivationForm.controls.shortDescription?.dirty) {
      richDataUpdateOperation.shortDescription = data.shortDescription;
    }
    if (this.businessActivationForm.controls.description?.dirty) {
      richDataUpdateOperation.description = data.description;
    }

    updateOperations.addUpdateOperation(richDataUpdateOperation);

    this.submitting$$.next(true);
    const agUpdate$ = this.accountGroupService.bulkUpdate(this.businessId, updateOperations);
    this.subscriptions.push(
      combineLatest([agUpdate$, listingProfileUpdate$]).subscribe({
        next: () => {
          this.alertService.openSuccessSnack('FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.SUCCESS_MESSAGE');
          this.currentSubmittedFormValues = JSON.parse(JSON.stringify(data));
          this.businessActivationForm.updateValueAndValidity();
          this.hasUnsavedChanges$$.next(false);
        },
        error: (err) => {
          console.error('Error saving to account group %s; error %s', this.businessId, err);
          this.alertService.openErrorSnack('FRONTEND.BUSINESSES.PRODUCT_ACTIVATION_FORM.ERROR_MESSAGE');
          this.submitting$$.next(false);
        },
        complete: () => {
          this.submitting$$.next(false);
        },
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }
}
