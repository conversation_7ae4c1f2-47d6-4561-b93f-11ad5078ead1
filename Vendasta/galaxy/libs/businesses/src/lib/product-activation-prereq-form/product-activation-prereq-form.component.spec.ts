import { ProductActivationPrereqFormComponent } from './product-activation-prereq-form.component';

describe('ProductActivationPrereqFormComponent', () => {
  describe('checkChipFieldValidity', () => {
    it('should return false when field is null', () => {
      expect(ProductActivationPrereqFormComponent.checkChipFieldValidity(null)).toBeFalsy();
    });

    it('should return false when field is undefined', () => {
      expect(ProductActivationPrereqFormComponent.checkChipFieldValidity(undefined)).toBeFalsy();
    });

    it('should return false when field is an array with a single empty string', () => {
      expect(ProductActivationPrereqFormComponent.checkChipFieldValidity([''])).toBeFalsy();
    });

    it('should return true when field is an array with multiple empty strings', () => {
      expect(ProductActivationPrereqFormComponent.checkChipFieldValidity(['', '', ''])).toBeTruthy();
    });

    it('should return true when there is an array with a non-empty string value', () => {
      expect(ProductActivationPrereqFormComponent.checkChipFieldValidity(['test'])).toBeTruthy();
    });

    it('should return true when a non-array value is passed', () => {
      expect(ProductActivationPrereqFormComponent.checkChipFieldValidity(25)).toBeTruthy();
    });
  });
});
