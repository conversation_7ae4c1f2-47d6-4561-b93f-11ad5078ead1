@use 'design-tokens' as *;

.stencil-shimmer {
  width: 100%;
  height: 100px;
}

.title-container {
  padding-bottom: 0;
  border-bottom: 0;
}

.mat-expansion-panel-header-title {
  flex-grow: 0;
}

.expansion-title-text {
  display: flex;

  .material-icons {
    margin-right: $spacing-2;
  }
}

.warning {
  color: $yellow;
}

.info {
  color: $tertiary-font-color;
}

.unsaved-changes {
  color: $error-text-color;
  padding-left: $spacing-3;
}

@media print {
  button {
    display: none;
  }

  :host ::ng-deep .mat-expansion-indicator {
    display: none !important;
  }

  .mat-mdc-card {
    break-inside: avoid;
    box-shadow: none;
    display: block;
    border: 1px solid $light-grey !important;
    border-radius: 0 !important;
    overflow: visible !important;
  }

  .mat-mdc-card-title {
    font-size: 32px;
  }

  .mat-mdc-form-field-hint {
    display: none;
  }

  .mat-expansion-panel-content {
    overflow: visible !important;
    display: block !important;
    visibility: visible !important;
    height: inherit !important;
  }

  .mat-expansion-panel {
    overflow: visible !important;
    display: block;
    border-top: none;
  }

  forms-input-repeated {
    page-break-inside: avoid;
    display: block;
  }
}

.print-hide {
  @media print {
    display: None;
  }
}

.print {
  display: none;
  @media print {
    display: flex;
    outline: 1px solid;
    margin-top: 1px;
    margin-left: 1px;
    page-break-inside: avoid;

    .label {
      flex-basis: 30%;
      padding: 8px;
      font-weight: 500;
      border-right: 1px solid black;
    }

    .answer {
      flex-basis: 70%;
      padding: 8px;
    }
  }
}
