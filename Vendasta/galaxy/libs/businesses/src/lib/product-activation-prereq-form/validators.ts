import { AbstractControl, ValidatorFn } from '@angular/forms';

export function totalLengthOfRepeatedFieldValidator(maxLength: number, friendlyName: string): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } => {
    if (!control.value) {
      return null;
    }
    if (control.value.length === 0) {
      return null;
    }

    const value = control.value.filter((val: string) => !!val).join(',');
    return value.length > maxLength
      ? {
          lengthOfRepeatedField: {
            maxLength: maxLength,
            friendlyName: friendlyName,
          },
        }
      : null;
  };
}

// Validation to ensure that all items in an array control have truthy values
export function arrayAllTruthyItemsRequired(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } => {
    const values = (control.value || []).filter((v: any) => !!v);
    return values.length !== control.value.length ? { required: '' } : null;
  };
}
