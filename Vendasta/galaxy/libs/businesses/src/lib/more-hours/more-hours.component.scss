@use 'design-tokens' as *;

.existing-chip {
  min-height: $spacing-4;
  background-color: $success-background-color !important;
  font-size: $font-preset-4-size;
}

.add-chip {
  min-height: $spacing-4;
  background-color: $info-background-color !important;
  font-size: $font-preset-4-size;
}

:host ::ng-deep .mdc-evolution-chip__action--presentational {
  cursor: pointer !important;
}

.empty-hours {
  display: flex;
  flex-direction: column;
  width: 100%;
  justify-content: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.57);

  img {
    height: 145px;
    margin-bottom: $spacing-4;
  }
}
