<mat-card appearance="outlined">
  <mat-card-header>
    <mat-card-title>
      {{ 'FRONTEND.FORMS.MORE_HOURS.TITLE' | translate }}
    </mat-card-title>
  </mat-card-header>
  <mat-card-content [ngClass]="{ 'disabled-overlay': editDisabled }">
    <ng-container *ngIf="(loading$ | async) === false; else loading">
      <ng-container *ngIf="moreHoursTypes?.length > 0; else empty">
        <ng-container *ngIf="setTypes.size > 0">
          <h3>{{ 'FRONTEND.BUSINESSES.ACTIVE' | translate }}</h3>
          <mat-chip-set>
            <ng-container *ngFor="let moreHourType of setTypes | keyvalue">
              <mat-chip
                [disabled]="editDisabled"
                [ngClass]="{ 'existing-chip': !editDisabled }"
                (click)="openEditDialog(moreHourType.value)"
              >
                {{ moreHourType.value.localizedDisplayName }}
              </mat-chip>
            </ng-container>
          </mat-chip-set>
        </ng-container>

        <ng-container *ngIf="unsetTypes.size > 0">
          <h3>{{ 'FRONTEND.FORMS.INPUT.ADD' | translate }}</h3>
          <mat-chip-set>
            <ng-container *ngFor="let moreHourType of unsetTypes | keyvalue">
              <mat-chip
                [disabled]="editDisabled"
                [ngClass]="{ 'add-chip': !editDisabled }"
                (click)="openEditDialog(moreHourType.value)"
              >
                <mat-icon matChipAvatar>add</mat-icon>
                {{ moreHourType.value.localizedDisplayName }}
              </mat-chip>
            </ng-container>
          </mat-chip-set>
        </ng-container>
      </ng-container>
    </ng-container>
  </mat-card-content>
</mat-card>

<ng-template #loading>
  <glxy-loading-spinner></glxy-loading-spinner>
</ng-template>

<ng-template #empty>
  <div class="empty-hours">
    <img
      src="https://vstatic-prod.apigateway.co/listing-builder-client/assets/images/listing-profile-more-hours-empty.svg"
      alt="Empty More Hours"
    />
    <div>
      {{ 'FRONTEND.FORMS.MORE_HOURS.EMPTY' | translate }}
    </div>
  </div>
</ng-template>
