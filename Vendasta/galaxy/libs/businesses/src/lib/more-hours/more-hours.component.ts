import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { RegularHoursPeriod } from '@vendasta/listing-products';
import { EditRegularHoursDialogComponent } from '@vendasta/forms';
import { filter, Observable, Subscription } from 'rxjs';

export interface MoreHoursType {
  hoursTypeId: string;
  displayName: string;
  localizedDisplayName: string;
}

@Component({
  selector: 'business-profile-more-hours',
  templateUrl: './more-hours.component.html',
  styleUrls: ['./more-hours.component.scss'],
  standalone: false,
})
export class MoreHoursComponent implements OnInit, OnDestroy {
  @Input() parentFormGroup: UntypedFormGroup;
  @Input() appearance: 'outline' | 'standard' = 'standard';
  @Input() markedAsRequired = false;
  @Input() moreHoursTypes: MoreHoursType[] = [];
  @Input() loading$: Observable<boolean>;
  @Input() editDisabled = false;
  @Output() changed = new EventEmitter();

  subscriptions: Subscription[] = [];

  private editDialogRef: MatDialogRef<EditRegularHoursDialogComponent>;

  public setTypes: Map<string, MoreHoursType> = new Map<string, MoreHoursType>();
  public unsetTypes: Map<string, MoreHoursType> = new Map<string, MoreHoursType>();

  constructor(
    private dialog: MatDialog,
    private fb: UntypedFormBuilder,
    private translateService: TranslateService,
  ) {}

  ngOnInit(): void {
    this.subscriptions.push(
      this.loading$.pipe(filter((loading) => !loading)).subscribe(() => {
        this.moreHoursTypes.forEach((moreHoursType) => {
          const existingHours: RegularHoursPeriod[] = this.parentFormGroup.get(moreHoursType.hoursTypeId)?.value;
          if (existingHours?.length > 0) {
            this.setTypes.set(moreHoursType.hoursTypeId, moreHoursType);
          } else {
            const control = this.fb.control({ value: [] });
            this.unsetTypes.set(moreHoursType.hoursTypeId, moreHoursType);
            this.parentFormGroup.addControl(moreHoursType.hoursTypeId, control);
            const hoursTypeId = this.parentFormGroup.get(moreHoursType.hoursTypeId);
            if (hoursTypeId) {
              hoursTypeId.markAsPristine();
            }
          }
        });
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  openEditDialog(moreHoursType: MoreHoursType): void {
    const control = this.parentFormGroup.get(moreHoursType.hoursTypeId);
    if (control.disabled || this.editDisabled) {
      return;
    }

    control.markAsTouched();
    this.dialog.closeAll();
    this.editDialogRef = this.dialog.open(EditRegularHoursDialogComponent, {
      disableClose: true,
      width: '660px',
      maxWidth: '100vw',
      data: { hideHolidayHours: true },
    });
    this.editDialogRef.componentInstance.control = control as UntypedFormControl;
    this.editDialogRef.componentInstance.showRemoveButton = true;
    this.editDialogRef.componentInstance.customTitle = `${
      moreHoursType.localizedDisplayName
    } ${this.translateService.instant('FRONTEND.BUSINESSES.HOURS')}`;
    this.editDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        control.markAsDirty();
        control.setValue(result);
        if (result.length) {
          this.unsetTypes.delete(moreHoursType.hoursTypeId);
          this.setTypes.set(moreHoursType.hoursTypeId, moreHoursType);
        } else {
          this.setTypes.delete(moreHoursType.hoursTypeId);
          this.unsetTypes.set(moreHoursType.hoursTypeId, moreHoursType);
        }
        this.changed.emit();
      }
      this.editDialogRef = null;
    });
  }
}
