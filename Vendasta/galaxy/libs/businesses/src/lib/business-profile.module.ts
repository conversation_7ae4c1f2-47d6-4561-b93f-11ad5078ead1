import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BusinessCategoryModule } from '@galaxy/business-category';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateModule } from '@ngx-translate/core';
import { AuxiliaryDataModule } from '@vendasta/auxiliary-data-components';
import { VaFormsModule } from '@vendasta/forms';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyCheckboxModule } from '@vendasta/galaxy/checkbox';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyTimezoneSelectorModule } from '@vendasta/galaxy/timezone-selector';
import { ImageTransformationModule } from '@vendasta/image-transformation';
import { SalespersonServiceModule } from '@vendasta/salesperson';
import { TaxonomyServiceModule } from '@vendasta/taxonomy';
import { TaxonomyI18nModule } from '@vendasta/taxonomy-i18n';
import { VaFullscreenLightboxService } from '@vendasta/uikit';
import { AddressFormV2Module } from './address/v2/address-form-v2.module';
import baseTranslation from './assets/i18n/en_devel.json';
import { BusinessProfileComponent } from './business-profile.component';
import { CompetitorsTabModule } from './competitors-tab/competitors-tab.module';
import { FormatPhoneNumberPipe } from './format-phone-number.pipe';
import { MediaTabModule } from './media-tab/media-tab.module';
import { PlaceAutocompleteComponent } from './place-autocomplete/place-autocomplete.component';
import { ProductActivationPrereqFormModule } from './product-activation-prereq-form/product-activation-prereq-form.module';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MoreHoursComponent } from './more-hours/more-hours.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ImageTransformationModule,
    VaFormsModule,
    TaxonomyServiceModule,
    MatInputModule,
    MatCardModule,
    MatTabsModule,
    MatTooltipModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule,
    SalespersonServiceModule,
    MediaTabModule,
    MatCheckboxModule,
    MatDatepickerModule,
    AddressFormV2Module,
    TaxonomyI18nModule,
    GoogleMapsModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: 'common/businesses',
      baseTranslation: baseTranslation,
    }),
    ProductActivationPrereqFormModule,
    CompetitorsTabModule,
    GalaxyCheckboxModule,
    MatAutocompleteModule,
    GalaxyTimezoneSelectorModule,
    AuxiliaryDataModule,
    BusinessCategoryModule,
    GalaxyFormFieldModule,
    FormsModule,
    MatRadioModule,
    MatChipsModule,
    MatExpansionModule,
    MatFormFieldModule,
    GalaxyAlertModule,
    GalaxyLoadingSpinnerModule,
  ],
  declarations: [BusinessProfileComponent, FormatPhoneNumberPipe, PlaceAutocompleteComponent, MoreHoursComponent],
  exports: [BusinessProfileComponent, PlaceAutocompleteComponent],
  providers: [VaFullscreenLightboxService],
})
export class BusinessProfileModule {}
