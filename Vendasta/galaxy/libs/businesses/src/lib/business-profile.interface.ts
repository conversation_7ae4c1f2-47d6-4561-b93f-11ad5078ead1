import { AbstractControl, FormArray, FormGroup } from '@angular/forms';

export interface Market {
  value: string;
  name: string;
}

export enum BusinessProfileTab {
  Primary,
  Hours,
  Descriptions,
  Images,
  Social,
  Professional,
  Attributes,
  CustomFields,
  Administration,
}

export enum HiddenField {
  Markets,
  Sales,
  SocialFourSquare,
  CustomerIdentifier,
  Map,
}

export interface LightboxData {
  index: number;
  imageUrls: string[];
}

export interface InvalidFormField {
  key: string;
  form: FormGroup | FormArray | AbstractControl;
}
