import { AbstractControl, FormControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import {
  NotOnlyWhitespaceValidator,
  primaryIsNotInAdditionalSalespeople,
  primaryIsSelectedWhenHavingAdditionalSalespeople,
} from './custom-validators';

describe(`Custom Validators`, () => {
  describe(`Validate that input is not solely whitespace`, () => {
    function testValidatorFunction(
      testInput: string | null,
      testDescription: string,
      expectedError: ValidationErrors,
    ): void {
      const inputControl: FormControl = new FormControl();
      inputControl.setValidators([NotOnlyWhitespaceValidator]);
      inputControl.setValue(testInput);
      const actualErrors = inputControl.errors;

      it(testDescription, () => {
        expect(actualErrors).toEqual(expectedError);
      });
    }

    describe(`Case where there is input`, () => {
      testValidatorFunction(' This is good input ', `should return no error`, null);
    });

    describe(`Case where input is only whitespace`, () => {
      testValidatorFunction('    ', 'should return an error', {
        whitespace: true,
      });
    });

    describe(`Case where there input is not a string`, () => {
      testValidatorFunction(null, 'should not return an error', null);
    });
  });

  describe(`Validate that primary salesperson is not in additional salespeople -`, () => {
    const primarySalespersonName = 'TestName';

    function testValidatorFunction(
      testPrimarySalesperson: any,
      testAdditionalSalespeople: any[],
      testDescription: string,
      expectedError: ValidationErrors,
    ): void {
      const salespersonControl: FormControl = new FormControl();
      salespersonControl.setValue(testPrimarySalesperson);

      const additionalSalespersonControl: FormControl = new FormControl();
      additionalSalespersonControl.setValue(testAdditionalSalespeople);

      const controls: { [key: string]: AbstractControl } = {};
      controls['salespersonId'] = salespersonControl;
      controls['additionalSalespersonIds'] = additionalSalespersonControl;

      const formGroup: any = {
        controls: controls,
      };

      const validatorFunction: ValidatorFn = primaryIsNotInAdditionalSalespeople(() => primarySalespersonName);
      const actualErrors = validatorFunction(formGroup);

      it(testDescription, () => {
        expect(actualErrors).toEqual(expectedError);
        expect(additionalSalespersonControl.errors).toEqual(expectedError);
        if (expectedError) {
          expect(additionalSalespersonControl.touched).toBeTruthy();
        } else {
          expect(additionalSalespersonControl.touched).toBeFalsy();
        }
      });
    }

    describe(`Case where there is no primary salespeople`, () => {
      testValidatorFunction(null, [], `should return no error`, null);
    });

    describe(`Case where there is no additional salespeople`, () => {
      testValidatorFunction(primarySalespersonName, [], `should return no error`, null);
    });

    describe(`Case where there is some additional salespeople, but the primary is not selected`, () => {
      testValidatorFunction(null, ['Other name', 'Third name'], `should return no error`, null);
    });

    describe(`Case where there is some additional salespeople, but the primary is not in additional`, () => {
      testValidatorFunction(primarySalespersonName, ['Other name', 'Third name'], `should return no error`, null);
    });

    describe(`Case where there is some additional salespeople and the primary is in additional`, () => {
      testValidatorFunction(primarySalespersonName, [primarySalespersonName, 'Other name'], `should return an error`, {
        primaryName: primarySalespersonName,
      });
    });
  });

  describe(`Validate that primary salesperson is selected when having additional salespeople -`, () => {
    function testValidatorFunction(
      testPrimarySalesperson: any,
      testAdditionalSalespeople: any[],
      testDescription: string,
      expectedError: ValidationErrors,
    ): void {
      const salespersonControl: FormControl = new FormControl();
      salespersonControl.setValue(testPrimarySalesperson);

      const additionalSalespersonControl: FormControl = new FormControl();
      additionalSalespersonControl.setValue(testAdditionalSalespeople);

      const controls: { [key: string]: AbstractControl } = {};
      controls['salespersonId'] = salespersonControl;
      controls['additionalSalespersonIds'] = additionalSalespersonControl;

      const formGroup: any = {
        controls: controls,
      };

      const validatorFunction: ValidatorFn = primaryIsSelectedWhenHavingAdditionalSalespeople();
      const actualErrors = validatorFunction(formGroup);

      it(testDescription, () => {
        expect(actualErrors).toEqual(expectedError);
        expect(salespersonControl.errors).toEqual(expectedError);
        if (expectedError) {
          expect(salespersonControl.touched).toBeTruthy();
        } else {
          expect(salespersonControl.touched).toBeFalsy();
        }
      });
    }

    describe(`Case where there is no primary salesperson nor additional salesperson selected`, () => {
      testValidatorFunction(null, [], 'should return no error', null);
    });
    describe(`Case where there is a primary salesperson, but no additional salesperson selected`, () => {
      testValidatorFunction(`testPrimary`, [], 'should return no error', null);
    });
    describe(`Case where there is a primary salesperson additional salespeople selected`, () => {
      testValidatorFunction(`testPrimary`, [`other`], 'should return no error', null);
    });
    describe(`Case where there is a no primary salesperson selected, but some additional salespeople selected`, () => {
      testValidatorFunction(null, [`other`], 'should return an error', { primaryNotSelected: true });
    });
  });
});
