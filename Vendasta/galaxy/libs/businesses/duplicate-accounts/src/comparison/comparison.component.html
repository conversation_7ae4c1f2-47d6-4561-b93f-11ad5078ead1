<table mat-table [dataSource]="rowsShowing$ | async">
  <ng-container matColumnDef="company">
    <th mat-header-cell *matHeaderCellDef>Account</th>
    <td mat-cell *matCellDef="let element">
      <div>
        <a [routerLink]="accountDetailsUrl(element.accountGroupId)">
          {{ element.napData?.companyName || element.accountGroupId }}
        </a>
      </div>
      <div>{{ element.napData?.workNumber }}</div>
      <div>{{ address(element) }}</div>
    </td>
  </ng-container>
  <ng-container matColumnDef="users">
    <th mat-header-cell *matHeaderCellDef>Users</th>
    <td mat-cell *matCellDef="let element">
      <div>{{ element.associations?.length }}</div>
    </td>
  </ng-container>
  <ng-container matColumnDef="products">
    <th mat-header-cell *matHeaderCellDef>Products</th>
    <td mat-cell *matCellDef="let element">
      <div>{{ element.accounts?.length }}</div>
    </td>
  </ng-container>
  <ng-container matColumnDef="created">
    <th mat-header-cell *matHeaderCellDef>Created</th>
    <td mat-cell *matCellDef="let element">
      <div style="white-space: nowrap">
        {{ element.created | date: 'medium' }}
      </div>
    </td>
  </ng-container>
  <ng-container matColumnDef="action">
    <th mat-header-cell *matHeaderCellDef>Actions</th>
    <td mat-cell *matCellDef="let element">
      <a
        *ngIf="accountDeleteUrl(element.accountGroupId) !== null"
        [routerLink]="accountDeleteUrl(element.accountGroupId)"
      >
        <mat-icon>delete</mat-icon>
      </a>
    </td>
  </ng-container>
  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>
<a
  *ngIf="accountGroupIds.length > initialRowsToShow"
  class="toggleShowAllRows"
  (click)="toggleShowAllRows()"
>
  {{
    (showingAllRows$ | async)
      ? 'Show Less'
      : 'Show More (up to ' + maxRowsToShow + ' total)'
  }}
</a>
