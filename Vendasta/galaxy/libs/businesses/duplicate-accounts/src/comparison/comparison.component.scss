:host {
  width: 100%;
}

table {
  width: 100%;
  margin-bottom: 12px;
  background-color: transparent;
}

td.mat-mdc-cell,
th.mat-mdc-header-cell {
  padding-left: 24px;
  padding-top: 8px;
  padding-bottom: 8px;
  &:first-of-type {
    width: 100%;
  }
}

th.mat-mdc-header-cell {
  white-space: nowrap;
}

.toggleShowAllRows {
  display: block;
  padding-bottom: 8px;
  width: 100%;
  text-align: center;
}
