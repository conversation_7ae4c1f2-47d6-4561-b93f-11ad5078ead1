import { Component, Input, OnInit } from '@angular/core';
import { AccountGroup, AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { filter, map, startWith, switchMap, take } from 'rxjs/operators';

@Component({
  selector: 'business-duplicate-accounts-comparison',
  templateUrl: './comparison.component.html',
  styleUrls: ['./comparison.component.scss'],
  standalone: false,
})
export class ComparisonComponent implements OnInit {
  @Input() accountGroupIds: string[];
  @Input() set viewing(value: boolean) {
    if (value) {
      this.viewed.next(true);
    }
  }
  @Input() initialRowsToShow = 2;
  @Input() maxRowsToShow = 25;

  accountGroups$: Observable<AccountGroup[]>;
  displayedColumns: string[] = ['company', 'users', 'products', 'created', 'action'];
  rowsShowing$: Observable<AccountGroup[]>;

  private viewed = new BehaviorSubject(false);
  private showAllRows$$ = new BehaviorSubject(false);
  showingAllRows$ = this.showAllRows$$.asObservable();

  constructor(private readonly accountGroupService: AccountGroupService) {}

  @Input() accountDetailsUrl: (accountGroupId: string) => string = () => null;
  @Input() accountDeleteUrl: (accountGroupId: string) => string = () => null;

  ngOnInit(): void {
    this.accountGroups$ = this.viewed.pipe(
      filter((v) => v),
      take(1),
      switchMap(() =>
        this.accountGroupService.getMulti(
          this.accountGroupIds,
          new ProjectionFilter({ napData: true, accounts: true, associations: true }),
        ),
      ),
      startWith(this.accountGroupIds.map((agid) => new AccountGroup({ accountGroupId: agid }))),
    );
    this.rowsShowing$ = combineLatest([this.accountGroups$, this.showAllRows$$]).pipe(
      map(([accountGroups, showAllRows]) =>
        showAllRows ? accountGroups : [...accountGroups].splice(0, this.initialRowsToShow),
      ),
    );
  }

  address(accountGroup: AccountGroup): string {
    if (!accountGroup.napData) {
      return null;
    }
    const streetAddress: string = accountGroup.napData.address ? `${accountGroup.napData.address}, ` : '';
    const city: string = accountGroup.napData.city;
    const state: string = accountGroup.napData.state ? `, ${accountGroup.napData.state}` : '';
    return streetAddress + city + state;
  }

  toggleShowAllRows(): void {
    this.showAllRows$$.next(!this.showAllRows$$.getValue());
  }
}
