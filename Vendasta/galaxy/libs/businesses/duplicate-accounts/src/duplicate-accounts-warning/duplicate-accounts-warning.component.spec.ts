import { of, defer } from 'rxjs';
import { DuplicateAccountsService, PotentialDuplicates } from '../duplicate-accounts.service';
import { DuplicateAccountsWarningComponent, BusinessProfileFormData } from './duplicate-accounts-warning.component';

describe('EasyAccountCreateService', () => {
  it('fetches potential duplicates on the business profile changing', (done) => {
    const duplicateAccountsService = {
      getDuplicatesByLocation: () => of({ totalResults: 1 } as PotentialDuplicates),
    } as unknown as DuplicateAccountsService;
    const component = new DuplicateAccountsWarningComponent(duplicateAccountsService);
    component.potentialDuplicates$.subscribe((dupes) => {
      expect(dupes.totalResults).toBe(1);
      done();
    });
    component.businessProfile = {} as BusinessProfileFormData;
  });

  it('keeps the potential duplicates stream alive if there is an error checking for duplicates', (done) => {
    let called = 0;
    const duplicateAccountsService = {} as DuplicateAccountsService;
    duplicateAccountsService.getDuplicatesByLocation = () =>
      defer(() => {
        called++;
        if (called === 1) {
          throw new Error('');
        }
        return of({ totalResults: 1 } as PotentialDuplicates);
      });
    const component = new DuplicateAccountsWarningComponent(duplicateAccountsService);
    component.potentialDuplicates$.subscribe((dupes) => {
      if (called === 1) {
        expect(dupes.totalResults).toBe(0);
        component.businessProfile = {} as BusinessProfileFormData;
      }
      if (called === 2) {
        expect(dupes.totalResults).toBe(1);
        done();
      }
    });
    component.businessProfile = {} as BusinessProfileFormData;
  });
});
