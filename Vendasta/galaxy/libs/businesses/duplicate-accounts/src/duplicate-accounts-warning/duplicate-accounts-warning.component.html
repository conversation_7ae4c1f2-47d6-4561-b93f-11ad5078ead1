<ng-container *ngIf="potentialDuplicates$ | async as result">
  <div
    class="warning-banner"
    *ngIf="result?.totalResults > 0"
    matTooltip="The possible duplicates listed here are determined by normalizing an account's phone number and address into all of the different common formats, and then determining if more than one account match the formatting. The results may or may not be duplicates, and not all duplicates will be found using this method. This feature is under development and the algorithm will continue to be refined and improved."
  >
    <mat-icon class="warning-icon">warning</mat-icon>
    <div style="width: 100%">
      <div>{{ result.totalResults }} similiar account(s) already exist(s):</div>
      <business-duplicate-accounts-comparison
        [accountGroupIds]="potentialDuplicatesAccountGroupIds$ | async"
        [viewing]="true"
        [accountDetailsUrl]="accountDetailsUrl"
        [accountDeleteUrl]="accountDeleteUrl"
      ></business-duplicate-accounts-comparison>
    </div>
  </div>
</ng-container>
