import { Component, Input } from '@angular/core';
import { Observable, of, Subject } from 'rxjs';
import { catchError, debounceTime, filter, map, shareReplay, switchMap } from 'rxjs/operators';
import { DuplicateAccountsService, PotentialDuplicates } from '../duplicate-accounts.service';

export interface BusinessProfileFormData {
  businessName: string;
  city: string;
  state: { code: string };
  country: { code: string };
  zip: string;
  streetAddress: string;
  businessPhoneNumber: string[];
}

@Component({
  selector: 'business-duplicate-accounts-warning',
  templateUrl: './duplicate-accounts-warning.component.html',
  styleUrls: ['./duplicate-accounts-warning.component.scss'],
  providers: [DuplicateAccountsService],
  standalone: false,
})
export class DuplicateAccountsWarningComponent {
  private readonly businessProfile$$: Subject<BusinessProfileFormData> = new Subject();

  potentialDuplicates$: Observable<PotentialDuplicates> = this.businessProfile$$.pipe(
    debounceTime(500),
    filter((data) => !!data),
    switchMap((data) => {
      try {
        return this.duplicateAccountsService
          .getDuplicatesByLocation(
            this.partnerId,
            {
              company_name: data.businessName,
              city: data.city,
              state: data.state ? data.state.code : '',
              country: data.country ? data.country.code : '',
              zip: data.zip,
              address: data.streetAddress,
              work_number: data.businessPhoneNumber,
            },
            this.marketId,
            this.maxRowsToShow,
          )
          .pipe(
            catchError(() => {
              return of({ potentialDuplicates: [], totalResults: 0 } as PotentialDuplicates);
            }),
          );
      } catch (err) {
        return of({ potentialDuplicates: [], totalResults: 0 } as PotentialDuplicates);
      }
    }),
    shareReplay(1),
  );
  potentialDuplicatesAccountGroupIds$: Observable<string[]> = this.potentialDuplicates$.pipe(
    map((pds) => (pds && pds.potentialDuplicates ? pds.potentialDuplicates.map((d) => d.accountGroupId) : [])),
  );

  @Input() partnerId: string;
  @Input() marketId: string;
  @Input() maxRowsToShow = 25;

  @Input() set businessProfile(value: BusinessProfileFormData) {
    this.businessProfile$$.next(value);
  }

  constructor(private duplicateAccountsService: DuplicateAccountsService) {}

  @Input() accountDetailsUrl: (accountGroupId: string) => string = () => null;
  @Input() accountDeleteUrl: (accountGroupId: string) => string = () => null;
}
