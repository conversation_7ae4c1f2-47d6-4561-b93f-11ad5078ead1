import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { RouterModule } from '@angular/router';
import { ComparisonComponent } from './comparison/comparison.component';
import { DuplicateAccountsWarningComponent } from './duplicate-accounts-warning/duplicate-accounts-warning.component';

@NgModule({
  declarations: [ComparisonComponent, DuplicateAccountsWarningComponent],
  imports: [CommonModule, MatTableModule, MatProgressSpinnerModule, MatIconModule, RouterModule.forChild([])],
  exports: [ComparisonComponent, DuplicateAccountsWarningComponent],
})
export class DuplicateAccountsModule {}
