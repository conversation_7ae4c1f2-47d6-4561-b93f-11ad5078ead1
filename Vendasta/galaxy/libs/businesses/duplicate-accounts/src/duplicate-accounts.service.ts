import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { BehaviorSubject, EMPTY, Observable } from 'rxjs';
import { catchError, distinctUntilChanged, map } from 'rxjs/operators';

export class AccountGroupLocation {
  company_name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  work_number: string[];
}

export interface PotentialDuplicatesByKey {
  phoneNumber?: string;
  address?: string;
  accountGroupIds: string[];
}

export interface PotentialDuplicate {
  accountGroupId: string;
  score: number;
}

export interface PotentialDuplicates {
  potentialDuplicates?: PotentialDuplicate[];
  totalResults?: number;
}

interface State {
  duplicates: PotentialDuplicatesByKey[] | null;
  loading: boolean;
  error: any;
}

const initialState = {
  duplicates: null,
  loading: true,
  error: null,
};

@Injectable()
export class DuplicateAccountsService {
  private _state: State = { ...initialState };
  private readonly store = new BehaviorSubject<State>(this._state);
  public readonly loading$: Observable<boolean> = this.store.pipe(
    map((state) => state.loading),
    distinctUntilChanged(),
  );
  public readonly error$: Observable<any> = this.store.pipe(
    map((state) => state.error),
    distinctUntilChanged(),
  );
  public readonly duplicates$: Observable<PotentialDuplicatesByKey[] | null> = this.store.pipe(
    map((state) => state.duplicates),
    distinctUntilChanged(),
  );
  private readonly base_url: string;

  constructor(private readonly http: HttpClient, readonly environmentService: EnvironmentService) {
    let host = '';
    switch (environmentService.getEnvironment()) {
      case Environment.LOCAL:
      case Environment.DEMO:
        host = 'account-group-api-demo.apigateway.co';
        break;
      case Environment.PROD:
        host = 'account-group-api-prod.apigateway.co';
        break;
    }

    this.base_url = `https://${host}/accountgroup.v1.AccountGroupService`;
  }

  loadAllDuplicates(partnerId: string, marketId?: string): void {
    this.store.next((this._state = { ...this._state, duplicates: null, loading: true, error: null }));

    this.http
      .post<{ potentialDuplicates: PotentialDuplicatesByKey[] }>(
        `${this.base_url}/ListAllPotentialDuplicates`,
        { partner_id: partnerId, market_id: marketId },
        { withCredentials: true },
      )
      .pipe(
        map((res) => {
          return res.potentialDuplicates;
        }),
        catchError((err) => {
          this.store.next((this._state = { ...this._state, duplicates: null, loading: false, error: err }));
          return EMPTY;
        }),
      )
      .subscribe((duplicates) =>
        this.store.next(
          (this._state = {
            ...this._state,
            duplicates: duplicates || [],
            loading: false,
            error: null,
          }),
        ),
      );
  }

  getDuplicatesByLocation(
    partnerId: string,
    location: Partial<AccountGroupLocation>,
    marketId?: string,
    pageSize = 25,
  ): Observable<PotentialDuplicates> {
    return this.http.post<PotentialDuplicates>(
      `${this.base_url}/ListPotentialDuplicates`,
      {
        partner_id: partnerId,
        market_id: marketId,
        page_size: pageSize,
        location,
      },
      { withCredentials: true },
    );
  }
}
