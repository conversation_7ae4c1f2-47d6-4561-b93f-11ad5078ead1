{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["libs/businesses/tsconfig.*?.json"]}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "business", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "business", "style": "kebab-case"}], "@angular-eslint/prefer-standalone": "off"}, "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates"]}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template"], "rules": {}}]}