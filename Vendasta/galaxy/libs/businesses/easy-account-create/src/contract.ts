/*
 * Interfaces used to promote consistency between different implementations of
 * Easy Account Create.
 *
 * (This is a transitional move.  EAC is currently duplicated across
 * partner-center-client and sales-center-client.  If it becomes necessary
 * somewhere else, we should pull the code out of those code-bases and provide a
 * common implementation here.)
 */

/// <reference types="@types/google.maps" />
import { Observable, Subject } from 'rxjs';
import { Market, Salesperson } from './objects';

export interface PartnerService {
  partnerId: string;
}

export interface MarketsService {
  markets: Observable<Market[]>;
  currentMarket$: Observable<Market>;
}

export interface SalespersonService {
  latestSalespeople$$: Subject<Salesperson[]>;

  loadAllSalespeople(): void;
}

export class AccountGroupLocation {
  company_name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  work_number: string[];
}

export interface PotentialDuplicate {
  accountGroupId: string;
  score: number;
}

export interface PotentialDuplicates {
  potentialDuplicates?: PotentialDuplicate[];
  totalResults?: number;
}

export interface DuplicateAccountsServiceProvider {
  getDuplicatesByLocation(partnerId: string, location: Partial<AccountGroupLocation>): Observable<PotentialDuplicates>;
}
