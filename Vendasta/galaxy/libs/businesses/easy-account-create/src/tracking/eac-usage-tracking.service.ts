import { Inject, Injectable, Optional } from '@angular/core';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { EAC_USAGE_TRACKING_TOKEN, UsageTracking } from '../objects';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class EACUsageTrackingService {
  private sessionID: string;
  private skipToCreation = new BehaviorSubject<boolean>(null);
  skipToAccountCreationObservable = this.skipToCreation.asObservable();

  constructor(
    @Optional() @Inject(EAC_USAGE_TRACKING_TOKEN) private readonly eacTrackingTag: string,
    @Optional() @Inject(ProductAnalyticsService) private readonly analyticsService: ProductAnalyticsService,
  ) {}

  track(trackingID: UsageTracking): void {
    if (this.eacTrackingTag) {
      if (!this.sessionID) {
        this.trackNewSession();
      }
      this.analyticsService.trackEvent(trackingID, 'easy-account-create', '');
    }
  }

  trackNewSession(): void {
    this.sessionID = new Date().getTime().toString();
  }

  skipToAccountCreation(skip: boolean): void {
    this.skipToCreation.next(skip);
  }
}
