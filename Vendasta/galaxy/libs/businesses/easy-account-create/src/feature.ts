import { Observable } from 'rxjs';

export interface AccessService {
  hasAccessToFeature(feature: Feature): Observable<boolean>;
}

// This Feature enum contains all of the features that partner center client needs to know about
export const enum Feature {
  whitelabel = 'white-labeling',
  salesTeam = 'sales-team',
  lists = 'lists',
  brands = 'brands',
  marketingAutomation = 'marketing-automation',
  concierge = 'concierge',
  contentLibrary = 'content-library',
  guides = 'guides',
  snapshotWidget = 'snapshot-widget',
  salesAndSuccessCenterAccess = 'ssc-access',
  businessDirectory = 'business-directory',
  markets = 'markets-access',
  salesOrders = 'sales-orders',
  gettingStartedGuide = 'getting-started-guide',
  publicStore = 'public-store',
  localMarketingIndex = 'local-marketing-index',
  executiveReport = 'vbc-executive-report',
  prospects = 'prospects',
  bulkActivations = 'bulk-activations',
  theLoop = 'the-loop',
  uiKitNav = 'ui-kit-nav-redesign',
  snapshotBulkActivations = 'bulk-snapshot-activations',
  customCampaigns = 'custom-campaigns',
  emailSettings = 'advanced-email-settings',
  customFields = 'custom-fields',
}
