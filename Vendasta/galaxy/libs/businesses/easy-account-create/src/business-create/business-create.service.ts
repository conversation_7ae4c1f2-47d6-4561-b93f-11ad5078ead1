import { Injectable } from '@angular/core';
import { FormGroup, UntypedFormGroup } from '@angular/forms';
import { HoursOfOperationInterface } from '@vendasta/account-group';
import {
  CreateListingProfileRequest,
  BusinessHours as LPBusinessHours,
  ExternalIdentifiers as LPExternalIdentifiers,
  Location as LPLocation,
  ListingProfileApiService,
  RegularHoursPeriod,
  TimeOfDay,
  RichData,
  SocialURLs,
  LegacyProductDetails,
  MarketingInfo,
  CreateCompetitors,
  LifecycleStage,
  AppKey,
  ActivateAccounts,
  Activation,
} from '@vendasta/listing-products';
import { BehaviorSubject, Observable, catchError, finalize, map, tap, throwError } from 'rxjs';
import { sanitizeResults } from './helpers/utils';
import { IncludedProductsService } from './included-products.service';
import {
  CompetitorsInferenceResponse,
  NapDataInferenceResponse,
  SocialUrlsInferenceResponse,
} from './inference/inference-api-response';
import { InferenceApiService } from './inference/inference-api.service';

const EAC_ORIGIN = 'partner-center-eac';

export interface IncludedProduct {
  appId: string;
  editionId?: string;
}

export interface CompetitorLocation {
  name: string;
  url?: string;
  placeId: string;
}

export interface EasyAccountCreateForm {
  partnerId: string;
  businessProfileForm: UntypedFormGroup;
  fullBusinessProfileForm: UntypedFormGroup;
  administrationForm: UntypedFormGroup;
  includedProducts?: IncludedProduct[];
}

export interface LocationInterface {
  latitude: number;
  longitude: number;
}

@Injectable()
export class EasyAccountCreateService {
  private readonly creating$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private readonly napDataInferring$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private readonly competitorsInferring$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private readonly selectedCompetitorsInferring$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private readonly socialUrlsInferring$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private readonly businessProfile$$: BehaviorSubject<any> = new BehaviorSubject(null);

  creating$: Observable<boolean> = this.creating$$.asObservable();
  napDataInferring$: Observable<boolean> = this.napDataInferring$$.asObservable();
  competitorsInferring$: Observable<boolean> = this.competitorsInferring$$.asObservable();
  selectedCompetitorsInferring$: Observable<boolean> = this.selectedCompetitorsInferring$$.asObservable();
  socialUrlsInferring$: Observable<boolean> = this.socialUrlsInferring$$.asObservable();
  inferredFields: string[] | any = [];
  location: LocationInterface;

  private readonly formStateLogger =
    (window?.['environment'] ?? 'prod') === 'prod'
      ? () => {
          /**/
        }
      : logOutErrorsForFormGroup;

  constructor(
    private readonly inferenceApiService: InferenceApiService,
    private readonly includedProductsService: IncludedProductsService,
    private listingProfileService: ListingProfileApiService,
  ) {}

  setInferredVCategories(vCategoryIds: string[]): void {
    this.inferredFields.vCategoryIds = [...vCategoryIds];
  }

  public getIncludedProducts(partnerId: string, marketId: string): Observable<IncludedProduct[]> {
    return this.includedProductsService.getIncludedProducts(partnerId, marketId);
  }

  public getIncludedProductsText(partnerId: string, marketId: string): Observable<string> {
    return this.includedProductsService.getIncludedProductsText(partnerId, marketId);
  }

  public updateBusinessProfile(data: any): void {
    this.businessProfile$$.next(data);
  }

  private constructNapDataFromEasyAccountCreate(form: EasyAccountCreateForm): LPLocation {
    const napData = this.constructNapData(form.businessProfileForm);
    napData.callTrackingNumber = form.fullBusinessProfileForm.get('callTrackingNumbers').value;
    return napData;
  }

  private constructNapData(form: FormGroup): LPLocation {
    if (form.get('longitude').value === 0 && form.get('latitude').value === 0) {
      this.location = undefined;
    } else {
      this.location = {
        latitude: form.get('latitude').value,
        longitude: form.get('longitude').value,
      };
    }

    const address = form.get('addressForm').value;
    const napData = new LPLocation({
      companyName: form.get('businessName').value,
      address: address.address1,
      address2: address.address2,
      city: address.city,
      zip: address.zip,
      website: form.get('website').value,
      workNumber: form.get('businessPhoneNumber').value,
      location: this.location,
      serviceAreaBusiness: form.get('serviceAreaBusiness').value,
      serviceArea: form.get('serviceAreaBusiness').value ? form.get('serviceArea').value : null,
      country: typeof address.country === 'object' ? address.country?.code : null,
      state: typeof address.province === 'object' ? address.province?.code : null,
    });

    return napData;
  }

  private constructExternalIdentifiers(form: EasyAccountCreateForm): LPExternalIdentifiers {
    const primaryCategory = form.businessProfileForm.get('primaryBusinessCategory').value;
    const additionalCategories = form.businessProfileForm.get('businessCategories').value;
    const categories = Array.from(new Set([...primaryCategory, ...additionalCategories])).filter((id) => !!id);

    return new LPExternalIdentifiers({
      origin: EAC_ORIGIN,
      partnerId: form.partnerId,
      marketId: form.businessProfileForm.get('marketId').value.market_id || 'default',
      customerIdentifier: form.administrationForm.get('customerId').value,
      tags: form.administrationForm.get('tags').value.map((t) => t.text),
      vCategoryIds: categories,
      salesPersonId: form.administrationForm.get('salesperson').value.salesPersonId,
      additionalSalesPersonIds: form.administrationForm.get('additionalSalespeople').value.map((s) => s.salesPersonId),
      updateOrigin: EAC_ORIGIN,
    });
  }

  private constructAccountsToActivate(includedProducts: IncludedProduct[]): ActivateAccounts {
    return new ActivateAccounts({
      activations: includedProducts.map(
        (p) =>
          new Activation({
            appKey: new AppKey({ appId: p.appId, editionId: p.editionId }),
          }),
      ),
    });
  }

  private constructCompetitorsToCreate(form: EasyAccountCreateForm): CreateCompetitors {
    return new CreateCompetitors({ location: form.fullBusinessProfileForm.get('selectedCompetitors').value });
  }
  private constructMarketingInfoToCreate(form: FormGroup): MarketingInfo {
    return new MarketingInfo({
      lifecycleStage: form.get('lifecycleStage').value || LifecycleStage.LIFECYCLE_STAGE_UNSET,
    });
  }

  private constructLegacyProductDetails(form: EasyAccountCreateForm): LegacyProductDetails {
    return new LegacyProductDetails({
      commonName: form.fullBusinessProfileForm.get('commonBusinessNames').value,
      adminNotes: form.administrationForm.get('adminNotes').value,
    });
  }

  private constructRichData(form: EasyAccountCreateForm): RichData {
    return new RichData({
      tollFreeNumber: form.businessProfileForm.get('tollFreePhoneNumber').value,
      description: form.fullBusinessProfileForm.get('description').value,
      shortDescription: form.fullBusinessProfileForm.get('shortDescription').value,
      servicesOffered: form.fullBusinessProfileForm.get('servicesOffered').value,
      brandsCarried: form.fullBusinessProfileForm.get('brandsCarried').value,
      landmark: form.fullBusinessProfileForm.get('landmark').value,
      paymentMethods: form.fullBusinessProfileForm.get('paymentMethods').value.map((t) => t.code),
      inferredAttributes: [],
      faxNumber: form.fullBusinessProfileForm.get('faxNumber').value,
      cellNumber: form.fullBusinessProfileForm.get('cellNumber').value,
      email: form.fullBusinessProfileForm.get('businessEmail').value,
    });
  }

  private computeInferredAttributes(request: any): string[] {
    const inferredAttributes = [];
    for (const key in request) {
      if (this.inferredFields[key]) {
        if (typeof request[key] === 'string' && this.inferredFields[key] === request[key]) {
          inferredAttributes.push(key);
        } else if (Array.isArray(request[key]) && this.listEqual(this.inferredFields[key], request[key])) {
          inferredAttributes.push(key);
        }
      }
    }

    return inferredAttributes;
  }

  private listEqual(list1: string[], list2: string[]): boolean {
    if (!list1 || !list2) {
      return false;
    }
    if (list1 === list2) {
      return true;
    }
    if (list1 === null || list2 === null) {
      return false;
    }
    if (list1.length !== list2.length) {
      return false;
    }
    return list1.every((val, idx) => val === list2[idx]);
  }

  private constructSocialUrls(form: EasyAccountCreateForm): SocialURLs {
    return new SocialURLs({
      linkedinUrl: form.fullBusinessProfileForm.get('linkedInUrl').value,
      foursquareUrl: form.fullBusinessProfileForm.get('foursquareUrl').value,
      twitterUrl: form.fullBusinessProfileForm.get('twitterUrl').value,
      facebookUrl: form.fullBusinessProfileForm.get('facebookUrl').value,
      rssUrl: form.fullBusinessProfileForm.get('blogUrl').value,
      youtubeUrl: form.fullBusinessProfileForm.get('youtubeUrl').value,
      instagramUrl: form.fullBusinessProfileForm.get('instagramUrl').value,
      pinterestUrl: form.fullBusinessProfileForm.get('pinterestUrl').value,
    });
  }

  private hooSpanToBusinessHoursSpan(spanTime: string): [hours: number, minutes: number] {
    const [hoursStr, minutesStr] = spanTime.split(':');

    const hours: number = parseInt(hoursStr, 10);
    const minutes: number = parseInt(minutesStr, 10);

    return [hours, minutes];
  }

  private hooDayToBusinessHoursDay(day: string): number {
    enum DayOfWeek {
      DAY_OF_WEEK_UNSPECIFIED = 0,
      MONDAY = 1,
      TUESDAY = 2,
      WEDNESDAY = 3,
      THURSDAY = 4,
      FRIDAY = 5,
      SATURDAY = 6,
      SUNDAY = 7,
    }

    const dayUpper = day.toUpperCase();

    switch (dayUpper) {
      case 'MONDAY':
        return DayOfWeek.MONDAY;
      case 'TUESDAY':
        return DayOfWeek.TUESDAY;
      case 'WEDNESDAY':
        return DayOfWeek.WEDNESDAY;
      case 'THURSDAY':
        return DayOfWeek.THURSDAY;
      case 'FRIDAY':
        return DayOfWeek.FRIDAY;
      case 'SATURDAY':
        return DayOfWeek.SATURDAY;
      case 'SUNDAY':
        return DayOfWeek.SUNDAY;
      default:
        return DayOfWeek.DAY_OF_WEEK_UNSPECIFIED;
    }
  }

  private allHooSpansToRegularHoursPeriods(spans: HoursOfOperationInterface): RegularHoursPeriod[] {
    const periods: RegularHoursPeriod[] = [];
    spans.hoursOfOperation?.forEach((span) => {
      const period = this.hooSpanToRegularHours(span);
      periods.push(...period);
    });
    return periods;
  }

  private hooSpanToRegularHours(hooSpan): RegularHoursPeriod[] {
    const periods: RegularHoursPeriod[] = [];
    const [openHours, openMinutes] = this.hooSpanToBusinessHoursSpan(hooSpan.opens);
    const [closeHours, closeMinutes] = this.hooSpanToBusinessHoursSpan(hooSpan.closes);
    for (const day of hooSpan.dayOfWeek) {
      const openDay = this.hooDayToBusinessHoursDay(day);
      if (openDay === 0) {
        continue;
      }
      let closeDay = openDay;
      if (closeHours < openHours) {
        if (openDay === 7) {
          closeDay = 1;
        } else {
          closeDay = closeDay + 1;
        }
      }

      const currBH = new RegularHoursPeriod({
        openDay: openDay,
        openTime: {
          hours: openHours,
          minutes: openMinutes,
        },
        closeDay: closeDay,
        closeTime: {
          hours: closeHours,
          minutes: closeMinutes,
        },
      });
      periods.push(currBH);
    }

    return periods;
  }

  private SplitHours(timeSpanList: RegularHoursPeriod[]): RegularHoursPeriod[] {
    return timeSpanList.reduce((acc: RegularHoursPeriod[], curr: RegularHoursPeriod) => {
      if (curr.openDay === curr.closeDay || curr.closeTime.hours == 0) {
        acc.push(curr);
        return acc;
      }

      const preMidnightHoursPeriod = new RegularHoursPeriod({
        ...curr,
        closeTime: new TimeOfDay({ hours: 24, minutes: 0 }),
      });

      acc.push(preMidnightHoursPeriod);

      if (curr.closeTime.hours === 0) {
        return acc;
      }

      const crossMidnightHoursPeriod = new RegularHoursPeriod({
        ...curr,
        openTime: new TimeOfDay({ hours: 0, minutes: 0 }),
        closeDay: curr.closeDay,
        openDay: curr.closeDay,
      });

      acc.push(crossMidnightHoursPeriod);

      return acc;
    }, []);
  }

  private constructHoursOfOperation(form: FormGroup): HoursOfOperationInterface {
    return {
      hoursOfOperation: form.get('hoursOfOperation').value,
    };
  }

  private constructRequest(form: EasyAccountCreateForm): CreateListingProfileRequest {
    const napData = sanitizeResults(this.constructNapDataFromEasyAccountCreate(form));
    const externalIds = this.constructExternalIdentifiers(form);
    const socialUrls = sanitizeResults(this.constructSocialUrls(form));
    const hoursOfOperation = this.constructHoursOfOperation(form.fullBusinessProfileForm);
    const businessHours = this.constructBusinessHours(hoursOfOperation);
    const legacyDetails = sanitizeResults(this.constructLegacyProductDetails(form));
    const richData = sanitizeResults(this.constructRichData(form));
    const accountsToActivate = this.constructAccountsToActivate(form.includedProducts);
    const competitorsToCreate = this.constructCompetitorsToCreate(form);
    const marketingInfoToCreate = this.constructMarketingInfoToCreate(form.administrationForm);

    const inferredFields: string[] = this.computeInferredAttributes(napData);
    inferredFields.push(...this.computeInferredAttributes({ taxonomyId: externalIds.taxIds }));
    inferredFields.push(...this.computeInferredAttributes({ vCategoryIds: externalIds.vCategoryIds }));
    inferredFields.push(...this.computeInferredAttributes(socialUrls));
    inferredFields.push(...this.computeInferredAttributes({ hoursOfOperationJson: hoursOfOperation.hoursOfOperation }));
    inferredFields.push(...this.computeInferredAttributes({ competitor: legacyDetails.competitor }));
    richData.inferredAttributes = inferredFields;

    return new CreateListingProfileRequest({
      nap: napData,
      updateOperations: [
        { externalIdentifiers: externalIds },
        { socialUrls: socialUrls },
        { legacyProductDetails: legacyDetails },
        { richData: richData },
        { marketingInfo: marketingInfoToCreate },
        { businessHours: businessHours },
      ],
      createOperations: [{ activateAccounts: accountsToActivate }, { createCompetitors: competitorsToCreate }],
    });
  }

  constructRegularHours(hoo: HoursOfOperationInterface) {
    const rh = this.allHooSpansToRegularHoursPeriods(hoo);
    const splitHours = this.SplitHours(rh);

    return splitHours;
  }

  constructBusinessHours(hoo: HoursOfOperationInterface) {
    return new LPBusinessHours({
      hoursTypeId: 'GENERAL',
      regularHours: this.constructRegularHours(hoo),
    });
  }

  create(form: EasyAccountCreateForm): Observable<string> {
    if (this.creating$$.getValue() === true) {
      return;
    }
    this.creating$$.next(true);
    const { businessProfileForm, fullBusinessProfileForm, administrationForm } = form;

    if (businessProfileForm.valid && fullBusinessProfileForm.valid && administrationForm.valid) {
      return this.listingProfileService.create(this.constructRequest(form)).pipe(
        map((r) => {
          return r.businessId;
        }),
        finalize(() => this.creating$$.next(false)),
        catchError(() => {
          this.creating$$.next(false);
          return throwError('Form is invalid');
        }),
      );
    } else {
      this.formStateLogger('BusinessProfileForm', businessProfileForm);
      this.formStateLogger('FullBusinessProfileForm', fullBusinessProfileForm);
      this.formStateLogger('AdministrationForm', administrationForm);
      this.creating$$.next(false);
      return throwError('Form is invalid');
    }
  }

  inferNapData(partnerId: string, marketIds: string[], placeId: string): Observable<NapDataInferenceResponse> {
    this.napDataInferring$$.next(true);
    return this.inferenceApiService.inferNapData(partnerId, marketIds, placeId).pipe(
      tap(
        (data) => {
          this.inferredFields = data;
        },
        (_) => _,
        () => this.napDataInferring$$.next(false),
      ),
    );
  }

  inferCompetitorsNapData(
    partnerId: string,
    marketIds: string[],
    placeIds: string[],
  ): Observable<NapDataInferenceResponse[]> {
    this.selectedCompetitorsInferring$$.next(true);
    return this.inferenceApiService.inferNapDataMulti(partnerId, marketIds, placeIds).pipe(
      tap(
        (competitors) => (this.inferredFields.selectedCompetitors = competitors),
        (_) => _,
        () => this.selectedCompetitorsInferring$$.next(false),
      ),
    );
  }

  inferCompetitors(
    partnerId: string,
    marketIds: string[],
    placeId: string,
    companyName: string,
    city: string,
    categoryNames: string[],
  ): Observable<CompetitorsInferenceResponse> {
    this.competitorsInferring$$.next(true);
    return this.inferenceApiService
      .inferCompetitors(partnerId, marketIds, placeId, companyName, city, categoryNames)
      .pipe(
        tap(
          (competitors) => {
            this.inferredFields.competitor = competitors.competitors;
          },
          (_) => _,
          () => this.competitorsInferring$$.next(false),
        ),
      );
  }

  inferSocialUrls(
    partnerId: string,
    marketIds: string[],
    companyName: string,
    address: string,
    city: string,
    phone: string,
    website: string,
    zipCode: string,
  ): Observable<SocialUrlsInferenceResponse> {
    this.socialUrlsInferring$$.next(true);
    return this.inferenceApiService
      .inferSocialUrls(partnerId, marketIds, companyName, address, city, phone, website, zipCode)
      .pipe(
        tap(
          (socialUrls) => {
            this.inferredFields.linkedinUrl = socialUrls.linkedinUrl;
            this.inferredFields.foursquareUrl = socialUrls.foursquareUrl;
            this.inferredFields.twitterUrl = socialUrls.twitterUrl;
            this.inferredFields.facebookUrl = socialUrls.facebookUrl;
            this.inferredFields.rssUrl = socialUrls.rssUrl;
            this.inferredFields.youtubeUrl = socialUrls.youtubeUrl;
            this.inferredFields.instagramUrl = socialUrls.instagramUrl;
            this.inferredFields.pinterestUrl = socialUrls.pinterestUrl;
          },
          (_) => _,
          () => this.socialUrlsInferring$$.next(false),
        ),
      );
  }

  getProductNames(appIds: string[]): Observable<{ appId: string; name: string }[]> {
    return this.includedProductsService.getProductNames(appIds);
  }
}

function logOutErrorsForFormGroup(name: string, form: FormGroup): void {
  if (form.valid) return;
  console.log('Errors for', name, 'form:');

  for (const key in form.controls) {
    const control = form.controls[key];
    if (control instanceof FormGroup || control instanceof UntypedFormGroup) {
      logOutErrorsForFormGroup(key, control);
    } else if (control.errors) {
      console.log(key, ': ', control.errors, '; value: ', control.value);
    }
  }
}
