import {
  GetBusinessDataFromPlaceIdResponse,
  GetMultiBusinessDataFromPlaceIdResponse,
  InferenceService,
} from '@vendasta/account-group';
import { Observable, of } from 'rxjs';
import { NapDataInferenceResponse } from './inference-api-response';
import { InferenceApiService } from './inference-api.service';

class MockInferenceService {
  getResponse: any;
  constructor(getResponse: any) {
    this.getResponse = getResponse;
  }

  getBusinessDataFromPlaceId(): Observable<GetBusinessDataFromPlaceIdResponse> {
    return of(this.getResponse);
  }

  getMultiBusinessDataFromPlaceId(): Observable<GetMultiBusinessDataFromPlaceIdResponse> {
    return of(this.getResponse);
  }
}

describe('InferenceApiService', () => {
  describe('inferNapData', () => {
    it('should return the getBusinessDataFromPlaceId adapted response for partners', () => {
      const mockInferenceService = new MockInferenceService(getBusinessDataFromPlaceIdResponse);
      const service = new InferenceApiService(null, null, mockInferenceService as unknown as InferenceService);

      service.inferNapData('VUNI', ['market-test'], 'ChIJUyx4c0XxBFMRcE4qkjROzhg').subscribe((result) => {
        expect(result).toBe(expectedInferNapDataResult);
      });
    });
  });

  describe('inferMultiNapData', () => {
    it('should return the adapted getMultiBusinessDataFromPlaceId response for partners', () => {
      const mockInferenceService = new MockInferenceService(getMultiBusinessDataFromPlaceIdResponse);
      const service = new InferenceApiService(null, null, mockInferenceService as unknown as InferenceService);

      service
        .inferNapDataMulti('VUNI', ['market-test'], ['ChIJsfZsLFrxBFMRRtph_KN9bCY', 'ChIJwUXEf0b3BFMRZXE6XSx0W84'])
        .subscribe((result) => {
          expect(result).toBe(expectedInferDataMultiResult);
        });
    });
  });
});

const getBusinessDataFromPlaceIdResponse = {
  googleIdentifiers: {
    googlePlaceId: 'ChIJUyx4c0XxBFMRcE4qkjROzhg',
    googleCustomerId: '1787452089810177648',
  },
  napData: {
    companyName: 'Amazing Stories',
    address: '2508 8 Street East suite a',
    city: 'Saskatoon',
    state: 'SK',
    country: 'CA',
    zip: 'S7H 0V6',
    workNumber: ['+1 306-242-8996'],
    website: 'https://www.amazingstoriescomics.ca/',
    location: {
      latitude: 52.1143741,
      longitude: -106.6180414,
    },
  },
  taxonomyId: ['shopping:media:bookstores', 'shopping'],
  hoursOfOperation: {
    hoursOfOperation: [
      {
        dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Thursday', 'Friday', 'Saturday'],
        opens: '11:00',
        closes: '18:00',
      },
      {
        dayOfWeek: ['Wednesday'],
        opens: '11:00',
        closes: '20:00',
      },
    ],
  },
};

const expectedInferNapDataResult: NapDataInferenceResponse = {
  partnerId: 'VUNI',
  placeId: 'ChIJUyx4c0XxBFMRcE4qkjROzhg',
  companyName: 'Amazing Stories',
  address: '2508 8 Street East suite a',
  city: 'Saskatoon',
  state: 'SK',
  zip: 'S7H 0W2',
  country: 'CA',
  latitude: 52.1143741,
  longitude: -106.6180414,
  website: 'http://www.mcnallyrobinson.com/',
  taxonomyId: ['shopping:media:bookstores', 'shopping'],
  workNumber: ['+1 306-955-3599'],
  hoursOfOperationJson: [
    {
      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Thursday', 'Friday', 'Saturday'],
      closes: '18:00',
      description: '',
      opens: '11:00',
    },
    {
      dayOfWeek: ['Wednesday'],
      closes: '20:00',
      description: '',
      opens: '11:00',
    },
  ],
};

const getMultiBusinessDataFromPlaceIdResponse = {
  businesses: {
    ChIJsfZsLFrxBFMRRtph_KN9bCY: {
      googleIdentifiers: {
        googlePlaceId: 'ChIJsfZsLFrxBFMRRtph_KN9bCY',
        googleCustomerId: '2768726014193424966',
      },
      napData: {
        companyName: 'McNally Robinson',
        address: '3130 8 Street East',
        city: 'Saskatoon',
        state: 'SK',
        country: 'CA',
        zip: 'S7H 0W2',
        workNumber: ['+1 306-955-3599'],
        website: 'http://www.mcnallyrobinson.com/',
        location: {
          latitude: 52.114,
          longitude: -106.6064719,
        },
      },
      taxonomyId: ['shopping:media:bookstores', 'shopping:electronics', 'shopping:homeandgarden'],
      hoursOfOperation: {
        hoursOfOperation: [
          {
            dayOfWeek: ['Sunday'],
            opens: '10:00',
            closes: '18:00',
          },
          {
            dayOfWeek: ['Monday', 'Tuesday', 'Wednesday'],
            opens: '10:00',
            closes: '20:00',
          },
          {
            dayOfWeek: ['Thursday', 'Friday', 'Saturday'],
            opens: '10:00',
            closes: '21:00',
          },
        ],
      },
    },
    ChIJwUXEf0b3BFMRZXE6XSx0W84: {
      googleIdentifiers: {
        googlePlaceId: 'ChIJwUXEf0b3BFMRZXE6XSx0W84',
        googleCustomerId: '14869606328585318757',
      },
      napData: {
        companyName: 'Peryton Books',
        address: '408 20th Street West',
        city: 'Saskatoon',
        state: 'SK',
        country: 'CA',
        zip: 'S7M 0X4',
        workNumber: ['+1 306-244-1442'],
        website: 'http://www.perytonbooks.com/',
        location: {
          latitude: 52.1263172,
          longitude: -106.6756183,
        },
      },
      taxonomyId: ['shopping:media:bookstores', 'shopping'],
      hoursOfOperation: {
        hoursOfOperation: [
          {
            dayOfWeek: ['Sunday'],
            opens: '12:00',
            closes: '17:00',
          },
          {
            dayOfWeek: ['Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
            opens: '10:30',
            closes: '18:00',
          },
        ],
      },
    },
  },
};

const expectedInferDataMultiResult: NapDataInferenceResponse[] = [
  {
    partnerId: 'VUNI',
    placeId: 'ChIJsfZsLFrxBFMRRtph_KN9bCY',
    companyName: 'McNally Robinson',
    address: '3130 8 Street East',
    city: 'Saskatoon',
    state: 'SK',
    zip: 'S7H 0W2',
    country: 'CA',
    latitude: 52.114,
    longitude: -106.6064719,
    website: 'http://www.mcnallyrobinson.com/',
    taxonomyId: ['shopping:media:bookstores', 'shopping:electronics', 'shopping:homeandgarden'],
    workNumber: ['+1 306-955-3599'],
    hoursOfOperationJson: [
      {
        dayOfWeek: ['Sunday'],
        opens: '10:00',
        closes: '18:00',
      },
      {
        dayOfWeek: ['Monday', 'Tuesday', 'Wednesday'],
        opens: '10:00',
        closes: '20:00',
      },
      {
        dayOfWeek: ['Thursday', 'Friday', 'Saturday'],
        opens: '10:00',
        closes: '21:00',
      },
    ],
  },
  {
    partnerId: 'VUNI',
    placeId: 'ChIJwUXEf0b3BFMRZXE6XSx0W84',
    companyName: 'Peryton Books',
    address: '408 20th Street West',
    city: 'Saskatoon',
    state: 'SK',
    zip: 'S7M 0X4',
    country: 'CA',
    latitude: 52.1263172,
    longitude: -106.6756183,
    website: 'http://www.perytonbooks.com/',
    taxonomyId: ['shopping:media:bookstores', 'shopping'],
    workNumber: ['+1 306-244-1442'],
    hoursOfOperationJson: [
      {
        dayOfWeek: ['Sunday'],
        opens: '12:00',
        closes: '17:00',
      },
      {
        dayOfWeek: ['Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
        opens: '10:30',
        closes: '18:00',
      },
    ],
  },
];
