import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, timeout } from 'rxjs/operators';
import {
  CompetitorsInferenceResponse,
  NapDataInferenceResponse,
  SocialUrlsInferenceResponse,
} from './inference-api-response';
import { INFER_COMPETITORS_URL, INFER_SOCIAL_URL } from './routes';
import { InferenceApiHost } from './inference-api.host.service';
import { sanitizeResults } from '../helpers/utils';
import {
  GetBusinessDataFromPlaceIdResponse,
  GetMultiBusinessDataFromPlaceIdResponse,
  InferBusinessCategoryRequest,
  InferenceRequestMode,
  InferenceService,
  NAPData,
} from '@vendasta/account-group';
import { Category } from '@vendasta/category';

const EAC_ORIGIN = 'partner-center-eac';

@Injectable()
export class InferenceApiService {
  constructor(
    public http: HttpClient,
    private readonly inferenceHost: InferenceApiHost,
    private readonly inferenceService: InferenceService,
  ) {}

  inferBusinessCategoriesByNapData(napData: NAPData, partnerId: string): Observable<Category[]> {
    return this.inferenceService
      .inferBusinessCategory(
        new InferBusinessCategoryRequest({
          napData: napData,
          partnerId: partnerId,
          origin: EAC_ORIGIN,
          mode: InferenceRequestMode.LIVE,
          returnBusinessData: true,
        }),
      )
      .pipe(
        map((response) => {
          const result = sanitizeResults(response);
          return (
            result.categories.map(
              (c) =>
                new Category({
                  externalId: c,
                }),
            ) || []
          );
        }),
        timeout(20000),
        catchError(() => {
          return of([]);
        }),
      );
  }

  inferNapData(partnerId: string, marketIds: string[], placeId: string): Observable<NapDataInferenceResponse> {
    return this.inferenceService.getBusinessDataFromPlaceId(placeId, partnerId, marketIds).pipe(
      map((response: any) => {
        const result = sanitizeResults(this.adaptBusinessData(response));
        result.partnerId = partnerId;
        result.placeId = placeId;
        return result;
      }),
    );
  }

  inferNapDataMulti(
    partnerId: string,
    marketIds: string[],
    placeIds: string[],
  ): Observable<NapDataInferenceResponse[]> {
    return this.inferenceService.getMultiBusinessDataFromPlaceId(placeIds, partnerId, marketIds).pipe(
      map((response: GetMultiBusinessDataFromPlaceIdResponse) => {
        const napDataResponse: NapDataInferenceResponse[] = [];
        const responseArray = Object.values(response.businesses);
        responseArray.forEach((r, index) => {
          const resp = this.adaptBusinessData(r);
          const result = sanitizeResults(resp);
          if (resp.companyName) {
            result.partnerId = partnerId;
            result.placeId = placeIds[index];
            napDataResponse.push(result);
          }
        });
        return napDataResponse;
      }),
    );
  }

  inferCompetitors(
    partnerId: string,
    marketIds: string[],
    placeId: string,
    companyName: string,
    city: string,
    categoryNames: string[],
  ): Observable<CompetitorsInferenceResponse> {
    return this.http
      .post(
        this.inferenceHost.host() + INFER_COMPETITORS_URL,
        {
          partnerId: partnerId,
          placeId: placeId,
          companyName: companyName,
          city: city,
          categoryNames: categoryNames,
          marketIds: marketIds,
        },
        { withCredentials: true },
      )
      .pipe(map((response: any) => response.data));
  }

  inferSocialUrls(
    partnerId: string,
    marketIds: string[],
    companyName: string,
    address: string,
    city: string,
    phone: string,
    website: string,
    zipCode: string,
  ): Observable<SocialUrlsInferenceResponse> {
    return this.http
      .post(
        this.inferenceHost.host() + INFER_SOCIAL_URL,
        {
          partnerId: partnerId,
          companyName: companyName,
          address: address,
          city: city,
          phone: phone,
          website: website,
          zipCode: zipCode,
          marketIds: marketIds,
        },
        { withCredentials: true },
      )
      .pipe(map((response: any) => sanitizeResults(response.data)));
  }

  public adaptBusinessData(business: GetBusinessDataFromPlaceIdResponse): NapDataInferenceResponse {
    const napData = business?.napData;
    return {
      placeId: business?.googleIdentifiers?.googlePlaceId || '',
      companyName: napData?.companyName || '',
      address: napData?.address || '',
      city: napData?.city || '',
      state: napData?.state || '',
      zip: napData?.zip || '',
      country: napData?.country || '',
      latitude: napData?.location?.latitude || 0,
      longitude: napData?.location?.longitude || 0,
      website: napData?.website || '',
      taxonomyId: business?.taxonomyId || [],
      workNumber: napData?.workNumber || [],
      hoursOfOperationJson: business?.hoursOfOperation?.hoursOfOperation || [],
    };
  }
}
