import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';

export interface HostProvider {
  host(): string;
}

@Injectable()
export class InferenceApiHost implements HostProvider {
  private _host: string;

  constructor(private readonly environmentService: EnvironmentService) {}

  host(): string {
    if (this._host) {
      return this._host;
    }

    switch (this.environmentService.getEnvironment()) {
      case Environment.LOCAL:
        this._host = 'http://localhost:8081';
        break;
      case Environment.DEMO:
        this._host = 'https://partner-central-demo.appspot.com';
        break;
      case Environment.PROD:
        this._host = 'https://partners.vendasta.com';
        break;
    }
    return this._host;
  }
}
