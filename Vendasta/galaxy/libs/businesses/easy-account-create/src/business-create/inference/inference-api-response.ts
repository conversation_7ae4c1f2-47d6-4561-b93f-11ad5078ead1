import { HoursOfOperationSpanInterface } from '@vendasta/account-group';

export interface BusinessCategoriesResponse {
  categories: {
    name: string;
    externalId: string;
  }[];
}

export interface NapDataInferenceResponse {
  partnerId?: string;
  placeId?: string;
  companyName?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  website?: string;
  taxonomyId?: string[];
  workNumber?: string[];
  hoursOfOperationJson?: HoursOfOperationSpanInterface[];
}

export interface CompetitorsInferenceResponse {
  competitors?: string[];
}

export interface SocialUrlsInferenceResponse {
  linkedinUrl?: string;
  foursquareUrl?: string;
  twitterUrl?: string;
  facebookUrl?: string;
  rssUrl?: string;
  youtubeUrl?: string;
  instagramUrl?: string;
  pinterestUrl?: string;
}
