const LANDMARK_TOOLTIP = `The general area in which your business is physically located. Typical Landmark addresses \
include mall and office names (for example, Oakview Mall or Clocktower Plaza). Only enter a Landmark if your street \
address does not accurately pinpoint your business's location.`;

const SERVICES_OFFERED_TOOLTIP = `The primary services that your business offers (for example, personal tax filing or root canals).`;

const BRANDS_CARRIED_TOOLTIP = `The primary brands that your business carries (for example, Ford, Apple,  Firestone, or Allstate).`;

const SHARE_OF_VOICE_TOOLTIP = ` Enter products, services or other keywords to compare the business' search engine share \
of voice to their competition.`;

const COMPETITORS_TOOLTIP = `Enter competitor names to benchmark the search engine prominence of those competitors against the business.`;

const ADDITIONAL_SALESPERSON_TOOLTIP = `The primary salesperson's information will be displayed in the store and in campaign emails \
for this account. However, you can assign up to 5 additional salespeople to this account.`;

const CUSTOMER_IDENTIFIER_TOOLTIP = `Internal identifier to be used in billing spreadsheets and account searches.`;

const ADMIN_NOTES_TOOLTIP = `These notes are not client-facing.`;

const TAGS_TOOLTIP = `Tagging helps you categorize and search for an account.`;

const GET_VTAX_TOOLTIP = (rmProductName: string, smProductName: string): string => {
  return `Business Categories help to ensure that relevant listing sources are turned on within \
${rmProductName} (Ex. Selecting 'Restaurants' will turn Urbanspoon to ON and turn Cars.com \
to OFF). Business Categories will also help retrieve the appropriate RSS Feeds in \
${smProductName} and industry averages throughout the product suite.`;
};

const GET_CALL_TRACKING_TOOLTIP = (rmProductName: string): string => {
  return `A Call Tracking Number is a phone number that gathers analytics for inbound calls. \
Call tracking is commonly used as a method of performance assessment for marketing campaigns. This number will be used \
to find business listings and assess their accuracy in the Listings tab of ${rmProductName}.`;
};

const GET_TAGLINE_TOOLTIP = (msProductName: string): string => {
  return `Your company slogan or a brief description of the business. The tagline will appear directly below the \
company name at the top of the ${msProductName} My Listing.`;
};

const GET_COMMON_BUSINESS_NAME_TOOLTIP = (rmProductName: string): string => {
  return `Other names that your business is commonly called (Ex. Patty's Irish Pub \
referred to as Patty's or Patty's Pub). Common Business Names will become \
Mention Searches within ${rmProductName}.`;
};

const GET_SEO_CATEGORY_TOOLTIP = (msProductName: string): string => {
  return `SEO (Search Engine Optimization) Keywords are key words or phrases that help customers \
find your business via search engines. These keywords will be displayed on the \
${msProductName} My Listing to enhance SEO.`;
};

export const Tooltips = {
  LANDMARK_TOOLTIP,
  SERVICES_OFFERED_TOOLTIP,
  BRANDS_CARRIED_TOOLTIP,
  SHARE_OF_VOICE_TOOLTIP,
  COMPETITORS_TOOLTIP,
  CUSTOMER_IDENTIFIER_TOOLTIP,
  ADMIN_NOTES_TOOLTIP,
  TAGS_TOOLTIP,
  GET_VTAX_TOOLTIP,
  GET_CALL_TRACKING_TOOLTIP,
  GET_COMMON_BUSINESS_NAME_TOOLTIP,
  GET_TAGLINE_TOOLTIP,
  GET_SEO_CATEGORY_TOOLTIP,
  ADDITIONAL_SALESPERSON_TOOLTIP,
};
