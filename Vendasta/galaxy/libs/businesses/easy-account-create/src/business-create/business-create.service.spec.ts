import { HoursOfOperationInterface } from '@vendasta/account-group';
import { RegularHoursPeriod } from '@vendasta/listing-products';
import { EasyAccountCreateService } from './business-create.service';

describe('ConstructBusinessHours', () => {
  test('empty hours set does not implode', () => {
    const input: HoursOfOperationInterface = {};
    const expected: RegularHoursPeriod[] = [];
    const EACService = new EasyAccountCreateService(null, null, null, null);
    const actual: RegularHoursPeriod[] = EACService.constructRegularHours(input);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });
  test('9 to 5 Monday to Friday work', () => {
    const input: HoursOfOperationInterface = {
      hoursOfOperation: [
        {
          dayOfWeek: ['Monday'],
          opens: '09:00',
          closes: '17:00',
        },
        {
          dayOfWeek: ['Tuesday', 'Wednesday', 'Thursday', 'Friday'],
          opens: '09:00',
          closes: '17:00',
        },
        {
          dayOfWeek: ['PublicHolidays'],
          opens: '12:00',
          closes: '20:00',
        },
      ],
    };
    const expected = [
      {
        openDay: 1,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 1,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 2,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 2,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 3,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 3,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 4,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 4,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
      {
        openDay: 5,
        openTime: {
          hours: 9,
          minutes: 0,
        },
        closeDay: 5,
        closeTime: {
          hours: 17,
          minutes: 0,
        },
      },
    ];
    const EACService = new EasyAccountCreateService(null, null, null, null);
    const actual: RegularHoursPeriod[] = EACService.constructRegularHours(input);
    console.log('output: ', actual);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });
  test('spans over midnight work', () => {
    const input: HoursOfOperationInterface = {
      hoursOfOperation: [
        {
          dayOfWeek: ['Monday', 'Thursday'],
          opens: '12:00',
          closes: '2:00',
        },
      ],
    };
    const expected = [
      {
        openDay: 1,
        openTime: {
          hours: 12,
          minutes: 0,
        },
        closeDay: 2,
        closeTime: {
          hours: 24,
          minutes: 0,
        },
      },
      {
        openDay: 2,
        openTime: {
          hours: 0,
          minutes: 0,
        },
        closeDay: 2,
        closeTime: {
          hours: 2,
          minutes: 0,
        },
      },
      {
        openDay: 4,
        openTime: {
          hours: 12,
          minutes: 0,
        },
        closeDay: 5,
        closeTime: {
          hours: 24,
          minutes: 0,
        },
      },
      {
        openDay: 5,
        openTime: {
          hours: 0,
          minutes: 0,
        },
        closeDay: 5,
        closeTime: {
          hours: 2,
          minutes: 0,
        },
      },
    ];
    const EACService = new EasyAccountCreateService(null, null, null, null);
    const actual: RegularHoursPeriod[] = EACService.constructRegularHours(input);
    console.log('output: ', actual);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });
  test('spans until midnight work', () => {
    const input: HoursOfOperationInterface = {
      hoursOfOperation: [
        {
          dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
          opens: '7:00',
          closes: '0:00',
        },
      ],
    };
    const expected = [
      {
        openDay: 1,
        openTime: { hours: 7, minutes: 0 },
        closeDay: 2,
        closeTime: { hours: 0, minutes: 0 },
      },
      {
        openDay: 2,
        openTime: { hours: 7, minutes: 0 },
        closeDay: 3,
        closeTime: { hours: 0, minutes: 0 },
      },
      {
        openDay: 3,
        openTime: { hours: 7, minutes: 0 },
        closeDay: 4,
        closeTime: { hours: 0, minutes: 0 },
      },
      {
        openDay: 4,
        openTime: { hours: 7, minutes: 0 },
        closeDay: 5,
        closeTime: { hours: 0, minutes: 0 },
      },
      {
        openDay: 5,
        openTime: { hours: 7, minutes: 0 },
        closeDay: 6,
        closeTime: { hours: 0, minutes: 0 },
      },
    ];
    const EACService = new EasyAccountCreateService(null, null, null, null);
    const actual: RegularHoursPeriod[] = EACService.constructRegularHours(input);
    console.log('output: ', actual);
    expect(actual.length).toStrictEqual(expected.length);
    for (let i = 0; i < actual.length; i++) {
      expect(actual[i].openDay).toStrictEqual(expected[i].openDay);
      expect(actual[i].openTime.hours).toStrictEqual(expected[i].openTime.hours);
      expect(actual[i].openTime.minutes).toStrictEqual(expected[i].openTime.minutes);
      expect(actual[i].closeDay).toStrictEqual(expected[i].closeDay);
      expect(actual[i].closeTime.hours).toStrictEqual(expected[i].closeTime.hours);
      expect(actual[i].closeTime.minutes).toStrictEqual(expected[i].closeTime.minutes);
    }
  });
});
