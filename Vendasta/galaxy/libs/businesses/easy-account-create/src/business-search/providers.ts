/**
 * This library has external dependencies that need to be provided by the host app in the module.
 * Provided type for PARTNER_ID_TOKEN is `Observable<string>`.
 * Provided type for LAZY_MAPS_API_CONFIG is an LazyMapsAPILoaderConfigLiteral Object
 *
 * Example:
 * @NgModule({
 *   declarations: [MyHostComponent],
 *   providers: [
 *     {provide: PARTNER_ID_TOKEN, useExisting: MY_EXISTING_PROVIDED_PARTNER_ID},
*      {
        provide: LAZY_MAPS_API_CONFIG,
        useValue: { apiKey: 'my-key', libraries: ['places'] },
       }
 *   ],
 * })
 * export class MyHostModule {}
 *
 *
 * @Component({
 *   selector: 'my-host-component',
 *   template: `
 *     <business-search-map></business-search-map>
 *   `,
 * })
 * export class MyHostComponent {}
 */
import { InjectionToken, Injector } from '@angular/core';
import { Observable } from 'rxjs';
import { share, switchMap } from 'rxjs/operators';
import { GooglePlace } from '../objects';

interface FeatureFlagServiceInterface {
  checkFeatureFlag(partnerId: string, marketId: string, featureId: string): Observable<boolean>;
}

const displaySimpleSearchFeatureFlagId = 'business_search_cost_reduction';

export interface DuplicateServiceInterface {
  mapDuplicates: (results: Observable<GooglePlace[]>) => Observable<Map<string, boolean>>;
}

export const BUSINESS_SEARCH_DUPLICATE_SERVICE = new InjectionToken<DuplicateServiceInterface>(
  'Optional service to check for duplicates when searching for businesses',
);

export const PARTNER_ID_TOKEN = new InjectionToken<Observable<string>>('partnerId$');

export const DISPLAY_SIMPLE_SEARCH = `DISPLAY_SIMPLE_SEARCH`;
export const accessDisplaySimpleSearchFactory = (
  featureFlagService: FeatureFlagServiceInterface,
  injector: Injector,
) => {
  const partnerId$ = injector.get(PARTNER_ID_TOKEN);
  return partnerId$.pipe(
    switchMap((pid) => featureFlagService.checkFeatureFlag(pid, '', displaySimpleSearchFeatureFlagId)),
    share(),
  );
};
