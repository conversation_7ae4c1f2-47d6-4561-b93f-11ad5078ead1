import { GooglePlacesService } from './google-place.service';

describe('google place service', () => {
  describe('isNonGenericType', () => {
    it('should return false if is a generic business type', () => {
      const service = new GooglePlacesService(null);

      const genericTypes = ['point_of_interest', 'establishment'];
      for (const g of genericTypes) {
        expect(service.isNonGenericType(g)).toBe(false);
      }
    });
    it('should return true if is a non-generic business type', () => {
      const service = new GooglePlacesService(null);

      const genericTypes = ['store', 'paintball-course', 'brewpub'];
      for (const g of genericTypes) {
        expect(service.isNonGenericType(g)).toBe(true);
      }
    });
  });
});
