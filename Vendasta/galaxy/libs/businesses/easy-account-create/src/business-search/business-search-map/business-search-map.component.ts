import {
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { GoogleMap, MapInfoWindow, MapMarker } from '@angular/google-maps';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Observable, ReplaySubject, Subscription, combineLatest, firstValueFrom } from 'rxjs';
import { defaultIfEmpty, filter, map, share, shareReplay, tap } from 'rxjs/operators';

import { BUSINESS_CREATE_URL } from '../../business-create/route';
import { GooglePlace, UsageTracking } from '../../objects';
import { EACUsageTrackingService } from '../../tracking/eac-usage-tracking.service';
import { GooglePlacesService, SIMPLE_SEARCH, SearchType } from '../google-place.service';
import { PARTNER_ID_TOKEN } from '../providers';

export const competitorMarkerIcon: google.maps.Icon = {
  url: 'https://storage.googleapis.com/galaxy-libs-public-images/map-competitor-marker.png',
  labelOrigin: new google.maps.Point(14, 16),
};

@Component({
  selector: 'business-search-map',
  templateUrl: './business-search-map.component.html',
  styleUrls: ['./business-search-map.component.scss'],
  providers: [GooglePlacesService],
  standalone: false,
})
export class BusinessSearchMapComponent implements OnInit, OnDestroy {
  @Input() searchType: SearchType = SIMPLE_SEARCH;
  @Input() hideCompetitors = false;
  @Output() searchTypeChanged = new EventEmitter<SearchType>();
  @Output() selectBusinessToggle = new EventEmitter<boolean>();
  @Output() selectedBusinesses = new EventEmitter<GooglePlace[]>();
  @ViewChild('search', { static: true })
  searchElementRef: ElementRef;
  @ViewChild(MapInfoWindow) infoWindow: MapInfoWindow;
  @ViewChild('map') mapRef: GoogleMap;

  businesses$: Observable<GooglePlace[]> = new Observable<GooglePlace[]>();
  loading$: Observable<boolean>;
  competitors$: Observable<GooglePlace[]> = new Observable<GooglePlace[]>();
  competitorsLoading$: Observable<boolean>;
  mapExpanded$: Observable<boolean>;
  expand$: Observable<boolean>;
  noResults$: Observable<boolean>;

  highlightedBusiness$$ = new ReplaySubject<GooglePlace>(1);
  highlightedInCompetitors$: Observable<boolean>;
  selectedBusiness$: Observable<GooglePlace>;

  maxZoom = 16;
  openedInfoWindow: string;
  markerAnimation: google.maps.Animation;
  searchPlaceholder: string;
  selectedCompetitors: string[];
  highlightedBusiness: string;

  competitorIcon = competitorMarkerIcon;

  private subscriptions: Subscription[] = [];

  constructor(
    private googlePlaceService: GooglePlacesService,
    private router: Router,
    @Inject(PARTNER_ID_TOKEN) private partnerId$: Observable<string>,
    private translate: TranslateService,
    private sanitizer: DomSanitizer,
    private readonly eacTrackingService: EACUsageTrackingService,
    private readonly route: ActivatedRoute,
  ) {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  ngOnInit(): void {
    this.businesses$ = this.googlePlaceService.googlePlaces$;
    this.loading$ = this.googlePlaceService.loading$;
    this.noResults$ = this.googlePlaceService.noResults$;

    this.selectedBusiness$ = this.businesses$.pipe(map((bs) => (bs?.length === 1 ? bs[0] : null)));
    this.subscriptions.push(
      this.selectedBusiness$.subscribe((b) => {
        if (!this.hideCompetitors) {
          this.googlePlaceService.setCompetitorSearch(b);
        }
      }),
    );

    this.competitors$ = this.googlePlaceService.competitors$;
    this.competitorsLoading$ = this.googlePlaceService.competitorsLoading$;

    this.mapExpanded$ = this.businesses$.pipe(
      tap(() => {
        this.openedInfoWindow = null;
      }),
      map((businesses) => {
        return businesses.length > 0;
      }),
      share(),
    );
    this.expand$ = this.mapExpanded$.pipe(defaultIfEmpty(false));
    this.searchPlaceholder =
      this.searchType === SIMPLE_SEARCH
        ? this.translate.instant('BUSINESS_SEARCH_MAP.SIMPLE_SEARCH_PLACEHOLDER')
        : this.translate.instant('BUSINESS_SEARCH_MAP.ADVANCED_SEARCH_PLACEHOLDER');

    this.highlightedInCompetitors$ = combineLatest([
      this.highlightedBusiness$$.asObservable().pipe(filter<GooglePlace>(Boolean)),
      this.competitors$.pipe(shareReplay()),
    ]).pipe(
      map(([highlighted, competitors]) => {
        if (competitors) {
          return competitors.findIndex((place) => place.placeId === highlighted.placeId) > -1;
        }
        return false;
      }),
    );
    // This subscription must exist so the UI updates properly
    this.subscriptions.push(this.highlightedInCompetitors$.subscribe());
  }

  onMapReady(googleMap: google.maps.Map): void {
    this.googlePlaceService.init(googleMap, this.searchElementRef.nativeElement, this.searchType);
    this.markerAnimation = google.maps.Animation.DROP;
  }

  onScroll(): void {
    const pacContainers = document.getElementsByClassName('pac-container');
    if (pacContainers.length > 0) {
      (pacContainers[0] as HTMLElement).style.display = 'none';
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  highlightBusiness(business: GooglePlace, marker?: MapMarker): void {
    this.highlightedBusiness$$.next(null);
    this.openedInfoWindow = business.markerLabel;
    this.highlightedBusiness = business.placeId;
    this.highlightedBusiness$$.next(business);
    this.infoWindow.options = { pixelOffset: new google.maps.Size(0, -35) };
    this.infoWindow.position = { lat: business.lat, lng: business.lng };
    this.infoWindow.open();
  }

  closeInfoWindow(): void {
    this.highlightedBusiness$$.next(null);
    this.openedInfoWindow = null;
    this.highlightedBusiness = '';
  }

  goToGoogleMaps(lat: number, lng: number, placeId: string): void {
    window.open(`https://www.google.com/maps/search/?api=1&query=${lat},${lng}&query_place_id=${placeId}`, '_blank');
  }

  setSelectedCompetitors(placeIds: string[]): void {
    this.selectedCompetitors = placeIds;
  }

  setSelectedBusinesses(selectedBusinesses: GooglePlace[]): void {
    this.selectedBusinesses.emit(selectedBusinesses);
    if (selectedBusinesses.length > 0) {
      this.selectBusinessToggle.emit(true);
    } else {
      this.selectBusinessToggle.emit(false);
    }
  }

  skipToAccountCreate(): void {
    this.eacTrackingService.track(UsageTracking.SkipToAccountCreation);
    this.eacTrackingService.skipToAccountCreation(true);
    this.goToBusinessCreate('');
  }

  goToBusinessCreate(placeId: string): void {
    this.asyncGoToBusinessCreate(placeId);
  }

  async asyncGoToBusinessCreate(placeId: string): Promise<void> {
    const queryParams = {};
    const paramMap = await firstValueFrom(this.route.paramMap);
    const extras: NavigationExtras = { queryParams: {} };

    if (placeId) {
      extras.queryParams['place_id'] = placeId;
      queryParams['place_id'] = placeId;
    }

    if (this.selectedCompetitors?.length > 0) {
      extras.queryParams['competitor_place_ids'] = this.selectedCompetitors;
      queryParams['competitor_place_ids'] = this.selectedCompetitors;
    }

    if (paramMap.get('origin')) {
      extras.queryParams['origin'] = paramMap.get('origin');
    }

    if (paramMap.get('origin')) {
      this.router.navigate([BUSINESS_CREATE_URL], extras);
    } else {
      const queryString = Object.keys(queryParams)
        .map((key: string) => [key, queryParams[key]].map(encodeURIComponent).join('='))
        .join('&');
      const url = BUSINESS_CREATE_URL + '?' + queryString;

      this.router.navigateByUrl(url);
    }
  }

  shouldExpand(mapExpand: boolean): boolean {
    return !!mapExpand;
  }

  shouldHide(mapExpand: boolean): boolean {
    return !mapExpand;
  }

  getSearchTerm(): string {
    return (this.searchElementRef.nativeElement as HTMLInputElement).value;
  }

  search(searchTerm: string): void {
    (this.searchElementRef.nativeElement as HTMLInputElement).value = searchTerm;
    this.googlePlaceService.setPlacesSearch(searchTerm);
  }

  enterButtonCheck($event: KeyboardEvent): void {
    if ($event.key === 'Enter') {
      this.eacTrackingService.track(UsageTracking.SearchButtonEnterUsage);
    }
  }
}
