@use 'design-tokens' as *;

.business-info {
  background-color: $white;
  cursor: pointer;
  height: auto !important;
  padding: 16px 0;

  &:hover {
    background-color: $lighter-gray;
  }
}

.selected {
  background-color: $lighter-gray;
}

.scrollable {
  scroll-margin-top: 105px;
}

.business-info--address {
  overflow: initial;
  white-space: unset;
  color: $dark-gray;
}

.mat-list-item-disabled {
  background-color: $white;
}

.loader {
  display: flex;
  justify-content: center;
  mat-spinner {
    padding: 4px 0;
  }
}

.selection-list {
  padding-top: 0px;
}

.marker-pin {
  background-image: url(https://storage.googleapis.com/galaxy-libs-public-images/business-marker.png);

  background-repeat: no-repeat;
  width: 28px !important;
  font-size: 16px !important;
  line-height: 24px;
  text-align: center;
  height: 42px !important;
  border-radius: 0 !important;
}

.competitor-marker-pin {
  background-image: url(https://storage.googleapis.com/galaxy-libs-public-images/map-competitor-marker.png);

  background-repeat: no-repeat;
  width: 28px !important;
  font-size: 16px !important;
  line-height: 24px;
  text-align: center;
  height: 42px !important;
  border-radius: 0 !important;
}

.instructions {
  background-color: $light-gray;
  padding: 8px 0 8px 12px;
  color: $dark-gray;
}

.sticky {
  position: sticky;
  top: 0;
  z-index: 50;
}
