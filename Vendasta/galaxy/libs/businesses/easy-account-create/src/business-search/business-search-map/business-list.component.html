<div [class.sticky]="businesses.length === 1">
  <ng-container *ngFor="let business of businesses">
    <mat-list-item
      class="business-info"
      (click)="emitBusinessHighlighted(business)"
      [ngClass]="{ selected: isBusinessHighlighted(business.markerLabel) }"
      [attr.id]="'business-info-' + business.markerLabel"
    >
      <mat-checkbox
        *ngIf="searchType === prospectSearch && displayFindAccountsSearch === true"
        (change)="businessSelectChange($event.checked, business)"
      ></mat-checkbox>
      <span matListItemIcon class="marker-pin">{{ business.markerLabel }}</span>
      <h3 matListItemTitle>{{ business.name }}</h3>
      <p matListItemLine class="business-info--address">
        {{ business.formattedAddress }}
      </p>
      <ng-container *ngIf="searchType === simpleSearch || displayFindAccountsSearch === false">
        <button
          mat-raised-button
          color="primary"
          [ngClass]="{ visible: businesses.length <= 1 }"
          (click)="emitPrimaryClicked(business.placeId)"
        >
          {{ 'BUSINESS_SEARCH_MAP.CONTINUE' | translate }}
        </button>
      </ng-container>
    </mat-list-item>
    <mat-divider *ngIf="businesses.length > 1"></mat-divider>
  </ng-container>
  <div class="instructions" *ngIf="competitors?.length > 0">
    {{ 'BUSINESS_SEARCH_MAP.COMPETITOR_INSTRUCTIONS' | translate }}
  </div>
</div>

<mat-list-item class="loader" *ngIf="competitorsLoading">
  <mat-spinner [diameter]="48" [strokeWidth]="4"></mat-spinner>
</mat-list-item>

<ng-container *ngIf="competitors?.length > 0">
  <mat-divider></mat-divider>
  <mat-selection-list
    #list
    [(ngModel)]="_selectedCompetitors"
    (selectionChange)="selectedCompetitorsChange()"
    class="selection-list"
  >
    <mat-list-option
      #option
      class="scrollable"
      *ngFor="let competitor of competitors"
      [disabled]="list.selectedOptions.selected.length >= MAX_SELECTED_COMPETITORS && !option.selected"
      [checkboxPosition]="'after'"
      [value]="competitor.placeId"
      [attr.id]="'business-info-' + competitor.markerLabel"
      [ngClass]="{ selected: isBusinessHighlighted(competitor.markerLabel) }"
    >
      <span matListItemIcon class="competitor-marker-pin">
        {{ competitor.markerLabel }}
      </span>
      <h3 matListItemTitle>{{ competitor.name }}</h3>
      <p matListItemLine class="business-info--address">
        {{ competitor.formattedAddress }}
      </p>
      <mat-divider></mat-divider>
    </mat-list-option>
  </mat-selection-list>
</ng-container>
