import { Component, EventEmitter, Inject, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { Observable } from 'rxjs';
import { GooglePlace, UsageTracking } from '../../objects';
import { EACUsageTrackingService } from '../../tracking/eac-usage-tracking.service';
import { PROSPECT_SEARCH, SIMPLE_SEARCH, SearchType } from '../google-place.service';

@Component({
  selector: 'business-list',
  templateUrl: './business-list.component.html',
  styleUrls: ['./business-list.component.scss'],
  standalone: false,
})
export class BusinessListComponent implements OnChanges {
  readonly MAX_SELECTED_COMPETITORS = 3;
  @Input() competitorsLoading: boolean;
  @Input() businesses: GooglePlace[];
  @Input() competitors: GooglePlace[] = [];
  @Input() highlightedLabel: string;
  @Input() searchType: SearchType;
  @Input() displayFindAccountsSearch = false;

  @Output() primaryClick: EventEmitter<string> = new EventEmitter<string>();
  @Output() businessHighlighted: EventEmitter<GooglePlace> = new EventEmitter<GooglePlace>();

  @Output() selectedCompetitors: EventEmitter<string[]> = new EventEmitter<string[]>();
  @Output() selectedBusinesses: EventEmitter<GooglePlace[]> = new EventEmitter<GooglePlace[]>();

  private currentlyHighlighted: string;

  public _selectedCompetitors: string[] = [];

  private _selectedBusinesses: GooglePlace[] = [];
  prospectSearch = PROSPECT_SEARCH;
  simpleSearch = SIMPLE_SEARCH;

  constructor(
    private readonly trackingService: EACUsageTrackingService,
    @Inject('partnerId$') private readonly partnerId$: Observable<string>,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['highlightedLabel']) {
      if (this.highlightedLabel !== this.currentlyHighlighted) {
        this.scrollToHighlighted(this.highlightedLabel);
      }
    }
    if (changes['competitors']) {
      this._selectedCompetitors = [];
      this.selectedCompetitorsChange();
    }
  }

  businessSelectChange(selected: boolean, business: GooglePlace): void {
    if (selected) {
      this._selectedBusinesses.push(business);
    } else {
      const businessIndex = this._selectedBusinesses.indexOf(business);
      this._selectedBusinesses.splice(businessIndex, 1);
    }
    this.selectedBusinesses.emit(this._selectedBusinesses);
  }

  isBusinessHighlighted(markerLabel: string): boolean {
    return this.highlightedLabel === markerLabel;
  }

  scrollToHighlighted(markerLabel: string): void {
    const infoElement = document.getElementById(`business-info-${markerLabel}`);
    if (infoElement) {
      infoElement.scrollIntoView({ behavior: 'smooth' });
    }
  }

  emitBusinessHighlighted(business: GooglePlace): void {
    this.currentlyHighlighted = business.markerLabel;
    this.businessHighlighted.emit(business);
  }

  emitPrimaryClicked(placeId: string): void {
    this.trackingService.track(UsageTracking.MapPageContinue);
    this.trackingService.skipToAccountCreation(false);

    this.primaryClick.emit(placeId);
  }

  selectedCompetitorsChange(): void {
    this.selectedCompetitors.emit(this._selectedCompetitors);
  }
}
