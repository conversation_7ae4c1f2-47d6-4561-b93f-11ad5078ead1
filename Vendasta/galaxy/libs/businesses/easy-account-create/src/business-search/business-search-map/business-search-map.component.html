<div class="map-page" [ngClass]="{ 'map-page-expanded': shouldExpand(mapExpanded$ | async) }">
  <div class="map-search">
    <EXP__glxy-wrap>
      <mat-form-field
        [appearance]="'outline'"
        [ngClass]="{
          'map-search--field-space': shouldHide(mapExpanded$ | async) && searchType === 'prospect'
        }"
      >
        <input
          matInput
          #search
          type="text"
          [placeholder]="searchPlaceholder"
          autocorrect="off"
          autocapitalize="off"
          spellcheck="off"
          (keyup)="enterButtonCheck($event)"
          data-action="business-create-input-field"
        />
        <mat-hint class="map-search-tip" *ngIf="shouldHide(mapExpanded$ | async) && searchType === 'prospect'">
          {{ 'BUSINESS_SEARCH_MAP.ADVANCED_SEARCH_TIP' | translate }}
        </mat-hint>
        <span matSuffix>
          <mat-spinner [diameter]="24" [strokeWidth]="2" *ngIf="loading$ | async; else searchIcon"></mat-spinner>
        </span>
      </mat-form-field>
    </EXP__glxy-wrap>

    <ng-template #searchIcon>
      <button mat-icon-button color="primary" data-action="business-create-search-icon-btn">
        <mat-icon>search</mat-icon>
      </button>
    </ng-template>

    <div class="contents" [ngClass]="{ 'hide-map': shouldHide(mapExpanded$ | async) }">
      <google-map
        #map
        [options]="{ maxZoom: maxZoom, mapTypeControl: true, fullscreenControl: true }"
        (mapInitialized)="onMapReady($event)"
      >
        <map-marker
          #marker="mapMarker"
          *ngFor="let business of businesses$ | async"
          [options]="{ animation: markerAnimation }"
          [position]="{ lat: business.lat, lng: business.lng }"
          [clickable]="true"
          (mapClick)="highlightBusiness(business, marker)"
          [label]="business.markerLabel"
        ></map-marker>
        <map-marker
          #competitorMarker="mapMarker"
          *ngFor="let competitor of competitors$ | async"
          [options]="{ animation: markerAnimation }"
          [position]="{ lat: competitor.lat, lng: competitor.lng }"
          [clickable]="true"
          (mapClick)="highlightBusiness(competitor, competitorMarker)"
          [label]="competitor.markerLabel"
          [icon]="competitorIcon"
        ></map-marker>
        <map-info-window (closeclick)="closeInfoWindow()">
          <div class="agm-info-window-content" *ngIf="highlightedBusiness$$ | async as business">
            <img *ngIf="business.photo" class="agm-info-window-content--image" src="{{ business.photo }}" />
            <div class="agm-info-window-content--info">
              <h2>
                {{ business.name }}
                <a color="primary" (click)="goToGoogleMaps(business.lat, business.lng, business.placeId)">
                  <mat-icon class="google-map-opener">open_in_new</mat-icon>
                </a>
              </h2>
              <div>{{ business.shortAddress }}</div>
              <button
                *ngIf="(highlightedInCompetitors$ | async) === false"
                mat-flat-button
                class="agm-info-window-content--action"
                color="primary"
                data-action="business-create-continue"
                (click)="goToBusinessCreate(business.placeId)"
              >
                {{ 'BUSINESS_SEARCH_MAP.CONTINUE' | translate }}
              </button>
            </div>
          </div>
        </map-info-window>
      </google-map>
      <mat-list class="businesses" *ngIf="businesses$ | async as businesses">
        <div class="table-header">
          {{ 'BUSINESS_SEARCH_MAP.RESULTS' | translate : { results: businesses?.length?.toString() || '0' } }}
        </div>
        <mat-divider></mat-divider>
        <business-list
          [businesses]="businesses"
          [competitors]="competitors$ | async"
          [competitorsLoading]="competitorsLoading$ | async"
          [highlightedLabel]="openedInfoWindow"
          [searchType]="searchType"
          [displayFindAccountsSearch]="true"
          (primaryClick)="goToBusinessCreate($event)"
          (businessHighlighted)="highlightBusiness($event)"
          (selectedCompetitors)="setSelectedCompetitors($event)"
          (selectedBusinesses)="setSelectedBusinesses($event)"
        ></business-list>
      </mat-list>
    </div>
  </div>

  <div *ngIf="noResults$ | async" class="no-results">
    <ng-container *ngIf="searchType === 'simple'; else prospectSearchEmptyState">
      <p>{{ 'BUSINESS_SEARCH_MAP.SIMPLE_SEARCH.NO_SELECTION' | translate }}</p>
      <p>
        {{ 'BUSINESS_SEARCH_MAP.SIMPLE_SEARCH.SELECTION_REQUIRED' | translate }}
      </p>
      <br />

      <p>
        {{ 'BUSINESS_SEARCH_MAP.SIMPLE_SEARCH.RERUN_AS_FIND_ACCOUNTS_TEXT' | translate }}
      </p>
    </ng-container>

    <ng-template #prospectSearchEmptyState>
      <p>{{ 'BUSINESS_SEARCH_MAP.NO_RESULTS' | translate }}</p>
      <p>
        {{ 'BUSINESS_SEARCH_MAP.DISCLAIMER.INSTRUCTIONS' | translate }}
        <a (click)="goToBusinessCreate('')" [attr.data-action]="'business-create-empty-state-create'">
          {{ 'BUSINESS_SEARCH_MAP.DISCLAIMER.LINK' | translate }}
        </a>
        .
      </p>
    </ng-template>
  </div>
  <div class="map-search--footer">
    <a (click)="skipToAccountCreate()" [attr.data-action]="'business-create-search-skip'">
      {{ 'BUSINESS_SEARCH_MAP.SKIP_TO_ACC_CREATION' | translate }}
    </a>
    <img src="/static/images/powered-by-google-on-white.png" />
  </div>
</div>
