@use 'design-tokens' as *;

.map-page {
  width: 100%;
  margin: 0 auto;
  padding-top: 20px;
  transition: width 225ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);

  @media only screen and (min-width: $mobile-breakpoint-max) {
    // glxy-nav transition point
    width: 620px;
  }
}

.map-page-expanded {
  width: 100%;
}

.map-search--field-space {
  margin-bottom: $gutter-width-dense;
}

.map-search {
  .contents {
    background: $white;
    margin-bottom: $gutter-width-dense;
    display: flex;
    border: 1px solid $border-color;
    border-radius: 4px;
    transition: all 0.3s ease-in-out;
  }

  .hide-map {
    display: none;
  }

  google-map {
    display: none;
  }

  @media only screen and (min-width: $media--tablet-minimum) {
    .contents {
      height: 500px;
      overflow: hidden;
    }
    google-map {
      display: block;
    }
  }
}

@media only screen and (max-width: $media--tablet-minimum) {
  :host ::ng-deep {
    .marker-pin {
      display: none;
    }
    .mat-list-text {
      margin-left: -$gutter-width-dense;
    }
  }
}

.map-search--footer {
  img {
    float: right;
  }
}

.agm-info-window-content {
  margin: 6px 0 4px 0;
  display: flex;
  flex-grow: 1;
  .agm-info-window-content--image {
    width: 150px;
    height: 150px;
    object-fit: cover;
    margin-right: 12px;
  }
  .agm-info-window-content--info {
    h2 {
      font-size: $font-preset-3-size;
      font-weight: normal;
      margin: 4px 0;
      white-space: pre-line !important;
      .google-map-opener {
        font-size: 14px;
        height: 14px;
        width: 14px;
      }
    }
    div {
      font-size: $font-preset-4-size;
      white-space: pre-line !important;
    }
    .agm-info-window-content--action {
      margin-top: $gutter-width-dense;
    }
  }
}

.businesses {
  width: 100%;
  padding: 0;
  overflow-y: auto;
}

.competitor-instruction {
  height: 20px;
  background-color: $light-gray;
}

.table-header {
  font-size: $font-preset-3-size;
  font-weight: 500;
  padding: $gutter-width-dense;
}

.no-results {
  display: flex;
  flex-flow: column;
  margin: 32px 16px;
  text-align: center;

  p {
    margin: auto;
  }
}
