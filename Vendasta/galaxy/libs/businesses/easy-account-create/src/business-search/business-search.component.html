<ng-container [ngSwitch]="displayNewSearch$ | async">
  <mat-tab-group
    mat-align-tabs="center"
    *ngSwitchCase="'show'"
    [(selectedIndex)]="selectedTab"
    (selectedTabChange)="trackTabUsage($event)"
  >
    <mat-tab label="{{ 'BUSINESS_SEARCH.SEARCH_TAB' | translate }}">
      <business-search-map
        #simpleSearch
        [searchType]="'simple'"
        (searchTypeChanged)="changeSearchType($event)"
      ></business-search-map>
    </mat-tab>
    <mat-tab label="{{ 'BUSINESS_SEARCH.ADVANCED_TAB' | translate }}">
      <business-search-map
        #prospectSearch
        [searchType]="'prospect'"
        (searchTypeChanged)="changeSearchType($event)"
      ></business-search-map>
    </mat-tab>
  </mat-tab-group>
  <ng-container *ngSwitchCase="'hide'">
    <business-search-map [searchType]="'prospect'" (searchTypeChanged)="changeSearchType($event)"></business-search-map>
  </ng-container>
  <ng-container *ngSwitchDefault></ng-container>
</ng-container>
