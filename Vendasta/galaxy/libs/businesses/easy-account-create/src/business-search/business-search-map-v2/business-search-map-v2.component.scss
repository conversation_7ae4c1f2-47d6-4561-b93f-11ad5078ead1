@use 'design-tokens' as *;

.search-bar {
  padding-top: $spacing-3;
}

.business-lead-wrapper {
  padding: 0 25px;
}

.hide-map {
  display: none;
}

.header-cell {
  padding-left: $spacing-2;
}

.base-cell {
  padding: $spacing-2;
  @include text-preset-4;
}

.glxy-badge {
  margin: 0;
}

.duplicate-cell {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: $spacing-2;
}

.review-star {
  color: $warn-border-color;
}

.reviews-cell {
  display: flex;
  flex-direction: row;
  gap: $spacing-1;
  align-items: center;
}

.cell-header-duplicate-text {
  @include text-preset-4--bold;
  color: $tertiary-font-color;
}

.cell-header-text {
  @include text-preset-4--bold;
}

.marker-pin {
  @include text-preset-3--bold;
  background-image: url(https://storage.googleapis.com/galaxy-libs-public-images/business-marker.png);
  background-repeat: no-repeat;
  width: 26px;
  text-align: center;
  height: $spacing-5;
}

.agm-info-window-content {
  margin: 6px 0 4px 0;
  display: flex;
  flex-grow: 1;
  .agm-info-window-content--image {
    width: 150px;
    height: 150px;
    object-fit: cover;
    margin-right: 12px;
  }
  .agm-info-window-content--info {
    h2 {
      font-size: $font-preset-3-size;
      font-weight: normal;
      margin: 4px 0;
      white-space: pre-line !important;
      .google-map-opener {
        font-size: 14px;
        height: 14px;
        width: 14px;
      }
    }
    div {
      font-size: $font-preset-4-size;
      white-space: pre-line !important;
    }
    .agm-info-window-content--action {
      margin-top: $gutter-width-dense;
    }
  }
}

.no-results {
  display: flex;
  flex-flow: column;
  margin: 32px $spacing-3;
  text-align: center;

  p {
    margin: $spacing-3;
  }
}

.business-table {
  margin-top: $spacing-4;
}

.business-table-header {
  display: none;
}

.cell-sub-text {
  @include text-preset-5;
  color: $secondary-text-color;
}

.cell-sub-duplicate-text {
  @include text-preset-5;
  color: $tertiary-font-color;
}

.empty-state-hero {
  img {
    width: 280px;
  }
}
