import { PagedListRequestInterface, PagedResponseInterface, PaginatedAPIInterface } from '@vendasta/galaxy/table';
import { catchError, combineLatest, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { GooglePlacesService } from '../google-place.service';
import { GooglePlace } from '../../objects';
import { DuplicateServiceInterface } from '../providers';
import { Optional } from '@angular/core';

export class SearchMapDataRow extends GooglePlace {
  isDuplicate?: boolean;
}

export class SearchMapData implements PaginatedAPIInterface<SearchMapDataRow> {
  constructor(
    private googlePlaceService: GooglePlacesService,
    @Optional() private readonly duplicateService?: DuplicateServiceInterface,
  ) {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  get(req: PagedListRequestInterface): Observable<PagedResponseInterface<SearchMapDataRow>> {
    if (this.duplicateService) {
      const googlePlaces$ = this.googlePlaceService.googlePlaces$;
      const duplicates$ = this.duplicateService
        .mapDuplicates(googlePlaces$)
        .pipe(catchError((_) => of(new Map<string, boolean>())));
      return combineLatest([googlePlaces$, duplicates$]).pipe(
        map(([businesses, dupeMap]) => {
          const businessesWithDuplicates = businesses.map(
            (business) =>
              ({
                ...business,
                isDuplicate: dupeMap.get(business.placeId),
              }) as SearchMapDataRow,
          );
          return {
            data: businessesWithDuplicates,
            pagingMetadata: {
              nextCursor: '',
              hasMore: false,
            },
          } as PagedResponseInterface<SearchMapDataRow>;
        }),
      );
    }
    return this.googlePlaceService.googlePlaces$.pipe(
      map((businesses) => {
        return {
          data: businesses,
          pagingMetadata: {
            nextCursor: '',
            hasMore: false,
          },
        } as PagedResponseInterface<SearchMapDataRow>;
      }),
    );
  }
}
