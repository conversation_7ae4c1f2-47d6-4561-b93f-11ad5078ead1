import {
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  inject,
  Inject,
  Input,
  OnInit,
  Optional,
  Output,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { GoogleMap, MapInfoWindow, MapMarker } from '@angular/google-maps';
import {
  FetchProspectDataRequestInterface,
  GetProspectDataRequestInterface,
  ProspectData,
  ProspectIdentifierInterface,
  ProspectingSdkService,
} from '@vendasta/account-group';
import { GalaxyColumnDef, GalaxyDataSource, Row } from '@vendasta/galaxy/table';
import { BehaviorSubject, combineLatest, Observable, of, ReplaySubject, retry, RetryConfig } from 'rxjs';
import { catchError, filter, map, shareReplay, startWith, switchMap, withLatestFrom } from 'rxjs/operators';
import { getImageSrc } from '../../../../src/lib/common/utils';
import { GooglePlace, UsageTracking } from '../../objects';
import { EACUsageTrackingService } from '../../tracking/eac-usage-tracking.service';
import { GooglePlacesService, PROSPECT_SEARCH, SearchType } from '../google-place.service';
import { BUSINESS_SEARCH_DUPLICATE_SERVICE, DuplicateServiceInterface, PARTNER_ID_TOKEN } from '../providers';
import { SearchMapData } from './search-map-data';
@Component({
  selector: 'business-search-map-v2',
  templateUrl: './business-search-map-v2.component.html',
  styleUrls: ['./business-search-map-v2.component.scss'],
  providers: [GooglePlacesService],
  standalone: false,
})
export class BusinessSearchMapV2Component implements OnInit {
  private readonly searchType: SearchType = PROSPECT_SEARCH;
  @Output() selectedBusinesses = new EventEmitter<GooglePlace[]>();
  @Output() prospectingDataOutput = new EventEmitter<Map<string, Observable<ProspectData>>>();
  @Output() searchTermOutput = new EventEmitter<string>();
  @Output() create = new EventEmitter<void>();

  @ViewChild('search', { static: true })
  searchElementRef: ElementRef;
  @ViewChild(MapInfoWindow) infoWindow: MapInfoWindow;
  @ViewChild('map') mapRef: GoogleMap;
  @Input() showMap: boolean;
  @Input() enableCreateAndKeepSearching = false;
  @Input() createTypePlural = '';
  @Input() createTypeSingle = '';

  protected businesses$: Observable<GooglePlace[]> = new Observable<GooglePlace[]>();
  protected numSelected$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  protected prospectingData = new Map<string, Observable<ProspectData>>();
  protected mapExpanded$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  protected noResults$: Observable<boolean>;

  protected showSearchPrompt$: Observable<boolean>;
  protected highlightedBusiness$$ = new ReplaySubject<GooglePlace>(1);

  protected readonly maxZoom = 16;
  protected openedInfoWindow: string;
  protected markerAnimation: google.maps.Animation;

  protected dataSource: GalaxyDataSource<GooglePlace>;
  protected readonly getImageSrc = getImageSrc;

  protected readonly columns: GalaxyColumnDef[] = [
    {
      id: 'marker',
      title: '',
    },
    {
      id: 'name',
      title: 'Businesses',
    },
    {
      id: 'reviews',
      title: 'Reviews',
    },
    {
      id: 'gbp',
      title: 'Google Business Profile',
    },
    {
      id: 'website',
      title: 'Website',
    },
  ];

  private readonly retryConfig: RetryConfig = {
    count: 60,
    delay: 1000, //1 second
  };

  private readonly destroyRef: DestroyRef = inject(DestroyRef);

  constructor(
    private readonly googlePlaceService: GooglePlacesService,
    @Inject(PARTNER_ID_TOKEN) private readonly partnerId$: Observable<string>,
    private readonly eacTrackingService: EACUsageTrackingService,
    private readonly prospectorService: ProspectingSdkService,
    @Optional()
    @Inject(BUSINESS_SEARCH_DUPLICATE_SERVICE)
    private readonly duplicateService?: DuplicateServiceInterface,
  ) {}
  ngOnInit(): void {
    this.dataSource = new GalaxyDataSource<GooglePlace>(
      new SearchMapData(this.googlePlaceService, this.duplicateService),
    );
    this.businesses$ = this.googlePlaceService.googlePlaces$;

    const hasFetchedProspectData$ = this.businesses$.pipe(
      filter((businesses) => businesses?.length > 0),
      withLatestFrom(this.partnerId$),
      switchMap(([businesses, partnerId]) => {
        const prospects = businesses.map((business) => {
          return {
            placeId: business.placeId,
            location: {
              latitude: business.lat || 0,
              longitude: business.lng || 0,
            },
          } as ProspectIdentifierInterface;
        });
        const request: FetchProspectDataRequestInterface = {
          prospects: prospects,
          partnerId: partnerId,
        };
        return this.prospectorService.fetchProspectData(request);
      }),
    );

    combineLatest([hasFetchedProspectData$, this.businesses$])
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter(([fetched, businesses]) => !!fetched && businesses?.length > 0),
        withLatestFrom(this.partnerId$),
      )
      .subscribe(([[, businesses], partnerId]) => {
        businesses.forEach((business) => {
          if (business?.placeId) {
            const prospectData = this.createProspectDataFetcher(business, partnerId);
            this.prospectingData.set(business.placeId, prospectData);
          }
        });
      });

    this.businesses$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((businesses) => {
      this.openedInfoWindow = null;
      this.mapExpanded$$.next(businesses.length > 0);
    });

    this.noResults$ = this.googlePlaceService.noResults$;
    this.showSearchPrompt$ = combineLatest([this.mapExpanded$$, this.noResults$]).pipe(
      map(([mapExpanded, noResults]) => !(mapExpanded || noResults)),
      startWith(true),
    );
  }

  private createProspectDataFetcher(business: GooglePlace, partnerId: string): Observable<ProspectData> {
    const request: GetProspectDataRequestInterface = {
      placeId: business.placeId,
      partnerId,
    };
    const prospectDataRetry = this.prospectorService.getProspectData(request).pipe(
      map((resp) => {
        if (!resp?.prospectData) {
          throw new Error('Could not fetch prospect data');
        }
        return resp;
      }),
      retry(this.retryConfig),
    );

    return prospectDataRetry.pipe(
      map((resp) => {
        return resp?.prospectData;
      }),
      catchError(() => {
        return of({} as ProspectData);
      }),
      shareReplay(1),
    );
  }

  protected onMapReady(googleMap: google.maps.Map): void {
    this.googlePlaceService.init(googleMap, this.searchElementRef.nativeElement, this.searchType);
    this.markerAnimation = google.maps.Animation.DROP;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  protected highlightBusiness(business: GooglePlace, marker?: MapMarker): void {
    this.openedInfoWindow = business.markerLabel;
    this.highlightedBusiness$$.next(business);
    this.infoWindow.options = { pixelOffset: new google.maps.Size(0, -35) };
    this.infoWindow.position = { lat: business.lat, lng: business.lng };
    this.infoWindow.open();
  }

  protected closeInfoWindow(): void {
    this.highlightedBusiness$$.next(null);
    this.openedInfoWindow = null;
  }

  protected goToGoogleMaps(lat: number, lng: number, placeId: string): void {
    window.open(`https://www.google.com/maps/search/?api=1&query=${lat},${lng}&query_place_id=${placeId}`, '_blank');
  }

  protected setSelectedBusinesses(rows: Row[]): void {
    const selectedBusinesses = rows.map((row) => this.convertRowToGooglePlace(row));
    this.selectedBusinesses.emit(selectedBusinesses);
    this.prospectingDataOutput.emit(this.prospectingData);
    this.searchTermOutput.emit(this.getSearchTerm());
    this.numSelected$$.next(selectedBusinesses?.length | 0);
  }

  protected convertRowToGooglePlace(row: Row): GooglePlace {
    return {
      formattedAddress: row['formattedAddress'] || '',
      lat: row['lat'] || 0,
      lng: row['lng'] || 0,
      name: row['name'] || '',
      placeId: row['placeId'] || '',
      regionShort: row['regionShort'] || '',
      shortAddress: row['shortAddress'] || '',
      markerLabel: row['markerLabel'] || '',
      photo: row['photo'] || '',
      phoneNumber: row['phoneNumber'] || '',
      website: row['website'] || '',
      country: row['country'] || '',
      countryShort: row['countryShort'] || '',
      region: row['region'] || '',
      types: row['types'] || [],
      reviewScore: row['reviewScore'] || undefined,
      reviewCount: row['reviewCount'] || undefined,
    } as GooglePlace;
  }
  protected createOnSingleBusiness(place: GooglePlace): void {
    this.selectedBusinesses.emit([place]);
    this.prospectingDataOutput.emit(this.prospectingData);
    this.searchTermOutput.emit(this.getSearchTerm());
    this.create.emit();
  }
  protected noneSelected(): void {
    this.selectedBusinesses.emit([]);
    this.create.emit();
  }

  protected getSearchTerm(): string {
    return (this.searchElementRef.nativeElement as HTMLInputElement).value;
  }

  protected search(searchTerm: string): void {
    (this.searchElementRef.nativeElement as HTMLInputElement).value = searchTerm;
    this.googlePlaceService.setPlacesSearch(searchTerm);
  }

  protected enterButtonCheck($event: KeyboardEvent): void {
    if ($event.key === 'Enter') {
      this.eacTrackingService.track(UsageTracking.SearchButtonEnterUsage);
    }
  }

  protected createClicked(): void {
    this.create.emit();
  }

  clearSearch(): void {
    this.dataSource.clearSelection();
    this.googlePlaceService.setPlacesSearch(this.getSearchTerm());
  }
}
