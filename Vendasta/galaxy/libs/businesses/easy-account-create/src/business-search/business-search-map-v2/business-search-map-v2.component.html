<glxy-page-wrapper class="business-lead-wrapper" widthPreset="wide">
  <glxy-form-field suffixIcon="search" class="search-bar">
    <input
      matInput
      #search
      type="text"
      [placeholder]="'BUSINESS_SEARCH_MAP.EXAMPLE_SEARCH_PLACEHOLDER' | translate"
      autocorrect="off"
      autocapitalize="off"
      spellcheck="off"
      (keyup)="enterButtonCheck($event)"
      data-action="business-prospector-create-input-field"
    />
  </glxy-form-field>
  <glxy-empty-state *ngIf="showSearchPrompt$ | async">
    <glxy-empty-state-hero class="empty-state-hero">
      <img [src]="getImageSrc('images/empty-state/lead-finder-empty-state.png')" />
    </glxy-empty-state-hero>
    <glxy-empty-state-title>
      <ng-container *ngIf="createTypePlural !== ''; else defaultTitle">
        {{ 'BUSINESS_SEARCH_MAP.FIND_NEW_LEADS_TITLE' | translate: { createType: createTypePlural } }}
      </ng-container>
      <ng-template #defaultTitle>
        {{ 'BUSINESS_SEARCH_MAP.DEFAULT.FIND_NEW_LEADS_TITLE' | translate }}
      </ng-template>
    </glxy-empty-state-title>
    <p>
      {{ 'BUSINESS_SEARCH_MAP.FIND_NEW_LEADS_SUBTITLE' | translate }}
    </p>
  </glxy-empty-state>
  <div [ngClass]="{ 'hide-map': (mapExpanded$$ | async) === false }">
    <google-map
      #map
      width="100%"
      [options]="{ maxZoom: maxZoom, mapTypeControl: true, fullscreenControl: true }"
      (mapInitialized)="onMapReady($event)"
      [ngClass]="{ 'hide-map': !showMap }"
    >
      <map-marker
        #marker="mapMarker"
        *ngFor="let business of businesses$ | async"
        [options]="{ animation: markerAnimation }"
        [position]="{ lat: business.lat, lng: business.lng }"
        [clickable]="true"
        (mapClick)="highlightBusiness(business, marker)"
        [label]="business.markerLabel"
      ></map-marker>
      <map-info-window (closeclick)="closeInfoWindow()">
        <div class="agm-info-window-content" *ngIf="highlightedBusiness$$ | async as business">
          <img *ngIf="business.photo" class="agm-info-window-content--image" src="{{ business.photo }}" />
          <div class="agm-info-window-content--info">
            <h2>
              {{ business.name }}
              <a color="primary" (click)="goToGoogleMaps(business.lat, business.lng, business.placeId)">
                <mat-icon class="google-map-opener">open_in_new</mat-icon>
              </a>
            </h2>
            <div>{{ business.shortAddress }}</div>
            <button
              mat-flat-button
              class="agm-info-window-content--action"
              color="primary"
              (click)="createOnSingleBusiness(business)"
            >
              {{ 'BUSINESS_SEARCH_MAP.CREATE_TYPE' | translate: { createType: createTypeSingle } }}
            </button>
          </div>
        </div>
      </map-info-window>
    </google-map>
    <glxy-table-container
      class="business-table"
      *ngIf="businesses$"
      [dataSource]="dataSource"
      [columns]="columns"
      [showSelection]="true"
      (selectionChanged)="setSelectedBusinesses($event)"
      [pageSizeOptions]="[20]"
      [pageSize]="20"
      [fullWidth]="false"
      [showFooter]="false"
    >
      <glxy-table-content-header
        class="business-table-header"
        [showFilters]="false"
        [showSearch]="false"
        [showSort]="false"
        [showExport]="false"
        [showColumnArrange]="false"
        [showActions]="false"
      ></glxy-table-content-header>

      <table mat-table>
        <tr mat-header-row *matHeaderRowDef="[]"></tr>

        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <glxy-table-selection></glxy-table-selection>
          </th>
          <td mat-cell *matCellDef="let row">
            <glxy-table-selection [row]="row" [disableSelection]="row.isDuplicate"></glxy-table-selection>
          </td>
        </ng-container>

        <ng-container matColumnDef="marker">
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let row">
            <div class="marker-pin">
              {{ row.markerLabel }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef class="header-cell">
            {{ 'BUSINESS_SEARCH_MAP.TABLE.BUSINESSES_HEADER' | translate }}
          </th>
          <td mat-cell *matCellDef="let row" class="base-cell">
            @if (row.isDuplicate) {
              <div class="duplicate-cell">
                <span>
                  <div class="cell-header-duplicate-text">{{ row.name }}</div>
                  <div class="cell-sub-duplicate-text">{{ row.formattedAddress }}</div>
                </span>
                <glxy-badge [size]="'small'" [color]="'green'"
                  >✓ {{ 'BUSINESS_SEARCH_MAP.TABLE.ADDED' | translate }}</glxy-badge
                >
              </div>
            } @else {
              <div class="cell-header-text">{{ row.name }}</div>
              <div class="cell-sub-text">{{ row.formattedAddress }}</div>
            }
          </td>
        </ng-container>

        <ng-container matColumnDef="reviews">
          <th mat-header-cell *matHeaderCellDef class="header-cell">
            {{ 'BUSINESS_SEARCH_MAP.TABLE.REVIEWS_HEADER' | translate }}
          </th>
          <td mat-cell *matCellDef="let row" class="base-cell">
            <div class="reviews-cell">
              <div class="cell-header-text">
                {{ row.reviewScore }}
              </div>
              <mat-icon class="review-star">star</mat-icon>
              ( {{ row.reviewCount }} )
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="gbp">
          <th mat-header-cell *matHeaderCellDef class="header-cell">
            {{ 'BUSINESS_SEARCH_MAP.TABLE.GBP_HEADER' | translate }}
          </th>
          <td mat-cell *matCellDef="let row" class="base-cell">
            <div *ngIf="prospectingData.get(row.placeId) | async as prospectData; else stillFetching">
              <glxy-badge [color]="'grey'" *ngIf="prospectData?.gbpClaimStatus?.gbpIsClaimed; else notClaimed">
                {{ 'BUSINESS_SEARCH_MAP.TABLE.GBP_CLAIMED' | translate }}
              </glxy-badge>
              <ng-template #notClaimed>
                <glxy-badge [color]="'green'" *ngIf="prospectData?.gbpClaimStatus; else inconclusive">
                  {{ 'BUSINESS_SEARCH_MAP.TABLE.GBP_UNCLAIMED' | translate }}
                </glxy-badge>
              </ng-template>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="website">
          <th mat-header-cell *matHeaderCellDef class="header-cell">
            {{ 'BUSINESS_SEARCH_MAP.TABLE.WEBSITE_HEADER' | translate }}
          </th>
          <td mat-cell *matCellDef="let row" class="base-cell">
            <div *ngIf="prospectingData.get(row.placeId) | async as prospectData; else stillFetching">
              <glxy-badge [color]="'grey'" *ngIf="prospectData?.websiteStatus?.hasWebsite; else notClaimed">
                {{ 'BUSINESS_SEARCH_MAP.TABLE.WEBSITE_FOUND' | translate }}
              </glxy-badge>
              <ng-template #notClaimed>
                <glxy-badge [color]="'green'" *ngIf="prospectData?.websiteStatus; else inconclusive">
                  {{ 'BUSINESS_SEARCH_MAP.TABLE.WEBSITE_NOT_FOUND' | translate }}
                </glxy-badge>
              </ng-template>
            </div>
          </td>
        </ng-container>
        <tr mat-row *matRowDef="let row; columns: []"></tr>
      </table>
    </glxy-table-container>
    <ng-container *ngIf="numSelected$$ | async as numSelected">
      <glxy-sticky-footer *ngIf="numSelected > 0" [rightAligned]="true">
        <button mat-flat-button color="primary" (click)="createClicked()">
          {{ 'BUSINESS_SEARCH_MAP.CREATE_TYPE' | translate: { createType: createTypePlural } }}
        </button>
        <glxy-sticky-footer-secondary>
          {{ 'BUSINESS_SEARCH_MAP.SELECTED' | translate: { count: numSelected } }}
        </glxy-sticky-footer-secondary>
      </glxy-sticky-footer>
    </ng-container>
  </div>
  <div *ngIf="noResults$ | async" class="no-results">
    <ng-container>
      <glxy-empty-state>
        <glxy-empty-state-title>
          {{ 'BUSINESS_SEARCH_MAP.NO_RESULTS' | translate }}
        </glxy-empty-state-title>
        <p>
          <ng-container *ngIf="createTypeSingle !== ''; else defaultInstructions">
            {{ 'BUSINESS_SEARCH_MAP.DISCLAIMER.INSTRUCTIONS_V2' | translate: { createType: createTypeSingle } }}
          </ng-container>
          <ng-template #defaultInstructions>
            {{ 'BUSINESS_SEARCH_MAP.DEFAULT.INSTRUCTIONS_V2' | translate }}
          </ng-template>
        </p>
        <glxy-empty-state-actions>
          <a
            mat-stroked-button
            (click)="noneSelected()"
            [attr.data-action]="'business-prospector-create-empty-state-create'"
          >
            {{ 'BUSINESS_SEARCH_MAP.DISCLAIMER.BUTTON' | translate: { createType: createTypeSingle } }}
          </a>
        </glxy-empty-state-actions>
      </glxy-empty-state>
    </ng-container>
  </div>
</glxy-page-wrapper>

<ng-template #inconclusive>
  <glxy-badge [color]="'red'">
    {{ 'BUSINESS_SEARCH_MAP.TABLE.INCONCLUSIVE' | translate }}
  </glxy-badge>
</ng-template>
<ng-template #stillFetching>
  <glxy-loading-spinner [inline]="true"></glxy-loading-spinner>
</ng-template>
