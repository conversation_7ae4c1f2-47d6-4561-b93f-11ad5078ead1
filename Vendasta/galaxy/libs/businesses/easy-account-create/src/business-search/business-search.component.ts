import { Component, Inject, ViewChild } from '@angular/core';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { TranslateService } from '@ngx-translate/core';
import { Observable, firstValueFrom } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { UsageTracking } from '../objects';
import { EACUsageTrackingService } from '../tracking/eac-usage-tracking.service';
import { BusinessSearchMapComponent } from './business-search-map/business-search-map.component';
import { PROSPECT_SEARCH, SearchType } from './google-place.service';
import { DISPLAY_SIMPLE_SEARCH } from './providers';

enum SearchTab {
  SimpleSearch,
  ProspectSearch,
}

@Component({
  selector: 'business-search',
  templateUrl: './business-search.component.html',
  styleUrls: [],
  standalone: false,
})
export class BusinessSearchComponent {
  displayNewSearch$: Observable<string> = this.displaySimpleSearch$.pipe(
    startWith('loading'),
    map((display) => (display ? 'show' : 'hide')),
  );
  public selectedTab: number = SearchTab.SimpleSearch;

  @ViewChild('prospectSearch') prospectSearch: BusinessSearchMapComponent;
  @ViewChild('simpleSearch') simpleSearch: BusinessSearchMapComponent;

  constructor(
    @Inject(DISPLAY_SIMPLE_SEARCH)
    readonly displaySimpleSearch$: Observable<boolean>,
    private readonly eacTrackingService: EACUsageTrackingService,
    @Inject(TranslateService) private readonly translateService: TranslateService,
  ) {
    this.eacTrackingService.trackNewSession();
  }

  switchToAdvancedTab(): void {
    this.selectedTab = SearchTab.ProspectSearch;
  }

  changeSearchType($event: SearchType): void {
    if ($event === PROSPECT_SEARCH) {
      this.switchToAdvancedTab();
      this.prospectSearch.search(this.simpleSearch.getSearchTerm());
    } else {
      throw new Error('Unsupported search type');
    }
  }

  async trackTabUsage($event: MatTabChangeEvent): Promise<void> {
    const advancedTabLabel = await firstValueFrom(this.translateService.stream('BUSINESS_SEARCH.ADVANCED_TAB'));

    if ($event.tab.textLabel === advancedTabLabel) {
      this.eacTrackingService.track(UsageTracking.GeneralTab);
    } else {
      this.eacTrackingService.track(UsageTracking.SpecificTab);
    }
  }
}
