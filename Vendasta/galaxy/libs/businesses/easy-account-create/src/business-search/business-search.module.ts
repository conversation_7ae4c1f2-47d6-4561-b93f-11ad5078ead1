import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyWrapModule } from '@vendasta/galaxy/galaxy-wrap';
import { ProductAnalyticsModule } from '@vendasta/product-analytics';
import baseTranslation from '../assets/i18n/en_devel.json';
import { BusinessSearchConfirmationComponent } from './business-search-confirmation/business-search-confirmation.component';
import { BusinessListComponent } from './business-search-map/business-list.component';
import { BusinessSearchMapComponent } from './business-search-map/business-search-map.component';
import { BusinessSearchComponent } from './business-search.component';
import { BusinessSearchMapV2Component } from './business-search-map-v2/business-search-map-v2.component';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyTableModule } from '@vendasta/galaxy/table';
import { MatMenuModule } from '@angular/material/menu';
import { MatTableModule } from '@angular/material/table';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyStickyFooterModule } from '@vendasta/galaxy/sticky-footer';

@NgModule({
  declarations: [
    BusinessSearchMapComponent,
    BusinessSearchMapV2Component,
    BusinessSearchComponent,
    BusinessListComponent,
    BusinessSearchConfirmationComponent,
  ],
  exports: [
    BusinessSearchMapComponent,
    BusinessSearchMapV2Component,
    BusinessSearchComponent,
    BusinessSearchConfirmationComponent,
  ],
  imports: [
    CommonModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    GoogleMapsModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: 'common/businesses-easy-account-create',
      baseTranslation: baseTranslation,
    }),
    ProductAnalyticsModule,

    MatCheckboxModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatCardModule,
    MatSelectModule,
    GalaxyWrapModule,
    GalaxyFormFieldModule,
    GalaxyEmptyStateModule,
    GalaxyTableModule,
    MatMenuModule,
    MatTableModule,
    GalaxyLoadingSpinnerModule,
    GalaxyBadgeModule,
    GalaxyTooltipModule,
    GalaxyPageModule,
    GalaxyStickyFooterModule,
  ],
})
export class BusinessSearchModule {}
