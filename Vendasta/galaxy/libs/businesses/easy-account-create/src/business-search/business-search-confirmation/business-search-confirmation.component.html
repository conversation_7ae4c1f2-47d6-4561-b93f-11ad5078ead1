<ng-container *ngIf="marketOptions$ | async as markets">
  <mat-card appearance="outlined" class="market-card" *ngIf="markets?.length > 1">
    <div class="title">{{ 'MULTI_SEARCH.ADD_ACCOUNTS_TO_MARKET' | translate }}</div>
    <mat-divider></mat-divider>
    <div class="market-selector-container">
      <glxy-form-field [bottomSpacing]="false">
        <mat-select [(value)]="defaultMarketId" (selectionChange)="handleMarketChange($event)">
          <mat-option *ngFor="let market of markets" [value]="market.market_id">
            {{ market.name }}
          </mat-option>
        </mat-select>
      </glxy-form-field>
    </div>
  </mat-card>
</ng-container>

<mat-card appearance="outlined" class="multi-search-card">
  <div class="title">{{ businesses?.length }} {{ 'MULTI_SEARCH.BUSINESSES_SELECTED' | translate }}</div>
  <mat-divider></mat-divider>
  <ng-container class="multi-search-list" *ngFor="let business of businesses; let last = last">
    <mat-list-item>
      <h2>{{ business.name }}</h2>
      <div class="business-info--address">
        {{ business.formattedAddress }}
        <br />
        {{ tags[business.placeId] }}
      </div>
    </mat-list-item>
    <mat-divider *ngIf="businesses.length > 1 && !last"></mat-divider>
  </ng-container>
</mat-card>
