import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { MatSelectChange } from '@angular/material/select';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { GooglePlace, Market, MarketsService } from '../../../src';

@Component({
  selector: 'business-search-confirmation',
  templateUrl: './business-search-confirmation.component.html',
  styleUrls: ['./business-search-confirmation.component.scss'],
  standalone: false,
})
export class BusinessSearchConfirmationComponent implements OnInit {
  @Input() businesses: GooglePlace[];
  @Output() selectedMarketId = new EventEmitter<string>();

  readonly marketOptions$: Observable<Market[]>;
  defaultMarketId: string;
  tags: string[] = [];

  constructor(@Inject('MARKET_SERVICE_TOKEN') marketSvc: MarketsService) {
    this.marketOptions$ = marketSvc.markets;
    marketSvc.currentMarket$
      .pipe(
        map((market) => (this.defaultMarketId = market.market_id)),
        take(1),
      )
      .subscribe();
  }

  ngOnInit(): void {
    this.selectedMarketId.emit(this.defaultMarketId);
    this.formatTags();
  }

  handleMarketChange(selectedMarket: MatSelectChange): void {
    this.selectedMarketId.emit(selectedMarket.value);
  }

  formatTags(): void {
    this.businesses?.forEach((business) => {
      const tagArray = business.types;
      let businessTags = tagArray.toString().replace(/_/g, ' ');
      businessTags = businessTags.replace(/,+/g, ', ');
      this.tags[business.placeId] = businessTags;
    });
  }
}
