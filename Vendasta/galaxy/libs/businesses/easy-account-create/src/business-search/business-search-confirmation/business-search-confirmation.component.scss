@use 'design-tokens' as *;

.market-card {
  display: block;
  max-width: 600px;
  margin: 25px auto;
  padding: 6px 12px;
  .market-selector-container {
    padding: 10px;
  }
}

.multi-search-card {
  display: block;
  max-width: 600px;
  margin: auto;
  padding: 6px 12px;
  h2 {
    margin-bottom: 8px;
    font-size: 14px;
  }
  .business-info--address {
    overflow: initial;
    white-space: unset;
    color: $secondary-text-color;
    font-size: 14px;
    line-height: 24px;
    margin-bottom: 8px;
  }
  mat-list-item {
    padding: 10px;
    display: block;
  }
}

.title {
  font-size: 18px;
  padding: 12px 2px;
}
