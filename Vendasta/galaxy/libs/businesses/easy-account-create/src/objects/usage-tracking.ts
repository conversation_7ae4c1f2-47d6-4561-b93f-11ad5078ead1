export const EAC_USAGE_TRACKING_TOKEN = 'lib.businesses.eac.eac-tracking-token';

export enum UsageTracking {
  GeneralTab = 'eac-click-general-tab',
  SpecificTab = 'eac-click-specific-tab',
  SkipToAccountCreation = 'eac-skip-to-acct-creation',
  SearchButtonEnterUsage = 'eac-search-button-enter-usage',
  CompetitionSelection = 'eac-selecting-competitors',
  MapPageContinue = 'eac-map-continue-button',
  CreateAccountWithDefaults = 'eac-create-account-button-with-defaults',
  DefaultProductsChange = 'eac-default-products-change',
  CategoryValidationError = 'eac-category-validation-error',
  PhoneValidationError = 'eac-phone-validation-error',
  GenerateSnapshotCTA = 'eac-generate-snapshot-cta',
  AddContactsCTA = 'eac-add-contacts-cta',
  CreateOpportunityCTA = 'eac-create-opportunity-cta',
  OverviewVideoClick = 'eac-overview-video-played',
  GoToNewBusiness = 'eac-go-to-new-business',
}
