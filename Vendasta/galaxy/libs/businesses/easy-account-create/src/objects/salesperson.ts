export interface Person {
  email: string;
  partnerId: string;
  salesPersonId: string;
  marketId?: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  photoUrl?: string;
}

export class Salesperson implements Person {
  firstName: string;
  lastName: string;
  partnerId: string;
  marketId: string;
  salesPersonId: string;
  email: string;
  phoneNumber: string;
  photoUrl: string;

  static fromSalesPersonVObject(vobject: any): Salesperson {
    return new Salesperson({
      firstName: vobject.first_name,
      lastName: vobject.last_name,
      partnerId: vobject.partner_id,
      marketId: vobject.market_id,
      salesPersonId: vobject.sales_person_id,
      email: vobject.email,
      phoneNumber: vobject.phone_number,
      photoUrl: vobject.picture_serving_url,
    });
  }

  constructor(data: Person) {
    Object.assign(this, data);
  }

  get contactNumber(): string {
    if (this.phoneNumber.length > 0) {
      return this.phoneNumber[0];
    }
    return '';
  }

  get name(): string {
    return `${this.firstName || ''} ${this.lastName || ''}`.trim();
  }

  get photo(): string {
    return this.photoUrl || '/static/images/Profile.svg';
  }
}
