CHANGE LOG
==========
### 13.0.0
- Upgrade to Angular 20

### 12.2.1
- Add `standalone: false` to all standalone components to prepare for Angular 19 update

### 12.2.0
- Fix IsValid$ stream in ProductActivationPrereqFormComponent

### 12.1.0
- Add experimental BusinessSearchMapV2 component 

### 12.0.0
- Remove `AddressFormService`

### 11.0.0
- Remove usages of `AddressFormService` in Easy Account Create and business profile pages

### 10.33.0
- reduce internal dependence on AddressFormService

### 10.32.1
- Fix issue for country field in `business-address-country-input` component
  - Putting in empty spaces/numbers/letters made the field valid for submission 

### 10.32.0

- Add more professional credentials
  - RD

## 10.31.2
- Set max-width for `.address-form-field-zip` to 45% to prevent overlapping fields.

## 10.31.1
- Fixes for merging hours of operation spanning midnight.

## 10.31.0
- Remove `bias_search_results_for_account_creation` feature flag
  - This feature flag was an experiment to bias account creation search results for South African users.

## 10.30.0
- Utilize galaxy form for business profile

## 10.29.0
- Hours of operation spanning midnight are now enterable and displayed.
  - Ex. 9:00 AM (Monday) to 2:00 AM (Tuesday) is displayed as 9:00 AM to 2:00 AM Monday.

## 10.28.2
- Categories and timezone won't load if admin tab is not enabled

## 10.28.1
- Remove feature flags from business profile
- Block user interaction until account group information form is loaded

## 10.28.0
- Remove deprecated service availability form fields

## 10.27.1
- Use undefined instead of null for empty service area object

## 10.27.0
- Add 'custom-fields' ID to features

## 10.26.1
- Fix account lists modal component
- Use MDC components instead of legacy ones for ingesting dialog data 

## 10.26.0
- Update business selector dialog to mdc components

## 10.25.2
- Handle error when getting country configuration fails

## 10.25.1
- Remove hard location limit on place selector

## 10.25.0
- Alert user when the business has invalid (inactive or hidden) categories

## 10.24.1
- Replace legacy category selector with new one in business profile

## 10.24.0
- Replace legacy category selector with new one in business profile

## 10.23.0
- Add input for `BusinessProfileComponent`'s tab background

## 10.22.2
- Add validator for invalid company name characters

## 10.22.1
- Fix validators for non-service area businesses

## 10.22.0
- Add support for selecting multiple service area businesses

## 10.21.0
- Add `place-autocomplete` component for selecting a google place

## 10.20.2
- Fix EAC service to use the correct form field for vCategories

## 10.20.1
- Detect health categories from business-category library

## 10.20.0
- Remove `business_category_library` feature flag

## 10.19.2
- Fix show empty fields in business profile

## 10.19.1
- Fix the Professional Records visibility

## 10.19.0
- Replace `@agm/core` with `@angular/google-maps`

## 10.18.3
- Hotfix: can't edit other address fields without also editing country.

## 10.18.2
- Hotfix: can't edit country on business profile.

## 10.18.1
- Fix media tab image modals

## 10.18.0
- Refactor `address-form-v2` to use country input, refactor `country-input` into subform.

## 10.17.0
- Remove unused IDs from `Feature`

## 10.16.0
- Refactor address form to fix bugs into `address-form-v2`

### 10.15.0
- Added new country input to refactor out some of the complex logic

### 10.14.3
- HotFix: don't call `EasyAccountCreateService.listEquals` with strings

### 10.14.2
- Fix empty names in the category selector in the business profile
- Remove the category publishing check from the business profile

### 10.14.1
- Fix unintended snack bar for custom fields

### 10.14.0
- Lock category changes when publishing

### 10.13.2
 - Handle deleted account groups in `getSectionConfig$()` method of `ProductActivationPrereqFormComponent`

 ### 10.13.1
 - update copy for find leads search
 
### 10.13.0
- Polish for Find Leads flow
  - Clear loading spinner on success or error
  - Update content to not reference old general search
  - Add option to hide competitors list
  
### 10.11.3
- Fix - New easy account create form submission error

### 10.11.2

- Fix - update equality check in `product-activation-prereq-form.component.ts` - equality check between arrays compares
  memory location

### 10.11.1

- Fix - Use the feature flag to control the old and new versions of the general search

### 10.11.0

- Add `business-search-confimation` page

### 10.10.0

- Remove `auxiliary-data` feature flag usage

### 10.9.0

- Add `AccountListsModalComponent` to library
  - Plug in to va-mat-filter-table to add accounts to lists

### 10.8.1

- Save `vCategories` to `richdata.inferredFields`, in case they were not changed

### 10.8.0

- Add usage of businessCategoryLibrary under feature flag

### 10.7.0

- `BusinessSelectorDialogComponent`
  - Export`DialogData`, which is a required input to the dialog component
  - Allow the account group lookup's projection filters to be overridden so you
    can load additional data about the businesses.

### 10.6.0

- Provide a `clearAddress` function in `AddressFormService`

### 10.5.8

- Return empty province/state if address service cannot get province

### 10.5.7

- Update auxiliary-data library to latest
  - support for Users' data

### 10.5.6

- Fix country not required in easy account create form
- Add address form required fields error message
- Remove bottom margin from address preview
- Remove form field checkbox top margin to keep form compact.

### 10.5.5

- Fix typeError applying data to uninitialized form.

### 10.5.4

- Republish to remove @locl/core dependency

### 10.5.3

- Fix request when updating the gender field

### 10.5.2

- Remove unused, non-source, translation files

### 10.5.1

- Fix translation string for gender in an account's professional records.

### 10.5.0

- Add en_devel.json

### 10.4.2

- Fix translation string for Operational Information
- Add labels to Operational Information dropdowns

### 10.4.1

- Fix @ngx-translate stream multiple items bug

### 10.4.0

- Make breadcrumbs compatible with new SSC manage accounts route

### 10.3.0

- After installing `@vendasta/marketplace-packages`, replace usages of `vendasta/core/marketplace-packages`.

### 10.2.1

- Updated business info error messages for Services Offered and Brands Carried

### 10.2.0

- Restyle ProductActivationPrereqFormComponent to unnest the expansion panel from its container and align styles (
  spacing, text) with what's used in other order form components

### 10.1.0

- added new error messages for Product Activation Form into i18n

### 10.0.2

- Remove the warning icon on the product activation pre req form

### 10.0.1

- change description maximum character back to 750

### 10.0.0

- BusinessSelectorDialogComponent now requires partnerId and (in non-partner contexts) marketId to be passed into the
  dialog component's data; now outputs both NAP data and external identifiers
  - removes the partnerId injection token as usages of BusinessSelectorDialogComponent are now required to provide this
    input when opening the dialog
  - marketId is required for proper filtering of account group data in non partner user contexts (eg. SSC users in a
    specific market won't have access to many account groups otherwise)
  - updated README documentation

### 9.13.1

- Fix form validation inaccuracies with the ProductActivationPrereqFormComponent
  - stuck as invalid when properly filled out
  - passing as valid when form has values but they haven't been submitted/saved

### 9.13.0

- Increase the long business description maximum characters limit to match with SSC create account business description
  maximum characters

### 9.12.1

- Update the use of confirm function in Confirmation service.

### 9.12.0

- Fix formatting phone numbers, for numbers in a different country than the account.

### 9.11.0

- Sort the competitors based on 'rating'

### 9.10.2

- Fix the Media Tab not showing in Business App, remove the AccountGroupMediaModule & MediaModule from MediaTabModule.

### 9.10.1

- Fix long business description maximum character length. Now changes the max based on the partner.

### 9.10.0

- Remove feature flag checks for `account_creation_competitors`

### 9.9.1

- Add session ID tracking to EAC

### 9.9.0

- Rename AccountGroupSelectorDialogComponent and AccountGroupSelectorDialogModule to BusinessSelectorDialogComponent and
  BusinessSelectorDialogModule

### 9.8.0

- Add usage tracking to EAC

### 9.7.0

- add AccountGroupSelectorDialogComponent to the library

### 9.6.1

- Responsive Business Search

### 9.6.0

- Lexicon changes

### 9.5.1

- Modify EAC simple business search empty state
  - Include new empty state message
  - Add CTA to rerun search in general tab

### 9.5.0

#### Added

- info text showing phone number format in business profile

### 9.4.0

- Modify competitor search
  - Increase competitor search radius
  - Filter out generic types 'point_of_interest' and 'establishment'

### 9.3.2

#### Reverted

- Phone numbers on the edit-business-profile form.
  - The new phone-input works for non-North-American numbers, but not NA numbers.

### 9.3.1

#### Fixed

- Phone numbers on the edit-business-profile form no longer show error messages for non-north-american numbers
  - The phone inputs look visually different, and require the user to select a country, but no changes are needed in the
    pages using this form. (The phone inputs are now galaxy-phone-input.)

### 9.3.0

- Adds the reopening date to the operational information. This will only appear if the business is set to a variant of
  closed.

### 9.2.0

- remove internal timezone selector for timezone selector from @vendasta/galaxy

### 9.1.5

- fix deep import warnings

### 9.1.4

- Migrate from 'frontend/address' to 'common/address' weblate component name
- Migrate from 'frontend/businesses' to 'common/businesses' weblate component name

### 9.1.3

- Fix undefined error when no business is provided for activation prereq form

### 9.1.2

- Remove translation file exports from index

### 9.1.1

- Transfer czech translations from frontend/businesses

### 9.1.0

- Load easy-account-create translations using LexiconModule
- Load product-activation-prereq-form translations using LexiconModule

### 9.0.0

- Load translations using LexiconModule
- Requires adding LexiconModule.forRoot() in the root app module

### 8.16.0

- Add timezone selector to business profile account group editor

### 8.15.12

- Unrevert 8.15.9, fixing a bug

### 8.15.11

- Fixes bug where only the competitor checkbox could be selected
- Fixes bug where competitors would scroll behind the business

### 8.15.10

- Change competitor marker color to orange
- Update README

### 8.15.9

- Revert 8.15.8

### 8.15.8

- Unrevert 8.15.1, fixing a bug

### 8.15.7

- Selected business is sticky at the top of the list in map

### 8.15.6

- Show competitor addresses in map info popup

### 8.15.5

- Only show competitors to select in the easy account create map page if there's a single business in the business list

### 8.15.4

- Selecting Continue in the easy account create map should use list of selected competitors.
- If there's only 1 business in the easy account create map found, Continue CTA should stay visible

### 8.15.3

- Hide continue button for competitor info popups on maps screen

### 8.15.2

- Revert 8.15.1

### 8.15.1

- Remove manual entry of `Date of business disruption`
  - it is now inferred such that it is cleared if the business open/closed state is set to unspecified or open.
    Otherwise it is set to the current date.

### 8.15.0

- Add createCompetitors to create operations in create account

### 8.14.0-alpha.1

- Easy account create allows selection of competitors in the map stage

### 8.13.0

- Add `inferCompetitorsNapData` function
  - looks up NAP data for a list of competitor place ids

### 8.12.2

- Fix clicking cancel and there is only one blank competitor it should reset the fields

### 8.12.1

- Fix update competitors in competitors tab

### 8.12.0

- Add Competitors tab service

### 8.11.1

- Fix setting default value of gender incorrectly in healthcare professional information

### 8.11.0

- Add Competitors tab

### 8.10.4

- Move appointments options to business hours tab

### 8.10.3

- Fix Heimdall module import for SSC by adding module import directly to the `BusinessSearchModule`

### 8.10.2

- handle orders with no line items so that the prereq form doesn't render

### 8.10.1

- Add Check for `healthCareProfessionalInformation` in business profile.

### 8.10.0

- Support better print styles for the Product Activation Prereq Form

### 8.9.0

- Update agm dependency to a version that support changes in angular 9 and 10

### 8.8.2

- Improve UI with updated copies
- Fix issue where search would blink as advanced search before loading properly with both searches

### 8.8.1

- Fix injector token name being used inside `BusinessSearchMapComponent`

### 8.8.0

- Add `business-search-map` component to be able to re-use both in PC and SSC.

### 8.7.5

- product-activation-prereq-form changes:
  - Clear the unsaved changes message if the values get reset to their original values

### 8.7.4

- Don't show message about unsaved changes in read-only mode
- Clear the unsaved changes message once the form has been saved; listen for changes to display it again upon new input

### 8.7.3

- Don't call `combineLatest` on an empty list - it will never emit

### 8.7.2

- Don't consider unsaved changes when calculating prereq form validity

### 8.7.1

- Create `isValid$` stream after creating businessProfileForm
- Publish replay of publish streams
- Stifle `valueChange` events after marking controls as touched

### 8.7.0

- Expose `isValid$` and `hasUnsavedChanges$` observables on activation prerequisite form

### 8.6.2

- Include disabled apps when building activation prerequisite form

### 8.6.1

- Translations update from Weblate for Frontend/businesses

### 8.6.0

- product-activation-prereq-form changes:
  - update the wording of the expansion panel when in read-only mode (form is not editable)
  - bug fix: check whether productIds are null before mapping over them

### 8.5.2

- Relaxed dependencies to include older/newer versions that have been tested
- Added "repository" to package.json so services like dependabot can link to
  change log.

### 8.5.1

- Bugfix: Show additional salespeople even if their market does not match the
  business' market. This allows those salespeople to be removed from the
  business via the UI.

### 8.5.0

- Load marketplace apps for prereq form using @vendasta/marketplace-apps

### 8.4.1

- Bugfix: Don't show loading state on error

### 8.4.0

- Product Activation Prereq form allows submission of empty HoO; fix issue with form built for more Business fields than
  specified; styling/wording changes

### 8.3.0

- update HoO component with styles and title marked as required; allow readonly view of the form

### 8.2.0

- Add Product Activation Form component used to surface fields that need to
  be saved to the account group on product activations

### 8.1.4

- Eliminate conjoined translation file hierarchy

### 8.1.3

- Translations update from Weblate for Frontend/businesses

### 8.1.2

- Translations update from Weblate for Frontend/address

### 8.1.1

- Fix a bug for `ServiceAvailability`

### 8.1.0

- Add `ServiceAvailability` into `business-profile`

### 8.0.0

- Convert translations to json files
  - Clients MUST include `"resolveJsonModule": true` in their tsconfig's `compilerOptions`

### 7.3.1

- Bump version

### 7.3.0

- Use translations from @vendasta/taxononmy-i18n

### 7.2.2

- Fix import from @vendasta/core -> @vendasta/partner

### 7.2.1

- Revert 7.2.0

### 7.2.0

- Store translation files as json

### 7.1.2

- Fix a typo in czech translations

### 7.1.1

- Remove references to core partner

### 7.1.0

- Add English translations for taxonomies

### 7.0.2

- Correct some czech translation strings

### 7.0.1

- Update @Vendasta dependencies

### 7.0.0

- Using Angular 9

### 6.7.0

- Add address translations for french and dutch

### 6.6.0

- Use MediaApiService instead of AccountGroupMediaApiService

### 6.5.0

- Export language files from business and address modules

### 6.4.0

- Use address microservice to load countries. Remove dependency on CountryStateService.

### 6.3.0

- Added `defaultBounds` parameter to init function in business-search/google-place.service.

### 6.2.0

- Add french and dutch translations

### 6.1.1

- Fix when partner does not have both `msDefaultEnabled` nor `adintelDefaultEnabled` enabled.
- Creating in sales-center-client was broken (page would just hang).

### 6.1.0

- Add a service to return the included products for a partner when creating new accounts.

* Remove functionality from business-create.service.ts to get product names

### 6.0.3

- address-form: Event to take the input from Google autofill

### 6.0.1

- Update dependencies

### 6.0.0

- Using Angular 8

### 5.2.0

- Internationalize taxonomy category names in business profile and add czech translations

### 5.1.1

- easy-account-create: Set country and state codes in contructNAPData when present
- address: Clear selected province when province options change

### 5.1.0

- Expanded the GooglePlace object to include more fields.

### 5.0.0

- BREAKING: Remove 'setLocale' from AddressService
  - Use TranslateService.use instead
- Use TranslateService locale to request address field labels
- Fix czech tooltips

### 4.30.0

- Add Czech translation file for address module

### 4.29.0

- Optionally use language values from injected tokens

### 4.28.1

- Do not import HttpClientModule as the implementing application should only import that

### 4.28.0

- Add relaxed matching back

### 4.27.2

- Set relaxed matching to false while we work out the details of how to roll it out

### 4.27.1

- Validate phone numbers with relaxed matching

### 4.27.0

- Always show the service area business checkbox on the business profile
  - Remove feature flag check for `service_area_business_checkbox`

### 4.26.5

- Grab timezone from account group's nap data

### 4.26.4

- Include timezone in NAP update operation

### 4.26.3

- Fix Professional Records translation strings
- Bind 'lengthOfRepeatedField' error function to component

### 4.26.2

- Format 'null' standardized title using correct object definition

### 4.26.1

- Return early when validating payment methods if control has no value

### 4.26.0

- Add Country-specific payment methods

### 4.25.0

- Extract translation strings into translation files

### 4.24.0

- Use @vendasta/partner SDK to check feature flags

### 4.23.0

- Changelog missing

### 4.22.0

- Changelog missing

### 4.21.3

- Take first value of address stream when submitting business details

### 4.21.2

- Handle case where the address service provides us with `undefined` for required fields.

### 4.21.1

- Apply province input field value after other field values have loaded

### 4.21.0

- Load required fields from country config. Set required state on address form fields.

### 4.20.0

- Use tssdk from address µs to get country configuration. Cache responses.

### 4.19.2

- Add padding around save footer in CSS.

### 4.19.1

- Mark address form as dirty for zip, address, and city fields

### 4.19.0

- If present in business profile form, add `address2` to AccountGroupLocation during EAC create

### 4.18.1

- Unsubscribe from subscriptions on component destroy

### 4.18.0

- Add 'dirty' observable to AddressFormService

### 4.17.0

- Handle address components in business profile using <address-form>

### 4.16.0

- Add `updateBusinessProfile` in `EasyAccountCreateService` for PCC implementation of EAC

### 4.15.0

- Add address package for handling address input and output
- Includes an ng form for country, zone, address1, address2, and zip fields

### 4.14.0

- Add more professional credentials
  - AGNP-C
  - ANP-C
  - CCC/SLP
  - DNP
  - DOT
  - FNP-MC
  - LGSW
  - LP
  - MBBS
  - MBCHB
  - NNP
  - NP-C
  - SLP

### 4.13.0

[easy-account-create](easy-account-create)

- move sales-center-client implementation (copied from partner-center-client) services for easy account create
  duplicated components

### 4.10.0

- Type Salesperson constructor

### 4.9.0

- Add GooglePlacesServiceProvider interface to Easy Account Create "contracts".

### 4.8.0

- Publish interface "contracts" for Easy Account Create.

### 4.7.1

- Fix a bug loading the media tab, released in 4.7.0

### 4.7.0

- Remove all required fields except for company name, to match the account group create API
  - market is also required, but it can remain required because by the time you edit you will always have a market, even
    if it's the default market
- If there is an invalid tab, jump to the first invalid tab upon clicking save
- Highlight the tab as invalid without first needing to submit.

### 4.6.0

- Add map, customerIdentifier and Foursquare to the hidden fields

### 4.5.0

- Remove Google Plus from Business Profile Component

### 4.4.1

- Fix an issue loading the business profile when the account group doesn't have any source URLs
- Import the AGMCoreModule

### 4.4.0

- Add a tooltip to the customer ID field

### 4.3.0

- Allow customer identifier to be edited, and don't hide it when the customer ID is blank.

### 4.2.0

- Support ng 7

### 4.1.3

- Update the FeatureFlagHost on local environment

### 4.1.2

- Update the wording of the placeholder for the zip field.

### 4.1.1

- Update the wording for the service area business checkbox.

### 4.1.0

- Add Service Area Business checkbox

### 4.0.2

- `@vendasta/core` no longer supports deep imports

### 4.0.1

- Expose FeatureFlagService again

### 4.0.0

- Follow Angular package format

BREAKING CHANGES:

- No longer support deep imports from library
  eg. `import { blah } from '@vendata/businesses/blah` should be changed to `import { blah } from '@vendasta/businesses`
  .

### 3.0.0

- Add support for rxjs 6

### 2.12.0

- Validate the required business details fields for whitespace

### 2.11.0

- Add Input to Business Profile Component to specify disabled fields such as 'markets' and 'sales'

### 2.10.0

- Remove green dot styling denoting unsaved changes on Business Profile Tabs after changes have been saved
- Do not remove green dot styling denoting unsaved changes on Business Profile tab when just the Hours tab has been
  changed
- Do not allow Media tab center icon to be clickable

### 2.9.0

- Save hours of operation from the modal dialog
- Remove save buttons from hours and media tab that does its own saving

### 2.8.0

- Updated Core dependency and updated GetSrcSet functions to use the ImageTransformationService.

### 2.7.1

- Update styling of the dot that indicates changes under a certain tab

### 2.7.0

- Removes address/location validation

### 2.6.1

- Add bullet point to label on changed tabs so that the change is visible to people with colorblindness

### 2.6.0

- Change colour of tab when there are unsaved changes

### 2.5.1

- fix reset pin button to mark field as dirty for submission

### 2.5.0

- address/geo values are now watched and geocoding is performed to determine if the address and geo match.
  If they don't, a button to reset the pin to the inferred location is presented.

### 2.4.1

- fix bug by preventing business profile component from passing undefined cellphone numbers into FormatPhoneNumberPipe

### 2.4.0

- add pipe for country specific phone number formatting and use it to display formatted phone numbers on edit screen

### 2.3.0

- Add totalLengthOfRepeatedFieldValidator check to Services Offered and Brands Carried

### 2.2.1

- import CoreModule to use http interceptor to fix 401 on get-multi

### 2.2.0

- update the business profile media service to use updated media api

### 2.1.0

- Remove feature flag check for media tab on load to improve apparent performance

### 2.0.2

- fix salespeople in conflicting markets show us unassigned in frontend

## 2.0.1

- Ignore version. Is no different than 2.0.0.

### 2.0.0

- No longer compatible with angular 4

### 1.4.0

- Update Media library to support deleting, associate image as type

### 1.3.1

- Fix business profile component when no account group id provided

### 1.3.1

- Fix business profile component so it reacts to account group id input changes

### 1.3.0

- Add support for angular 5

### 1.2.3

- Fix labels not appearing after material upgrade

### 1.2.2

- Set feature flag hosts to use https

### 1.2.0

- increase the size of the notes field

### 1.1.1

- styling fixes for media tab
- Fix media-tab service not clearing fields when accounts switched
- update @vendasta/forms version to 1.1.0

### 1.1.0

- Add media tab and library
- Add feature flag service
- Update @vendasta/forms version to 1.0.0
- Add @vendasta/uikit dependency (AlertService, Lightbox)

### 1.0.0

- Prepare for Angular Material beta 12 version
- Upgraded to use "Mat" prefixes for Angular material components.
- Follow https://www.npmjs.com/package/angular-material-prefix-updater to update your project

### 0.4.13

- Don't require a phone number for editing

### 0.4.12

- Remove BrowserAnimationsModule from imports. This hsould only be imported at the app root.
  SEE: https://github.com/angular/angular-cli/issues/5684

### 0.4.11

- Change angular libs to peer dependencies

### 0.4.10

- Support case where the LegacyProductDetails comes back as null

### 0.4.9

- Update @vendasta/core dependency

### 0.4.8

- Update @vendasta/core imports to be compatible with version 1.0.0 changes

### 0.4.7

- Do not add admin and sales form when admin tab is disabled

### 0.4.6

- Update @vendasta/form version to 0.6.5

### 0.4.5

- Update business categories placeholder to have an asterisk, since the field is (and always be) required.
- Update customer identifer to not show when there is no value.

### 0.4.4

- Update error message styling for additional salespeople

### 0.4.3

- Update font size for md-selects to match rest of the fields/components

### 0.4.2

- Update vendasta form version

### 0.4.1

- Added tooltip for additional salesperson to business-profile

### 0.4.0

- Add media-tab-gallery Sub-Module to business-profile module

### 0.3.3

- Support clearing out salesperson selection

### 0.3.2

- Fix type with `additionalSalesPersonIds`

### 0.3.1

- Pass in includeNullMarket as true to salesperson service

### 0.3.0

- Add additional salespeople input to the admin tab.

### 0.2.0

- Use SalespersonService to get salespeople options

### 0.1.4

- Update `@vendasta/forms` package to `0.6.2`

### 0.1.3

- Add max allowed number of tags to be 15

### 0.1.2

- Update @vendasta/core dependency to 0.1.0
- Update @vendasta/form dependency to 0.5.4

### 0.1.1

- Made `isTabEnabled` public method as it is being accessed in template and AOT complains if its private

### 0.1.0

- Add a new tab to the `buisiness-profile` component. It contains administration fields, i.e. `Market`, `Salesperson`
  , `Customer Identifier`, `Tags` and `Notes`.
- Update the `@vendasta/forms` package to `0.5.2`

### 0.0.4

- Update Vendasta/forms dependancy to version 0.5.1
- Update Changelog versions to match the move from the old business-profile component

### 0.0.3

- Emit onSave event when business-profile form is successfully saved

## Previous Package

### 5.0.1

#### Fix

- Add email validation to the personalInformation form

#### Update

- Remove media tab

### 5.0.0

- Update to fix breaking changes from account-group-sdk v3.0.0

### 4.0.0

- Update to fix breaking changes from account-group-sdk v2.0.0
- Fix dependancies

### 3.1.0

- More Validation

### 3.0.1

#### Update

- Balanced form cards in Location tab

### 3.0.0

#### Breaking

- Apps are now required to provide google maps credentials with `AgmCoreModule.forRoot()`

#### Add

- Use geo control from `@vendasta/forms` to control geo location

### 2.1.0

#### Add

- Hours of Operation Section

### 2.0.0

#### Breaking

- Use `@vendasta/taxonomy-service`

#### Add

- Use input tags control from `@vendasta/forms` to control taxonomy categories
  -- Professional information tab only displays when certain categories are selected

### 1.3.1

#### FIX

- ES5 target

### 1.3.0

#### Add

- New inputs `maxShareOfVoiceKeywords` and `maxCompetitors` (both defaulting to 3) which control the maximum number of
  items on those repeated fields

### 1.2.0

#### Add

- New input `editDisabled` which disables all form controls, prevents fields being added to repeated fields,
  and removes the save button

### 1.1.0

#### Add

- Tooltips
- productNames component input for tooltip whitelabeling
- Use `@vendasta/country-state-service`

#### Update

- Use @vendasta/form utils/validators to replace duplicated code

### 1.0.0

#### Add

- Packaged Business Profile Component
