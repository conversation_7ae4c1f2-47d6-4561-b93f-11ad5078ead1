{"name": "businesses", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/businesses/src", "prefix": "business", "targets": {"test": {"executor": "@nx/jest:jest", "options": {"jestConfig": "libs/businesses/jest.config.ts"}, "outputs": ["{workspaceRoot}/coverage/libs/businesses"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": ["scope:shared"]}