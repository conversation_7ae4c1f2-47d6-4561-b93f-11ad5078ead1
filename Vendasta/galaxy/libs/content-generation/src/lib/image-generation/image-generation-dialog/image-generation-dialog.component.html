<h2 mat-dialog-title>
  {{ 'IMAGE_GENERATION.TITLE' | translate }}
</h2>
<mat-dialog-content>
  <glxy-form-field>
    <glxy-label>
      {{ 'IMAGE_GENERATION.INSTRUCTIONS' | translate }}
    </glxy-label>
    <textarea
      #promptInput
      matInput
      placeholder="{{ 'EMAIL_BUILDER.PROMPT_PLACEHOLDER' | translate }}"
      [(ngModel)]="promptText"
      maxlength="400"
    ></textarea>
  </glxy-form-field>
  <div class="generate-content-button-wrapper">
    <button
      mat-flat-button
      color="primary"
      (click)="suggestImages(promptText)"
      [disabled]="(loadingImages$ | async) || !promptText"
    >
      <glxy-button-loading-indicator [isLoading]="loadingImages$ | async">
        {{ 'EMAIL_BUILDER.GENERATE_CONTENT' | translate }}
      </glxy-button-loading-indicator>
    </button>
  </div>
  <div class="suggested-images">
    <ng-container *ngFor="let img of generatedImages$ | async; let i = index">
      <button class="image-buttons" (click)="selectImage(img, i)" [ngClass]="{ 'selected-image': selectedIndex === i }">
        <mat-icon *ngIf="selectedIndex === i" class="selected-icon">check_circle</mat-icon>
        <mat-icon *ngIf="selectedIndex !== i" class="unselected-icon">circle</mat-icon>
        <img src="{{ img }}" alt="" />
      </button>
    </ng-container>
    <button class="image-buttons add-image-button" (click)="addPhoto()" *ngIf="(generatedImages$ | async)?.length > 0">
      <glxy-button-loading-indicator [isLoading]="loadingImages$ | async">
        <mat-icon>add</mat-icon>
      </glxy-button-loading-indicator>
    </button>
  </div>
</mat-dialog-content>
<mat-dialog-actions class="use-content-actions">
  <button mat-stroked-button (click)="closeDialog()">
    {{ 'EMAIL_BUILDER.CANCEL' | translate }}
  </button>
  <button
    mat-flat-button
    color="accent"
    [disabled]="!selectedImage || (loadingSelectedImage$ | async)"
    (click)="useSelectedImage(selectedImage)"
    data-action="clicked-use-suggested-image"
  >
    <glxy-button-loading-indicator [isLoading]="loadingSelectedImage$ | async">
      {{ 'EMAIL_BUILDER.USE_IMAGE' | translate }}
    </glxy-button-loading-indicator>
  </button>
</mat-dialog-actions>
