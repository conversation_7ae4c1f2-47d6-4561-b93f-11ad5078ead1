import { CommonModule } from '@angular/common';
import { Component, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatRadioModule } from '@angular/material/radio';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { AsyncUiModule, VaBadgeModule, VaStencilsModule } from '@vendasta/uikit';
import { BehaviorSubject, Observable, catchError, filter, map, scan, share, take, tap } from 'rxjs';
import { ContentGenerationI18nModule } from '../../assets/i18n/content-generation-i18n.module';
import { ImageGenerationService } from '../image-generation.service';

@Component({
  providers: [ImageGenerationService],
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatListModule,
    MatDividerModule,
    MatButtonModule,
    MatTooltipModule,
    MatMenuModule,
    VaStencilsModule,
    GalaxyEmptyStateModule,
    TranslateModule,
    AsyncUiModule,
    VaBadgeModule,
    MatDialogModule,
    FormsModule,
    GalaxyFormFieldModule,
    GalaxyButtonLoadingIndicatorModule,
    MatInputModule,
    ContentGenerationI18nModule,
    MatRadioModule,
  ],
  templateUrl: './image-generation-dialog.component.html',
  styleUrls: ['./image-generation-dialog.component.scss'],
})
export class ImageGenerationDialogComponent {
  private loadingImages$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private loadingSelectedImage$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  loadingImages$: Observable<boolean> = this.loadingImages$$.asObservable();
  loadingSelectedImage$: Observable<boolean> = this.loadingSelectedImage$$.asObservable();
  private generatedImage$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  generatedImages$ = this.generatedImage$$.asObservable().pipe(
    share(),
    filter((a) => !!a),
    scan((acc, curr) => [...acc, curr], [] as string[]),
    map((arr) => arr.slice(-3)),
  );
  promptText = '';

  selectedImage = '';
  selectedIndex = -1;

  @ViewChild('promptInput', { read: MatInputModule, static: true }) promptInput: MatInputModule | undefined;

  constructor(
    public dialogRef: MatDialogRef<ImageGenerationDialogComponent>,
    private posthogService: ProductAnalyticsService,
    private snackbarService: SnackbarService,
    private readonly imageGenerationService: ImageGenerationService,
  ) {}

  suggestImages(prompt: string): void {
    this.posthogService.trackEvent('user-clicked-suggest-image', 'suggest-image', 'click', 0, {
      prompt: prompt,
    });
    this.loadingImages$$.next(true);

    this.imageGenerationService
      .suggestImagesWithUrls(prompt, '1024x1024', 1)
      .pipe(
        map((results) => results[0]),
        catchError((err) => {
          this.snackbarService.openErrorSnack('IMAGE_GENERATION.ERROR_MESSAGE');
          this.loadingImages$$.next(false);
          throw err;
        }),
        tap(() => this.loadingImages$$.next(false)),
      )
      .subscribe((img) => this.generatedImage$$.next(img));
  }

  selectImage(img: string, index: number): void {
    this.selectedIndex = index;
    this.selectedImage = img;
  }

  addPhoto(): void {
    if (this.loadingImages$$.getValue()) {
      return;
    }
    this.suggestImages(this.promptText);
  }

  useSelectedImage(img: string): void {
    this.loadingSelectedImage$$.next(true);
    this.posthogService.trackEvent('user-used-suggested-image', 'suggest-image', 'click', 0, {
      prompt: prompt,
    });
    const selectedFile = this.imageGenerationService.getImageAsFile(img);
    selectedFile
      .pipe(
        take(1),
        map((img) => {
          this.dialogRef.close(img);
          this.loadingSelectedImage$$.next(false);
        }),
      )
      .subscribe();
  }
  closeDialog(): void {
    this.dialogRef.close();
  }
}
