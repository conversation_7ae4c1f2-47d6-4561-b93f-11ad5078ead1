@use 'design-tokens' as *;
.use-content-actions {
  display: flex;
  justify-content: flex-end;
}

.suggested-images {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 900px;
  margin: 0 auto;
}

.image-buttons {
  display: flex;
  align-items: center;
  justify-items: center;
  justify-content: center;
  flex-grow: 1;
  padding: 2px;
  background-color: $border-color;
  border: 2px;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  max-width: 25%;
}

.add-image-button {
  color: $glxy-blue-700;
}

.selected-image {
  background-color: $glxy-blue-500;
  border: 2px;
  padding: 2px !important;
  box-shadow:
    0 0 0 1px $glxy-blue-500,
    0 0 2px 5px #2196f330;
  border-radius: 4px;
}

.selected-icon {
  transition: 0.2s opacity ease-in-out;
  position: absolute;
  background-color: $glxy-blue-50;
  border-radius: 50%;
  color: $glxy-blue-500;
  top: 8px;
  right: 8px;
}

.unselected-icon {
  transition: 0.2s opacity ease-in-out;
  position: absolute;
  background-color: $dark-grey;
  border-radius: 50%;
  color: $glxy-grey-50;
  top: 8px;
  right: 8px;
}

.generate-content-button-wrapper {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 16px;
}

img {
  max-width: 256px;
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 4px;
}

textarea {
  height: 70px;
}
