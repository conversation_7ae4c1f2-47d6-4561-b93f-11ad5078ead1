import { Inject, Injectable } from '@angular/core';
import { CreateImageRequestInterface, CreateImageResponse, SocialPostsV2Service } from '@vendasta/social-posts';
import { from, map, Observable, switchMap } from 'rxjs';

@Injectable()
export class ImageGenerationService {
  constructor(
    @Inject(SocialPostsV2Service)
    private readonly imageCreationService: SocialPostsV2Service,
  ) {}

  createSuggestedImages(
    prompt: string,
    size?: string,
    imageAmount?: number,
    businessId?: string,
  ): Observable<CreateImageResponse> {
    //Dalle 3 request return only one image and it's needed to set imageAmount to 1
    const imageRequest: CreateImageRequestInterface = {
      prompt: prompt,
      size: size,
      imageAmount: imageAmount,
      businessId: businessId || '',
      model: 'dall-e-3',
    };
    return this.imageCreationService.createImage(imageRequest);
  }

  getImageAsFile(url: string): Observable<File> {
    let fileName = '';
    return this.imageCreationService.getImageByUrl(url).pipe(
      map((resp) => {
        fileName = `${resp.blob.slice(0, 30)}.png`;
        return `data:image/png;base64,${resp.blob}`;
      }),
      switchMap((file64) => {
        return this.toBlob(file64);
      }),
      map((blobResponse) => {
        const d = new Date();
        return new File([blobResponse], fileName, { type: blobResponse.type, lastModified: d.valueOf() });
      }),
    );
  }

  toBlob(file: string): Observable<Blob> {
    return from(fetch(file).then((res) => res.blob()));
  }

  suggestImagesWithUrls(
    prompt: string,
    size?: string,
    imageAmount?: number,
    businessId?: string,
  ): Observable<string[]> {
    return this.createSuggestedImages(prompt, size, imageAmount, businessId).pipe(
      map((resp) => {
        return resp.generatedImages.map((imgs) => {
          return imgs.url;
        });
      }),
    );
  }
}
