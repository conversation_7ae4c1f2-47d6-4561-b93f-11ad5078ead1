import { Injectable, InjectionToken } from '@angular/core';
import { ConversationChannel, PlatformLocation } from '@vendasta/conversation';
import { Observable } from 'rxjs';
import { ConversationChannelService } from './channels/conversation-channel.abstract';
import { ConversationConfig } from './interface/config.interface';
import { HostAppInterface } from './interface/host-app.interface';
import { RouteConfig } from './interface/routes.interface';

export const CONVERSATION_CONFIG_TOKEN = new InjectionToken<ConversationConfig>(
  '[Conversation]: token for configuration',
);

export const USER_ID_TOKEN: InjectionToken<Observable<string>> = new InjectionToken('[Conversation]: token for userId');

export const ACCOUNT_GROUP_ID_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[Conversation]: token for accountGroupId',
);

export const CONVERSATION_COUNTRY_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[Conversation]: token for country',
);

export const CONVERSATION_GEOGRAPHICAL_STATE_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[Conversation]: token for state',
);

export const PARTNER_ID_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[Conversation]: token for partnerId',
);

export const PARTNER_BRAND_NAME_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[Conversation]: token for partnerBrandName',
);

export const MARKET_ID_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[Conversation]: token for marketId',
);

export const COMPANY_NAME_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[Conversation]: token for companyName',
);

export const FEATURE_FLAG_TOKEN = new InjectionToken<Observable<boolean>>('[Conversation]: token to show up the Inbox');

export const CONVERSATION_IMAGE_SERVICE_TOKEN: InjectionToken<InboxImageService> = new InjectionToken(
  '[Conversation]: token for InboxImageService',
);

export const CONVERSATION_PLATFORM_LOCATION_TOKEN: InjectionToken<PlatformLocation> = new InjectionToken(
  '[Conversation]: token for platformLocation',
);

export const CONVERSATION_CONVERSATION_CHANNELS_ENABLED_TOKEN: InjectionToken<Observable<ConversationChannel[]>> =
  new InjectionToken('[Conversation]: token for conversationChannelsEnabled');

export const CONVERSATION_ROUTES_TOKEN = new InjectionToken<Observable<RouteConfig>>(
  '[Conversation]: token for routes',
);

export const CONVERSATION_GOOGLE_BUSINESS_MESSAGES_AVAILABLE_TOKEN = new InjectionToken<Observable<boolean>>(
  '[Conversation]: token for googleBusinessMessagesAvailable - indicates whether the Google Business Messaging feature is available',
);

export const CONVERSATION_FACEBOOK_MESSENGER_AVAILABLE_TOKEN = new InjectionToken<Observable<boolean>>(
  '[Conversation]: token for facebookMessengerAvailable - indicates whether the Facebook Messenger feature is available',
);

export const CONVERSATION_INSTAGRAM_MESSAGES_AVAILABLE_TOKEN = new InjectionToken<Observable<boolean>>(
  '[Conversation]: token for instagramMessagesAvailable - indicates whether the Instagram Messages feature is available',
);

export const CONVERSATION_WHATSAPP_MESSAGES_AVAILABLE_TOKEN = new InjectionToken<Observable<boolean>>(
  '[Conversation]: token for whatsAppMessagesAvailable - indicates whether the WhatsApp Messages feature is available',
);

export const CONVERSATION_SMS_ENABLED_TOKEN = new InjectionToken<Observable<boolean>>(
  '[Conversation]: token for smsEnabled',
);

export const CONVERSATION_WEB_CHAT_ENABLED_TOKEN = new InjectionToken<Observable<boolean>>(
  '[Conversation]: token for webChatEnabled',
);

export const CONVERSATION_CHANNEL_SERVICE_TOKEN = new InjectionToken<
  Record<ConversationChannel, ConversationChannelService | undefined>
>('[Conversation]: token for ConversationChannelService');

export const GROUP_ID_TOKEN: InjectionToken<Observable<string>> = new InjectionToken(
  '[Conversation]: token for business app group/brand id - empty if not accessed from business app multi-location',
);

export const MARKETPLACE_APP_ID_TOKEN: InjectionToken<string> = new InjectionToken(
  '[Conversation]: token for marketplace app id',
);

export interface InboxImageService {
  getImageSrc(imageName: string): string;
}

@Injectable({ providedIn: 'root' })
export class StubInboxImageService implements InboxImageService {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getImageSrc(_imageName: string): string {
    throw new Error('Method not implemented.');
  }
}

export const CONVERSATION_HOST_APP_INTERFACE_TOKEN = new InjectionToken<HostAppInterface>(
  '[Conversation]: token for HostAppInterface',
);
