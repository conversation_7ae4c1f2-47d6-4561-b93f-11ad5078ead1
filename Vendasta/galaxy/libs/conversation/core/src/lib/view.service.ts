import { HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ConversationApiService } from '@vendasta/conversation';
import { View } from '@vendasta/conversation/lib/_internal/objects/conversation-view';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { BehaviorSubject, Observable, combineLatest, switchMap } from 'rxjs';
import { distinctUntilChanged, filter, map, tap } from 'rxjs/operators';
import { ALL_VIEW_ID, FOLLOWING_VIEW_ID } from './inbox.constants';
import { ParticipantService } from './participant.service';
import { filterNullAndUndefined } from '@vendasta/rx-utils';

const localStorageKey = 'inbox-selected-view-id';

export const getDefaultViewId = (): string => localStorage.getItem(localStorageKey) || ALL_VIEW_ID;

/**
 * Service that interacts with Inbox Views in conversation µservice
 */
@Injectable()
export class ViewService {
  private readonly selectedViewId$$ = new BehaviorSubject<string>(getDefaultViewId());
  readonly selectedViewId$ = this.selectedViewId$$.pipe(
    distinctUntilChanged(),
    tap((selectedViewId) => {
      this.analyticsService.trackEvent('inbox', 'views-tab', 'click', 0, {
        viewId: selectedViewId,
      });
      localStorage.setItem(localStorageKey, selectedViewId);
    }),
  );

  constructor(
    private analyticsService: ProductAnalyticsService,
    private conversationApiService: ConversationApiService,
    private participantService: ParticipantService,
  ) {}

  setViewId(viewId: string): void {
    this.selectedViewId$$.next(viewId);
  }

  // TODO MEGA-248: move view logic from conversationService to viewService
  loadViews(): Observable<View[]> {
    return this.participantService.currentParticipant$.pipe(
      filterNullAndUndefined(),
      filter((participant) => !!participant.participantId),
      switchMap((participant) =>
        this.conversationApiService.getConversationViews({
          participantId: participant.participantId,
        }),
      ),
      map((res) => res.views),
    );
  }

  addConversationToConversationView(conversationId: string): Observable<HttpResponse<null>> {
    return combineLatest([this.participantService.currentParticipant$, this.loadViews()]).pipe(
      switchMap(([currentUserParticipant]) => {
        const value = {
          viewId: FOLLOWING_VIEW_ID,
          conversationId: conversationId,
          participantId: currentUserParticipant.participantId,
        };
        return this.conversationApiService.addConversationToConversationView(value);
      }),
    );
  }

  removeConversationFromConversationView(conversationId: string): Observable<HttpResponse<null>> {
    return this.participantService.currentParticipant$.pipe(
      switchMap((currentUserParticipant) => {
        const value = {
          viewId: FOLLOWING_VIEW_ID,
          conversationId: conversationId,
          participantId: currentUserParticipant.participantId,
        };
        return this.conversationApiService.removeConversationFromConversationView(value);
      }),
    );
  }
}
