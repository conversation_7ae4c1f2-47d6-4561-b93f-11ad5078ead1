import { HttpResponse } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import {
  ConversationApiService,
  GlobalParticipantType,
  ListMessageTemplateRequest,
  MessageTemplate,
  SubjectParticipant,
  GetMessageTemplateRequestHydrationOptionsInterface,
} from '@vendasta/conversation';
import { GetMessageTemplateResponse, ListMessageTemplateResponse } from '@vendasta/conversation/lib/_internal/objects';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Observable, combineLatest, firstValueFrom, of, switchMap } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from './tokens';
@Injectable({ providedIn: 'root' })
export class TemplateService {
  private readonly conversationApiService = inject(ConversationApiService);
  private readonly snackbarService = inject(SnackbarService);

  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);

  listTemplates(cursor: string, pageSize: number): Observable<ListMessageTemplateResponse> {
    return combineLatest([this.accountGroupId$, this.partnerId$]).pipe(
      switchMap(([accountGroupId, partnerId]) => {
        const subjectParticipant = new SubjectParticipant({
          participantType: accountGroupId
            ? GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP
            : GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
          internalParticipantId: accountGroupId || partnerId,
        });

        return this.conversationApiService.listMessageTemplate(
          new ListMessageTemplateRequest({
            pagingOptions: { cursor: cursor, pageSize: pageSize },
            subjectParticipant: subjectParticipant,
          }),
        );
      }),
      map((response) => {
        response.pagingMetadata.hasMore = response.pagingMetadata.hasMore || false;
        return response;
      }),
    );
  }

  async createTemplate(template: MessageTemplate): Promise<void> {
    const sb = await firstValueFrom(
      combineLatest([this.accountGroupId$, this.partnerId$]).pipe(
        map(([accountGroupId, partnerId]) => {
          return new SubjectParticipant({
            participantType: accountGroupId
              ? GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP
              : GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
            internalParticipantId: accountGroupId || partnerId,
          });
        }),
      ),
    );
    await firstValueFrom(
      this.conversationApiService.createMessageTemplate({
        template: template,
        subjectParticipant: sb,
      }),
    );
  }

  async delete(templateId: string): Promise<HttpResponse<null>> {
    return await firstValueFrom(this.conversationApiService.deleteMessageTemplate({ templateId: templateId }));
  }

  async updateTemplate(template: MessageTemplate): Promise<void> {
    await firstValueFrom(
      this.conversationApiService.updateMessageTemplate({
        template: template,
        fieldMask: {
          paths: ['name', 'content'],
        },
      }),
    );
  }

  getTemplate(
    templateId: string,
    hydrationOptions: GetMessageTemplateRequestHydrationOptionsInterface = null,
  ): Observable<GetMessageTemplateResponse> {
    return this.conversationApiService
      .getMessageTemplate({ templateId: templateId, hydrationOptions: hydrationOptions })
      .pipe(
        catchError(() => {
          this.snackbarService.openErrorSnack('INBOX.SETTINGS.MESSAGE_TEMPLATES.LOAD_TEMPLATE_FAILED');
          return of(null);
        }),
      );
  }
}
