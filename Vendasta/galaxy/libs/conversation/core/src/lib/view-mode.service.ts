import { inject, Injectable } from '@angular/core';
import { ViewMode } from './types';
import {
  combineLatest,
  firstValueFrom,
  Observable,
  ReplaySubject,
  distinctUntilChanged,
  filter,
  map,
  shareReplay,
  startWith,
  tap,
} from 'rxjs';
import { NavigationEnd, Router, UrlTree } from '@angular/router';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { toFirestoreId } from './conversation-utils';

const localStorageId = 'conversation-view-mode';
const AI_ROUTE = 'ai';
const INBOX_ROUTE = 'inbox';
const CONVERSATION_OVERLAY_ROUTE = 'conversation';
const CHAT_ROUTE = 'chat';

@Injectable({ providedIn: 'root' })
export class ViewModeService {
  private readonly router = inject(Router);
  private readonly analyticsService = inject(ProductAnalyticsService);
  private readonly viewMode$$ = new ReplaySubject<ViewMode>(1);
  public readonly viewMode$ = this.viewMode$$.pipe(
    startWith(this.getUserViewMode || 'sidebar'),
    distinctUntilChanged(),
    tap((mode) => localStorage.setItem(localStorageId, mode)),
    tap((mode) =>
      this.analyticsService.trackEvent(INBOX_ROUTE, 'inbox-view-mode', 'state', 0, {
        viewMode: mode,
      }),
    ),
    shareReplay(1),
  );

  public readonly isOpen$ = this.router.events.pipe(
    filter((event) => event instanceof NavigationEnd),
    map((event: NavigationEnd) => this.router.parseUrl(event.url)),
    map(
      (urlTree) =>
        this.checkSegment(urlTree, 0, INBOX_ROUTE) || this.checkSegment(urlTree, 0, CONVERSATION_OVERLAY_ROUTE),
    ),
    startWith(window.location.pathname.includes('(inbox:')),
    distinctUntilChanged(),
    shareReplay(1),
  );

  public readonly isAIOpen$ = this.router.events.pipe(
    filter((event) => event instanceof NavigationEnd),
    map((event: NavigationEnd) => this.router.parseUrl(event.url)),
    map((urlTree) => this.checkSegment(urlTree, 1, AI_ROUTE)),
    startWith(window.location.pathname.includes('(inbox:inbox/ai')),
    distinctUntilChanged(),
    shareReplay(1),
  );

  public readonly isAIConversationOpen$ = this.router.events.pipe(
    filter((event) => event instanceof NavigationEnd),
    map((event: NavigationEnd) => this.router.parseUrl(event.url)),
    map((urlTree) => this.checkSegment(urlTree, 1, AI_ROUTE) && this.checkSegment(urlTree, 3, CHAT_ROUTE)),
    startWith(window.location.pathname.includes('(inbox:inbox/ai') && window.location.pathname.includes('/chat/')),
    distinctUntilChanged(),
    shareReplay(1),
  );

  // isConversationOpen$ is used to determine if the conversation overlay is open
  // conversation overlay is when the user fly open the conversation to the side from Inbox
  public readonly isConversationOpen$ = this.router.events.pipe(
    filter((event) => event instanceof NavigationEnd),
    map((event: NavigationEnd) => this.router.parseUrl(event.url)),
    map((urlTree) => this.checkSegment(urlTree, 0, CONVERSATION_OVERLAY_ROUTE)),
    startWith(window.location.pathname.includes('(inbox:conversation')),
    distinctUntilChanged(),
    shareReplay(1),
  );

  public readonly isInboxOpen$: Observable<boolean> = combineLatest([
    this.isOpen$,
    this.isAIOpen$,
    this.isConversationOpen$,
  ]).pipe(
    map(([isOpen, isAIOpen, isConversationOpen]) => isOpen && !isAIOpen && !isConversationOpen),
    distinctUntilChanged(),
    shareReplay(1),
  );

  public readonly isDrawerOpen$ = combineLatest([this.viewMode$, this.isOpen$]).pipe(
    map(([mode, isOpen]) => mode === 'sidebar' && isOpen),
  );

  public readonly isModalOpen$ = combineLatest([this.viewMode$, this.isOpen$]).pipe(
    map(([mode, isOpen]) => mode === 'modal' && isOpen),
  );

  public async toggleViewMode(): Promise<void> {
    const currentMode = await firstValueFrom(this.viewMode$);
    this.viewMode$$.next(currentMode === 'modal' ? 'sidebar' : 'modal');
  }

  public setViewMode(mode: ViewMode): void {
    this.viewMode$$.next(mode);
  }

  public async toggleInboxOverlayOpen(): Promise<void> {
    const urlTree = this.router.parseUrl(this.router.url);
    if (this.checkSegment(urlTree, 0, INBOX_ROUTE)) {
      if (!this.checkSegment(urlTree, 1, AI_ROUTE)) {
        this.close();
        return;
      }
      await this.closeAIConversationOverlay();
    }

    this.open();
  }

  public async open(conversationId?: string, prefilledMessage?: string): Promise<void> {
    const viewMode = await firstValueFrom(this.viewMode$);

    this.router.navigate(this.routerCommands(viewMode, conversationId), {
      queryParamsHandling: 'merge',
      queryParams: { inboxPrefilledMessage: prefilledMessage || null },
    });
  }

  public openSettings(): void {
    this.router
      .navigate(['inbox/settings'])
      .then(() => this.analyticsService.trackEvent('inbox', 'platform-settings', 'open'));
  }

  // the type any[] is the type used by router.navigate
  public routerCommands(mode: ViewMode, conversationId?: string): unknown[] {
    const path = conversationId ? `inbox/conversations/${toFirestoreId(conversationId)}` : INBOX_ROUTE;
    return [
      '',
      {
        outlets: {
          inbox: path,
        },
      },
    ];
  }

  close(): void {
    this.router.navigate(['', { outlets: { inbox: null } }], {
      queryParamsHandling: 'merge',
      queryParams: { inboxPrefilledMessage: null },
    });
  }

  private get getUserViewMode(): ViewMode {
    return localStorage.getItem(localStorageId) as ViewMode;
  }

  public toggleAIConversationOverlayOpen(): void {
    const urlTree = this.router.parseUrl(this.router.url);
    if (this.checkSegment(urlTree, 1, AI_ROUTE)) {
      this.closeAIConversationOverlay();
    } else {
      this.viewMode$$.next('sidebar');
      this.openAIConversationOverlay();
    }
  }

  public async openAIConversationOverlay(): Promise<void> {
    this.router.navigate([{ outlets: { inbox: 'inbox/ai' } }]);
  }

  public async closeAIConversationOverlay(): Promise<void> {
    this.router.navigate([{ outlets: { inbox: null } }], {
      queryParamsHandling: 'merge',
    });
  }

  /**
   * openConversationOverlay opens the conversation overlay for the given conversation ID and redirect user to /inbox route.
   *
   * @param {string} conversationId - The unique identifier of the conversation to be displayed in the overlay.
   * @return {Promise<void>} A promise that resolves when the navigation is complete.
   */
  public async openConversationOverlay(
    conversationId: string,
    goToInbox?: boolean,
    prefilledMessage?: string,
  ): Promise<void> {
    let commands: any[];
    if (goToInbox) {
      commands = [{ outlets: { primary: 'inbox', inbox: ['conversation', toFirestoreId(conversationId)] } }];
    } else {
      commands = [{ outlets: { inbox: `conversation/${toFirestoreId(conversationId)}` } }];
    }

    //this is really a problem within the same app(url) so need to clean it up until we get rid of the conversation side panel in other centers
    localStorage.removeItem(localStorageId);

    await this.router.navigate(commands, {
      queryParamsHandling: 'merge',
      queryParams: { inboxPrefilledMessage: prefilledMessage || null },
    });
  }

  private checkSegment(urlTree: UrlTree, segmentIndex: number, segmentPath: string): boolean {
    return urlTree.root.children?.inbox?.segments[segmentIndex]?.path === segmentPath;
  }
}
