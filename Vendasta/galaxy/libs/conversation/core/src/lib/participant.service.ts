import { Inject, Injectable, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { CrmFieldService } from '@galaxy/crm/static';
import {
  ConversationApiService,
  ConversationChannel,
  GetMultiParticipantsRequest,
  GetMultiParticipantsResponse,
  GetParticipantsByKeyRequest,
  GetParticipantsByKeyResponse,
  GlobalParticipantType,
  NamespaceDetail,
  Participant,
  ParticipantKey,
  ParticipantType,
} from '@vendasta/conversation';
import {
  CreateCrmObjectRequest,
  CreateCrmObjectResponse,
  CRMApiService,
  CrmObject,
  CrmObjectSearch,
  FilterGroupOperator,
  FilterOperator,
  ListCrmObjectsRequest,
} from '@vendasta/crm';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  map,
  Observable,
  of,
  shareReplay,
  Subject,
  switchMap,
  take,
} from 'rxjs';
import { CONTACTS_PAGE_SIZE } from './inbox.constants';
import { InboxContact } from './interface/inbox.interface';
import { buildCrmFieldsForCreateContact, getInboxContactFromCrm } from './participant-utils';
import { ACCOUNT_GROUP_ID_TOKEN, GROUP_ID_TOKEN, PARTNER_ID_TOKEN, USER_ID_TOKEN } from './tokens';

export class ParticipantBuilder {
  private readonly participant: Participant;
  constructor() {
    this.participant = new Participant();
  }

  partnerId(partnerId: string): ParticipantBuilder {
    this.participant.partnerId = partnerId;
    return this;
  }

  accountGroupId(accountGroupId: string): ParticipantBuilder {
    this.participant.accountGroupId = accountGroupId;
    return this;
  }

  channel(channel: ConversationChannel): ParticipantBuilder {
    this.participant.channel = channel;
    return this;
  }

  participantType(participantType: ParticipantType): ParticipantBuilder {
    this.participant.participantType = participantType;
    return this;
  }

  internalParticipantId(internalParticipantId: string): ParticipantBuilder {
    this.participant.internalParticipantId = internalParticipantId;
    return this;
  }

  isSubjectParticipant(isSubjectParticipant: boolean): ParticipantBuilder {
    this.participant.isSubjectParticipant = isSubjectParticipant;
    return this;
  }

  build(): Participant {
    return this.participant;
  }
}

@Injectable()
export class ParticipantService implements OnDestroy {
  constructor(
    @Inject(USER_ID_TOKEN) readonly userId$: Observable<string>,
    @Inject(PARTNER_ID_TOKEN) readonly partnerId$: Observable<string>,
    @Inject(ACCOUNT_GROUP_ID_TOKEN) readonly accountGroupId$: Observable<string>,
    @Inject(GROUP_ID_TOKEN) readonly groupId$: Observable<string>,
    private conversationApiService: ConversationApiService,
    private crmService: CRMApiService,
    private readonly snackbarService: SnackbarService,
    private readonly router: Router,
    private readonly fieldService: CrmFieldService,
  ) {}
  private unsubscribe$ = new Subject<void>();
  private contacts$$: BehaviorSubject<Array<InboxContact>> = new BehaviorSubject([]);

  private readonly participantDetails$ = combineLatest([
    this.partnerId$,
    this.accountGroupId$,
    this.userId$,
    this.groupId$,
  ]).pipe(
    map(([partnerId, accountGroupId, userId, groupId]) => {
      return {
        partnerId: partnerId ?? '',
        accountGroupId: accountGroupId ?? '',
        userId: userId ?? '',
        groupId: groupId ?? '',
      };
    }),
  );

  readonly currentParticipant$: Observable<Participant> = this.participantDetails$.pipe(
    switchMap((details) => this.getParticipant(details)),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  public contacts$ = this.contacts$$.asObservable();
  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  getParticipant(participantDetails: {
    userId: string;
    partnerId: string;
    accountGroupId: string;
    groupId?: string;
  }): Observable<Participant> {
    const participantKey = {
      internalParticipantId: participantDetails.userId,
      partnerId: participantDetails.partnerId ?? '',
      accountGroupId: participantDetails.accountGroupId ?? '',
      participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_IAM_USER,
      namespaceHierarchy: this.buildNamespaceHierarchy(
        participantDetails.partnerId,
        participantDetails.accountGroupId,
        participantDetails.groupId,
      ),
    } as ParticipantKey;
    return this.getParticipantsByKey(participantKey).pipe(
      map((resp) => {
        return resp?.participant;
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  // buildNamespaceHierarchy is a helper function to build the namespace hierarchy for a participant
  buildNamespaceHierarchy(partnerId: string, accountGroupId: string, groupId?: string): NamespaceDetail[] {
    const namespaceHierarchy = [
      new NamespaceDetail({
        participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
        internalParticipantId: partnerId,
      }),
    ];

    // either group or account group will be set.
    if (groupId) {
      namespaceHierarchy.push(
        new NamespaceDetail({
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_GROUP,
          internalParticipantId: groupId,
        }),
      );
    } else if (accountGroupId) {
      namespaceHierarchy.push(
        new NamespaceDetail({
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
          internalParticipantId: accountGroupId,
        }),
      );
    }
    return namespaceHierarchy;
  }

  buildIAMUserParticipant(isSubjectParticipant?: boolean): Observable<Participant> {
    return combineLatest([this.partnerId$, this.accountGroupId$, this.userId$]).pipe(
      map(([partnerId, accountGroupId, userId]) => {
        return this.buildIamUser(partnerId, accountGroupId, userId, isSubjectParticipant);
      }),
    );
  }

  public buildIamUser(
    partnerId: string,
    accountGroupId: string,
    userId: string,
    isSubjectParticipant?: boolean,
  ): Participant {
    return accountGroupId
      ? new ParticipantBuilder()
          .partnerId(partnerId)
          .accountGroupId(accountGroupId)
          .participantType(ParticipantType.PARTICIPANT_TYPE_IAM_USER)
          .internalParticipantId(userId)
          .isSubjectParticipant(isSubjectParticipant ?? false)
          .build()
      : new ParticipantBuilder()
          .partnerId(partnerId)
          .participantType(ParticipantType.PARTICIPANT_TYPE_IAM_USER)
          .internalParticipantId(userId)
          .isSubjectParticipant(isSubjectParticipant ?? false)
          .build();
  }

  public buildAccountGroup(partnerId: string, accountGroupId: string): Participant {
    return new ParticipantBuilder()
      .partnerId(partnerId)
      .accountGroupId(accountGroupId)
      .participantType(ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP)
      .internalParticipantId('')
      .isSubjectParticipant(true)
      .build();
  }

  public buildPartner(partnerId: string): Participant {
    return new ParticipantBuilder()
      .partnerId(partnerId)
      .participantType(ParticipantType.PARTICIPANT_TYPE_PARTNER)
      .internalParticipantId('')
      .isSubjectParticipant(true)
      .build();
  }

  public buildVendor(vendorId: string): Participant {
    return new ParticipantBuilder()
      .partnerId(vendorId)
      .participantType(ParticipantType.PARTICIPANT_TYPE_VENDOR)
      .internalParticipantId('')
      .isSubjectParticipant(true)
      .build();
  }

  public buildContactParticipant(internalId: string, partnerId: string, accountGroupId?: string): Participant {
    return new ParticipantBuilder()
      .partnerId(partnerId)
      .accountGroupId(accountGroupId ?? '')
      .participantType(ParticipantType.PARTICIPANT_TYPE_CUSTOMER)
      .internalParticipantId(internalId)
      .isSubjectParticipant(true)
      .build();
  }

  public buildAIAssistantParticipant(assistantId: string): Observable<Participant> {
    return combineLatest([this.partnerId$, this.accountGroupId$]).pipe(
      map(([partnerId, accountGroupId]) => {
        return new ParticipantBuilder()
          .partnerId(partnerId)
          .accountGroupId(accountGroupId)
          .participantType(ParticipantType.PARTICIPANT_TYPE_AI_ASSISTANT)
          .internalParticipantId(assistantId)
          .isSubjectParticipant(true)
          .build();
      }),
    );
  }

  public addContact(contact: InboxContact): Observable<InboxContact> {
    if (!contact) return of({} as InboxContact);
    return this.createCrmContact(contact);
  }

  private createCrmContact(contact: InboxContact): Observable<InboxContact> {
    return this.crmService
      .createContact({
        namespace: contact.accountGroupId || contact.partnerId,
        crmObject: {
          fields: buildCrmFieldsForCreateContact(contact),
        },
      } as CreateCrmObjectRequest)
      .pipe(
        map((crmObjectResponse: CreateCrmObjectResponse) => {
          contact.contactId = crmObjectResponse?.crmObjectId;
          return contact;
        }),
      );
  }

  public getContactByField(namespace: string, crmField: string, value: string): Observable<Array<InboxContact>> {
    if (namespace === '' || crmField === '' || value === '') return of([] as Array<InboxContact>);

    return this.getCrmContactByField(namespace, this.fieldService.getFieldId(crmField), value);
  }

  public searchContacts(term: string, accountGroupId: string): void {
    this.getContactsFromCrmService(accountGroupId, term)
      .pipe(take(1))
      .subscribe((contactList) => {
        this.contacts$$.next(contactList);
      });
  }

  /**
   * Get contact list from CRM Service
   * @param {string} accountGroupId - accountGroupId
   * @param {string} term - search term
   * @return {Observable<Array<InboxContact>>} - Inbox Contact list observable
   */
  private getContactsFromCrmService(accountGroupId: string, term: string): Observable<Array<InboxContact>> {
    return this.crmService
      .listContacts({
        namespace: accountGroupId,
        search: { searchTerm: term } as CrmObjectSearch,
        pagingOptions: {
          pageSize: CONTACTS_PAGE_SIZE,
        },
      } as ListCrmObjectsRequest)
      .pipe(
        map((response) =>
          response?.crmObjects
            ?.filter((contact) => !!contact)
            .map((contact: CrmObject) => getInboxContactFromCrm(accountGroupId, contact)),
        ),
        catchError(() => of([] as Array<InboxContact>)),
      );
  }

  /**
   * Get contact from CRM Service by email
   * @param {string} namespace - Crm namespace
   * @param {string} fieldId - Crm field to filter
   * @param {string} term - search term
   * @return {Observable<Array<InboxContact>>} - Inbox Contact list observable
   */
  private getCrmContactByField(namespace: string, fieldId: string, term: string): Observable<Array<InboxContact>> {
    return this.crmService
      .listContacts({
        namespace: namespace,
        pagingOptions: {
          pageSize: 1,
        },
        filtersV2: {
          operator: FilterGroupOperator.FILTER_GROUP_OPERATOR_AND,
          filters: [
            {
              fieldId: fieldId,
              operator: FilterOperator.FILTER_OPERATOR_IS,
              values: [
                {
                  string: term,
                },
              ],
            },
          ],
        },
      } as ListCrmObjectsRequest)
      .pipe(
        map((response) =>
          response?.crmObjects
            ?.filter((contact) => !!contact)
            .map((contact: CrmObject) => getInboxContactFromCrm(namespace, contact)),
        ),
        catchError(() => of([] as Array<InboxContact>)),
      );
  }

  /**
   * get multi participants from the Conversation µs
   * @param {string[]} participantIds - the participant IDs
   * @return {Observable<GetMultiParticipantsResponse>}
   */
  public getMultiParticipants(
    participantIds: string[],
    conversationId: string,
  ): Observable<GetMultiParticipantsResponse> {
    if (!participantIds || participantIds.length === 0) {
      console.error('the participantIds is not valid');
      return of({ participants: [] as Participant[] } as GetMultiParticipantsResponse);
    }

    const req = {
      conversationId: conversationId,
      participantIds: participantIds,
    } as GetMultiParticipantsRequest;
    return this.conversationApiService.getMultiParticipants(req).pipe(
      catchError((err) => {
        console.error('getMultiParticipants error:', err?.message);
        return of({} as GetMultiParticipantsResponse);
      }),
    );
  }

  /**
   * get participants by key from the Conversation µs
   */
  public getParticipantsByKey(participantKeyInfo: ParticipantKey): Observable<GetParticipantsByKeyResponse | never> {
    const req = {
      participantKey: participantKeyInfo,
    } as GetParticipantsByKeyRequest;

    return this.conversationApiService.getParticipantsByKey(req);
  }

  organizationId$ = combineLatest([this.partnerId$, this.accountGroupId$]).pipe(
    map(([partnerId, accountGroupId]) => {
      return accountGroupId || partnerId;
    }),
  );
}
