import { ConversationChannel, PlatformLocation } from '@vendasta/conversation';
import { Observable } from 'rxjs';
import { InboxImageService } from '../tokens';
import { RouteConfig } from './routes.interface';

export interface ConversationConfig {
  // The current userId. This is the IAM ID for a user regardless of their persona.
  // Conversation requires a userId for attributing data to a user across
  // different platforms and apps.
  userId$: Observable<string | undefined | null>;

  // Current account group ID.
  accountGroupId$: Observable<string | undefined | null>;

  // Current country.
  // https://address-demo.apigateway.co/address.v1.Address/ListAllCountryOptions
  country$: Observable<string | undefined | null>;

  // Current geographical state.
  // https://address-demo.apigateway.co/address.v1.Address/ListAllCountryOptions
  geographicalState$: Observable<string | undefined | null>;

  // Current partner ID.
  partnerId$: Observable<string | undefined | null>;

  // Partner brand name is used in the Terms of service modal.
  partnerBrandName$?: Observable<string | undefined | null>;

  // Current market ID
  marketId$: Observable<string | undefined | null>;

  // Group ID when business app multi-location is accessed
  groupId$?: Observable<string | undefined | null>;

  // Company name
  companyName$?: Observable<string | undefined | null>;

  // Whether or not to show the Inbox.
  featureFlag$?: Observable<boolean>;

  // InboxImageService is used to fetch images from the assets
  inboxImageService?: InboxImageService;

  // Current platform location
  platformLocation: PlatformLocation;

  // Conversation Channels enabled
  conversationChannelsEnabled$?: Observable<ConversationChannel[] | undefined | null>;

  // The application routes where ConversationCore will be instantiated
  routes$?: Observable<RouteConfig | undefined | null>;

  // Hide the new message button
  hideNewMessageButton?: boolean;

  // A link that can provide more information about Inbox to the user.
  learnMoreUrl?: string;

  // A function that will resolve the url for image sources. This can be used in place of implementing the
  // InboxImageService interface.
  imageSrc?(imageName: string): string;

  // Whether or not the user is impersonating another user.
  isImpersonating$?: Observable<boolean>;

  // Whether Google Business Messages is supported in the parent application
  googleBusinessMessagesSupported$?: Observable<boolean>;

  // Whether Facebook Messenger is supported in the parent application
  facebookMessengerSupported$?: Observable<boolean>;

  // Whether Instagram Messages is supported in the parent application
  instagramMessagesSupported$?: Observable<boolean>;

  whatsAppMessagesSupported$?: Observable<boolean>;

  // Whether SMS is enabled
  smsEnabled$?: Observable<boolean>;

  // Whether WhatsApp is enabled
  whatsAppEnabled$?: Observable<boolean>;

  // Whether or not the user has access to see the full inbox page.
  hasAccessToInboxPage$?: Observable<boolean>;

  // Whether or not the SMB chat with partner feature is enabled.
  allowSmbChatWithPartner$?: Observable<boolean>;

  // Whether or not the user has access to see web chat.
  webChatEnabled$?: Observable<boolean>;

  // sms ai responder enabled
  smsAIResponderEnabled$?: Observable<boolean>;

  // A function that will open the Zendesk chat widget, if available.
  openZendeskChat?(): void;

  // internal link domain
  internalDomainLinks?: string[];
}
