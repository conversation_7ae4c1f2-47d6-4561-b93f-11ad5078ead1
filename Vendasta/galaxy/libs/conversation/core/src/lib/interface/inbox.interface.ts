import { ConversationDetail } from './conversation.interface';
import { GlobalParticipantType } from '@vendasta/conversation';

/**
 * InboxContact is the Inbox Contact. This object is used for parsing the endpoint call
 * from Contact µs (future CRM).
 */
export interface InboxContact {
  contactId?: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  phone?: string;
  email?: string;
  conversation?: ConversationDetail;
  permissionToContact?: boolean;
  errorCode?: number;
  accountGroupId?: string;
  partnerId?: string;
}

export interface InboxContactInfo {
  name: string;
  phoneAndEmail: string;
}

export interface CreateContactState {
  phone?: string;
  email?: string;
  firstName?: string;
}

export interface InboxStatusMessage {
  status: 'loading' | 'success' | 'error';
  message?: string;
}

export interface InboxBannerAlertInfo {
  title: string;
  description: string;
  actionText: string;
}

export interface InboxNamespace {
  id: string;
  namespaceType: GlobalParticipantType;
}

export type InboxAtlasRoute = [string, { outlets: { [key: string]: string[] } }];
