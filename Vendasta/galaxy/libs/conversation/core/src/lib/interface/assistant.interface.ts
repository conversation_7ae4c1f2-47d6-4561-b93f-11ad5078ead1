import { Assistant } from '@vendasta/ai-assistants';

//time that is used to determine if the AI responder should be aborted
// TODO: will be replaced with 0 time when a proper approach is implemented in the backend
export const abortAiResponderTime = new Date(1971, 1, 1, 3, 0, 0, 0);

export interface AiResponder {
  isEnabled: boolean;
  assistant?: Assistant | null;
  isConversationEnabled: boolean;
}
