import { convertMediaJSONIntoMedia } from './conversation.interface';

describe('convertMediaJSONIntoMedia', () => {
  it('should return null if media is null', () => {
    // @ts-expect-error null not included in the type of the argument
    expect(convertMediaJSONIntoMedia(null)).toBeNull();
  });
  it('should return null if media is undefined', () => {
    // @ts-expect-error undefined not included in the type of the argument
    expect(convertMediaJSONIntoMedia(undefined)).toBeNull();
  });
  it('should return null if media is empty', () => {
    expect(convertMediaJSONIntoMedia('')).toBeNull();
  });
  it('should return "not-json if media is a non-empty non-json string', () => {
    expect(convertMediaJSONIntoMedia('https://wakka.wakka')).toBe('not-json');
  });
  it('should return a Media object if media is valid', () => {
    expect(
      convertMediaJSONIntoMedia(
        '{"MediaContentType":"image/jpeg","MediaUrl":"https://api.twilio.com/2010-04-01/Accounts/AC53833056fc0ea1e41e45a74f72aedb5d/Messages/MMccfe024a5d0a462c20162c42a7f8f1da/Media/ME5afe2b6ad50f24ca3855a39889a65c7c"}',
      ),
    ).toEqual({
      mediaContentType: 'image/jpeg',
      mediaUrl:
        'https://api.twilio.com/2010-04-01/Accounts/AC53833056fc0ea1e41e45a74f72aedb5d/Messages/MMccfe024a5d0a462c20162c42a7f8f1da/Media/ME5afe2b6ad50f24ca3855a39889a65c7c',
    });
  });
});
