import { formatPhoneNumber } from './phone-number-utils';

describe('formatPhoneNumber', () => {
  it('should format a valid phone number', () => {
    const result = formatPhoneNumber('************');
    expect(result).toEqual('+****************');
  });

  it('should format a valid phone number that is already formatted', () => {
    const result = formatPhoneNumber('+****************');
    expect(result).toEqual('+****************');
  });

  it('should return the input value for an invalid phone number', () => {
    const result = formatPhoneNumber('Website Lead D7703');
    expect(result).toEqual('Website Lead D7703');
  });

  it('should return the input value for an empty string', () => {
    const result = formatPhoneNumber('');
    expect(result).toEqual('');
  });

  it('should return the input value for a non-numeric string', () => {
    const result = formatPhoneNumber('invalid');
    expect(result).toEqual('invalid');
  });
});
