import { GlobalParticipantType, NamespaceDetail, Participant, ParticipantType } from '@vendasta/conversation';
import { CrmObject, FieldValue, PhoneFieldsInterface } from '@vendasta/crm';
import { INBOX_CRM_SOURCE_NAME, STANDARD_CRM_FIELD_EXTERNAL_IDS } from './inbox.constants';
import { ConversationAvailableChannels, ConversationDetail } from './interface/conversation.interface';
import { InboxContact } from './interface/inbox.interface';
import { PhoneNumberUtil } from 'google-libphonenumber';

const DEFAULT_COUNTRY_CODE = 'US';

export function getSubjectParticipantByType(participants: Participant[], type: ParticipantType): Participant | null {
  return (
    participants?.find((participant) => participant?.isSubjectParticipant && participant?.participantType === type) ??
    null
  );
}

/**
 * isSameParticipant returns true if the target participant is the same based on the fields that makes a participant unique
 * @param userId
 * @param partnerId
 * @param accountGroupId
 * @param {Participant} targetParticipant
 * @returns {boolean}
 */
export function isSameParticipant(
  userId: string,
  partnerId: string,
  accountGroupId: string,
  targetParticipant: Participant,
): boolean {
  return (
    targetParticipant?.internalParticipantId === userId &&
    targetParticipant?.partnerId === partnerId &&
    targetParticipant?.accountGroupId === accountGroupId
  );
}

/** Create a map of contact fields, using fieldID as key
 * @param {CrmObject} customerFields - contactFields
 * @return {Map<string, FieldValue>} - Map of contact fields
 */
export function createContactFieldMap(contactFields: CrmObject): Map<string, FieldValue> {
  const contactFieldMap = new Map<string, FieldValue>();
  contactFields.fields.forEach((field) => {
    if (field?.fieldId) contactFieldMap.set(field.externalId, field);
  });
  return contactFieldMap;
}

/** Return an InboxContact object from CrmObject
 * @param {string} accountGroupId - accountGroupId
 * @param {CrmObject} contactFields - accountGroupId
 * @return {InboxContact} - Inbox Contact object
 */
export function getInboxContactFromCrm(accountGroupId: string, contactFields: CrmObject): InboxContact {
  const contactFieldMap = createContactFieldMap(contactFields);
  const inboxContact: InboxContact = {
    contactId: contactFields.crmObjectId,
    firstName: contactFieldMap.get(STANDARD_CRM_FIELD_EXTERNAL_IDS.firstName)?.stringValue ?? '',
    lastName: contactFieldMap.get(STANDARD_CRM_FIELD_EXTERNAL_IDS.lastName)?.stringValue ?? '',
    email: contactFieldMap.get(STANDARD_CRM_FIELD_EXTERNAL_IDS.email)?.stringValue ?? '',
    phone: contactFieldMap.get(STANDARD_CRM_FIELD_EXTERNAL_IDS.phoneNumber)?.stringValue ?? '',
    permissionToContact: true, //TODO (MEGA-221): hydrate this field when CRM service support it or delete it if not needed
    accountGroupId: accountGroupId,
  };
  return inboxContact;
}

export function createCrmField(fieldName: string, stringValue: string): FieldValue | null {
  if (!fieldName || !stringValue) return null;
  return {
    externalId: fieldName,
    stringValue: stringValue,
  } as FieldValue;
}

export function buildCrmFieldsForCreateContact(inboxContact: InboxContact): FieldValue[] {
  const phoneNumberUtil = PhoneNumberUtil.getInstance();
  let parsedPhoneVals: PhoneFieldsInterface | null = null;

  if (inboxContact?.phone) {
    try {
      const parsedNumber = phoneNumberUtil.parseAndKeepRawInput(inboxContact.phone, DEFAULT_COUNTRY_CODE);
      const countryCode = phoneNumberUtil.getRegionCodeForNumber(parsedNumber);
      parsedPhoneVals = {
        isoCountryCode: countryCode,
        nationalNumber: parsedNumber.getNationalNumberOrDefault().toString(),
        extension: parsedNumber.getExtensionOrDefault(),
      };
    } catch (error) {
      console.error('Failed to parse phone number:', error);
    }
  }

  const object = [
    createCrmField(STANDARD_CRM_FIELD_EXTERNAL_IDS.firstName, inboxContact?.firstName),
    createCrmField(STANDARD_CRM_FIELD_EXTERNAL_IDS.lastName, inboxContact?.lastName),
    createCrmFieldPhoneValue(STANDARD_CRM_FIELD_EXTERNAL_IDS.phoneNumber, parsedPhoneVals),
    createCrmField(STANDARD_CRM_FIELD_EXTERNAL_IDS.email, inboxContact?.email),
    createCrmField(STANDARD_CRM_FIELD_EXTERNAL_IDS.source, INBOX_CRM_SOURCE_NAME),
  ].filter((field) => !!field);

  return object;
}

export function createCrmFieldPhoneValue(
  fieldName: string,
  phoneFieldValues?: PhoneFieldsInterface | null,
): FieldValue | null {
  if (!fieldName || !phoneFieldValues) return null;
  return {
    externalId: fieldName,
    phoneFieldValues: phoneFieldValues,
  } as FieldValue;
}

/**
 * Checks if the contact information is missing
 * @param {ConversationDetail} conversationDetail - Conversation Detail information
 * @param {ConversationAvailableChannels} availableChannels - Conversation Available Channels
 * @return {boolean}
 */
export function isContactInformationMissing(
  conversationDetail: ConversationDetail,
  availableChannels: ConversationAvailableChannels,
): boolean {
  if (!conversationDetail?.conversation) return false;

  const recipients = conversationDetail?.participants
    ?.filter((p) => p.isSubjectParticipant && p.internalParticipantId)
    .filter((participant) => participant.participantType === ParticipantType.PARTICIPANT_TYPE_CUSTOMER);

  if (recipients?.length === 0) return false;

  const contactParticipant = conversationDetail?.participants?.find(
    (participant) => participant?.internalParticipantId === recipients[0].internalParticipantId,
  );

  return (
    !contactParticipant?.phoneNumber && !contactParticipant?.email && !availableChannels?.availableChannels?.length
  );
}

/**
 * Checks if the NamespaceDetail information used for describe participant hierarchy contains a participantType
 * @param {NamespaceDetail[]} hierarchy - Participant Hierarchy
 * @param {GlobalParticipantType} participantType - Participant Type
 * @return {boolean}
 */
export function isSubjectParticipantInHierarchy(
  hierarchy: NamespaceDetail[],
  participantType: GlobalParticipantType,
): boolean {
  if (!hierarchy || !hierarchy.length) return false;
  return hierarchy.some((namespace) => namespace.participantType === participantType);
}
