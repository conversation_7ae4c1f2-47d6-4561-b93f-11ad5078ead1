import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { ViewModeService } from './view-mode.service';
import { NavigationEnd, Router, UrlTree, DefaultUrlSerializer } from '@angular/router';
import { of } from 'rxjs';
import { ConversationService } from './state/conversation.service';
import { conversationServiceMock } from './mocks';
import { FeatureFlagService } from '@galaxy/partner';
import { PARTNER_ID_TOKEN } from './tokens';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

class MockRouter {
  // Router
  public get events() {
    return of(new NavigationEnd(0, 'http://localhost:4200/', 'http://localhost:4200/'));
  }
  parseUrl = jest.fn();
  navigate = jest.fn();
}
describe('ViewModeService', () => {
  const mockRouter = new MockRouter();

  let spectator: SpectatorService<ViewModeService>;
  const createService = createServiceFactory({
    service: ViewModeService,
    providers: [
      {
        provide: Router,
        useValue: mockRouter,
      },
      {
        provide: ProductAnalyticsService,
        useValue: {
          trackEvent: jest.fn(),
        },
      },
      {
        provide: ConversationService,
        useValue: conversationServiceMock,
      },
      {
        provide: FeatureFlagService,
        useValue: {
          batchGetStatus: jest.fn().mockReturnValue(of({ fullscreen_inbox_view: false })),
        },
      },
      {
        provide: PARTNER_ID_TOKEN,
        useValue: of('mock-partner-id'),
      },
    ],
  });

  describe('view mode', function () {
    beforeEach(() => (spectator = createService()));
    it('should be sidebar by default', (done) => {
      spectator.service.viewMode$.subscribe((viewMode) => {
        expect(viewMode).toEqual('sidebar');
        done();
      });
    });
  });

  describe('toggleViewMode', function () {
    beforeEach(() => (spectator = createService()));
    it('should toggle view mode', (done) => {
      spectator.service.viewMode$.subscribe((viewMode) => {
        expect(viewMode).toEqual('modal');
        done();
      });
      spectator.service.toggleViewMode();
    });
  });

  describe('AI > toggleInboxAIOpen', function () {
    beforeEach(() => (jest.clearAllMocks(), (spectator = createService())));
    it('should open Inbox AI when triggering toggleInboxAIOpen', (done) => {
      const router = spectator.inject(Router);
      const openInboxAI = jest.spyOn(spectator.service, 'openAIConversationOverlay');
      const parseUrl = jest.spyOn(router, 'parseUrl').mockReturnValue({ root: { children: {} } } as unknown as UrlTree);

      spectator.service.toggleAIConversationOverlayOpen();

      expect(parseUrl).toHaveBeenCalled();
      expect(openInboxAI).toHaveBeenCalled();
      expect(router.navigate).toHaveBeenCalledWith([{ outlets: { inbox: 'inbox/ai' } }]);

      spectator.service.viewMode$.subscribe((viewMode) => {
        expect(viewMode).toEqual('sidebar');
        done();
      });

      spectator.service.isAIOpen$.subscribe((isAIOpen) => {
        expect(isAIOpen).toEqual(true);
        done();
      });
    });
    it('should close Inbox AI when triggering toggleInboxAIOpen and the route contains inbox ai', () => {
      const router = spectator.inject(Router);

      const parseUrl = jest.spyOn(router, 'parseUrl').mockReturnValue({
        root: { children: { inbox: { segments: [{ path: 'inbox' }, { path: 'ai' }] } } },
      } as unknown as UrlTree);
      const closeInboxAI = jest.spyOn(spectator.service, 'closeAIConversationOverlay');

      spectator.service.toggleAIConversationOverlayOpen();

      expect(parseUrl).toHaveBeenCalled();
      expect(closeInboxAI).toHaveBeenCalled();
      expect(router.navigate).toHaveBeenCalledWith([{ outlets: { inbox: null } }], {
        queryParamsHandling: 'merge',
      });
    });
    it('should close inbox chat if InboxAI gets Open', (done) => {
      const router = spectator.inject(Router);
      const parseUrl = jest.spyOn(router, 'parseUrl').mockReturnValue({
        root: {
          children: {
            inbox: { segments: [{ path: 'inbox' }, { path: 'conversations' }, { path: 'C-123' }] },
          },
        },
      } as unknown as UrlTree);

      spectator.service.toggleAIConversationOverlayOpen();

      expect(parseUrl).toHaveBeenCalled();
      expect(router.navigate).toHaveBeenCalledWith([{ outlets: { inbox: 'inbox/ai' } }]);
      spectator.service.viewMode$.subscribe((viewMode) => {
        expect(viewMode).toEqual('sidebar');
        done();
      });
    });
  });

  describe('Inbox > toggleInboxOpen', function () {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    it('should open Inbox when triggering toggleInboxOpen', async () => {
      spectator = createService({
        providers: [
          {
            provide: ConversationService,
            useValue: {
              ...conversationServiceMock,
              currentFirestoreConversationId$: of('C-123'),
            },
          },
        ],
      });
      const router = spectator.inject(Router);

      const openInbox = await jest.spyOn(spectator.service, 'open');
      const parseUrl = jest.spyOn(router, 'parseUrl').mockReturnValue({ root: { children: {} } } as any);

      await spectator.service.toggleInboxOverlayOpen();

      expect(parseUrl).toHaveBeenCalledTimes(1);
      expect(openInbox).toHaveBeenCalledWith();
    });
    it('should close Inbox when triggering toggleInboxOpen and the route contains inbox without AI', async () => {
      spectator = createService();
      const router = spectator.inject(Router);

      const close = await jest.spyOn(spectator.service, 'close');
      const parseUrl = jest
        .spyOn(router, 'parseUrl')
        .mockReturnValue({ root: { children: { inbox: { segments: [{ path: 'inbox' }] } } } } as any);

      await spectator.service.toggleInboxOverlayOpen();

      expect(parseUrl).toHaveBeenCalledTimes(1);
      expect(close).toHaveBeenCalled();
    });
    it('should close inbox AI if Inbox chat gets Open', async () => {
      spectator = createService({
        providers: [
          {
            provide: ConversationService,
            useValue: {
              ...conversationServiceMock,
              currentFirestoreConversationId$: of('C-123'),
              removeLastConversationId: jest.fn(),
            },
          },
        ],
      });
      const router = spectator.inject(Router);

      const openInbox = await jest.spyOn(spectator.service, 'open');
      const closeInboxAI = await jest.spyOn(spectator.service, 'closeAIConversationOverlay');
      const parseUrl = jest
        .spyOn(router, 'parseUrl')
        .mockReturnValue({ root: { children: { inbox: { segments: [{ path: 'inbox' }, { path: 'ai' }] } } } } as any);

      await spectator.service.toggleInboxOverlayOpen();

      expect(parseUrl).toHaveBeenCalledTimes(1);
      expect(closeInboxAI).toHaveBeenCalled();
      expect(openInbox).toHaveBeenCalledWith();
    });
  });

  describe('isOpen$', function () {
    let mockRouter: any;

    const createServiceWithUrl = (testUrl: string) => {
      const urlSerializer = new DefaultUrlSerializer();

      mockRouter = {
        events: of(new NavigationEnd(0, testUrl, testUrl)),
        parseUrl: (url: string) => urlSerializer.parse(url),
        navigate: jest.fn(),
      };

      return createService({
        providers: [{ provide: Router, useValue: mockRouter }],
      });
    };

    beforeEach(() => {
      jest.clearAllMocks();
      Object.defineProperty(window, 'location', {
        value: { pathname: '/' },
        writable: true,
      });
    });

    it('should return true for basic inbox route', (done) => {
      const testUrl = '/inbox(inbox:inbox)';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(true);
        expect(mockRouter.parseUrl).toBeDefined();
        done();
      });
    });

    it('should return true for inbox conversation route', (done) => {
      const testUrl = '/inbox(inbox:inbox/conversations/C-123)';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(true);
        done();
      });
    });

    it('should return true for inbox AI route', (done) => {
      const testUrl = '/inbox(inbox:inbox/ai)';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(true);
        done();
      });
    });

    it('should return true for inbox AI chat route', (done) => {
      const testUrl = '/inbox(inbox:inbox/ai/chat/123)';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(true);
        done();
      });
    });

    it('should return true for conversation overlay route', (done) => {
      const testUrl = '/partners.vendasta.com/inbox(inbox:conversation/dbe43aa4-d13e-4189-bb8d-06d808e5f39c)';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(true);
        done();
      });
    });

    it('should return false for root route', (done) => {
      const testUrl = '/';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(false);
        done();
      });
    });

    it('should return false for different outlet route', (done) => {
      const testUrl = '/dashboard(sidebar:settings)';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(false);
        done();
      });
    });

    it('should return false for non-inbox primary route', (done) => {
      const testUrl = '/dashboard';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(false);
        done();
      });
    });

    it('should return false when inbox outlet has no segments', (done) => {
      const testUrl = '/inbox(inbox:)'; // Empty segments after inbox:
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(false);
        done();
      });
    });

    it('should handle startWith initial value from window.location.pathname containing (inbox:', (done) => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/partners.vendasta.com/inbox(inbox:conversation/dbe43aa4-d13e-4189-bb8d-06d808e5f39c)' },
        writable: true,
      });

      const testUrl = '';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(true);
        done();
      });
    });

    it('should handle startWith initial value from window.location.pathname not containing (inbox:', (done) => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/dashboard' },
        writable: true,
      });

      const testUrl = '';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(false);
        done();
      });
    });

    it('should return true for URL with query parameters and fragments', (done) => {
      const testUrl = '/partners.vendasta.com/inbox(inbox:inbox/conversations/C-123)?param=value#fragment';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(true);
        done();
      });
    });

    it('should return true for URL with multiple outlets where inbox is present', (done) => {
      const testUrl = '/dashboard(sidebar:menu//inbox:inbox/ai)';
      spectator = createServiceWithUrl(testUrl);

      spectator.service.isOpen$.subscribe((isOpen) => {
        expect(isOpen).toBe(true);
        done();
      });
    });
  });

  describe('open', function () {
    beforeEach(() => (spectator = createService()));
    it('should redirect to the conversation Id', async () => {
      const router = spectator.inject(Router);

      await spectator.service.open('C-123');
      expect(router.navigate).toHaveBeenCalledWith(
        [
          '',
          {
            outlets: { inbox: 'inbox/conversations/C-123' },
          },
        ],
        {
          queryParams: { inboxPrefilledMessage: null },
          queryParamsHandling: 'merge',
        },
      );
    });
    it('should redirect to the conversation Id with a Prefilled message', async () => {
      const router = spectator.inject(Router);
      const msg = 'This is a prefilled message';

      await spectator.service.open('C-123', msg);
      expect(router.navigate).toHaveBeenCalledWith(
        [
          '',
          {
            outlets: { inbox: 'inbox/conversations/C-123' },
          },
        ],
        {
          queryParams: { inboxPrefilledMessage: msg },
          queryParamsHandling: 'merge',
        },
      );
    });
  });
});
