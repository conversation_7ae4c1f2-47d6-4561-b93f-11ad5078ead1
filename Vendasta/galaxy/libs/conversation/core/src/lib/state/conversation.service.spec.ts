import { HttpClient } from '@angular/common/http';
import { createServiceFactory, mockProvider, SpectatorService } from '@ngneat/spectator/jest';
import { TranslateService } from '@ngx-translate/core';
import {
  Conversation,
  ConversationApiService,
  ConversationChannel,
  EventType,
  GetMultiConversationDetailsResponseDetailedConversation,
  GetParticipantsByKeyResponse,
  MediaInterface,
  Message,
  MessageType,
  Participant,
  ParticipantType,
  PlatformLocation,
  SendMessageResponse,
  SetLastSeenRequestStatus,
  SetLastSeenResponse,
} from '@vendasta/conversation';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { JestScheduler } from '@vendasta/rx-utils';
import { of, throwError } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { ConversationService } from './conversation.service';
import { FirestoreService } from '../firestore.service';
import { InboxService } from '../inbox.service';
import {
  accountGroupIdMock,
  accountGroupIdTokenMock,
  accountGroupMock,
  conversationApiServiceMock,
  conversationChannelsEnabledTokenMock,
  conversationChannelServiceTokenMock,
  conversationParticipantSenderMock,
  conversationStatelessServiceMock,
  countryTokenMock,
  featureFlagTokenMock,
  geographicalStateTokenMock,
  inboxServiceMock,
  marketIdTokenMock,
  partnerIdTokenMock,
  platformLocationTokenMock,
  routesTokenMock,
  snackbarServiceMock,
  translateServiceMock,
  userIdTokenMock,
  userMock,
  viewServiceMock,
} from '../mocks';
import { ParticipantService } from '../participant.service';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_CHANNEL_SERVICE_TOKEN,
  CONVERSATION_CONFIG_TOKEN,
  CONVERSATION_CONVERSATION_CHANNELS_ENABLED_TOKEN,
  CONVERSATION_COUNTRY_TOKEN,
  CONVERSATION_GEOGRAPHICAL_STATE_TOKEN,
  CONVERSATION_GOOGLE_BUSINESS_MESSAGES_AVAILABLE_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  CONVERSATION_PLATFORM_LOCATION_TOKEN,
  CONVERSATION_ROUTES_TOKEN,
  CONVERSATION_SMS_ENABLED_TOKEN,
  CONVERSATION_WEB_CHAT_ENABLED_TOKEN,
  FEATURE_FLAG_TOKEN,
  GROUP_ID_TOKEN,
  MARKET_ID_TOKEN,
  PARTNER_ID_TOKEN,
  USER_ID_TOKEN,
} from '../tokens';
import { ViewService } from '../view.service';
import { ConversationStatelessService } from '../conversation-stateless.service';

describe('ConversationService', () => {
  let spectator: SpectatorService<ConversationService>;
  let consoleErrorSpy: jest.SpyInstance<void, [message?: any, ...optionalParams: any[]]>;

  const createService = createServiceFactory({
    service: ConversationService,
    providers: [
      { provide: ConversationStatelessService, useValue: conversationStatelessServiceMock },
      { provide: ConversationApiService, useValue: conversationApiServiceMock },
      { provide: SnackbarService, useValue: snackbarServiceMock },
      { provide: ParticipantService },
      { provide: InboxService, useValue: inboxServiceMock },
      { provide: CONVERSATION_GOOGLE_BUSINESS_MESSAGES_AVAILABLE_TOKEN, useValue: of(true) },
      { provide: CONVERSATION_SMS_ENABLED_TOKEN, useValue: of(true) },
      { provide: CONVERSATION_WEB_CHAT_ENABLED_TOKEN, useValue: of(true) },
      { provide: HttpClient, useValue: {} },
      { provide: TranslateService, useValue: translateServiceMock },
      { provide: USER_ID_TOKEN, useValue: userIdTokenMock },
      { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: accountGroupIdTokenMock },
      { provide: PARTNER_ID_TOKEN, useValue: partnerIdTokenMock },
      { provide: MARKET_ID_TOKEN, useValue: marketIdTokenMock },
      { provide: FEATURE_FLAG_TOKEN, useValue: featureFlagTokenMock },
      { provide: CONVERSATION_PLATFORM_LOCATION_TOKEN, useValue: platformLocationTokenMock },
      { provide: CONVERSATION_CONVERSATION_CHANNELS_ENABLED_TOKEN, useValue: conversationChannelsEnabledTokenMock },
      { provide: CONVERSATION_ROUTES_TOKEN, useValue: routesTokenMock },
      { provide: CONVERSATION_COUNTRY_TOKEN, useValue: countryTokenMock },
      { provide: CONVERSATION_GEOGRAPHICAL_STATE_TOKEN, useValue: geographicalStateTokenMock },
      { provide: CONVERSATION_CONFIG_TOKEN, useValue: {} },
      { provide: ViewService, useValue: viewServiceMock },
      { provide: CONVERSATION_CHANNEL_SERVICE_TOKEN, useValue: conversationChannelServiceTokenMock },
      { provide: GROUP_ID_TOKEN, useValue: of('') },
      { provide: CONVERSATION_HOST_APP_INTERFACE_TOKEN, useValue: {} },
    ],
    mocks: [FirestoreService],
  });

  function defaultCreateService(): void {
    spectator = createService();
  }

  describe('setConversationLastSeen', () => {
    const date = '2020-01-01T00:00:00.000Z';
    const currentPartnerId = accountGroupMock.partnerId;
    const currentAccountGroupId = accountGroupIdMock;
    const currentUserId = userMock.userId;

    const newParticipant = {
      participantId: 'participant-123',
      partnerId: currentPartnerId,
      accountGroupId: currentAccountGroupId,
      internalParticipantId: currentUserId,
      channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
      participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
      created: new Date(date),
      updated: new Date(date),
      deleted: new Date(date),
    } as Participant;

    beforeEach(() => {
      jest.spyOn(console, 'error').mockImplementation(() => {
        //donothing
      });
    });

    it('should set last seen by participant field in any center when participant exist', async () => {
      spectator = createService({
        providers: [
          mockProvider(ConversationApiService, {
            getParticipantsByKey: jest.fn().mockReturnValue(
              of({
                participant: newParticipant,
                participants: [newParticipant] as Participant[],
              } as GetParticipantsByKeyResponse),
            ),
            setLastSeen: jest.fn().mockReturnValue(of({ participant: newParticipant } as SetLastSeenResponse)),
          }),
        ],
      });
      await spectator.service.setConversationLastSeen(
        'vstore-123',
        SetLastSeenRequestStatus.SET_LAST_SEEN_REQUEST_STATUS_READ,
      );
      expect(spectator.service['conversationApiService'].setLastSeen).toHaveBeenCalledWith({
        conversationId: 'vstore-123',
        participant: newParticipant,
        status: SetLastSeenRequestStatus.SET_LAST_SEEN_REQUEST_STATUS_READ,
      });
    });
  });

  describe('sendAndStageMessage', () => {
    let testScheduler: TestScheduler;
    const messageMedia: MediaInterface[] = [
      {
        mediaContentType: 'image/png',
        mediaLocationPath: '2023-07-05-cfa39d6e-e89c-4ab8-9413-16951edcd92b',
        mediaFileName: 'test.png',
        fileSize: 100,
      },
    ];

    beforeEach(() => {
      spectator = createService({
        providers: [
          mockProvider(ConversationApiService, {
            sendMessage: jest.fn().mockReturnValue(of({} as SendMessageResponse)),
          }),
        ],
      });
      testScheduler = new TestScheduler((actual, expected) => expect(actual).toEqual(expected));
      const participantService = spectator.inject(ParticipantService);
      jest.replaceProperty(participantService, 'currentParticipant$', of(conversationParticipantSenderMock));
    });

    afterEach(() => {
      testScheduler.flush();
    });

    it('should be able to send a message with media', async () => {
      jest
        .spyOn(spectator.service['conversationStatelessService'], 'sendMessage')
        .mockReturnValue(Promise.resolve({ messageId: 'MESSAGE-123' } as SendMessageResponse));

      const result = await spectator.service.sendAndStageMessage(
        'CONVERSATION-123',
        MessageType.MESSAGE_TYPE_MESSAGE,
        'Test body',
        ConversationChannel.CONVERSATION_CHANNEL_SMS,
        PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
        messageMedia,
      );

      expect(spectator.service['conversationStatelessService'].sendMessage).toHaveBeenCalled();
      expect(result).toEqual({ messageId: 'MESSAGE-123' });
    });

    it('should be able to send a message without media', async () => {
      jest
        .spyOn(spectator.service['conversationStatelessService'], 'sendMessage')
        .mockReturnValue(Promise.resolve({ messageId: 'MESSAGE-123' } as SendMessageResponse));

      const result = await spectator.service.sendAndStageMessage(
        'CONVERSATION-123',
        MessageType.MESSAGE_TYPE_MESSAGE,
        'Test body',
        ConversationChannel.CONVERSATION_CHANNEL_SMS,
        PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
      );

      expect(spectator.service['conversationStatelessService'].sendMessage).toHaveBeenCalled();
      expect(result).toEqual({ messageId: 'MESSAGE-123' });
    });

    it('should throw an error when the sendMessage fails', async () => {
      jest
        .spyOn(spectator.service['conversationStatelessService'], 'sendMessage')
        .mockReturnValue(Promise.reject(new Error('Test error')));

      await expect(
        spectator.service.sendAndStageMessage(
          'CONVERSATION-123',
          MessageType.MESSAGE_TYPE_MESSAGE,
          'Test body',
          ConversationChannel.CONVERSATION_CHANNEL_SMS,
          PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
        ),
      ).rejects.toThrow('Test error');
    });

    it('should add the message to the unsaved messages', async () => {
      jest
        .spyOn(spectator.service['conversationStatelessService'], 'sendMessage')
        .mockReturnValue(Promise.resolve({ messageId: 'MESSAGE-123' } as SendMessageResponse));

      const result = await spectator.service.sendAndStageMessage(
        'CONVERSATION-123',
        MessageType.MESSAGE_TYPE_MESSAGE,
        'Test body',
        ConversationChannel.CONVERSATION_CHANNEL_SMS,
        PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
      );

      expect(spectator.service['conversationStatelessService'].sendMessage).toHaveBeenCalled();
      expect(result).toEqual({ messageId: 'MESSAGE-123' });

      const unsavedMessages = spectator.service['unsavedMessages$$'].getValue();
      console.log(unsavedMessages);
      expect(unsavedMessages['123']).toBeDefined();
      expect(unsavedMessages['123'].length).toBe(1);
      expect(unsavedMessages['123'][0].id).toBe('123');
      expect(unsavedMessages['123'][0].messageId).toBe('MESSAGE-123');
      expect(unsavedMessages['123'][0].body).toBe('Test body');
    });
  });

  describe('getPreviewContent', () => {
    let sched: TestScheduler;
    beforeEach(() => {
      defaultCreateService();
      sched = new JestScheduler();
    });
    afterEach(() => sched.flush());
    it('should get preview content with user name at front', (done) => {
      const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
        participants: [
          new Participant({
            name: 'Test User',
            participantId: 'PARTICIPANT-123',
            isParticipantInternalInfoDeleted: false,
            internalParticipantId: 'U-ABC-345',
          }),
        ],
        message: {
          body: 'Test preview content',
          participantId: 'PARTICIPANT-123',
        } as Message,
      });

      const previewContent$ = spectator.service.getPreviewContent(conversationDetail);
      sched.expectObservable(previewContent$).toBe('(x|)', {
        x: 'Test User: Test preview content',
      });
      done();
    });
    it('should get preview content with current user as `You` at front', (done) => {
      const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
        participants: [
          new Participant({
            name: 'Test User',
            participantId: 'PARTICIPANT-123',
            isParticipantInternalInfoDeleted: false,
            // same as the user id token
            internalParticipantId: 'U-ABC-123',
          }),
        ],
        message: {
          body: 'Test preview content',
          participantId: 'PARTICIPANT-123',
        } as Message,
      });

      const previewContent$ = spectator.service.getPreviewContent(conversationDetail);
      sched.expectObservable(previewContent$).toBe('(x|)', {
        x: 'You: Test preview content',
      });
      done();
    });
    it('should get preview content with `Deleted User` label at front', (done) => {
      const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
        participants: [
          new Participant({
            name: 'Test User',
            participantId: 'PARTICIPANT-123',
            isParticipantInternalInfoDeleted: true,
            internalParticipantId: 'U-ABC-345',
          }),
        ],
        message: {
          body: 'Test preview content',
          participantId: 'PARTICIPANT-123',
        } as Message,
      });

      const previewContent$ = spectator.service.getPreviewContent(conversationDetail);
      sched.expectObservable(previewContent$).toBe('(x|)', {
        x: 'Deleted User: Test preview content',
      });
      done();
    });
  });

  describe('getPreviewContentText', () => {
    let sched: TestScheduler;
    beforeEach(() => {
      defaultCreateService();
      sched = new JestScheduler();
      spectator.service['translateService'].instant = jest.fn().mockReturnValue('Shared a file');
    });
    afterEach(() => sched.flush());
    it('should use the message body text as preview content text when message has both body text and media file', (done) => {
      const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
        participants: [
          new Participant({
            name: 'Test User',
            participantId: 'PARTICIPANT-123',
            isParticipantInternalInfoDeleted: false,
            internalParticipantId: 'U-ABC-345',
          }),
        ],
        message: {
          body: 'Test message body text',
          participantId: 'PARTICIPANT-123',
          media: ['Test message media info'],
        } as Message,
      });

      const previewContent = spectator.service.getPreviewContentText(conversationDetail);
      expect(previewContent).toEqual('Test message body text');
      expect(spectator.service['translateService'].instant).toHaveBeenCalledTimes(0);
      done();
    });
    it('should use the event.label with event.message for the preview content when most recent activity is a phone call event', (done) => {
      const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
        participants: [
          new Participant({
            name: 'Test User',
            participantId: 'PARTICIPANT-123',
            isParticipantInternalInfoDeleted: false,
            internalParticipantId: 'U-ABC-345',
          }),
        ],
        event: {
          labelKey: 'EVENT_TYPE_LABEL',
          message: 'Test event message',
          type: EventType.EVENT_TYPE_PHONE_CALL,
        },
      });

      spectator.service['translateService'].instant = jest.fn().mockReturnValue('Event Type');
      const previewContent = spectator.service.getPreviewContentText(conversationDetail);
      expect(previewContent).toEqual('[Event Type] Test event message');
      expect(spectator.service['translateService'].instant).toHaveBeenCalledTimes(1);
      done();
    });
    it('should use the event.label with no event.message for the preview content when most recent activity is a form submission event', (done) => {
      const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
        participants: [
          new Participant({
            name: 'Test User',
            participantId: 'PARTICIPANT-123',
            isParticipantInternalInfoDeleted: false,
            internalParticipantId: 'U-ABC-345',
          }),
        ],
        event: {
          labelKey: 'EVENT_TYPE_LABEL',
          message: 'Test event message',
          type: EventType.EVENT_TYPE_FORM_SUBMISSION,
        },
      });

      spectator.service['translateService'].instant = jest.fn().mockReturnValue('Event Type');
      const previewContent = spectator.service.getPreviewContentText(conversationDetail);
      expect(previewContent).toEqual('[Event Type]');
      expect(spectator.service['translateService'].instant).toHaveBeenCalledTimes(1);
      done();
    });
    it('should use the message body text as preview content text when message only has body text but no media file', (done) => {
      const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
        participants: [
          new Participant({
            name: 'Test User',
            participantId: 'PARTICIPANT-123',
            isParticipantInternalInfoDeleted: false,
            internalParticipantId: 'U-ABC-345',
          }),
        ],
        message: {
          body: 'Test message body text',
          participantId: 'PARTICIPANT-123',
          media: [] as string[],
        } as Message,
      });

      const previewContent = spectator.service.getPreviewContentText(conversationDetail);
      expect(previewContent).toEqual('Test message body text');
      expect(spectator.service['translateService'].instant).toHaveBeenCalledTimes(0);
      done();
    });
    it('should display "Shared a file" as preview content text when message only has media file but no body text', (done) => {
      const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
        participants: [
          new Participant({
            name: 'Test User',
            participantId: 'PARTICIPANT-123',
            isParticipantInternalInfoDeleted: false,
            internalParticipantId: 'U-ABC-345',
          }),
        ],
        message: {
          body: '',
          participantId: 'PARTICIPANT-123',
          media: ['Test message media info'],
        } as Message,
      });

      const previewContent = spectator.service.getPreviewContentText(conversationDetail);
      expect(previewContent).toEqual('Shared a file');
      expect(spectator.service['translateService'].instant).toHaveBeenCalledTimes(1);
      done();
    });
  });

  describe('getMultiMessages', () => {
    beforeEach(() => {
      defaultCreateService();
    });
    it('should show an error on console when not provide a message Ids valid', () => {
      consoleErrorSpy = jest.spyOn(console, 'error').mockClear();
      spectator.service.getMultiMessages([], 'CONVERSATION-123');
      expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
    });

    it('should be call the getMultiMessages from conversationApiService when set a valid message Ids previously', () => {
      spectator.service['conversationApiService'].getMultiMessages = jest.fn().mockReturnValue(of([]));
      spectator.service.getMultiMessages(['XYZ', 'ABC'], 'CONVERSATION-123');
      expect(spectator.service['conversationApiService'].getMultiMessages).toHaveBeenCalledTimes(1);
    });
  });

  describe('getMessage', () => {
    beforeEach(() => {
      defaultCreateService();
    });
    it('should show an error on console when not provide a message Id valid', () => {
      consoleErrorSpy = jest.spyOn(console, 'error').mockClear();
      spectator.service.getMessage('');
      expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
    });

    it('should be call the getMessage from conversationApiService when set a valid message Id previously', () => {
      spectator.service['conversationApiService'].getMessage = jest.fn().mockReturnValue(of({} as Message));
      spectator.service.getMessage('XYZ');
      expect(spectator.service['conversationApiService'].getMessage).toHaveBeenCalledTimes(1);
    });
  });
  describe('setConversationAIConfiguration', () => {
    const conversationId = 'CONVO-123';
    const conversationMock = {
      conversationId: conversationId,
      aiConfiguration: {
        aiResponseEnabled: true,
      },
    } as Conversation;

    beforeEach(() => {
      spectator = createService({
        providers: [
          mockProvider(ConversationApiService, {
            updateConversation: jest.fn().mockReturnValue(of({ conversation: conversationMock } as any)),
          }),
        ],
      });
    });

    it('should update the AI configuration of a conversation and show success snackbar when AI response is enabled', async () => {
      spectator.service['conversationApiService'].updateConversation = jest
        .fn()
        .mockReturnValue(of({ conversation: conversationMock }));
      spectator.service['snackbarService'].openSuccessSnack = jest.fn();

      const result = await spectator.service.setConversationAIResponderStatus(conversationId, true);

      expect(spectator.service['conversationApiService'].updateConversation).toHaveBeenCalledWith({
        conversation: {
          conversationId: conversationId,
          aiConfiguration: {
            aiResponseEnabled: true,
          },
        },
        fieldMask: {
          paths: ['ai_response_enabled'],
        },
      });

      expect(spectator.service['snackbarService'].openSuccessSnack).toHaveBeenCalledWith(
        'INBOX.INFO.AI_RESPONSES_ENABLED',
      );
      expect(result).toEqual(conversationMock);
    });

    it('should update the AI configuration of a conversation and show success snackbar when AI response is disabled', async () => {
      const conversationMockDisabled = {
        conversationId: 'CONVO-123',
        aiConfiguration: {
          aiResponseEnabled: false,
        },
      };

      spectator.service['conversationApiService'].updateConversation = jest
        .fn()
        .mockReturnValue(of({ conversation: conversationMockDisabled }));
      spectator.service['snackbarService'].openSuccessSnack = jest.fn();

      const result = await spectator.service.setConversationAIResponderStatus(conversationId, false);

      expect(spectator.service['conversationApiService'].updateConversation).toHaveBeenCalledWith({
        conversation: {
          conversationId: conversationId,
          aiConfiguration: {
            aiResponseEnabled: false,
          },
        },
        fieldMask: {
          paths: ['ai_response_enabled'],
        },
      });

      expect(spectator.service['snackbarService'].openSuccessSnack).toHaveBeenCalledWith(
        'INBOX.INFO.AI_RESPONSES_DISABLED',
      );
      expect(result).toEqual(conversationMockDisabled);
    });
    it('should raise an error when the conversation update fails', async () => {
      const err = new Error('simulated error');
      spectator.service['conversationApiService'].updateConversation = jest.fn().mockReturnValue(throwError(() => err));
      await expect(spectator.service.setConversationAIResponderStatus(conversationId, true)).rejects.toThrowError();
    });
  });
});
