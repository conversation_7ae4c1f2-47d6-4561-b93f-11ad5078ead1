import { computed, inject, Injectable } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  BehaviorSubject,
  combineLatest,
  distinctUntilChanged,
  firstValueFrom,
  map,
  Observable,
  switchMap,
  of,
  catchError,
} from 'rxjs';
import { ConversationChannel, Participant, ParticipantType, MessageType } from '@vendasta/conversation';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_PLATFORM_LOCATION_TOKEN,
  PARTNER_ID_TOKEN,
  USER_ID_TOKEN,
} from '../tokens';
import { toFirestoreId, fromFirestoreId } from '../conversation-utils';
import { ConversationService } from './conversation.service';
import { ConversationStatelessService } from '../conversation-stateless.service';
import { v4 as uuidv4 } from 'uuid';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { AiAssistantService, SYSTEM_ASSISTANT_ID, VENDASTA_AI_AVATAR_SVG_ICON } from '@galaxy/ai-assistant';
import { ConversationAvailableChannels } from '../interface/conversation.interface';
import { DEFAULT_OPENAI_BOT_NAME } from '../inbox.constants';
import { MessageInfo } from '../interface/conversation.interface';
import { buildAIContextMetadata } from '../conversation-utils';
import { InboxService } from '../inbox.service';
import { TranslateService } from '@ngx-translate/core';

export interface SuggestionChip {
  label: string;
  message: string;
}

@Injectable()
export class AiAssistantConversationService {
  private readonly conversationService = inject(ConversationService);
  private readonly conversationStatelessService = inject(ConversationStatelessService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly aiAssistantService = inject(AiAssistantService);
  private readonly inboxService = inject(InboxService);
  private readonly translateService = inject(TranslateService);

  private readonly userId$ = inject(USER_ID_TOKEN).pipe(distinctUntilChanged());
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN).pipe(distinctUntilChanged());
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN).pipe(distinctUntilChanged());
  private readonly senderParticipant$: Observable<Participant>;
  private readonly platformLocation = inject(CONVERSATION_PLATFORM_LOCATION_TOKEN);

  private readonly aiAssistantId$$ = new BehaviorSubject<string | null>(null);
  readonly aiAssistantId$ = this.aiAssistantId$$.pipe(distinctUntilChanged());

  readonly assistantId = toSignal(this.aiAssistantId$);

  readonly currentAssistant = toSignal(
    combineLatest([this.aiAssistantId$, this.partnerId$, this.accountGroupId$]).pipe(
      switchMap(([assistantId, partnerId, accountGroupId]) => {
        if (!assistantId) {
          return of(null);
        }

        return assistantId === SYSTEM_ASSISTANT_ID
          ? this.aiAssistantService.getSystemAssistant().pipe(
              catchError((err) => {
                console.warn('System AI assistant not found, proceeding with default assistant info:', err);
                return of(null);
              }),
            )
          : this.aiAssistantService.getAssistant(partnerId, accountGroupId, assistantId).pipe(
              catchError((err) => {
                console.warn('AI assistant not found, proceeding with default assistant info:', err);
                return of(null);
              }),
            );
      }),
    ),
    { initialValue: null },
  );

  readonly dynamicAssistantName = computed(() => {
    const assistant = this.currentAssistant();
    return assistant?.name || null;
  });

  readonly assistantAvatarUrl = computed(() => {
    const assistant = this.currentAssistant();
    return assistant?.avatarUrl;
  });

  readonly assistantIconName = computed(() => {
    return VENDASTA_AI_AVATAR_SVG_ICON;
  });

  readonly systemAssistant = toSignal(
    this.aiAssistantService.getSystemAssistant().pipe(
      catchError((err) => {
        console.error('Error getting system AI assistant information', err);
        return of(null);
      }),
    ),
    { initialValue: null },
  );

  readonly isSystemAssistant = computed(() => {
    const assistant = this.currentAssistant();
    const systemAssistant = this.systemAssistant();
    return !!assistant && !!systemAssistant && assistant.id === systemAssistant.id;
  });

  readonly availableChannels: ConversationAvailableChannels = {
    availableChannels: [ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT],
    preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
    channelAvailabilities: [
      {
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        isAvailable: true,
      },
    ],
  };

  readonly DEFAULT_OPENAI_BOT_NAME = DEFAULT_OPENAI_BOT_NAME;

  constructor() {
    this.senderParticipant$ = combineLatest([this.userId$, this.accountGroupId$, this.partnerId$]).pipe(
      map(([user, agid, partnerID]) => {
        return new Participant({
          participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
          partnerId: partnerID,
          accountGroupId: agid,
          internalParticipantId: user,
          isSubjectParticipant: true,
        });
      }),
    );
  }

  setAssistantId(assistantId: string | null): void {
    this.aiAssistantId$$.next(assistantId);
  }

  async startConversation(): Promise<void> {
    const assistantId = await firstValueFrom(this.aiAssistantId$);
    if (!assistantId) {
      this.snackbarService.openErrorSnack('INBOX.ERROR.UNABLE_TO_CREATE_CONVERSATION');
      return;
    }

    const sender = await firstValueFrom(this.senderParticipant$);
    const partnerId = await firstValueFrom(this.partnerId$);
    const agid = await firstValueFrom(this.accountGroupId$);

    const aiParticipant = new Participant({
      participantType: ParticipantType.PARTICIPANT_TYPE_AI_ASSISTANT,
      partnerId: partnerId,
      accountGroupId: agid,
      internalParticipantId: assistantId,
      isSubjectParticipant: true,
    });

    try {
      const resp = await firstValueFrom(
        this.conversationStatelessService.createConversation(
          [sender, aiParticipant],
          ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
          this.platformLocation,
          uuidv4(),
        ),
      );

      this.conversationService.setCurrentFirestoreConversationId(
        toFirestoreId(resp?.conversation?.conversationId ?? ''),
      );
    } catch (error) {
      this.snackbarService.openErrorSnack('INBOX.ERROR.UNABLE_TO_CREATE_CONVERSATION');
    }
  }

  readonly aiSuggestionChips = computed(() => {
    if (!this.isSystemAssistant()) {
      return [];
    }

    const suggestionKeys = [
      'INBOX.AI_CHAT.SUGGESTIONS.HELP_GETTING_STARTED',
      'INBOX.AI_CHAT.SUGGESTIONS.WALK_THROUGH_FEATURE',
      'INBOX.AI_CHAT.SUGGESTIONS.HELP_FIXING_SOMETHING',
    ];

    return suggestionKeys.map(
      (key): SuggestionChip => ({
        label: this.translateService.instant(key),
        message: this.translateService.instant(key),
      }),
    );
  });

  readonly aiTitle = computed(() => {
    const assistantName = this.dynamicAssistantName();
    return this.translateService.instant('INBOX.AI_CHAT.NO_MESSAGES.TITLE', { assistantName });
  });

  readonly aiDescription = computed(() => {
    const assistantName = this.dynamicAssistantName();
    const descriptionKey = this.isSystemAssistant()
      ? 'INBOX.AI_CHAT.NO_MESSAGES.DESCRIPTION_WITH_SUGGESTIONS'
      : 'INBOX.AI_CHAT.NO_MESSAGES.DESCRIPTION_WITHOUT_SUGGESTIONS';
    return this.translateService.instant(descriptionKey, { assistantName });
  });

  async sendMessage(messageInfo: MessageInfo): Promise<string> {
    const currentFirestoreConversationId = await firstValueFrom(
      this.conversationService.currentFirestoreConversationId$,
    );

    const metadata = buildAIContextMetadata();
    await this.conversationService.sendAndStageMessage(
      fromFirestoreId(currentFirestoreConversationId),
      MessageType.MESSAGE_TYPE_MESSAGE,
      messageInfo.text ?? '',
      messageInfo.channel,
      this.inboxService.platformLocation,
      messageInfo.attachments,
      undefined,
      undefined,
      metadata,
    );

    return currentFirestoreConversationId;
  }
}
