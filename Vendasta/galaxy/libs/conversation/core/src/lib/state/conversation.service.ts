import { HttpErrorResponse } from '@angular/common/http';
import { Inject, inject, Injectable, Optional, signal, computed } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { collectionData, Query } from '@angular/fire/firestore';
import { TranslateService } from '@ngx-translate/core';
import {
  Conversation,
  ConversationApiService,
  ConversationChannel,
  EvaluateResponseRequestInterface,
  EvaluationSentiment,
  EventType,
  GetAvailableChannelsForConversationResponse,
  GetMessageRequest,
  GetMultiMessagesRequest,
  GetMultiMessagesResponse,
  GlobalParticipantType,
  LookupConversationsRequestInterface,
  LookupConversationsResponse,
  MediaInterface,
  Message,
  MessageStatus,
  MessageType,
  MetadataIdentifier,
  Participant,
  ParticipantType as pType,
  PlatformLocation,
  SendMessageResponse,
  SetLastSeenRequestStatus,
} from '@vendasta/conversation';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { GetAccountInfoResponse, ParticipantType, SmsService } from '@vendasta/smsv2';
import {
  asyncScheduler,
  BehaviorSubject,
  catchError,
  combineLatest,
  distinctUntilChanged,
  EMPTY,
  filter,
  firstValueFrom,
  map,
  merge,
  Observable,
  observeOn,
  of,
  shareReplay,
  skip,
  skipWhile,
  startWith,
  switchMap,
} from 'rxjs';
import { AlertOptions, ConversationChannelService } from '../channels/conversation-channel.abstract';
import { conversationUnseen, fromFirestoreId, toFirestoreId } from '../conversation-utils';
import { FirestoreService } from '../firestore.service';
import { LOCAL_STORAGE_ID } from '../inbox.constants';
import { InboxService } from '../inbox.service';
import {
  ConversationAvailableChannels,
  ConversationDetail,
  ConversationMessage,
  FirestoreConversation,
  Media,
  MessageInfo,
} from '../interface/conversation.interface';
import { HostAppInterface } from '../interface/host-app.interface';
import { convertMessageIntoConversationMessage } from '../message-utils';
import { ParticipantService } from '../participant.service';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_CHANNEL_SERVICE_TOKEN,
  CONVERSATION_CONFIG_TOKEN,
  CONVERSATION_CONVERSATION_CHANNELS_ENABLED_TOKEN,
  CONVERSATION_COUNTRY_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  MARKET_ID_TOKEN,
  PARTNER_ID_TOKEN,
  USER_ID_TOKEN,
} from '../tokens';
import { Event } from '@vendasta/conversation/lib/_internal/objects/event';
import { AiAssistantService, ConnectionType, SMSConnectionID } from '@galaxy/ai-assistant';
import { AssistantType } from '@vendasta/ai-assistants';
import { Assistant } from '@vendasta/ai-assistants/lib/_internal/objects/assistant';
import { abortAiResponderTime, AiResponder } from '../interface/assistant.interface';
import { ConversationStatelessService } from '../conversation-stateless.service';

/**
 * ConversationService is responsible for managing conversation state and operations
 * within an Inbox instance. This service handles the core functionality needed
 * for conversations including:
 *
 * - Managing current conversation selection and state
 * - Managing conversation messages/events
 * - Providing conversation availability for different channels
 *
 * Each instance of this service maintains its own independent state, allowing
 * multiple Inbox instances to operate concurrently with separate conversation contexts.
 */
@Injectable()
export class ConversationService {
  private currentConversationChannel = ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED;

  private readonly conversationSelected$$ = new BehaviorSubject<boolean>(false);
  readonly conversationSelected$ = this.conversationSelected$$.asObservable();

  readonly currentParticipant$ = this.participantService.currentParticipant$;
  private readonly currentFirestoreConversationId$$: BehaviorSubject<string> = new BehaviorSubject('');
  readonly currentFirestoreConversationId$ = this.currentFirestoreConversationId$$.pipe(
    map(toFirestoreId),
    distinctUntilChanged(),
  );

  // The currentConversationDetail$ has to emit whenever firestoreService.conversation$ emits
  // this enables realtime updates in the current conversation whenever something changes
  // this.currentParticipant$ is used to ensure the current participant is loaded along with the conversation detail
  readonly currentConversationDetail$: Observable<ConversationDetail | null> = combineLatest([
    this.currentFirestoreConversationId$,
    this.currentParticipant$,
  ]).pipe(
    switchMap(([id, _]) => {
      if (!id) return of(null);
      return merge(
        this.conversationStatelessService.getConversationDetail(fromFirestoreId(id)),
        this.firestoreService.conversation$(id).pipe(
          skip(1),
          switchMap(() => this.conversationStatelessService.getConversationDetail(fromFirestoreId(id))),
        ),
      );
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
    skipWhile((v) => v === null),
  );

  readonly currentConvoSMSNumber$ = this.currentConversationDetail$.pipe(
    switchMap((convoDetail) => {
      if (
        !this.inboxService.isBusinessApp ||
        !convoDetail?.conversation.subjectParticipants.find(
          (p) => p.participantType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_CUSTOMER,
        )
      ) {
        return of({} as GetAccountInfoResponse);
      }
      const accountGroupId = convoDetail?.conversation.subjectParticipants.find(
        (p) => p.participantType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
      )?.internalParticipantId;
      if (!accountGroupId) {
        return of({} as GetAccountInfoResponse);
      }
      return this.smsService
        .getAccountInfo({
          internalId: accountGroupId,
          type: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
        })
        .pipe(
          catchError((error: HttpErrorResponse) => {
            console.error('error to get account info:', error.message);
            return of({} as GetAccountInfoResponse);
          }),
        );
    }),
    shareReplay(1),
  );

  draftMessages: MessageInfo | null = null;

  readonly displayBadge$ = of(!this.inboxService.isBusinessApp);
  readonly showInboxViews$ = this.inboxService.showInboxViews$;

  private getActiveAssistantAssistant$(partnerId: string, accountGroupId: string): Observable<Assistant | null> {
    if (!this.aiAssistantService) {
      return of(null);
    }

    return this.aiAssistantService
      .listAllAssistantsAssociatedToConnection(partnerId, accountGroupId, SMSConnectionID, ConnectionType.SMS)
      .pipe(
        map((res) => res.find((assistant) => assistant?.type === AssistantType.ASSISTANT_TYPE_INBOX) || null),
        catchError(() => of(null)),
      );
  }

  // aiResponder$ checks existence of sms connection associated with inbox assistant to load assistant if applicable.
  public aiResponder$(conversationDetail: ConversationDetail) {
    return combineLatest([this.partnerId$, this.hostAppInterface.getNamespace()]).pipe(
      switchMap(([partnerId, namespace]) => {
        let accountGroupId =
          namespace.namespaceType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP ? namespace.id : '';

        if (this.hostAppInterface.getAppOptions().is_multilocation) {
          // if it is a multilocation app, we need to get the accountGroup from the conversation
          accountGroupId =
            conversationDetail?.conversation.subjectParticipants.find(
              (p) => p.participantType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
            )?.internalParticipantId || '';
        }
        return this.getActiveAssistantAssistant$(partnerId, accountGroupId);
      }),
      map(
        (activeChatAssistant: Assistant): AiResponder => ({
          isEnabled: !!activeChatAssistant,
          assistant: activeChatAssistant,
          isConversationEnabled: conversationDetail.conversation.aiConfiguration?.aiResponseEnabled,
        }),
      ),
    );
  }

  readonly aiResponderForConversation$ = this.currentConversationDetail$.pipe(
    distinctUntilChanged((a, b) => a?.conversation.conversationId === b?.conversation.conversationId),
    switchMap((conversationDetail) => {
      if (!conversationDetail) return of(null);

      // if is not a conversation with a customer, return false
      const customer = conversationDetail.conversation.subjectParticipants.some(
        (participant) => participant.participantType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_CUSTOMER,
      );

      if (!customer) return of(null);

      return this.aiResponder$(conversationDetail);
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  constructor(
    private readonly conversationApiService: ConversationApiService,
    private readonly snackbarService: SnackbarService,
    private readonly analyticsService: ProductAnalyticsService,
    @Inject(USER_ID_TOKEN) readonly userId$: Observable<string>,
    @Inject(ACCOUNT_GROUP_ID_TOKEN) private readonly accountGroupId$: Observable<string>,
    @Inject(PARTNER_ID_TOKEN) private readonly partnerId$: Observable<string>,
    @Inject(CONVERSATION_CONVERSATION_CHANNELS_ENABLED_TOKEN)
    private readonly conversationChannelsEnabled$: Observable<ConversationChannel[]>,
    @Inject(MARKET_ID_TOKEN) readonly marketId$: Observable<string>,
    private readonly participantService: ParticipantService,
    private readonly inboxService: InboxService,
    private readonly translateService: TranslateService,
    private readonly firestoreService: FirestoreService,
    @Inject(CONVERSATION_CHANNEL_SERVICE_TOKEN)
    private readonly conversationChannelServices: Record<ConversationChannel, ConversationChannelService | undefined>,
    private readonly smsService: SmsService,
    @Inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN) private readonly hostAppInterface: HostAppInterface,
    @Inject(CONVERSATION_COUNTRY_TOKEN) readonly country$: Observable<string>,
    @Optional() private readonly aiAssistantService: AiAssistantService,
    private readonly conversationStatelessService: ConversationStatelessService,
  ) {
    this.conversationChannelsEnabled$.pipe(takeUntilDestroyed()).subscribe((conversationChannelsEnabled) => {
      if (!conversationChannelsEnabled || conversationChannelsEnabled.length === 0) {
        console.warn(
          'You need to set up the conversationChannelsEnabled in the ConversationConfig. To fix see documentation in the README of @galaxy/conversation/core',
        );
      }

      if (conversationChannelsEnabled && conversationChannelsEnabled.length > 0) {
        this.setConversationChannel(conversationChannelsEnabled[0]);
      }
    });

    this.messages$.pipe(takeUntilDestroyed()).subscribe((messages) => {
      const multipleParticipantsHaveMessages = (messages || []).some(
        (message) => !!message.sender && message.sender.participantId !== messages[0].sender?.participantId,
      );
      if (multipleParticipantsHaveMessages) {
        this.analyticsService.trackEvent('inbox', 'biDirectionConversation', 'view');
      }
    });
  }

  private setConversationChannel(conversationChannel: ConversationChannel): void {
    this.currentConversationChannel = conversationChannel;
  }

  getConversationChannel(): ConversationChannel {
    return this.currentConversationChannel;
  }

  async sendAndStageMessage(
    conversationId: string,
    type: MessageType,
    body: string,
    channel: ConversationChannel,
    originLocation: PlatformLocation,
    media: MediaInterface[] = [],
    recipient?: Participant,
    sender?: Participant,
    metadata?: {
      identifier: MetadataIdentifier;
      data: {
        [key: string]: string;
      };
    }[],
    skipMessageDelivery?: boolean,
  ): Promise<SendMessageResponse> {
    if (!sender) {
      sender = await firstValueFrom(this.participantService.currentParticipant$);
    }

    try {
      const result: SendMessageResponse = await this.conversationStatelessService.sendMessage(
        conversationId,
        type,
        body,
        channel,
        originLocation,
        media,
        sender,
        recipient,
        metadata,
        skipMessageDelivery,
      );
      if (!result) {
        return result;
      }

      const now = new Date();
      const newMessage: ConversationMessage = {
        id: result.messageId.replace('MESSAGE-', ''),
        messageId: result.messageId,
        conversationId: conversationId,
        sender,
        type: type,
        channel,
        body,
        media: media as Media[],
        created: now,
        sendStatus: {
          created: now,
          status: MessageStatus.MESSAGE_STATUS_SENDING,
        },
      };
      this.addUnsavedMessage(toFirestoreId(conversationId), newMessage);
      return result;
    } catch (httpErrorResponse) {
      console.error('ConversationService sendSMS error', httpErrorResponse);
      throw httpErrorResponse;
    }
  }

  /**
   * set current firestore conversation ID
   * @param {string} conversationId - An conversation ID
   */
  setCurrentFirestoreConversationId(conversationId: string): void {
    this.currentFirestoreConversationId$$.next(conversationId ?? '');
  }

  /**
   * get the preview content of the conversation with message sender name
   * @param {ConversationDetail} conversationDetail - conversationDetail
   * @return {string} - the preview content with message sender name
   */
  getPreviewContent(conversationDetail: ConversationDetail): Observable<string> {
    return this.userId$.pipe(
      map((userId) => {
        const previewContent = this.getPreviewContentText(conversationDetail);
        const latestMessageParticipant = conversationDetail?.participants?.find(
          (participant) => participant?.participantId === conversationDetail.message?.participantId,
        );
        if (latestMessageParticipant?.isParticipantInternalInfoDeleted) {
          return `${this.translateService.instant('INBOX.PREVIEW_PANE.DELETED_USER')}: ${previewContent}`;
        }
        const otherUserPreviewContent = !latestMessageParticipant?.name
          ? `${previewContent}`
          : `${latestMessageParticipant?.name}: ${previewContent}`;
        return latestMessageParticipant?.internalParticipantId === userId
          ? `${this.translateService.instant('INBOX.PREVIEW_PANE.CURRENT_USER')}: ${previewContent}`
          : otherUserPreviewContent;
      }),
    );
  }

  /**
   * get the preview text of the conversation according to the message body text and media info
   * @param {ConversationDetail} conversationDetail - conversationDetail
   * @return {string} - the preview text without message sender name
   */
  getPreviewContentText(conversationDetail: ConversationDetail): string {
    if (conversationDetail?.message) {
      if (conversationDetail?.message?.body) {
        return conversationDetail.message.body;
      }

      // If message only has media without text
      if (conversationDetail?.message?.media.length > 0) {
        return this.translateService.instant('INBOX.PREVIEW_PANE.SHARED_A_FILE');
      }
    }

    if (conversationDetail?.event) {
      const label = '[' + this.translateService.instant(conversationDetail.event.labelKey) + ']';
      if (conversationDetail.event.type === EventType.EVENT_TYPE_PHONE_CALL) {
        return label + ' ' + conversationDetail.event.message;
      }
      return label;
    }

    return '';
  }

  /**
   * get multi messages from the Conversation µs
   * @param {string[]} messageIds - the message IDs
   * @param {string} conversationId - the conversation ID
   * @return {Observable<GetMultiMessagesResponse>}
   */
  getMultiMessages(messageIds: string[], conversationId: string): Observable<GetMultiMessagesResponse> {
    if (!messageIds || messageIds.length === 0) {
      console.error('the messageIds is not valid');
      return of({ messages: [] as Message[] } as GetMultiMessagesResponse);
    }

    const req = {
      conversationId: conversationId,
      messageIds: messageIds,
    } as GetMultiMessagesRequest;
    return this.conversationApiService.getMultiMessages(req).pipe(
      catchError((err) => {
        console.error('getMultiMessages error:', err?.message);
        return of({} as GetMultiMessagesResponse);
      }),
    );
  }

  /**
   * get a message from the Conversation µs
   * @param {string} messageId - the message ID
   * @return {Observable<Message>}
   */
  getMessage(messageId: string): Observable<Message> {
    if (!messageId || messageId === '') {
      console.error('the messageId is not valid');
      return of({} as Message);
    }

    const req = {
      messageId,
    } as GetMessageRequest;

    return this.conversationApiService.getMessage(req).pipe(
      catchError((err) => {
        console.error('getMessage error:', err?.message);
        return of({} as Message);
      }),
    );
  }

  /**
   * set conversation lastSeenTime by Participant
   * @param {string} conversationId - the conversation ID
   * @param {SetLastSeenRequestStatus} status - the timestamp
   */
  async setConversationLastSeen(conversationId: string, status: SetLastSeenRequestStatus): Promise<void> {
    const participant = await firstValueFrom(this.currentParticipant$);
    await firstValueFrom(
      this.conversationApiService.setLastSeen({
        conversationId: conversationId,
        participant,
        status: status,
      }),
    );
  }

  async setConversationOpenStatus(conversationId: string, isOpen: boolean): Promise<Conversation> {
    const resp = await firstValueFrom(
      this.conversationApiService.updateConversation({
        conversation: {
          conversationId: conversationId,
          isOpen: isOpen,
        },
        fieldMask: {
          paths: ['is_open'],
        },
      }),
    );

    this.snackbarService.openSuccessSnack(
      isOpen ? 'INBOX.INFO.CONVERSATION_STATUS_OPENED' : 'INBOX.INFO.CONVERSATION_STATUS_CLOSED',
    );

    return resp.conversation;
  }

  async setConversationAIResponderStatus(conversationId: string, status: boolean): Promise<Conversation> {
    const resp = await firstValueFrom(
      this.conversationApiService.updateConversation({
        conversation: {
          conversationId: conversationId,
          aiConfiguration: {
            aiResponseEnabled: status,
          },
        },
        fieldMask: {
          paths: ['ai_response_enabled'],
        },
      }),
    );

    this.snackbarService.openSuccessSnack(
      status ? 'INBOX.INFO.AI_RESPONSES_ENABLED' : 'INBOX.INFO.AI_RESPONSES_DISABLED',
    );

    return resp.conversation;
  }

  async abortAIResponder(conversationId: string): Promise<Conversation> {
    const resp = await firstValueFrom(
      this.conversationApiService.updateConversation({
        conversation: {
          conversationId: conversationId,
          aiConfiguration: {
            willRespondAt: abortAiResponderTime,
          },
        },
        fieldMask: {
          paths: ['ai_will_respond_at'],
        },
      }),
    );

    return resp.conversation;
  }

  conversationUnseen(conversation: Conversation): Observable<boolean> {
    return this.currentParticipant$.pipe(
      map((participant) => {
        return conversationUnseen(conversation, participant?.participantId);
      }),
    );
  }

  lookupConversation(req: LookupConversationsRequestInterface): Observable<LookupConversationsResponse> {
    return this.conversationApiService.lookupConversations(req).pipe(
      catchError((err) => {
        if (err?.status === 403) {
          this.snackbarService.openErrorSnack('INBOX.ERROR.PERMISSION_DENY');
        }
        return of({} as LookupConversationsResponse);
      }),
    );
  }

  isUserImpersonated$ = inject(CONVERSATION_CONFIG_TOKEN)?.isImpersonating$ || of(false);

  getConversationEvents(docId: string): Observable<Event[]> {
    const conversationId = fromFirestoreId(docId);
    return this.firestoreService.events$(docId).pipe(
      switchMap((events) => {
        const eventIds: string[] = events.map((m) => m.eventId).filter((id): id is string => !!id);
        return this.conversationApiService.getMultiEvents({ eventIds, conversationId: conversationId }).pipe(
          map((res) => res.events || new Array<Event>()),
          catchError((error) => {
            console.error('error to call getMultiEvents', error);
            return of(new Array<Event>());
          }),
        );
      }),
    );
  }

  getUnreadConversationsCount(viewId: string): Observable<number> {
    return this.currentParticipant$.pipe(
      switchMap((participant) =>
        this.countUnreadConversations(
          this.firestoreService.getUnreadConversationsByView(participant?.participantId, viewId),
        ),
      ),
    );
  }

  countUnreadConversations(conversationsCollection: Query<FirestoreConversation>): Observable<number> {
    return collectionData(conversationsCollection, { idField: 'id' }).pipe(
      map((cs) => cs.map((c) => c.conversationId).filter((id): id is string => !!id)),
      switchMap((ids) => of(ids.length)),
    );
  }

  removeLastConversationId(): void {
    localStorage.removeItem(LOCAL_STORAGE_ID);
    this.currentFirestoreConversationId$$.next('');
  }

  conversationSelected(selected: boolean): void {
    this.conversationSelected$$.next(selected);
  }

  updateButtonClickState(messageId: string, buttonId: string, clickedAt: Date): void {
    this.buttonClickStates.update((currentStates) => ({
      ...currentStates,
      [messageId]: {
        ...currentStates[messageId],
        [buttonId]: clickedAt,
      },
    }));
  }

  private readonly messagesScrollable$: Observable<{
    messages: ConversationMessage[];
    events: Event[];
    loading: boolean;
  }> = this.currentFirestoreConversationId$.pipe(
    observeOn(asyncScheduler),
    switchMap((docId) => {
      if (!docId) {
        return of({
          messages: [],
          events: [],
          loading: false,
        });
      }

      const conversationId = fromFirestoreId(docId);
      const conversationMessages$ = combineLatest([
        combineLatest([this.currentConversationDetail$, this.firestoreService.messages$(docId)]).pipe(
          switchMap(([conversationDetail, messages]) => {
            if (conversationDetail?.conversation?.conversationId !== conversationId) {
              return EMPTY;
            }

            const messageIds: string[] = messages.map((m) => m.messageId).filter((id): id is string => !!id);
            return this.conversationApiService.getMultiMessages({ messageIds, conversationId }).pipe(
              map((res) =>
                (res.messages || new Array<Message>())
                  .filter((msg) => msg && msg.messageId)
                  .map((msg) =>
                    convertMessageIntoConversationMessage(
                      msg,
                      conversationDetail.participants.find((p) => p.participantId === msg.participantId),
                    ),
                  )
                  .filter((msg): msg is ConversationMessage => {
                    return Boolean(msg && (msg.sender || msg.type === MessageType.MESSAGE_TYPE_SYSTEM));
                  }),
              ),
              catchError((error) => {
                console.error('error to call getMultiMessages', error);
                return of(new Array<ConversationMessage>());
              }),
            );
          }),
        ),
        this.unsavedMessages$$,
      ]).pipe(
        map(([messages, unsavedMessages]) => {
          const unsavedMessagesForDocId = unsavedMessages[docId] || [];
          const filterSavedMessages = unsavedMessagesForDocId.filter(
            (msg) => !messages.find((m) => m.messageId === msg.messageId),
          );
          return [...filterSavedMessages, ...messages];
        }),
      );

      return combineLatest([conversationMessages$, this.getConversationEvents(docId)]).pipe(
        map(([messages, events]) => ({
          messages,
          events,
          loading: false,
        })),
        startWith({ messages: [], events: [], loading: true }),
      );
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  // Original messages observable (kept for backward compatibility) will refactor it later
  public readonly messages$ = this.messagesScrollable$.pipe(map((res) => res.messages));

  private readonly baseMessagesSignal = toSignal(this.messages$, { initialValue: [] as ConversationMessage[] });

  public readonly messagesSignal = computed(() => {
    const baseMessages = this.baseMessagesSignal();
    const buttonClickStates = this.buttonClickStates();

    return this.applyButtonClickStates(baseMessages, buttonClickStates);
  });

  /**
   * Applies button click states to a list of messages
   * @param messages - The base messages to apply button states to
   * @param buttonClickStates - The button click state mapping
   * @returns Messages with updated button click states
   */
  private applyButtonClickStates(
    messages: ConversationMessage[],
    buttonClickStates: { [messageId: string]: { [buttonId: string]: Date } },
  ): ConversationMessage[] {
    return messages.map((message) => {
      if (!message.UIComponents || !message.messageId) return message;

      const messageClickStates = buttonClickStates[message.messageId];
      if (!messageClickStates) return message;

      const updatedUIComponents = message.UIComponents.map((component) => {
        if (!component.button?.id) return component;

        const clickedAt = messageClickStates[component.button.id];
        if (clickedAt) {
          return {
            ...component,
            button: {
              ...component.button,
              clickedAt,
            },
          };
        }
        return component;
      });

      return {
        ...message,
        UIComponents: updatedUIComponents,
      };
    });
  }
  public readonly events$ = this.messagesScrollable$.pipe(map((res) => res.events));
  public readonly loadingMessages$ = this.messagesScrollable$.pipe(map((res) => res.loading));

  private readonly refetchAvailableChannels$ = this.messages$.pipe(
    distinctUntilChanged((prev, curr) => prev[0]?.id === curr[0]?.id),
    filter((messages) => messages.length > 0),
    map((messages) => messages[0]),
    filter(
      (message) =>
        (message.channel === ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP ||
          message.channel === ConversationChannel.CONVERSATION_CHANNEL_FACEBOOK ||
          message.channel === ConversationChannel.CONVERSATION_CHANNEL_INSTAGRAM) &&
        message.sender?.participantType === pType.PARTICIPANT_TYPE_CUSTOMER,
    ),
    map(() => null),
    startWith(null),
  );

  private readonly availableChannelsForConversation$: Observable<GetAvailableChannelsForConversationResponse | null> =
    combineLatest([this.currentFirestoreConversationId$, this.refetchAvailableChannels$]).pipe(
      filter(([conversationID]) => !!conversationID),
      switchMap(([conversationID]) => {
        return this.conversationApiService
          .getAvailableChannelsForConversation({
            conversationId: fromFirestoreId(conversationID),
          })
          .pipe(catchError(() => of(null)));
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
      distinctUntilChanged(),
    );

  private readonly filteredChannels$: Observable<ConversationChannel[]> = this.availableChannelsForConversation$.pipe(
    switchMap((availableChannelsForConversation) => {
      if (!availableChannelsForConversation) {
        return of(
          new GetAvailableChannelsForConversationResponse({
            channels: new Array<ConversationChannel>(),
            preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
          }),
        );
      }
      return of(availableChannelsForConversation);
    }),
    map((res) => res?.channels ?? new Array<ConversationChannel>()),
    switchMap((channels) => {
      if (this.inboxService.isPartnerCenter) {
        channels = channels.filter((channel) => channel !== ConversationChannel.CONVERSATION_CHANNEL_EMAIL);
      }

      if (this.hostAppInterface.getAppOptions().is_multilocation) {
        return this.currentConvoSMSNumber$.pipe(
          map((convoSMSNumber) => {
            if (convoSMSNumber) {
              return channels;
            } else {
              return channels.filter((channel) => channel !== ConversationChannel.CONVERSATION_CHANNEL_SMS);
            }
          }),
        );
      }

      // TODO: MEGA-791 - Remove this once SMS registration state check is implemented on backend
      return this.inboxService.isSMSChannelAvailableForOrg$.pipe(
        map((isSMSChannelAvailable) => {
          return isSMSChannelAvailable
            ? channels
            : channels.filter((channel) => channel !== ConversationChannel.CONVERSATION_CHANNEL_SMS);
        }),
      );
    }),
    catchError(() => of(new Array<ConversationChannel>())),
    distinctUntilChanged((prev, curr) => {
      if (prev.length === 0 && curr.length === 0) {
        return true;
      }
      return curr === prev;
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  //TODO: MEGA-791 Remove the filteredChannels dependency once we migrate the access control logic to the backend.
  //The entire channel availability will be in the backend.
  readonly availableChannels$: Observable<ConversationAvailableChannels> = combineLatest([
    this.availableChannelsForConversation$,
    this.filteredChannels$,
  ]).pipe(
    map(([availableChannelsForConversation, filteredAvailableChannels]): ConversationAvailableChannels => {
      if (
        !availableChannelsForConversation ||
        availableChannelsForConversation?.preferredChannel === ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED
      ) {
        return {
          preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
          channelAvailabilities: availableChannelsForConversation?.channelAvailabilities ?? [],
          availableChannels: filteredAvailableChannels,
        };
      }
      if (filteredAvailableChannels.includes(availableChannelsForConversation?.preferredChannel)) {
        return {
          preferredChannel: availableChannelsForConversation.preferredChannel,
          channelAvailabilities: availableChannelsForConversation?.channelAvailabilities ?? [],
          availableChannels: filteredAvailableChannels,
        };
      }
      return {
        preferredChannel: filteredAvailableChannels[0],
        channelAvailabilities: availableChannelsForConversation?.channelAvailabilities ?? [],
        availableChannels: filteredAvailableChannels,
      };
    }),
    distinctUntilChanged(),
  );

  displayAlertIfExists(
    conversationDetail: ConversationDetail | null,
    conversationChannel: ConversationChannel,
  ): Observable<AlertOptions | null> {
    const service = this.conversationChannelServices[conversationChannel];
    if (!service) return of(null);
    if (!conversationDetail) return of(null);

    return combineLatest([this.partnerId$, this.accountGroupId$, this.availableChannels$]).pipe(
      switchMap(([partnerId, accountGroupId, channels]) => {
        if (channels.availableChannels?.length > 0) return of(null);
        return service.getChatComposerAlert(
          partnerId,
          accountGroupId,
          conversationDetail ?? undefined,
          channels.availableChannels,
          channels.channelAvailabilities,
        );
      }),
    );
  }

  private readonly unsavedMessages$$ = new BehaviorSubject<{ [docId: string]: ConversationMessage[] }>({});
  private readonly buttonClickStates = signal<{ [messageId: string]: { [buttonId: string]: Date } }>({});

  private addUnsavedMessage(docId: string, message: ConversationMessage) {
    const unsavedMessages = this.unsavedMessages$$.getValue();
    const unsavedMessagesForDocId = unsavedMessages[docId] || [];
    this.unsavedMessages$$.next({
      ...unsavedMessages,
      [docId]: [message, ...unsavedMessagesForDocId],
    });
  }

  /**
   * evaluate the helpfulness of a response
   * @param {string} messageId - the id of the message response to evaluate
   * @param {string} eventId - the id of the event response to evaluate
   * @param {string} conversationId - the id of the conversation
   * @param {string} comment - a comment about the helpfulness of the response
   * @param {EvaluationSentiment} sentiment - the sentiment of the evaluation
   */

  // for event evaluation
  async evaluateResponse(
    comment: string,
    sentiment: EvaluationSentiment,
    messageId: string,
    eventId: string,
    conversationId: string,
  ): Promise<void>;

  // for message evaluation
  async evaluateResponse(comment: string, sentiment: EvaluationSentiment, messageId: string): Promise<void>;

  async evaluateResponse(
    comment: string,
    sentiment: EvaluationSentiment,
    messageId?: string,
    eventId?: string,
    conversationId?: string,
  ): Promise<void> {
    let req: EvaluateResponseRequestInterface;
    if (messageId) {
      req = {
        evaluatedMessageId: messageId,
        comment: comment,
        sentiment: sentiment,
      };
    } else {
      req = {
        evaluatedEventId: eventId,
        conversationId: conversationId,
        comment: comment,
        sentiment: sentiment,
      };
    }
    await firstValueFrom(this.conversationApiService.evaluateResponse(req));
  }
}
