import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { collectionData, limit, Query, query } from '@angular/fire/firestore';
import {
  asyncScheduler,
  BehaviorSubject,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  merge,
  Observable,
  observeOn,
  of,
  shareReplay,
  skip,
  startWith,
  Subject,
  switchMap,
  take,
} from 'rxjs';
import { ConversationFilters } from '../conversation-filters';
import { FirestoreService } from '../firestore.service';
import { FOLLOWING_VIEW_ID } from '../inbox.constants';
import { InboxService } from '../inbox.service';
import { ConversationDetail, FirestoreConversation } from '../interface/conversation.interface';
import { ParticipantService } from '../participant.service';
import { CONVERSATION_PLATFORM_LOCATION_TOKEN, GROUP_ID_TOKEN, USER_ID_TOKEN } from '../tokens';
import { ViewService } from '../view.service';
import { ConversationStatelessService } from '../conversation-stateless.service';
import { AiAssistantConversationService } from './ai-assistant-conversation.service';

@Injectable()
export class ConversationListService {
  private readonly participantService = inject(ParticipantService);
  private readonly inboxService = inject(InboxService);
  private readonly firestoreService = inject(FirestoreService);
  private readonly conversationStatelessService = inject(ConversationStatelessService);
  private readonly aiAssistantConversationService = inject(AiAssistantConversationService);
  private readonly groupId$ = inject(GROUP_ID_TOKEN);
  private userId$ = inject(USER_ID_TOKEN);
  private readonly aiAssistantId$ = this.aiAssistantConversationService.aiAssistantId$;

  private readonly filters$$ = new BehaviorSubject<ConversationFilters | null>(null);
  private readonly loadMoreConversations$$ = new Subject<void>();
  private readonly filterTerm$$ = new BehaviorSubject<string>('');

  private readonly platformLocation = inject(CONVERSATION_PLATFORM_LOCATION_TOKEN);

  private readonly conversationsScrollable$ = combineLatest([
    this.participantService.currentParticipant$,
    this.participantService.organizationId$,
    inject(ViewService).selectedViewId$,
    this.inboxService.showInboxViews$,
    this.filterTerm$$,
    this.filters$$,
    this.groupId$,
    this.userId$,
    this.aiAssistantId$,
  ]).pipe(
    observeOn(asyncScheduler),
    switchMap(
      ([
        currentParticipant,
        organizationId,
        viewId,
        showViews,
        filterTerm,
        filters,
        groupId,
        userId,
        aiAssistantId,
      ]) => {
        let pageSize = !filterTerm ? 20 : 200; // Get 200 results for a better chance of getting search results
        let conversations: ConversationDetail[] | null = []; // required to not trigger empty when scrolling
        if (!currentParticipant) {
          return of({
            conversations,
            loading: false,
            empty: true,
          });
        }

        return this.loadMoreConversations$$.pipe(
          startWith(null),
          switchMap(() => {
            let q;
            if (viewId === FOLLOWING_VIEW_ID && showViews) {
              q = this.firestoreService.getViewsConversationsQuery(currentParticipant.participantId, viewId);
            } else if (filters?.isPrivate) {
              if (aiAssistantId) {
                q = this.firestoreService.loadUserRelatedAiAssistantConversationsQuery(userId, this.platformLocation);
              } else {
                q = this.firestoreService.loadUserRelatedConversationsQuery(userId);
              }
            } else {
              q = this.firestoreService.setupBaseConversationsQuery(organizationId, filters, groupId);
            }
            q = query(q, limit(pageSize));
            pageSize += 40;
            return this.fetchConversations(q).pipe(
              map((newConversations) => {
                conversations = newConversations;
                return {
                  conversations: this.filterConversations(conversations, filterTerm, aiAssistantId ?? undefined),
                  loading: false,
                  empty: conversations.length === 0,
                };
              }),
              startWith({ conversations, loading: true, empty: null }),
            );
          }),
        );
      },
    ),
    takeUntilDestroyed(),
    shareReplay(1),
  );
  readonly conversations$: Observable<ConversationDetail[] | null> = this.conversationsScrollable$.pipe(
    map((scrollable) => scrollable.conversations),
    distinctUntilChanged(),
  );
  readonly loadingConversations$: Observable<boolean> = this.conversationsScrollable$.pipe(
    map((scrollable) => scrollable.loading),
    distinctUntilChanged(),
  );
  readonly emptyConversations$: Observable<boolean | null> = this.conversationsScrollable$.pipe(
    map((scrollable) => scrollable.empty),
    filter((empty) => empty !== null),
    distinctUntilChanged(),
  );

  readonly isEmptyConversations$ = combineLatest([this.conversations$, this.loadingConversations$]).pipe(
    map(([conversations, loading]) => !loading && (!conversations || conversations.length === 0)),
    distinctUntilChanged(),
  );

  // We use a realtime query for this instead of a count so that when the first message for an organization is sent, the
  // conversation list appears.
  readonly organizationHasConversations$ = combineLatest([this.participantService.organizationId$, this.groupId$]).pipe(
    switchMap(([organizationId, groupId]) => {
      let q = this.firestoreService.setupBaseConversationsQuery(organizationId, null, groupId, true, true);
      q = query(q, limit(1));
      return collectionData(q);
    }),
    map((res) => res.length > 0),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  readonly organizationHasClosedConversations$ = this.participantService.organizationId$.pipe(
    switchMap((id) => {
      const filters: ConversationFilters = { isOpen: false, isAnonymous: false, isPrivate: false };
      let q = this.firestoreService.setupBaseConversationsQuery(id, filters, null, true, true);
      q = query(q, limit(1));
      return collectionData(q);
    }),
    map((res) => res.length > 0),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  loadMoreConversations(): void {
    this.loadMoreConversations$$.next();
  }

  fetchConversations(conversationsCollection: Query<FirestoreConversation>): Observable<ConversationDetail[]> {
    const query = collectionData(conversationsCollection, { idField: 'id' }).pipe(
      map((cs) => cs.map((c) => c.conversationId).filter((id): id is string => !!id)),
      distinctUntilChanged(),
    );

    const exclusions = ['conversation_summary', 'message.metadata'];

    // It sometimes fire twice with first one wrong.
    // wait a bit so the second(correct) emit arrives
    // TODO: remove once issue fixed in rxfire: https://github.com/FirebaseExtended/rxfire/issues/105
    const first$ = query.pipe(
      take(2),
      debounceTime(400),
      switchMap((ids) => this.conversationStatelessService.getMultiConversationDetails(ids, exclusions)),
    );

    const second$ = query.pipe(
      skip(2),
      switchMap((ids) => this.conversationStatelessService.getMultiConversationDetails(ids, exclusions)),
    );

    return merge(first$, second$);
  }

  setFilterTerm(term: string): void {
    this.filterTerm$$.next(term);
  }

  private filterConversations(options: ConversationDetail[], term: string, assistantId?: string): ConversationDetail[] {
    term = term?.toUpperCase();
    if (!options) {
      return [];
    }

    if (!term && !assistantId) {
      return options;
    }

    return options.filter((conversationDetail) => {
      const participantsFiltered = conversationDetail?.participants?.filter((participant) => {
        if (assistantId) {
          return participant?.internalParticipantId === assistantId;
        }
        return (
          participant?.name?.toLocaleUpperCase().includes(term) ||
          participant?.phoneNumber?.toUpperCase().includes(term)
        );
      });
      if (participantsFiltered && participantsFiltered.length > 0) {
        return conversationDetail;
      }
    });
  }

  updateFilters(filters: ConversationFilters | null): void {
    this.filters$$.next(filters);
  }
}
