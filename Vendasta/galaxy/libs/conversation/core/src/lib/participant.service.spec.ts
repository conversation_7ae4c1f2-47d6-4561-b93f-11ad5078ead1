import { Router } from '@angular/router';
import { FeatureFlagService } from '@galaxy/partner';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator/jest';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import {
  ConversationApiService,
  GetMultiParticipantsResponse,
  GetParticipantsByKeyRequest,
  GetParticipantsByKeyResponse,
  GlobalParticipantType,
  NamespaceDetail,
  Participant,
  ParticipantKey,
  ParticipantType,
} from '@vendasta/conversation';
import { CreateCrmObjectResponse, CRMApiService } from '@vendasta/crm';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { JestScheduler } from '@vendasta/rx-utils';
import { of } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { InboxContact } from './interface/inbox.interface';
import { snackbarServiceMock } from './mocks';
import { ParticipantService } from './participant.service';
import { ACCOUNT_GROUP_ID_TOKEN, GROUP_ID_TOKEN, PARTNER_ID_TOKEN, USER_ID_TOKEN } from './tokens';
import { INBOX_CRM_SOURCE_NAME, STANDARD_CRM_FIELD_EXTERNAL_IDS } from './inbox.constants';
import { createCrmField } from './participant-utils';
import { ProductAnalyticsService } from '@galaxy/partner';

describe('ParticipantService', () => {
  let spectator: SpectatorService<ParticipantService>;
  const createService = createServiceFactory({
    service: ParticipantService,
    imports: [HttpClientModule],
    providers: [
      { provide: USER_ID_TOKEN, useValue: of('user-123') },
      { provide: PARTNER_ID_TOKEN, useValue: of('VUNI') },
      { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('AG-123') },
      { provide: GROUP_ID_TOKEN, useValue: of('') },
      { provide: SnackbarService, useValue: snackbarServiceMock },
      { provide: CRMApiService, useValue: {} },
      { provide: ProductAnalyticsService, useValue: {} },
      { provide: HttpClient, useValue: {} },
    ],
    mocks: [ConversationApiService, CRMApiService, FeatureFlagService, Router, SnackbarService],
  });
  const partnerId = 'PID';
  const accountGroupId = 'AG-123';

  beforeEach(() => (spectator = createService()));

  describe('buildAccountGroup', () => {
    it('should return account group participant object with correct properties', () => {
      const participant = spectator.service.buildAccountGroup(partnerId, accountGroupId);
      expect(participant).toEqual({
        accountGroupId: 'AG-123',
        partnerId: 'PID',
        participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
        internalParticipantId: '',
        isSubjectParticipant: true, // if AG is an organization
      } as Participant);
    });
  });

  describe('buildPartner', () => {
    it('should return partner participant object with correct properties', () => {
      const participant = spectator.service.buildPartner(partnerId);
      expect(participant).toEqual({
        partnerId: 'PID',
        participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
        internalParticipantId: '',
        isSubjectParticipant: true,
      } as Participant);
    });
  });

  describe('getMultiParticipants', () => {
    it('should show an error on console if entry is not valid', (done) => {
      spectator.service.getMultiParticipants([], 'CONVERSATION-123').subscribe((res) => {
        expect(res.participants.length).toBe(0);
        done();
      });
    });

    it('the getMultiParticipants from conversationApiService should be called when a valid entry is provided', (done) => {
      const conversationApiService = spectator.inject(ConversationApiService);
      const resp = {
        participants: [{ participantId: 'p-123' } as Participant],
      } as GetMultiParticipantsResponse;
      conversationApiService.getMultiParticipants.mockReturnValue(of(resp));
      spectator.service.getMultiParticipants(['p-123'], 'CONVERSATION-123').subscribe((resp) => {
        expect(resp.participants).toEqual([{ participantId: 'p-123' } as Participant]);
        done();
      });
    });
  });

  describe('searchContacts', () => {
    it('should called the getContactsFromCrmService if searchTerm > 2', (done) => {
      spectator.service['getContactsFromCrmService'] = jest
        .fn()
        .mockReturnValue(of([{ contactId: '123' } as InboxContact]));
      spectator.service.searchContacts('ven', 'AG-123');
      expect(spectator.service['getContactsFromCrmService']).toHaveBeenCalledWith('AG-123', 'ven');
      spectator.service.contacts$.subscribe((res) => {
        expect(res.length).toBe(1);
        done();
      });
    });
  });

  describe('addContact', () => {
    const contact = {
      firstName: 'john',
      accountGroupId: 'AG-123',
    } as InboxContact;
    it('should call createCrmContact', (done) => {
      spectator.service['createCrmContact'] = jest
        .fn()
        .mockReturnValue(of({ contactId: 'CONTACT-123', firstName: 'john' } as InboxContact));
      spectator.service.addContact(contact).subscribe((res) => {
        expect(res).toEqual({ contactId: 'CONTACT-123', firstName: 'john' } as InboxContact);
        done();
      });
      expect(spectator.service['createCrmContact']).toHaveBeenCalledWith(contact);
    });
    it('should include INBOX_CRM_SOURCE_NAME field', (done) => {
      const crmApiService = spectator.inject(CRMApiService);
      const createContact = jest
        .spyOn(crmApiService, 'createContact')
        .mockReturnValue(of({ contactId: 'CONTACT-123' } as unknown as CreateCrmObjectResponse));

      spectator.service.addContact(contact).subscribe(() => {
        expect(createContact).toHaveBeenCalledWith({
          namespace: 'AG-123',
          crmObject: {
            fields: [
              createCrmField(STANDARD_CRM_FIELD_EXTERNAL_IDS.firstName, 'john'),
              createCrmField(STANDARD_CRM_FIELD_EXTERNAL_IDS.source, INBOX_CRM_SOURCE_NAME),
            ],
          },
        });
        done();
      });
    });
  });

  describe('currentParticipant$', () => {
    let sched: TestScheduler;
    beforeEach(() => {
      sched = new JestScheduler();
    });
    it('should return account group participant object with correct properties', () => {
      const currentParticipant = {
        participantId: 'PARTICIPANT-123',
        internalParticipantId: 'IAM-123',
        name: 'Test IAM',
        email: '',
        phoneNumber: '**********',
        participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
        accountGroupId: 'AG-D2MFHDH4LQ',
      } as Participant;
      const conversationApiService = spectator.inject(ConversationApiService);
      conversationApiService.getParticipantsByKey.mockReturnValue(
        of({
          participant: currentParticipant,
          participants: [currentParticipant],
        } as GetParticipantsByKeyResponse),
      );
      sched.run(({ expectObservable }) => {
        spectator.service.getParticipantsByKey({} as ParticipantKey).subscribe();
        expectObservable(spectator.service.currentParticipant$).toBe('(x|', {
          x: currentParticipant,
        });
      });
    });
    it('should return account group participant object with null profile picture if it has the vendasta default', () => {
      const currentParticipant = {
        participantId: 'PARTICIPANT-123',
        internalParticipantId: 'IAM-123',
        name: 'Test IAM',
        email: '',
        phoneNumber: '**********',
        participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
        accountGroupId: 'AG-D2MFHDH4LQ',
        profileImageUrl: null,
        partnerId: 'VUNI',
        location: '',
        channel: ParticipantType.PARTICIPANT_TYPE_UNDEFINED,
        externalParticipantId: '',
        isSubjectParticipant: false,
        namespaceHierarchy: [],
      } as Participant;
      const participantResponse = { ...currentParticipant } as Participant;
      const conversationApiService = spectator.inject(ConversationApiService);
      conversationApiService.getParticipantsByKey.mockReturnValue(
        of({
          participant: participantResponse,
          participants: [participantResponse],
        } as GetParticipantsByKeyResponse),
      );
      sched.run(({ expectObservable }) => {
        spectator.service.getParticipantsByKey({} as ParticipantKey).subscribe();
        expectObservable(spectator.service.currentParticipant$).toBe('(x|', {
          x: currentParticipant,
        });
      });
    });
    it('should return participant in the accountGroup namespace with correct properties ', () => {
      const namespaceHierarchy = [
        new NamespaceDetail({
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
          internalParticipantId: 'VUNI',
        }),
        new NamespaceDetail({
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
          internalParticipantId: 'AG-D2MFHDH4LQ',
        }),
      ];
      const currentParticipant = {
        participantId: 'PARTICIPANT-123',
        internalParticipantId: 'IAM-123',
        name: 'Test IAM',
        email: '',
        participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
        accountGroupId: 'AG-D2MFHDH4LQ',
        namespaceHierarchy: namespaceHierarchy,
      } as unknown as Participant;
      const conversationApiService = spectator.inject(ConversationApiService);
      conversationApiService.getParticipantsByKey.mockReturnValue(
        of({
          participant: currentParticipant,
          participants: [currentParticipant],
        } as GetParticipantsByKeyResponse),
      );
      sched.run(({ expectObservable }) => {
        spectator.service
          .getParticipant({
            userId: 'IAM-123',
            partnerId: 'VUNI',
            accountGroupId: 'AG-D2MFHDH4LQ',
          })
          .subscribe();
        expectObservable(spectator.service.currentParticipant$).toBe('(x|', {
          x: currentParticipant,
        });

        const req = {
          participantKey: {
            participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
            internalParticipantId: 'IAM-123',
            partnerId: 'VUNI',
            accountGroupId: 'AG-D2MFHDH4LQ',
            namespaceHierarchy: namespaceHierarchy,
          } as unknown as ParticipantKey,
        } as GetParticipantsByKeyRequest;

        expect(conversationApiService.getParticipantsByKey).toBeCalledWith(req);
      });
    });
    it('should return participant in the group namespace with correct properties ', () => {
      const namespaceHierarchy = [
        new NamespaceDetail({
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
          internalParticipantId: 'VUNI',
        }),
        new NamespaceDetail({
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_GROUP,
          internalParticipantId: 'G-123',
        }),
      ];
      const currentParticipant = {
        participantId: 'PARTICIPANT-123',
        internalParticipantId: 'IAM-123',
        name: 'Test IAM',
        email: '',
        participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
        namespaceHierarchy: namespaceHierarchy,
      } as unknown as Participant;
      const conversationApiService = spectator.inject(ConversationApiService);
      conversationApiService.getParticipantsByKey.mockReturnValue(
        of({
          participant: currentParticipant,
          participants: [currentParticipant],
        } as GetParticipantsByKeyResponse),
      );
      sched.run(({ expectObservable }) => {
        spectator.service
          .getParticipant({
            userId: 'IAM-123',
            partnerId: 'VUNI',
            accountGroupId: '',
            groupId: 'G-123',
          })
          .subscribe();
        expectObservable(spectator.service.currentParticipant$).toBe('(x|', {
          x: currentParticipant,
        });

        const req = {
          participantKey: {
            participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
            internalParticipantId: 'IAM-123',
            partnerId: 'VUNI',
            namespaceHierarchy: namespaceHierarchy,
            accountGroupId: '',
          } as unknown as ParticipantKey,
        } as GetParticipantsByKeyRequest;

        expect(conversationApiService.getParticipantsByKey).toBeCalledWith(req);
      });
    });
    it('should return participant in the partner namespace with correct properties ', () => {
      const namespaceHierarchy = [
        new NamespaceDetail({
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
          internalParticipantId: 'VUNI',
        }),
      ];
      const currentParticipant = {
        participantId: 'PARTICIPANT-123',
        internalParticipantId: 'IAM-123',
        name: 'Test IAM',
        email: '',
        participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
        namespaceHierarchy: namespaceHierarchy,
      } as unknown as Participant;
      const conversationApiService = spectator.inject(ConversationApiService);
      conversationApiService.getParticipantsByKey.mockReturnValue(
        of({
          participant: currentParticipant,
          participants: [currentParticipant],
        } as GetParticipantsByKeyResponse),
      );
      sched.run(({ expectObservable }) => {
        spectator.service.getParticipant({ userId: 'IAM-123', partnerId: 'VUNI', accountGroupId: '' }).subscribe();
        expectObservable(spectator.service.currentParticipant$).toBe('(x|', {
          x: currentParticipant,
        });

        const req = {
          participantKey: {
            participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
            internalParticipantId: 'IAM-123',
            partnerId: 'VUNI',
            namespaceHierarchy: namespaceHierarchy,
            accountGroupId: '',
          } as unknown as ParticipantKey,
        } as GetParticipantsByKeyRequest;

        expect(conversationApiService.getParticipantsByKey).toBeCalledWith(req);
      });
    });
  });
});
