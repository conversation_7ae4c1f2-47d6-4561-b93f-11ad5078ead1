import { Inject, Injectable } from '@angular/core';
import { ListingsApiService } from '@vendasta/customer-voice-service';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { FACEBOOK_SOURCE_ID, GOOGLE_SOURCE_ID } from './inbox.constants';
import { ACCOUNT_GROUP_ID_TOKEN } from './tokens';

export interface ReviewLink {
  sourceName: string;
  reviewUrl: string;
}

@Injectable()
export class ListingService {
  private currentAccountGroupId: string;

  constructor(
    private listingsApiService: ListingsApiService,
    @Inject(ACCOUNT_GROUP_ID_TOKEN) readonly accountGroupId$: Observable<string>,
  ) {
    this.accountGroupId$.pipe(takeUntilDestroyed()).subscribe((accountGroupId) => {
      this.currentAccountGroupId = accountGroupId;
    });
  }

  public get reviewLinks$(): Observable<ReviewLink[]> {
    return this.listingsApiService.getReviewSourceSettings({ accountGroupId: this.currentAccountGroupId }).pipe(
      map((response) => {
        return response.reviewSources
          .filter((result) => result.url != null || result.overriddenUrl != null)
          .sort((a, b) => {
            if (
              a.sourceId == GOOGLE_SOURCE_ID ||
              a.sourceId == FACEBOOK_SOURCE_ID ||
              (a.sourceId == GOOGLE_SOURCE_ID && b.sourceId == FACEBOOK_SOURCE_ID)
            ) {
              return -1;
            }
            return 1;
          })
          .map((result) => {
            if (result.overriddenUrl) {
              return {
                sourceName: result.displayName,
                reviewUrl: result.overriddenUrl,
              } as ReviewLink;
            }
            return {
              sourceName: result.displayName,
              reviewUrl: result.url,
            } as ReviewLink;
          });
      }),
    );
  }
}
