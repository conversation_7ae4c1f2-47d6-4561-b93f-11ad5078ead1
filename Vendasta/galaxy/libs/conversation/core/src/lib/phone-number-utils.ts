import { parsePhoneNumberWithError } from 'libphonenumber-js';

export function formatPhoneNumber(value: string): string {
  const nonNumericPattern = /[^\d\s()+-]/;
  if (nonNumericPattern.test(value)) {
    return value;
  }

  try {
    const phoneNumber = parsePhoneNumberWithError(value, 'US');
    return `+${phoneNumber.countryCallingCode} ${phoneNumber.formatNational()}`;
  } catch (error) {
    return value;
  }
}
