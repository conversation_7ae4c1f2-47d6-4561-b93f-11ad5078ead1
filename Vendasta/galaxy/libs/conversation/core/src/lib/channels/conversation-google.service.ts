import { HttpErrorResponse, HttpStatusCode } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BusinessCommunicationsApiService } from '@vendasta/google-my-business';
import { addDays, isBefore } from 'date-fns';
import { combineLatest, Observable, of, catchError, map, switchMap } from 'rxjs';
import { ConversationDetail } from '../interface/conversation.interface';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_GOOGLE_BUSINESS_MESSAGES_AVAILABLE_TOKEN,
  PARTNER_ID_TOKEN,
} from '../tokens';
import { AlertOptions, ConversationChannelService } from './conversation-channel.abstract';

export enum BusinessMessagesEnabledState {
  IS_ENABLED = 'IS_ENABLED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  NOT_ENABLED = 'NOT_ENABLED',
  NOT_LAUNCHED = 'NOT_LAUNCHED',
  CONVERSATION_EXPIRED = 'CONVERSATION_EXPIRED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

@Injectable()
export class ConversationGoogleService extends ConversationChannelService {
  private readonly partnerId$: Observable<string> = inject(PARTNER_ID_TOKEN);
  readonly accountGroupId$: Observable<string> = inject(ACCOUNT_GROUP_ID_TOKEN);

  private router = inject(Router);
  private readonly googleMyBusinessService = inject(BusinessCommunicationsApiService);
  private readonly googleBusinessMessagesAvailable$ = inject(CONVERSATION_GOOGLE_BUSINESS_MESSAGES_AVAILABLE_TOKEN);

  getChatComposerAlert(
    partnerId: string,
    accountGroupId: string,
    conversationDetail: ConversationDetail,
  ): Observable<AlertOptions | null> {
    return this.canSendGoogleBusinessMessage(partnerId, accountGroupId, conversationDetail).pipe(
      map((canSend) => {
        if (canSend === BusinessMessagesEnabledState.IS_ENABLED) {
          return null;
        }

        return this.getGoogleBusinessMessagesError(canSend, accountGroupId);
      }),
    );
  }

  private getGoogleBusinessMessagesError(
    enabledState: BusinessMessagesEnabledState,
    accountGroup: string,
  ): AlertOptions {
    let alert: AlertOptions;

    switch (enabledState) {
      case BusinessMessagesEnabledState.NOT_ENABLED:
        alert = {
          title: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.STATUS.NOT_ENABLED.TITLE',
          description: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.STATUS.NOT_ENABLED.DESCRIPTION',
        };
        break;
      case BusinessMessagesEnabledState.INVALID_TOKEN:
        alert = {
          title: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.GOOGLE_BUSINESS_PROFILE',
          description: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.PROFILE_DISCONNECTED',
          action: {
            title: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.ACTIONS.GO_TO_SETTINGS',
            callback: () => this.router.navigate([`/account/location/${accountGroup}/settings/inbox`]),
          },
        };
        break;
      case BusinessMessagesEnabledState.CONVERSATION_EXPIRED:
        alert = {
          title: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.STATUS.EXPIRED_CONVERSATION.TITLE',
          description: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.STATUS.EXPIRED_CONVERSATION.DESCRIPTION',
        };
        break;
      case BusinessMessagesEnabledState.NOT_LAUNCHED:
        alert = {
          title: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.STATUS.NOT_LAUNCHED.TITLE',
          description: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.STATUS.NOT_LAUNCHED.DESCRIPTION',
          action: {
            title: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.ACTIONS.GO_TO_SETTINGS',
            callback: () => this.router.navigate([`/account/location/${accountGroup}/settings/inbox`]),
          },
        };
        break;
      case BusinessMessagesEnabledState.UNKNOWN_ERROR:
      default:
        alert = {
          title: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.STATUS.UNKNOWN_ERROR.TITLE',
          description: 'INBOX.SETTINGS.GOOGLE_BUSINESS_MESSAGES.STATUS.UNKNOWN_ERROR.DESCRIPTION',
        };
    }

    return alert;
  }

  private canSendGoogleBusinessMessage(
    partnerId: string,
    accountGroupId: string,
    conversationDetail?: ConversationDetail,
  ): Observable<BusinessMessagesEnabledState> {
    const expirationDate = addDays(conversationDetail?.message?.updated, 30);
    const currentDate = new Date();

    const launchStatus$: Observable<BusinessMessagesEnabledState> = this.googleMyBusinessService
      .getBusinessLaunchStatus({
        partnerId: partnerId,
        accountGroupId: accountGroupId,
      })
      .pipe(
        map((res) =>
          res.isLaunched ? BusinessMessagesEnabledState.IS_ENABLED : BusinessMessagesEnabledState.NOT_LAUNCHED,
        ),
        catchError((error) => {
          if (error instanceof HttpErrorResponse && error.status === HttpStatusCode.PreconditionFailed) {
            return of(BusinessMessagesEnabledState.INVALID_TOKEN);
          }
          return of(BusinessMessagesEnabledState.UNKNOWN_ERROR);
        }),
      );

    return combineLatest([this.googleBusinessMessagesAvailable$, launchStatus$]).pipe(
      map(([googleBusinessMessagesAvailable, launched]) => {
        if (isBefore(expirationDate, currentDate)) {
          return BusinessMessagesEnabledState.CONVERSATION_EXPIRED;
        }
        if (!googleBusinessMessagesAvailable) return BusinessMessagesEnabledState.NOT_ENABLED;
        return launched;
      }),
    );
  }

  readonly isGoogleBusinessMessagesEnabled$: Observable<boolean> = combineLatest([
    this.partnerId$,
    this.accountGroupId$,
  ]).pipe(
    switchMap(([partnerId, accountGroupId]) =>
      this.googleMyBusinessService.getBusinessLaunchStatus({ partnerId, accountGroupId }),
    ),
    map((res) => res.isLaunched),
    catchError(() => of(false)),
  );
}
