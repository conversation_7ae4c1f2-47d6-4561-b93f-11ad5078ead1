import { inject, Injectable } from '@angular/core';
import { FacebookApiService, WhatsAppTemplateStatus } from '@vendasta/facebook';
import { CRMApiService } from '@vendasta/crm';
import { combineLatest, map, Observable, of, shareReplay, switchMap } from 'rxjs';
import { ConversationChannel, ParticipantType, Status } from '@vendasta/conversation';
import { catchError } from 'rxjs/operators';
import { ACCOUNT_GROUP_ID_TOKEN, COMPANY_NAME_TOKEN, PARTNER_ID_TOKEN } from '../tokens';
import { ConversationService } from '../state/conversation.service';
import { StandardExternalIds } from '@galaxy/crm/static';
import { LanguageMap } from '@vendasta/galaxy/i18n';
import { InboxAlertService } from './inbox-alert.service';

@Injectable()
export class WhatsappTemplatesService {
  private facebookApiService = inject(FacebookApiService);
  private crmApiService = inject(CRMApiService);
  private conversationService = inject(ConversationService);
  private readonly partnerId$: Observable<string> = inject(PARTNER_ID_TOKEN);
  private readonly accountGroupId$: Observable<string> = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly companyName$: Observable<string> = inject(COMPANY_NAME_TOKEN);

  public readonly whatsappTemplatesOnly$ = this.conversationService.availableChannels$.pipe(
    map((availableChannels) => {
      const whatsappAvailability = availableChannels.channelAvailabilities.find(
        (channel) => channel.channel === ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP,
      );
      if (!whatsappAvailability?.isAvailable) {
        return false;
      }
      return whatsappAvailability.statuses?.some((status) => status.status === Status.RESTRICTED) ?? false;
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  private readonly allTemplates$ = combineLatest([this.partnerId$, this.accountGroupId$]).pipe(
    switchMap(([partnerId, accountGroupId]) => {
      return this.facebookApiService.getWhatsAppMessageTemplates({
        organizationId: accountGroupId || partnerId,
      });
    }),
    map((response) => response.templates),
    map((templates) => {
      return templates.filter((template) => {
        return template.name.startsWith('request_to_continue_conversation');
      });
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  public readonly approvedTemplates$ = this.allTemplates$.pipe(
    map((templates) => {
      return templates.filter((template) => template.status === WhatsAppTemplateStatus.TEMPLATE_STATUS_APPROVED);
    }),
  );

  public readonly templatesPendingApproval$ = this.allTemplates$.pipe(
    map((templates) => {
      const approvedTemplates = templates.filter(
        (template) => template.status === WhatsAppTemplateStatus.TEMPLATE_STATUS_APPROVED,
      );
      if (approvedTemplates.length > 0) {
        return false;
      }
      const pendingTemplates = templates.filter(
        (template) => template.status === WhatsAppTemplateStatus.TEMPLATE_STATUS_PENDING,
      );
      return pendingTemplates.length > 0;
    }),
  );

  private readonly customerFirstName$ = combineLatest([
    this.conversationService.currentConversationDetail$,
    this.accountGroupId$,
  ]).pipe(
    switchMap(([conversationDetail, accountGroupId]) => {
      const customerContactId = conversationDetail?.participants?.find(
        (participant) => participant.participantType === ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
      )?.internalParticipantId;
      if (!customerContactId) {
        return of(null);
      }
      return this.crmApiService
        .getMultiContact({
          namespace: accountGroupId,
          crmObjectIds: [customerContactId],
        })
        .pipe(catchError(() => of(null)));
    }),
    map((response) => {
      if (!response) return '';
      const firstName =
        response.crmObjects[0]?.fields?.find((field) => field.externalId === StandardExternalIds.FirstName)
          ?.stringValue ?? '';
      if (firstName === '-') {
        return '';
      }
      return firstName;
    }),
    catchError(() => of('')),
  );

  private readonly inboxAlertService = inject(InboxAlertService);

  public whatsappTemplate(languageCode$: Observable<string>) {
    return combineLatest([
      this.approvedTemplates$,
      this.companyName$,
      this.customerFirstName$,
      languageCode$,
      this.conversationService.currentConversationDetail$,
    ]).pipe(
      map(([templates, companyName, customerFirstName, languageCode, conversationDetail]) => {
        if (!templates) {
          return null;
        }
        const templateForLanguage = templates.find((template) => template.language === languageCode);
        if (!templateForLanguage) {
          return null;
        }
        return {
          id: templateForLanguage.id,
          body: templateForLanguage.body,
          bodyParameters: [
            {
              field: 1,
              text: customerFirstName,
              error:
                !customerFirstName && conversationDetail
                  ? this.inboxAlertService.getWhatsAppTemplateErrorMissingContactFirstName(conversationDetail)
                  : undefined,
            },
            {
              field: 2,
              text: companyName,
            },
          ],
        };
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  public hydratedWhatsappTemplate(languageCode$: Observable<string>) {
    return this.whatsappTemplate(languageCode$).pipe(
      map((template) => {
        if (!template) {
          return '';
        }
        let hydratedBody = template.body;

        template.bodyParameters?.forEach((parameter) => {
          const regex = new RegExp(`\\{\\{${parameter.field}\\}\\}`, 'g');
          hydratedBody = hydratedBody.replace(regex, parameter.text);
        });

        return hydratedBody;
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  public getTemplateLanguageLabel(languageCode: string): string {
    const matchedLanguage = LanguageMap.find((lang) => lang.code.split('-')[0] === languageCode);
    return matchedLanguage ? `${matchedLanguage.flag} ${matchedLanguage.label}` : languageCode;
  }
}
