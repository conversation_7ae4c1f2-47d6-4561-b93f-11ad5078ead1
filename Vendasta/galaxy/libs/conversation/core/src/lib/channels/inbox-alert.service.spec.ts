import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { InboxAlertService } from './inbox-alert.service';
import { Conversation, GlobalParticipantType, ParticipantType, SubjectParticipant } from '@vendasta/conversation';
import { CONVERSATION_HOST_APP_INTERFACE_TOKEN, CONVERSATION_ROUTES_TOKEN } from '../tokens';
import { of } from 'rxjs';
import { Router } from '@angular/router';
import { ConversationDetail } from '../interface/conversation.interface';
import { createServiceFactory, SpectatorService } from '@ngneat/spectator';

describe('InboxAlertService', () => {
  let spectator: SpectatorService<InboxAlertService>;

  const mockHostAppInterface = {
    viewContact: jest.fn(),
  };

  const createService = createServiceFactory({
    service: InboxAlertService,
    providers: [
      {
        provide: Router,
        useValue: { navigate: jest.fn() },
      },
      {
        provide: CONVERSATION_ROUTES_TOKEN,
        useValue: of({ useModal: false }),
      },
      {
        provide: CONVERSATION_HOST_APP_INTERFACE_TOKEN,
        useValue: mockHostAppInterface,
      },
      {
        provide: TranslateService,
        useValue: {
          instant: jest.fn((key: string, _interpolateParams?: unknown) => key),
        },
      },
    ],
    imports: [TranslateModule.forRoot()],
  });

  describe('checkNotAvailableErrorForLeads', () => {
    beforeEach(() => {
      spectator = createService();
    });

    const mockConversationDetail = {
      conversation: { conversationId: '123' },
      participants: [
        {
          participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
          phoneNumber: '',
          email: '',
          isSubjectParticipant: true,
        },
      ],
    } as ConversationDetail;

    it('should return null if no customer participant is found', () => {
      const result = spectator.service.checkNotAvailableErrorForLeads(
        {
          conversation: { conversationId: '123' },
        } as ConversationDetail,
        'US',
      );
      expect(result).toBeNull();
    });

    it('should return email-only alert when only email is available', () => {
      const translateService = spectator.inject(TranslateService);
      const conversationDetail = {
        ...mockConversationDetail,
        participants: [
          {
            ...mockConversationDetail.participants[0],
            email: '<EMAIL>',
          },
        ],
      } as ConversationDetail;

      const result = spectator.service.checkNotAvailableErrorForLeads(conversationDetail, 'US');
      expect(translateService.instant).toHaveBeenCalledWith('INBOX.CHAT.RESPOND_TO_LEAD_AT_EMAIL', {
        email: '<EMAIL>',
      });
      expect(result).toEqual({
        title: 'INBOX.CHAT.RESPOND_TO_LEAD_AT_EMAIL',
      });
    });

    it('should return phone-only alert when only phone is available', () => {
      const translateService = spectator.inject(TranslateService);
      const conversationDetail = {
        ...mockConversationDetail,
        participants: [
          {
            ...mockConversationDetail.participants[0],
            phoneNumber: '+12125551234',
          },
        ],
      } as ConversationDetail;

      const result = spectator.service.checkNotAvailableErrorForLeads(conversationDetail, 'US');

      expect(translateService.instant).toHaveBeenCalledWith('INBOX.CHAT.RESPOND_TO_LEAD_AT_PHONE_NUMBER', {
        phone: expect.any(String),
      });
      expect(result).toEqual({
        title: 'INBOX.CHAT.RESPOND_TO_LEAD_AT_PHONE_NUMBER',
      });
      expect(translateService.instant).toHaveBeenCalledWith('INBOX.CHAT.RESPOND_TO_LEAD_AT_PHONE_NUMBER', {
        phone: expect.any(String),
      });
    });

    it('should return both phone and email alert when both are available', () => {
      const translateService = spectator.inject(TranslateService);
      const conversationDetail = {
        ...mockConversationDetail,
        participants: [
          {
            ...mockConversationDetail.participants[0],
            phoneNumber: '+12125551234',
            email: '<EMAIL>',
          },
        ],
      } as ConversationDetail;

      const result = spectator.service.checkNotAvailableErrorForLeads(conversationDetail, 'US');

      expect(result).toEqual({
        title: 'INBOX.CHAT.RESPOND_TO_LEAD_AT_PHONE_NUMBER_OR_EMAIL',
      });
      expect(translateService.instant).toHaveBeenCalledWith('INBOX.CHAT.RESPOND_TO_LEAD_AT_PHONE_NUMBER_OR_EMAIL', {
        phone: expect.any(String),
        email: '<EMAIL>',
      });
    });

    it('should fallback to US formatting when invalid country code is provided', () => {
      const translateService = spectator.inject(TranslateService);
      const conversationDetail = {
        ...mockConversationDetail,
        participants: [
          {
            ...mockConversationDetail.participants[0],
            phoneNumber: '2125551234',
          },
        ],
      } as ConversationDetail;

      const result = spectator.service.checkNotAvailableErrorForLeads(conversationDetail, 'INVALID');

      expect(result).toEqual({
        title: 'INBOX.CHAT.RESPOND_TO_LEAD_AT_PHONE_NUMBER',
      });
      expect(translateService.instant).toHaveBeenCalledWith('INBOX.CHAT.RESPOND_TO_LEAD_AT_PHONE_NUMBER', {
        phone: '(*************',
      });
    });
  });

  describe('getConversationCustomerErrors', () => {
    const mockConversationDetail = {
      conversation: { conversationId: '123' },
    } as ConversationDetail;

    beforeEach(() => {
      spectator = createService();
      mockHostAppInterface.viewContact.mockReset();
    });

    it('should return anonymous participant with lead not captured error', () => {
      const conversationDetail = {
        ...mockConversationDetail,
        participants: [
          {
            participantType: ParticipantType.PARTICIPANT_TYPE_ANONYMOUS,
            isSubjectParticipant: true,
          },
        ],
      } as ConversationDetail;

      const result = spectator.service.getConversationCustomerErrors(conversationDetail);

      expect(result.participant).toBeTruthy();
      expect(result.participant?.participantType).toBe(ParticipantType.PARTICIPANT_TYPE_ANONYMOUS);
      expect(result.alert).toBeTruthy();
      expect(result.alert?.title).toBe('INBOX.WEBCHAT.SETTINGS.WEBCHAT_DISABLED_MISSING_CONTACT_INFO');
    });

    it('should return null participant when no customer or anonymous participant found', () => {
      const result = spectator.service.getConversationCustomerErrors(mockConversationDetail);

      expect(result.participant).toBeNull();
      expect(result.alert).toBeUndefined();
    });

    it('should return customer participant with deleted info error when isParticipantInternalInfoDeleted is true', () => {
      const conversationDetail = {
        ...mockConversationDetail,
        participants: [
          {
            internalParticipantId: 'CONTACT-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            isSubjectParticipant: true,
            isParticipantInternalInfoDeleted: true,
          },
        ],
      } as ConversationDetail;

      const result = spectator.service.getConversationCustomerErrors(conversationDetail);

      expect(result.participant).toBeTruthy();
      expect(result.participant?.participantType).toBe(ParticipantType.PARTICIPANT_TYPE_CUSTOMER);
      expect(result.alert).toBeTruthy();
      expect(result.alert?.title).toBe('INBOX.COMPOSER_ALERTS.PARTICIPANT_INTERNAL_INFO_DELETED.TITLE');
    });

    it('should return customer participant with missing contact info error and view contact action when no phone or email', () => {
      const subjectParticipants = [
        {
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_CUSTOMER,
          internalParticipantId: 'CONTACT-123',
        },
        {
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
          internalParticipantId: 'AG-123',
        },
      ] as SubjectParticipant[];

      const conversationDetail = {
        conversation: {
          conversationId: '123',
          subjectParticipants: subjectParticipants,
          subjectParticipantKey: {
            subjectParticipants: subjectParticipants,
          },
        } as Conversation,
        participants: [
          {
            internalParticipantId: 'CONTACT-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            isSubjectParticipant: true,
            phoneNumber: '',
            email: '',
          },
        ],
      } as ConversationDetail;

      const result = spectator.service.getConversationCustomerErrors(conversationDetail);

      expect(result.participant).toBeTruthy();
      expect(result.participant?.participantType).toBe(ParticipantType.PARTICIPANT_TYPE_CUSTOMER);
      expect(result.alert).toBeTruthy();
      expect(result.alert?.title).toBe('INBOX.ERROR.NO_PHONE_NUMBER_OR_EMAIL');
      expect(result.alert?.action?.title).toBe('INBOX.SENDER.VIEW_CONTACT');
      expect(result.alert?.action?.callback).toBeDefined();

      // Test the callback
      result.alert?.action?.callback();
      expect(mockHostAppInterface.viewContact).toHaveBeenCalledWith(conversationDetail);
    });
  });

  describe('getConversationCustomerErrors with modal', () => {
    let modalSpectator: SpectatorService<InboxAlertService>;
    beforeEach(() => {
      modalSpectator = createService({
        providers: [
          {
            provide: CONVERSATION_ROUTES_TOKEN,
            useValue: of({ useModal: true }),
          },
        ],
      });
    });

    it('should return customer participant with missing contact info error and no view contact action when useModal is true', () => {
      const conversationDetail = {
        conversation: {
          conversationId: '123',
          subjectParticipantKey: {
            subjectParticipants: [
              {
                participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_CUSTOMER,
                internalParticipantId: 'CONTACT-123',
              },
            ],
          },
        },
        participants: [
          {
            internalParticipantId: 'CONTACT-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            isSubjectParticipant: true,
            phoneNumber: '',
            email: '',
          },
        ],
      } as ConversationDetail;

      const result = modalSpectator.service.getConversationCustomerErrors(conversationDetail);

      expect(result.participant).toBeTruthy();
      expect(result.participant?.participantType).toBe(ParticipantType.PARTICIPANT_TYPE_CUSTOMER);
      expect(result.alert).toBeTruthy();
      expect(result.alert?.title).toBe('INBOX.ERROR.NO_PHONE_NUMBER_OR_EMAIL');
      expect(result.alert?.action).toBeUndefined();
    });
  });
});
