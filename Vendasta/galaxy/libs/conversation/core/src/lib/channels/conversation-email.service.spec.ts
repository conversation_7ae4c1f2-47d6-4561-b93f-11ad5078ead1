import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { take } from 'rxjs';
import { InboxService } from '../inbox.service';
import { ConversationDetail } from '../interface/conversation.interface';
import { ConversationEmailService } from './conversation-email.service';
import { InboxAlertService } from './inbox-alert.service';
import {
  Participant,
  ParticipantType,
  ConversationChannel,
  ChannelAvailabilityInterface,
} from '@vendasta/conversation';
import * as messageErrorsUtil from './message-errors.util';

class RouterMock {
  navigate = jest.fn();
}

describe('ConversationEmailService', () => {
  let svc: ConversationEmailService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        ConversationEmailService,
        {
          provide: InboxService,
          useValue: {
            isPartnerCenter: true,
          },
        },
        {
          provide: InboxAlertService,
          useValue: {
            getConversationCustomerErrors: jest.fn().mockReturnValue({ participant: {} as Participant }),
            getEmailOnlyAlertError: jest.fn().mockReturnValue({ title: 'Email not available' }),
          },
        },
        { provide: Router, useClass: RouterMock },
      ],
    });
  });

  it('should be created', () => {
    svc = TestBed.inject(ConversationEmailService);
    expect(svc).toBeTruthy();
  });

  it('should return customer errors when present in conversation', (done) => {
    const mockParticipant = {
      participantId: 'test-id',
      participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
    } as Participant;
    const mockConversation = {
      participants: [mockParticipant],
    } as ConversationDetail;

    TestBed.overrideProvider(InboxAlertService, {
      useValue: {
        getConversationCustomerErrors: jest.fn().mockReturnValue({
          participant: mockParticipant,
          alert: { title: 'Customer Error' },
        }),
      },
    });

    svc = TestBed.inject(ConversationEmailService);
    svc
      .getChatComposerAlert('partnerId', '', mockConversation, [], [])
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).not.toBeNull();
        expect(alertOptions?.title).toBe('Customer Error');
        expect(TestBed.inject(InboxAlertService).getConversationCustomerErrors).toHaveBeenCalledWith(mockConversation);
        done();
      });
  });

  it('should return error alert when customer has only email in Partner center', (done) => {
    const convoMock = {
      participants: [
        {
          participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
          isSubjectParticipant: true,
          email: '<EMAIL>',
        },
      ],
    } as ConversationDetail;

    svc = TestBed.inject(ConversationEmailService);
    svc
      .getChatComposerAlert('partnerId', '', convoMock, [], [])
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).not.toBeNull();
        expect(alertOptions?.title).toBe('Email not available');
        expect(TestBed.inject(InboxAlertService).getEmailOnlyAlertError).toHaveBeenCalled();
        done();
      });
  });

  it('should return alert options from channel availability when no customer errors are present', (done) => {
    const mockParticipant = {
      participantId: 'test-id',
      participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
      isSubjectParticipant: true,
      email: '<EMAIL>',
      phoneNumber: '**********',
    } as Participant;

    const mockConversation = {
      participants: [mockParticipant],
    } as ConversationDetail;

    const mockAvailabilities: ChannelAvailabilityInterface[] = [
      { channel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL, isAvailable: true },
    ];

    const mockAlertOptions = { title: 'Test Alert' };

    jest.spyOn(messageErrorsUtil, 'buildAlertOptionsFromAvailability').mockReturnValue(mockAlertOptions);

    TestBed.overrideProvider(InboxAlertService, {
      useValue: {
        getConversationCustomerErrors: jest.fn().mockReturnValue({
          participant: mockParticipant,
          alert: null,
        }),
      },
    });

    svc = TestBed.inject(ConversationEmailService);
    svc
      .getChatComposerAlert('partnerId', 'accountGroupId', mockConversation, [], mockAvailabilities)
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).toEqual(mockAlertOptions);
        expect(messageErrorsUtil.buildAlertOptionsFromAvailability).toHaveBeenCalledWith(
          ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
          mockAvailabilities,
        );
        done();
      });
  });

  it('should return null when getConversationCustomerErrors returns no participant and no alert', (done) => {
    const mockConversation = {
      participants: [],
    } as unknown as ConversationDetail;

    TestBed.overrideProvider(InboxAlertService, {
      useValue: {
        getConversationCustomerErrors: jest.fn().mockReturnValue({
          participant: null,
          alert: null,
        }),
      },
    });

    svc = TestBed.inject(ConversationEmailService);
    svc
      .getChatComposerAlert('partnerId', 'accountGroupId', mockConversation, [], [])
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).toBeNull();
        done();
      });
  });
});
