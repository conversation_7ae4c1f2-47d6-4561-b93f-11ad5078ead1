import { inject, Injectable } from '@angular/core';
import { ChannelAvailability, ConversationChannel } from '@vendasta/conversation';
import { Observable, of } from 'rxjs';
import { ConversationDetail } from '../interface/conversation.interface';
import { AlertOptions, ConversationChannelService } from './conversation-channel.abstract';
import { getMainConversationId } from './message-errors.util';
import { InboxAlertService } from './inbox-alert.service';

@Injectable()
export class ConversationWhatsappService extends ConversationChannelService {
  private readonly inboxAlerter = inject(InboxAlertService);

  getChatComposerAlert(
    _partnerId: string,
    _accountGroupId: string,
    conversationDetail?: ConversationDetail,
    channels?: ConversationChannel[],
    channelAvailabilities?: ChannelAvailability[],
  ): Observable<AlertOptions | null> {
    if (conversationDetail?.conversation.channel !== ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP) {
      return of(null);
    }

    const mainConversationID = getMainConversationId(conversationDetail);
    if (mainConversationID != '') {
      return of(this.inboxAlerter.getDuplicatedConversationError(mainConversationID));
    }

    const whatsappAvailability = channelAvailabilities?.find(
      (channelAvailability) => channelAvailability.channel === ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP,
    );

    return of({
      description:
        whatsappAvailability?.statuses?.[0]?.i18nKey ||
        'INBOX.SETTINGS.WHATSAPP_MESSAGES.ERRORS.CANNOT_SEND_MESSAGE_DESCRIPTION',
    });
  }
}
