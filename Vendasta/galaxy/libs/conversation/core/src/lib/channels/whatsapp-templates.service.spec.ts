import { TestBed } from '@angular/core/testing';
import { BehaviorSubject, combineLatest, of } from 'rxjs';
import { FacebookApiService, WhatsAppTemplateInterface, WhatsAppTemplateStatus } from '@vendasta/facebook';
import { CRMApiService } from '@vendasta/crm';
import { ConversationService } from '../state/conversation.service';
import { WhatsappTemplatesService } from './whatsapp-templates.service';
import { ACCOUNT_GROUP_ID_TOKEN, COMPANY_NAME_TOKEN, PARTNER_ID_TOKEN } from '../tokens';
import { ConversationChannel, ParticipantType, Status } from '@vendasta/conversation';
import { StandardExternalIds } from '@galaxy/crm/static';
import { InboxAlertService } from './inbox-alert.service';

describe('WhatsappTemplatesService', () => {
  let service: WhatsappTemplatesService;
  let facebookApiServiceMock: any;
  let crmApiServiceMock: any;
  let conversationServiceMock: any;

  beforeEach(() => {
    facebookApiServiceMock = {
      getWhatsAppMessageTemplates: jest.fn(),
    };

    crmApiServiceMock = {
      getMultiContact: jest.fn(),
    };

    conversationServiceMock = {
      availableChannels$: of({
        channelAvailabilities: [
          {
            channel: ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP,
            isAvailable: true,
            statuses: [{ status: Status.RESTRICTED }],
          },
        ],
      }),
      currentConversationDetail$: of({
        participants: [
          {
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            internalParticipantId: 'customer-123',
          },
        ],
      }),
    };

    TestBed.configureTestingModule({
      providers: [
        WhatsappTemplatesService,
        { provide: FacebookApiService, useValue: facebookApiServiceMock },
        { provide: CRMApiService, useValue: crmApiServiceMock },
        { provide: ConversationService, useValue: conversationServiceMock },
        { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of('account-group-id') },
        { provide: COMPANY_NAME_TOKEN, useValue: of('Sunshine Bakery') },
        { provide: PARTNER_ID_TOKEN, useValue: of('partner-id') },
        {
          provide: InboxAlertService,
          useValue: {
            getWhatsAppTemplateErrorMissingContactFirstName: jest.fn().mockReturnValue({ error: 'alert error' }),
          },
        },
      ],
    });

    service = TestBed.inject(WhatsappTemplatesService);
  });

  it('should return true for templates only when WhatsApp is restricted', (done) => {
    service.whatsappTemplatesOnly$.subscribe((result) => {
      expect(result).toBe(true);
      done();
    });
  });

  it('should generate English WhatsApp template with customer first name and company name, language of en', (done) => {
    facebookApiServiceMock.getWhatsAppMessageTemplates.mockReturnValue(
      of<{ templates: WhatsAppTemplateInterface[] }>({
        templates: [
          {
            name: 'request_to_continue_conversation',
            body: 'Hi {{1}}! Thanks for reaching out to {{2}}.',
            status: WhatsAppTemplateStatus.TEMPLATE_STATUS_APPROVED,
            language: 'en',
          },
        ],
      }),
    );

    crmApiServiceMock.getMultiContact.mockReturnValue(
      of({
        crmObjects: [{ fields: [{ externalId: StandardExternalIds.FirstName, stringValue: 'Jane' }] }],
      }),
    );

    const language = new BehaviorSubject<string>('en');
    service.hydratedWhatsappTemplate(language).subscribe((result) => {
      expect(result).toBe('Hi Jane! Thanks for reaching out to Sunshine Bakery.');
      done();
    });
  });

  it('should generate Spanish WhatsApp template with customer first name and company name, language of es', (done) => {
    facebookApiServiceMock.getWhatsAppMessageTemplates.mockReturnValue(
      of<{ templates: WhatsAppTemplateInterface[] }>({
        templates: [
          {
            name: 'request_to_continue_conversation',
            body: 'Hola {{1}}! Gracias por contactar a {{2}}.',
            status: WhatsAppTemplateStatus.TEMPLATE_STATUS_APPROVED,
            language: 'es',
          },
          {
            name: 'request_to_continue_conversation',
            body: 'Hi {{1}}! Thanks for reaching out to {{2}}.',
            status: WhatsAppTemplateStatus.TEMPLATE_STATUS_APPROVED,
            language: 'en',
          },
        ],
      }),
    );

    crmApiServiceMock.getMultiContact.mockReturnValue(
      of({
        crmObjects: [{ fields: [{ externalId: StandardExternalIds.FirstName, stringValue: 'Jane' }] }],
      }),
    );

    const language = new BehaviorSubject<string>('es');
    service.hydratedWhatsappTemplate(language).subscribe((result) => {
      expect(result).toBe('Hola Jane! Gracias por contactar a Sunshine Bakery.');
      done();
    });
  });

  it('should return empty string if no approved template is found', (done) => {
    facebookApiServiceMock.getWhatsAppMessageTemplates.mockReturnValue(
      of({
        templates: [],
      }),
    );

    const language = new BehaviorSubject<string>('en');
    service.hydratedWhatsappTemplate(language).subscribe((result) => {
      expect(result).toBe('');
      done();
    });
  });

  it('should return template with alert error if customer first name is not found', (done) => {
    facebookApiServiceMock.getWhatsAppMessageTemplates.mockReturnValue(
      of<{ templates: WhatsAppTemplateInterface[] }>({
        templates: [
          {
            name: 'request_to_continue_conversation',
            body: 'Hi {{1}}! Thanks for reaching out to {{2}}.',
            status: WhatsAppTemplateStatus.TEMPLATE_STATUS_APPROVED,
            language: 'en',
          },
        ],
      }),
    );

    crmApiServiceMock.getMultiContact.mockReturnValue(
      of({
        crmObjects: [{ fields: [] }],
      }),
    );

    const language = new BehaviorSubject<string>('en');

    combineLatest([service.whatsappTemplate(language), service.hydratedWhatsappTemplate(language)]).subscribe(
      ([template, hydratedTemplate]) => {
        expect(template?.bodyParameters[0].error).toBeDefined();
        expect(hydratedTemplate).toBe('Hi ! Thanks for reaching out to Sunshine Bakery.');
        done();
      },
    );
  });
});
