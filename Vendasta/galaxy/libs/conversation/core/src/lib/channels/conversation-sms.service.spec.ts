import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { RegistrationStage, A2PRegistrationService, RegistrationService, RegistrationType } from '@galaxy/sms';
import { of, take } from 'rxjs';
import { InboxService } from '../inbox.service';
import { ConversationDetail } from '../interface/conversation.interface';
import { CONVERSATION_COUNTRY_TOKEN, CONVERSATION_ROUTES_TOKEN, CONVERSATION_SMS_ENABLED_TOKEN } from '../tokens';
import { ConversationSMSService } from './conversation-sms.service';
import { InboxAlertService } from './inbox-alert.service';
import {
  Conversation,
  Participant,
  ParticipantType,
  ChannelAvailabilityInterface,
  ConversationChannel,
} from '@vendasta/conversation';
import { MAIN_CONVERSATION_ID_KEY } from './message-errors.util';

class RouterMock {
  navigate = jest.fn();
}

describe('ConversationSMSService', () => {
  let svc: ConversationSMSService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        ConversationSMSService,
        { provide: CONVERSATION_COUNTRY_TOKEN, useValue: of('us') },
        {
          provide: InboxService,
          useValue: {
            SMSNumber$: of('**********'),
            canAccessSMS$: of(true),
            hasAccessByCountry: () => true,
          },
        },
        {
          provide: InboxAlertService,
          useValue: {
            getConversationCustomerErrors: jest.fn().mockReturnValue({ participant: {} as Participant }),
            getSMSMessageEnabledError: jest.fn().mockReturnValue({ title: 'SMS Not Enabled' }),
            getSMSPhoneNumberError: jest.fn().mockReturnValue({ title: 'SMS Phone Number Error' }),
            getSMSRegistrationError: jest.fn().mockReturnValue({ title: 'Registration Required' }),
            getSMSRegulatoryComplianceError: jest.fn().mockReturnValue({ title: 'Regulatory Compliance Required' }),
            checkNotAvailableErrorForLeads: jest.fn().mockReturnValue({ title: 'Lead Error' }),
            getDuplicatedConversationError: jest.fn().mockReturnValue({ title: 'Go to main conversation' }),
          },
        },
        {
          provide: RegistrationService,
          useValue: {
            getRegistrationType$: jest.fn().mockReturnValue(of(RegistrationType.A2P_REGISTRATION)),
          },
        },
        { provide: Router, useClass: RouterMock },
      ],
    });
  });

  it('should be created', () => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.NotSubmitted),
    };
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    svc = TestBed.inject(ConversationSMSService);
    expect(svc).toBeTruthy();
  });

  it('should return null when registration is completed', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.RegistrationComplete),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert(
        'partnerId',
        'accountGroupId',
        { participants: [] as Participant[] } as ConversationDetail,
        [],
        [],
      )
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).toBeNull();
        done();
      });
  });

  it('should return customer errors when present in conversation', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.RegistrationComplete),
    };
    const mockParticipant = {
      participantId: 'test-id',
      participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
    } as Participant;
    const mockConversation = {
      participants: [mockParticipant],
    } as ConversationDetail;

    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });
    TestBed.overrideProvider(InboxAlertService, {
      useValue: {
        getConversationCustomerErrors: jest.fn().mockReturnValue({
          participant: mockParticipant,
          alert: { title: 'Customer Error' },
        }),
      },
    });

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert('partnerId', 'accountGroupId', mockConversation, [], [])
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).not.toBeNull();
        expect(alertOptions?.title).toBe('Customer Error');
        expect(TestBed.inject(InboxAlertService).getConversationCustomerErrors).toHaveBeenCalledWith(mockConversation);
        done();
      });
  });
  it('should return null when no customer participant present in SMS conversation', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.RegistrationComplete),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });
    TestBed.overrideProvider(InboxAlertService, {
      useValue: {
        getConversationCustomerErrors: jest.fn().mockReturnValue({
          participant: null,
        }),
      },
    });

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert('partnerId', 'accountGroupId', {} as ConversationDetail, [], [])
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).toBeNull();
        expect(TestBed.inject(InboxAlertService).getConversationCustomerErrors).toHaveBeenCalled();
        done();
      });
  });

  it('should return null when country is not available for SMS', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.RegistrationComplete),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });
    TestBed.overrideProvider(InboxService, {
      useValue: {
        SMSNumber$: of('**********'),
        canAccessSMS$: of(false),
        hasAccessByCountry: () => false,
      },
    });

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert(
        'partnerId',
        'accountGroupId',
        { participants: [] as Participant[] } as ConversationDetail,
        [],
        [],
      )
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).toBeNull();
        done();
      });
  });

  it('should call getSMSRegistrationError when registration is not submitted', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.NotSubmitted),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert(
        'partnerId',
        'accountGroupId',
        { participants: [] as Participant[] } as ConversationDetail,
        [],
        [],
      )
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).not.toBeNull();
        expect(alertOptions?.title).toBe('Registration Required');
        expect(TestBed.inject(InboxAlertService).getSMSRegistrationError).toHaveBeenCalled();
        done();
      });
  });
  it('should return null when registration is not submitted and country is CA', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.NotSubmitted),
    };
    const registrationService = {
      getRegistrationType$: jest.fn().mockReturnValue(of(RegistrationType.NONE)),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(RegistrationService, { useValue: registrationService });
    TestBed.overrideProvider(CONVERSATION_COUNTRY_TOKEN, { useValue: of('ca') });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert(
        'partnerId',
        'accountGroupId',
        { participants: [] as Participant[] } as ConversationDetail,
        [],
        [],
      )
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).toBeNull();
        done();
      });
  });

  it('should return go to main conversation if there is anonymous participant and a main conversation id in metadata', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.NotSubmitted),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(CONVERSATION_COUNTRY_TOKEN, { useValue: of('ca') });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert(
        'partnerId',
        'accountGroupId',
        {
          participants: [
            { participantType: ParticipantType.PARTICIPANT_TYPE_ANONYMOUS, participantId: 'anon-id' } as Participant,
          ] as Participant[],
          conversation: {
            metadata: [{ key: MAIN_CONVERSATION_ID_KEY, value: 'CONVERSATION-123' }],
          } as Conversation,
        } as ConversationDetail,
        [],
        [],
      )
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).not.toBeNull();
        expect(alertOptions?.title).toBe('Go to main conversation');
        expect(TestBed.inject(InboxAlertService).getDuplicatedConversationError).toHaveBeenCalled();
        done();
      });
  });

  it('should return error alert when smsEnabled is false and accountGroup is provided', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.RegistrationComplete),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });

    TestBed.overrideProvider(InboxService, {
      useValue: {
        SMSNumber$: of('**********'),
        canAccessSMS$: of(false),
        hasAccessByCountry: () => true,
      },
    });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });

    const convoMock = {
      participants: [
        {
          participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
          isSubjectParticipant: true,
        },
      ],
    } as ConversationDetail;
    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert('partnerId', 'accountGroupId', convoMock, [], [])
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).not.toBeNull();
        expect(alertOptions?.title).toBe('SMS Not Enabled');
        expect(TestBed.inject(InboxAlertService).getSMSMessageEnabledError).toHaveBeenCalled();
        done();
      });
  });

  it('should return the common lead error when smsEnabled is false and accountGroup is NOT provided', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.RegistrationComplete),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });

    TestBed.overrideProvider(InboxService, {
      useValue: {
        SMSNumber$: of('**********'),
        canAccessSMS$: of(false),
        hasAccessByCountry: () => true,
      },
    });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });
    svc = TestBed.inject(ConversationSMSService);

    const convoMock = {
      conversation: {
        conversationId: 'mock-id',
      } as Conversation,
      participants: [] as Participant[],
    } as ConversationDetail;
    svc
      .getChatComposerAlert('partnerId', 'accountGroupId', convoMock, [], [])
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).not.toBeNull();
        expect(alertOptions?.title).toBe('Lead Error');
        expect(TestBed.inject(InboxAlertService).checkNotAvailableErrorForLeads).toHaveBeenCalledWith(convoMock, 'us');
        done();
      });
  });

  it('should return error alert when SMSNumber$ is empty', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.RegistrationComplete),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(CONVERSATION_SMS_ENABLED_TOKEN, { useValue: of(true) });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });
    const inboxServiceOverride = {
      SMSNumber$: of(''),
      canAccessSMS$: of(true),
      hasAccessByCountry: () => true,
    };
    TestBed.overrideProvider(InboxService, { useValue: inboxServiceOverride });

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert(
        'partnerId',
        'accountGroupId',
        { participants: [] as Participant[] } as ConversationDetail,
        [],
        [],
      )
      .subscribe((alertOptions) => {
        expect(alertOptions).not.toBeNull();
        expect(alertOptions?.title).toBe('SMS Phone Number Error');
        expect(TestBed.inject(InboxAlertService).getSMSPhoneNumberError).toHaveBeenCalled();
        done();
      });
  });
  it('should return alert options when channel availabilities have statuses', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.RegistrationComplete),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });

    const mockChannelAvailabilities: ChannelAvailabilityInterface[] = [
      {
        channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
        isAvailable: false,
        statuses: [
          {
            i18nKey: 'INBOX.ERROR.SMS.TEST_ERROR',
          },
        ],
      },
    ];

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert(
        'partnerId',
        'accountGroupId',
        { participants: [] as Participant[] } as ConversationDetail,
        [],
        mockChannelAvailabilities,
      )
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).not.toBeNull();
        expect(alertOptions?.title).toBe('INBOX.ERROR.SMS.TEST_ERROR');
        done();
      });
  });

  it('should return null when no matching channel availability found', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.RegistrationComplete),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });

    const mockChannelAvailabilities: ChannelAvailabilityInterface[] = [
      {
        channel: ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP,
        isAvailable: false,
        statuses: [
          {
            i18nKey: 'INBOX.ERROR.WHATSAPP.TEST_ERROR',
          },
        ],
      },
    ];

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert(
        'partnerId',
        'accountGroupId',
        { participants: [] as Participant[] } as ConversationDetail,
        [],
        mockChannelAvailabilities,
      )
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).toBeNull();
        done();
      });
  });

  it('should return null when channel availability has no statuses', (done) => {
    const smsRegistrationService = {
      registrationStage$: of(RegistrationStage.RegistrationComplete),
    };
    TestBed.overrideProvider(A2PRegistrationService, { useValue: smsRegistrationService });
    TestBed.overrideProvider(CONVERSATION_ROUTES_TOKEN, { useValue: of({ useModal: false }) });

    const mockChannelAvailabilities: ChannelAvailabilityInterface[] = [
      {
        channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
        isAvailable: false,
        statuses: [],
      },
    ];

    svc = TestBed.inject(ConversationSMSService);
    svc
      .getChatComposerAlert(
        'partnerId',
        'accountGroupId',
        { participants: [] as Participant[] } as ConversationDetail,
        [],
        mockChannelAvailabilities,
      )
      .pipe(take(1))
      .subscribe((alertOptions) => {
        expect(alertOptions).toBeNull();
        done();
      });
  });
});
