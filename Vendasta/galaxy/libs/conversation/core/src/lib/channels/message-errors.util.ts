import { ChannelAvailabilityInterface, ConversationChannel, ParticipantType } from '@vendasta/conversation';
import { AlertOptions } from './conversation-channel.abstract';
import { ConversationDetail } from '../interface/conversation.interface';

// Whatsapp errors: https://developers.facebook.com/docs/whatsapp/cloud-api/support/error-codes/
export const WHATSAPP_ERROR_TRANSLATION_MAP: Record<string, string> = {
  '131026': 'INBOX.ERROR.MESSAGE_ERROR.UNABLE_TO_DELIVER',
  '131042': 'INBOX.ERROR.MESSAGE_ERROR.PAYMENT_ISSUE',
  '131045': 'INBOX.ERROR.MESSAGE_ERROR.PHONE_NUMBER_REGISTRATION',
  '131047': 'INBOX.ERROR.MESSAGE_ERROR.MORE_THAN_24_HOURS',
};

export const INBOX_ERROR_CODE_TRANSLATION_MAP: Record<string, string> = {
  unsubscribed: 'INBOX.SEND_MESSAGE.SMS.ERRORS.UNSUBSCRIBED',
  accountSuspended: 'INBOX.SEND_MESSAGE.SMS.ERRORS.ACCOUNT_SUSPENDED',
  messageBlocked: 'INBOX.SEND_MESSAGE.SMS.ERRORS.MESSAGE_BLOCKED',
  unknownDestination: 'INBOX.SEND_MESSAGE.SMS.ERRORS.UNKNOWN_DESTINATION',
  mediaSizeExceeded: 'INBOX.SEND_MESSAGE.SMS.ERRORS.MEDIA_SIZE_EXCEEDED',
  invalidContentType: 'INBOX.SEND_MESSAGE.SMS.ERRORS.INVALID_CONTENT_TYPE',
  toNumberIsShortCode: 'INBOX.SEND_MESSAGE.SMS.ERRORS.TO_NUMBER_IS_SHORT_CODE',
  sameToFromNumbers: 'INBOX.SEND_MESSAGE.SMS.ERRORS.SAME_TO_FROM_NUMBERS',
  premiumNumberSendingNotAllowed: 'INBOX.SEND_MESSAGE.SMS.ERRORS.PREMIUM_NUMBER_SENDING_NOT_ALLOWED',
  messageBodyTooLong: 'INBOX.SEND_MESSAGE.SMS.ERRORS.MESSAGE_BODY_TOO_LONG',
  messageFiltered: 'INBOX.SEND_MESSAGE.SMS.ERRORS.MESSAGE_FILTERED',
  toNumberRegionNotEnabled: 'INBOX.SEND_MESSAGE.SMS.ERRORS.REGION_NOT_ENABLED',
  invalidToNumber: 'INBOX.SEND_MESSAGE.SMS.ERRORS.INVALID_TO_NUMBER',
  toNumberIsLandline: 'INBOX.SEND_MESSAGE.SMS.ERRORS.TO_NUMBER_IS_LANDLINE',
  temporaryIssue: 'INBOX.SEND_MESSAGE.SMS.ERRORS.TEMPORARY_ISSUE',
  systemError: 'INBOX.SEND_MESSAGE.SMS.ERRORS.SYSTEM_ERROR',
  a2pIssue: 'INBOX.SEND_MESSAGE.SMS.ERRORS.A2P_ISSUE',
};

export function getErrorMessage(channel: ConversationChannel, code: string): string {
  if (channel === ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP) {
    return WHATSAPP_ERROR_TRANSLATION_MAP[code] || '';
  }
  return '';
}

export function getInboxSMSErrorTranslationKey(errorCode: string): string {
  return INBOX_ERROR_CODE_TRANSLATION_MAP[errorCode] || 'INBOX.SEND_MESSAGE.SMS.ERRORS.SYSTEM_ERROR';
}

export function buildAlertOptionsFromAvailability(
  channel: ConversationChannel,
  availability: ChannelAvailabilityInterface[],
): AlertOptions | null {
  const channelAvailability = availability.find((channelAvailability) => channelAvailability.channel === channel);

  if (channelAvailability?.statuses && channelAvailability.statuses.length > 0) {
    return {
      title: channelAvailability.statuses?.[0]?.i18nKey,
    } as AlertOptions;
  }

  return null;
}

export const MAIN_CONVERSATION_ID_KEY = 'main_conversation_id_key';

export function getMainConversationId(conversationDetail: ConversationDetail): string {
  if (conversationDetail.participants.some((p) => p.participantType === ParticipantType.PARTICIPANT_TYPE_ANONYMOUS)) {
    const kv = conversationDetail?.conversation?.metadata?.find((meta) => meta.key === MAIN_CONVERSATION_ID_KEY);
    return kv?.value ?? '';
  }

  return '';
}
