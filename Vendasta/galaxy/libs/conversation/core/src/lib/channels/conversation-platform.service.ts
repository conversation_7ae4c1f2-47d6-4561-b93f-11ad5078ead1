import { Injectable, inject } from '@angular/core';
import { ConversationChannel } from '@vendasta/conversation';
import { Observable, map, of } from 'rxjs';
import { ConversationDetail } from '../interface/conversation.interface';
import { CONVERSATION_COUNTRY_TOKEN } from '../tokens';
import { AlertOptions, ConversationChannelService } from './conversation-channel.abstract';
import { InboxAlertService } from './inbox-alert.service';

@Injectable()
export class ConversationPlatformService extends ConversationChannelService {
  private readonly country$ = inject(CONVERSATION_COUNTRY_TOKEN);
  private readonly inboxAlerter = inject(InboxAlertService);

  getChatComposerAlert(
    partnerId: string,
    accountGroupId: string,
    conversationDetail: ConversationDetail,
  ): Observable<AlertOptions | null> {
    if (conversationDetail.conversation.channel !== ConversationChannel.CONVERSATION_CHANNEL_INTERNAL) return of(null);

    //This will help to show proper errors for existing conversations with Contacts under Platform channel
    const checkContactInfo = this.inboxAlerter.getConversationCustomerErrors(conversationDetail);
    if (!checkContactInfo?.participant || checkContactInfo?.alert) return of(checkContactInfo?.alert ?? null);

    return this.country$.pipe(
      map((country) => {
        return this.inboxAlerter.checkNotAvailableErrorForLeads(conversationDetail, country);
      }),
    );
  }
}
