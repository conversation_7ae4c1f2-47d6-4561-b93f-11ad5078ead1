import { TestBed } from '@angular/core/testing';
import { ConversationPlatformService } from './conversation-platform.service';
import {
  ConversationChannel,
  GetMultiConversationDetailsResponseDetailedConversation,
  ParticipantType,
} from '@vendasta/conversation';
import { InboxService } from '../inbox.service';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { CONVERSATION_COUNTRY_TOKEN } from '../tokens';
import { of } from 'rxjs';
import * as en from '../../../../ui/src/i18n/assets/en_devel.json';
import { InboxAlertService } from './inbox-alert.service';

describe('ConversationPlatformService', () => {
  let svc: ConversationPlatformService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        TranslateTestingModule.withTranslations({
          en: en,
        }),
      ],
      providers: [
        ConversationPlatformService,
        { provide: InboxService, useValue: { isBusinessApp: false } },
        { provide: CONVERSATION_COUNTRY_TOKEN, useValue: of('CA') },
        {
          provide: InboxAlertService,
          useValue: { checkNotAvailableErrorForLeads: jest.fn(), getConversationCustomerErrors: jest.fn() },
        },
      ],
    });
  });

  describe('getChatComposerAlert', () => {
    it('should be created', () => {
      svc = TestBed.inject(ConversationPlatformService);
      expect(svc).toBeTruthy();
    });

    it('should return null if not in platform channel', (done) => {
      const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
        conversation: {
          channel: ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
        },
      });

      svc = TestBed.inject(ConversationPlatformService);
      const got = svc.getChatComposerAlert('partnerId', 'accountGroupId', conversationDetail);

      got.subscribe((alert) => {
        expect(alert).toBeNull();
        done();
      });
    });

    it('should return null if in business app', (done) => {
      TestBed.overrideProvider(InboxService, { useValue: { isBusinessApp: true } });
      const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
        conversation: {
          channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
        },
        participants: [
          {
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            phoneNumber: '**********',
            email: '<EMAIL>',
          },
          { participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER },
        ],
      });

      svc = TestBed.inject(ConversationPlatformService);
      const got = svc.getChatComposerAlert('partnerId', 'accountGroupId', conversationDetail);

      got.subscribe((alert) => {
        expect(alert).toBeNull();
        done();
      });
    });

    describe('Forms created with platform channel', () => {
      it('should return lead errors when present in platform conversation, contact found but not valid', (done) => {
        const mockParticipant = {
          participantType: ParticipantType.PARTICIPANT_TYPE_ANONYMOUS,
        };
        const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
          conversation: {
            channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
          },
          participants: [mockParticipant, { participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER }],
        });

        TestBed.overrideProvider(InboxAlertService, {
          useValue: {
            getConversationCustomerErrors: jest.fn().mockReturnValue({
              participant: mockParticipant,
              alert: { title: 'Lead not captured' },
            }),
            checkNotAvailableErrorForLeads: jest.fn(),
          },
        });

        svc = TestBed.inject(ConversationPlatformService);
        const got = svc.getChatComposerAlert('partnerId', 'accountGroupId', conversationDetail);

        got.subscribe((alertOptions) => {
          expect(alertOptions).not.toBeNull();
          expect(alertOptions?.title).toBe('Lead not captured');
          expect(TestBed.inject(InboxAlertService).getConversationCustomerErrors).toHaveBeenCalledWith(
            conversationDetail,
          );
          done();
        });
      });
      it('should return lead errors when present in platform conversation, contact not found', (done) => {
        const mockParticipant = {
          participantType: ParticipantType.PARTICIPANT_TYPE_ANONYMOUS,
        };
        const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
          conversation: {
            channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
          },
          participants: [mockParticipant, { participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER }],
        });

        TestBed.overrideProvider(InboxAlertService, {
          useValue: {
            getConversationCustomerErrors: jest.fn().mockReturnValue({
              participant: null,
            }),
            checkNotAvailableErrorForLeads: jest.fn(),
          },
        });

        svc = TestBed.inject(ConversationPlatformService);
        const result = svc.getChatComposerAlert('partnerId', 'accountGroupId', conversationDetail);

        result.subscribe((alertOptions) => {
          expect(alertOptions).toBeNull();
          expect(TestBed.inject(InboxAlertService).getConversationCustomerErrors).toHaveBeenCalledWith(
            conversationDetail,
          );
          done();
        });
      });

      it('should return the proper notification to contact the customer participant when exist', (done) => {
        const mockParticipant = {
          participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
          phoneNumber: '**********',
          email: '<EMAIL>',
        };
        const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
          conversation: {
            channel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
          },
          participants: [mockParticipant, { participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER }],
        });

        TestBed.overrideProvider(InboxAlertService, {
          useValue: {
            getConversationCustomerErrors: jest.fn().mockReturnValue({
              participant: mockParticipant,
            }),
            checkNotAvailableErrorForLeads: jest.fn().mockReturnValue({ title: 'Respond to this lead at **********.' }),
          },
        });

        svc = TestBed.inject(ConversationPlatformService);
        const result = svc.getChatComposerAlert('partnerId', 'accountGroupId', conversationDetail);

        result.subscribe((alertOptions) => {
          expect(alertOptions).not.toBeNull();
          expect(alertOptions?.title).toBe('Respond to this lead at **********.');
          expect(TestBed.inject(InboxAlertService).getConversationCustomerErrors).toHaveBeenCalledWith(
            conversationDetail,
          );
          expect(TestBed.inject(InboxAlertService).checkNotAvailableErrorForLeads).toHaveBeenCalledWith(
            conversationDetail,
            'CA',
          );
          done();
        });
      });
    });
  });
});
