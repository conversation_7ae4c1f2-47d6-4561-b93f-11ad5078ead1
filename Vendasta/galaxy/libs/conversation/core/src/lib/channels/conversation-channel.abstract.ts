import { ChannelAvailabilityInterface, ConversationChannel } from '@vendasta/conversation';
import { Observable } from 'rxjs';
import { ConversationDetail } from '../../index';

export interface AlertOptions {
  title?: string;
  description?: string;
  action?: AlertAction;
}

interface AlertAction {
  title: string;
  callback: () => void;
}

export abstract class ConversationChannelService {
  abstract getChatComposerAlert(
    partnerId: string,
    accountGroupId: string,
    conversationDetail?: ConversationDetail,
    availableChannels?: ConversationChannel[],
    channelAvailabilities?: ChannelAvailabilityInterface[],
  ): Observable<AlertOptions | null>;
}
