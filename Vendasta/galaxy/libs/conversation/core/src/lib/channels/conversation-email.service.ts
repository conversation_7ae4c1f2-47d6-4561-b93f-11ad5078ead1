import { inject, Injectable } from '@angular/core';
import { AlertOptions, ConversationChannelService } from './conversation-channel.abstract';
import { getSubjectParticipantByType } from '../participant-utils';
import { InboxAlertService } from './inbox-alert.service';
import { Observable, of } from 'rxjs';
import { ConversationChannel, ChannelAvailabilityInterface, ParticipantType } from '@vendasta/conversation';
import { InboxService } from '../inbox.service';
import { ConversationDetail } from '../interface/conversation.interface';
import { buildAlertOptionsFromAvailability } from './message-errors.util';

@Injectable()
export class ConversationEmailService extends ConversationChannelService {
  private readonly inboxService = inject(InboxService);
  private readonly inboxAlerter = inject(InboxAlertService);

  getChatComposerAlert(
    partnerId: string,
    accountGroupId: string,
    conversationDetail: ConversationDetail,
    _availableChannels: ConversationChannel[],
    channelAvailabilities: ChannelAvailabilityInterface[],
  ): Observable<AlertOptions | null> {
    const checkContactInfo = this.inboxAlerter.getConversationCustomerErrors(conversationDetail);
    if (!checkContactInfo?.participant || checkContactInfo?.alert) return of(checkContactInfo?.alert ?? null);

    const customerParticipant = getSubjectParticipantByType(
      conversationDetail?.participants,
      ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
    );

    if (customerParticipant) {
      const email = customerParticipant?.email;
      const phoneNumber = customerParticipant?.phoneNumber;

      if (this.inboxService.isPartnerCenter && email && !phoneNumber) {
        return of(this.inboxAlerter.getEmailOnlyAlertError());
      }
    }

    return of(buildAlertOptionsFromAvailability(ConversationChannel.CONVERSATION_CHANNEL_EMAIL, channelAvailabilities));
  }
}
