import { inject, Injectable } from '@angular/core';
import { ChannelAvailability, ConversationChannel } from '@vendasta/conversation';
import { Observable, of } from 'rxjs';
import { ConversationDetail } from '../interface/conversation.interface';
import { AlertOptions, ConversationChannelService } from './conversation-channel.abstract';
import { Router } from '@angular/router';

@Injectable()
export class ConversationFacebookService extends ConversationChannelService {
  private router = inject(Router);

  getChatComposerAlert(
    _partnerId: string,
    _accountGroupId: string,
    conversationDetail?: ConversationDetail,
    channels?: ConversationChannel[],
    channelAvailabilities?: ChannelAvailability[],
  ): Observable<AlertOptions | null> {
    if (conversationDetail?.conversation.channel !== ConversationChannel.CONVERSATION_CHANNEL_FACEBOOK) {
      return of(null);
    }

    const facebookAvailability = channelAvailabilities?.find(
      (channelAvailability) => channelAvailability.channel === ConversationChannel.CONVERSATION_CHANNEL_FACEBOOK,
    );

    if (facebookAvailability?.isAvailable === true) {
      return of(null);
    }

    return of({
      title: 'INBOX.SETTINGS.FACEBOOK_MESSENGER.ERRORS.CANNOT_SEND_MESSAGE',
      description:
        facebookAvailability?.statuses?.[0]?.i18nKey ||
        'INBOX.SETTINGS.FACEBOOK_MESSENGER.ERRORS.CANNOT_SEND_MESSAGE_DESCRIPTION',
      action: {
        title: 'INBOX.SENDER.CLOSE_CONVERSATION',
        callback: () => this.router.navigate([`/account/location/${_accountGroupId}/inbox`]),
      },
    });
  }
}
