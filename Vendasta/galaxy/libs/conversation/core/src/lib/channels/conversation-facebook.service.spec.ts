import { TestBed } from '@angular/core/testing';

import { ConversationChannel, GetMultiConversationDetailsResponseDetailedConversation } from '@vendasta/conversation';
import { ConversationFacebookService } from './conversation-facebook.service';

describe('ConversationFacebookService', () => {
  let service: ConversationFacebookService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [ConversationFacebookService],
    });
    service = TestBed.inject(ConversationFacebookService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return null if conversationDetail is not facebook', (done) => {
    const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
      conversation: {
        channel: ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
      },
    });
    const result = service.getChatComposerAlert('partnerId', 'accountGroupId', conversationDetail);
    result.subscribe((alert) => {
      expect(alert).toBeNull();
      done();
    });
  });

  it('should return alert if conversationDetail is facebook and channels are empty', (done) => {
    const conversationDetail = new GetMultiConversationDetailsResponseDetailedConversation({
      conversation: {
        channel: ConversationChannel.CONVERSATION_CHANNEL_FACEBOOK,
      },
    });
    const result = service.getChatComposerAlert('partnerId', 'accountGroupId', conversationDetail, []);
    result.subscribe((alert) => {
      expect(alert).toStrictEqual({
        title: 'INBOX.SETTINGS.FACEBOOK_MESSENGER.ERRORS.CANNOT_SEND_MESSAGE',
        description: 'INBOX.SETTINGS.FACEBOOK_MESSENGER.ERRORS.CANNOT_SEND_MESSAGE_DESCRIPTION',
        action: {
          callback: expect.any(Function),
          title: 'INBOX.SENDER.CLOSE_CONVERSATION',
        },
      });
      done();
    });
  });
});
