import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { RegistrationStage, A2PRegistrationService, RegistrationService, RegistrationType } from '@galaxy/sms';
import { combineLatest, map, Observable, of } from 'rxjs';
import { InboxService } from '../inbox.service';
import { ConversationDetail } from '../interface/conversation.interface';
import { CONVERSATION_COUNTRY_TOKEN } from '../tokens';
import { AlertOptions, ConversationChannelService } from './conversation-channel.abstract';
import { InboxAlertService } from './inbox-alert.service';
import { getSubjectParticipantByType } from '../participant-utils';
import { ChannelAvailabilityInterface, ConversationChannel, ParticipantType } from '@vendasta/conversation';
import { buildAlertOptionsFromAvailability, getMainConversationId } from './message-errors.util';

@Injectable()
export class ConversationSMSService extends ConversationChannelService {
  private router = inject(Router);
  private readonly smsRegistrationService = inject(A2PRegistrationService);
  private readonly registrationService = inject(RegistrationService);
  private readonly inboxService = inject(InboxService);
  private readonly country$ = inject(CONVERSATION_COUNTRY_TOKEN);
  private readonly canAccessSMS$ = this.inboxService.canAccessSMS$;
  private readonly inboxAlerter = inject(InboxAlertService);

  getChatComposerAlert(
    partnerId: string,
    accountGroupId: string,
    conversationDetail: ConversationDetail,
    _availableChannels: ConversationChannel[],
    channelAvailabilities: ChannelAvailabilityInterface[],
  ): Observable<AlertOptions | null> {
    const stage$ = this.smsRegistrationService.registrationStage$;
    const SMSNumber$ = this.inboxService.SMSNumber$;
    const registrationType$ = this.registrationService.getRegistrationType$();

    const checkContactInfo = this.inboxAlerter.getConversationCustomerErrors(conversationDetail);
    if (!checkContactInfo?.participant || checkContactInfo?.alert) return of(checkContactInfo?.alert ?? null);

    const mainConversationID = getMainConversationId(conversationDetail);
    if (mainConversationID != '') {
      return of(this.inboxAlerter.getDuplicatedConversationError(mainConversationID));
    }

    const accountGroupParticipant = getSubjectParticipantByType(
      conversationDetail?.participants,
      ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
    );

    return combineLatest([this.country$, this.canAccessSMS$, stage$, SMSNumber$, registrationType$]).pipe(
      map(([orgCountry, canAccessSMS, stage, smsNumber, registrationType]) => {
        const country = orgCountry?.toLowerCase() || '';
        if (!this.inboxService.hasAccessByCountry(country)) return null;

        if (!canAccessSMS) {
          if (!accountGroupParticipant)
            return this.inboxAlerter.checkNotAvailableErrorForLeads(conversationDetail, country);
          return this.inboxAlerter.getSMSMessageEnabledError();
        }

        if (!smsNumber) return this.inboxAlerter.getSMSPhoneNumberError();

        // Check for registration requirements based on registration type
        if (stage !== RegistrationStage.RegistrationComplete) {
          if (registrationType === RegistrationType.A2P_REGISTRATION) {
            return this.inboxAlerter.getSMSRegistrationError(accountGroupId);
          }
          if (registrationType === RegistrationType.REGULATORY_COMPLIANCE) {
            return this.inboxAlerter.getSMSRegulatoryComplianceError(accountGroupId);
          }
        }

        return buildAlertOptionsFromAvailability(ConversationChannel.CONVERSATION_CHANNEL_SMS, channelAvailabilities);
      }),
    );
  }
}
