import { Injectable, inject, runInInjectionContext, Injector } from '@angular/core';
import { RegistrationStage, A2PRegistrationService, COUNTRIES_REQUIRING_REGISTRATION } from '@galaxy/sms';
import { ConversationApiService, Widget } from '@vendasta/conversation';
import { Observable, catchError, combineLatest, map, of, shareReplay, switchMap } from 'rxjs';
import { InboxService } from '../inbox.service';
import { ConversationDetail } from '../interface/conversation.interface';
import { ACCOUNT_GROUP_ID_TOKEN, CONVERSATION_COUNTRY_TOKEN } from '../tokens';
import { AlertOptions, ConversationChannelService } from './conversation-channel.abstract';
import { InboxAlertService } from './inbox-alert.service';
import { CONVERSATION_WEB_CHAT_ENABLED_TOKEN } from '../tokens';
import { getMainConversationId } from './message-errors.util';

@Injectable()
export class ConversationWebchatService extends ConversationChannelService {
  private readonly inboxService = inject(InboxService);
  private readonly country$ = inject(CONVERSATION_COUNTRY_TOKEN);
  private readonly inboxAlerter = inject(InboxAlertService);
  private readonly conversationApiService = inject(ConversationApiService);
  private accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly injector = inject(Injector);
  private smsRegistrationService: A2PRegistrationService | null = null;

  private readonly webChatEnabled$ = inject(CONVERSATION_WEB_CHAT_ENABLED_TOKEN);
  private readonly canAccessSMS$ = this.inboxService.canAccessSMS$;

  // TODO: MEGA-791 - Remove this once SMS registration state check is implemented on backend
  private readonly SMSRegistrationState$ = this.canAccessSMS$.pipe(
    switchMap((canAccessSMS) => {
      if (!canAccessSMS) return of(null);
      if (!this.smsRegistrationService) {
        this.smsRegistrationService = runInInjectionContext(this.injector, () => {
          return inject(A2PRegistrationService);
        });
      }
      return this.smsRegistrationService?.registrationStage$ ?? of(null);
    }),
  );

  getChatComposerAlert(
    partnerId: string,
    accountGroupId: string,
    conversationDetail: ConversationDetail,
  ): Observable<AlertOptions | null> {
    const mainConversationID = getMainConversationId(conversationDetail);
    if (mainConversationID != '') {
      return of(this.inboxAlerter.getDuplicatedConversationError(mainConversationID));
    }

    const SMSNumber$ = this.inboxService.SMSNumber$;

    const checkContactInfo = this.inboxAlerter.getConversationCustomerErrors(conversationDetail);
    if (!checkContactInfo?.participant || checkContactInfo?.alert) return of(checkContactInfo?.alert ?? null);

    return combineLatest([
      this.country$,
      this.canAccessSMS$,
      SMSNumber$,
      this.webChatEnabled$,
      this.SMSRegistrationState$,
    ]).pipe(
      map(([orgCountry, canAccessSMS, smsNumber, webChatEnabled, stage]) => {
        const country = orgCountry?.toLowerCase() || '';
        if (
          !checkContactInfo.participant?.phoneNumber ||
          !webChatEnabled ||
          !this.inboxService.hasAccessByCountry(country)
        ) {
          return this.inboxAlerter.checkNotAvailableErrorForLeads(conversationDetail, country);
        }

        if (!canAccessSMS) return this.inboxAlerter.getSMSMessageEnabledError();

        if (!smsNumber) return this.inboxAlerter.getSMSPhoneNumberError();

        //TODO(MEGA-2361): replace with registrationService.isRegistrationRequired$
        if (!COUNTRIES_REQUIRING_REGISTRATION.includes(country)) return null;

        return stage !== RegistrationStage.RegistrationComplete
          ? this.inboxAlerter.getSMSRegistrationError(accountGroupId)
          : null;
      }),
    );
  }

  readonly webchats$: Observable<Widget[]> = this.accountGroupId$.pipe(
    switchMap((accountGroupId) => this.conversationApiService.listWidgets({ namespace: accountGroupId })),
    map((listWidgets) => listWidgets?.widgets),
    shareReplay({ bufferSize: 1, refCount: true }),
    catchError(async () => {
      return [];
    }),
  );

  readonly isAnyWebchatEnabled$ = this.webchats$.pipe(
    map((webchats) => {
      if (webchats) {
        return webchats.some((webchat) => webchat.isEnabled);
      }
      return false;
    }),
  );
}
