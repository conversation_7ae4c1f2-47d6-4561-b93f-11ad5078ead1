import { signal, WritableSignal } from '@angular/core';
import { Environment } from '@galaxy/core';
import { FeatureFlagStatusInterface } from '@galaxy/partner';
import { GetCustomerResponse } from '@vendasta/contacts';
import {
  Conversation,
  ConversationChannel,
  GetMultiMessagesResponse,
  GetMultiParticipantsResponse,
  GlobalParticipantType,
  Message,
  MessageType,
  Participant,
  ParticipantType,
  PlatformLocation,
  SendMessageResponse,
} from '@vendasta/conversation';
import { GetMultiConversationDetailsResponse } from '@vendasta/conversation/lib/_internal/objects';
import {
  GetConfigurationResponse,
  GetMultiConversationDetailsResponseDetailedConversation,
  ListWidgetsResponse,
  UpsertConfigurationResponse,
} from '@vendasta/conversation/lib/_internal/objects/api';
import { Timestamp } from '@angular/fire/firestore';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { AlertOptions, ConversationChannelService } from './channels/conversation-channel.abstract';
import { ConversationService } from './state/conversation.service';
import { ALL_VIEW_ID } from './inbox.constants';
import { ConversationDetail, ConversationMessage, FirestoreConversation } from './interface/conversation.interface';
import { RouteConfig } from './interface/routes.interface';
import { ViewService } from './view.service';
import { ConversationStatelessService } from './conversation-stateless.service';
import { SYSTEM_ASSISTANT_ID } from '@galaxy/ai-assistant';

/**************** Constants Mocks ****************/
export const accountGroupIdMock = 'AG-D2MFHDH4LQ';

/**************** Object Mocks ****************/

export const participantCustomerMock = {
  internalParticipantId: 'CUSTOMER-123',
  name: 'Test Customer',
  email: '',
  phoneNumber: '**********',
  channel: 'sms',
  type: 'customer',
  participantId: 'PARTICIPANT-456',
  accountGroupId: accountGroupIdMock,
};

export const participantCustomerMock2 = {
  internalParticipantId: 'CUSTOMER-456',
  name: 'Test Customer',
  email: '',
  phoneNumber: '**********',
  channel: 'sms',
  type: 'customer',
  participantId: 'PARTICIPANT-456',
  accountGroupId: accountGroupIdMock,
};

export const participantIAMMock = {
  internalParticipantId: 'IAM-123',
  name: 'Test IAM',
  email: '',
  phoneNumber: '**********',
  channel: 'sms',
  type: 'iam_user',
  participantId: 'PARTICIPANT-123',
  accountGroupId: accountGroupIdMock,
};

export const participantAccountGroupMock = {
  internalParticipantId: '',
  name: 'Test IAM',
  email: '',
  phoneNumber: '**********',
  channel: 'sms',
  type: 'account_group',
  participantId: 'PARTICIPANT-AGID-123',
  accountGroupId: accountGroupIdMock,
};

export const messageCustomerMock = {
  id: 'XYZ',
  conversationId: 'CONVERSATION-123',
  messageId: 'MB-123',
  sender: participantCustomerMock,
  type: 'message',
  body: 'Test message',
  created: new Timestamp(20800, 0),
  updated: new Timestamp(20800, 0),
  deleted: new Timestamp(10800, 0),
} as ConversationMessage;

export const messageOrganizationMock = {
  id: 'XYZ',
  conversationId: 'CONVERSATION-123',
  messageId: 'MB-123',
  sender: participantAccountGroupMock,
  type: 'message',
  body: 'Test message',
  created: new Timestamp(20800, 0),
  updated: new Timestamp(20800, 0),
  deleted: new Timestamp(10800, 0),
} as ConversationMessage;

export const messageAccountGroupMock = {
  id: 'XYZ',
  conversationId: 'CONVERSATION-123',
  messageId: 'MB-123',
  sender: participantIAMMock,
  type: 'message',
  body: 'Test message',
  created: new Timestamp(20800, 0),
  updated: new Timestamp(20800, 0),
  deleted: new Timestamp(10800, 0),
} as ConversationMessage;

export const userMock = {
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'Vendasta',
  fullName: '',
  phone: '+1**********',
  subjectId: '',
  userId: 'U-ABC-123',
};

export const accountGroupMock = {
  accountGroupId: 'AG-ABC',
  partnerId: 'ABC',
  companyName: 'Test Company',
} as any;

export const conversationParticipantSenderMock = {
  participantId: '',
  internalParticipantId: 'U-123',
  partnerId: 'ABC',
  accountGroupId: 'AG-123',
  channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
} as Participant;

export const conversationParticipantRecipientMock = {
  participantId: '',
  internalParticipantId: 'CUSTOMER-123',
  partnerId: 'ABC',
  accountGroupId: 'AG-123',
  channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
} as Participant;

export const firestoreConversationMock = {
  id: 'xyx',
  conversationId: 'CONVERSATION-ABC',
  externalConversationId: '',
  channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
  latestMsgSentTime: Timestamp.fromDate(new Date('2021-10-25 08:00:00')),
  created: Timestamp.fromDate(new Date('2021-10-25 08:00:00')),
  updated: Timestamp.fromDate(new Date('2021-10-25 08:00:00')),
  deleted: Timestamp.fromDate(new Date('2021-10-25 08:00:00')),
  subjectParticipantsKey: 'account_group:AG-123|customer:CONTACT-123',
  subjectParticipants: [
    {
      participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
      internalParticipantId: 'AG-123',
    },
    {
      participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_CUSTOMER,
      internalParticipantId: 'CONTACT-123',
    },
  ],
} as FirestoreConversation;

export const getCustomerResponseMock = {
  customer: {
    customerId: 'CUSTOMER-123',
    accountGroupId: 'AG-ABC',
    partnerId: 'ABC',
    firstName: 'Test',
    lastName: 'Customer',
    address: '423 2nd Ave N',
    city: 'Saskatoon',
    state: 'SK',
    country: 'Canada',
    phoneNumbers: ['**********'],
    emailAddresses: ['<EMAIL>'],
    tags: [''],
    permissionToContact: true,
  },
} as unknown as GetCustomerResponse;

export const participantKeyIAMMock = {
  participantId: 'PARTICIPANT-123',
  internalParticipantId: 'U-123',
  partnerId: 'ABC',
  accountGroupId: 'AG-123',
  channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
  participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
} as Participant;

export const firestoreParticipantKeyIAMMock = {
  participantId: 'PARTICIPANT-123',
  internalParticipantId: 'U-123',
  partnerId: 'ABC',
  accountGroupId: 'AG-123',
  channel: 'sms',
  type: 'iam_user',
  email: '',
  name: '',
  phoneNumber: '',
};

export const firestoreParticipantIAMMock = {
  participantId: 'PARTICIPANT-123',
  internalParticipantId: 'U-123',
  partnerId: 'ABC',
  accountGroupId: 'AG-123',
  channel: 'sms',
  name: 'IAM user',
  email: '<EMAIL>',
  phoneNumber: '+**********',
  type: 'iam_user',
  created: Timestamp.fromDate(new Date()),
  updated: Timestamp.fromDate(new Date()),
  deleted: Timestamp.fromDate(new Date()),
};

export const firestoreParticipantCustomerMock = {
  participantId: 'PARTICIPANT-456',
  internalParticipantId: 'CUS-123',
  partnerId: 'ABC',
  accountGroupId: 'AG-123',
  channel: 'sms',
  name: 'Customer user',
  email: '<EMAIL>',
  phoneNumber: '+**********',
  type: 'customer',
  created: Timestamp.fromDate(new Date()),
  updated: Timestamp.fromDate(new Date()),
  deleted: Timestamp.fromDate(new Date()),
};

export const messageMock = {
  messageId: 'MES-123',
  externalMessageId: 'EXT-MES-123',
  conversationId: firestoreConversationMock.conversationId,
  participantId: firestoreParticipantIAMMock.participantId,
  type: MessageType.MESSAGE_TYPE_MESSAGE,
  body: 'Hi',
  error: undefined,
  created: new Date(),
  updated: new Date(),
  deleted: new Date(),
} as Message;

export const systemMessageMock = {
  messageId: 'MES-123',
  externalMessageId: 'EXT-MES-123',
  conversationId: firestoreConversationMock.conversationId,
  type: MessageType.MESSAGE_TYPE_SYSTEM,
  body: 'Hi',
  error: undefined,
  created: new Date(),
  updated: new Date(),
  deleted: new Date(),
} as Message;

export const conversationDetailsMock = {
  conversation: firestoreConversationMock,
  latestMessage: messageMock,
  participants: [firestoreParticipantIAMMock, firestoreParticipantCustomerMock],
} as ConversationDetail;

export const conversationMock = {
  id: 'xyz',
  conversationId: 'CONVERSATION-ABC',
  externalConversationId: '',
  participants: [
    {
      internalId: 'U-123',
      firstName: 'Test',
      lastName: '',
      phone: '**********',
      email: '',
      channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
      type: 'iam_user',
      participantId: 'PARTICIPANT-123',
    },
    {
      internalId: 'CUSTOMER-456',
      firstName: 'Test Customer',
      lastName: '',
      phone: '**********',
      email: '',
      channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
      type: 'iam_user',
      participantId: 'PARTICIPANT-456',
    },
  ],
  channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
  locations: [PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP],
  accountGroupId: accountGroupIdMock,
  partnerId: 'ABC',
};

const conversationMock2 = {
  conversationId: 'CONVERSATION-ABC',
  created: new Date('2022-11-10T00:00:00.000Z'),
  updated: new Date('2022-11-16T00:00:00.000Z'),
  deleted: new Date(),
  latestMsgSentTime: new Date('2022-11-16T00:00:00.000Z'),
  latestRelevantActivityTime: new Date('2022-11-16T00:00:00.000Z'),
} as Conversation;

export const mockConversationDetails = {
  conversation: conversationMock2,
  latestMessage: messageMock,
  participants: [conversationParticipantSenderMock, conversationParticipantRecipientMock],
} as GetMultiConversationDetailsResponseDetailedConversation;

/**************** Function Spy ****************/

/**************** Service Mocks ****************/

export const imageServiceMock = {
  getImageSrc: () => {
    return 'something';
  },
};

export const inboxServiceMock = {
  currentChannelId$: of(''),
  setup: () => {
    return;
  },
  isMobile: () => {
    return false;
  },
  searchContacts: () => {
    return;
  },
  canAccess$: of(true),
  canAccessSMS$: of(true),
  getInboxConfigByLoggedNamespace$: () => {
    return of({});
  },
  channelUnseen: () => {
    return false;
  },
  platformLocation: PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER,
  showInboxViews$: of(true),
  isBusinessApp: false,
  isPartnerCenter: true,
  SMSNumber$: of(''),
  buildTemplateTrackProperties: jest.fn(),
  displayA2pCard$: async () => {
    return true;
  },
  showAvailabilityMessageSettings$: of(true),
  canAccessWebChatSettings$: of(true),
  canAccessMessageTemplateSettingsCard$: of(true),
  iamUser$: of(''),
  hasPlatformChatAccess$: of(false),
  isConversationSummaryEnabled$: of(false),
  isSMSChannelAvailableForOrg$: of(true),
};

class ConversationServiceMock implements Partial<ConversationService> {
  availableChannels$ = of({
    availableChannels: [ConversationChannel.CONVERSATION_CHANNEL_SMS],
    preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
  });
  useConversationMicroservice$ = of(true);
  loadingConversations$ = of(false);
  currentFirestoreConversationId$ = of('');
  currentConversationDetail$ = of(conversationDetailsMock);
  accountGroupId$ = of(accountGroupIdMock);
  partnerId$ = of('ABC');
  currentAccountGroupId$ = of(accountGroupIdMock);
  conversations$ = of([conversationDetailsMock] as ConversationDetail[]);
  displayBadge$ = of(true);
  messages$ = of([] as ConversationMessage[]);
  aiResponderForConversation$ = of(null);
  setup = () => {
    return of(true);
  };
  getConversations = () => {
    return of([conversationDetailsMock]);
  };
  getPartnerId = () => {
    return 'ABC';
  };
  getAccountGroupId = () => {
    return accountGroupIdMock;
  };
  conversationUnseen = () => {
    return of(true);
  };
  getUseConversationMicroservice = () => {
    return true;
  };
  isMobile = () => {
    return false;
  };
  showEmptyState = () => {
    return of(false);
  };
  isYourExpert = () => {
    return false;
  };
  isUserImpersonated$ = of(false);
  getRoutes = jest.fn().mockReturnValue({
    root: `/account/location/${accountGroupIdMock}`,
    sendNewMessage: `/account/location/${accountGroupIdMock}/inbox/new-message`,
    a2pForm: `/account/location/${accountGroupIdMock}/settings/inbox/sms-registration`,
    useModal: false,
  });
  setSMSNumber = () => {
    return;
  };
  removeLastConversationId(): void {
    return;
  }
  showAlert: WritableSignal<boolean> = signal(false);
  displayAlertIfExists(): Observable<AlertOptions> {
    return new BehaviorSubject<AlertOptions>({
      title: 'Test',
      description: 'Test',
    });
  }
  getConversationChannel(): ConversationChannel {
    return ConversationChannel.CONVERSATION_CHANNEL_SMS;
  }
  setConversationLastSeen = jest.fn();
  currentParticipant$ = of(conversationParticipantSenderMock);
  setCurrentFirestoreConversationId = jest.fn();
  userId$ = of(userMock.userId);
  sendAndStageMessage = jest.fn();
  createConversation = jest.fn().mockReturnValue(
    of({
      conversation: {
        conversationId: 'CONVERSATION-123',
      },
    }),
  );
  escalateToSupport = jest.fn();
}

export const conversationServiceMock = new ConversationServiceMock();

class ConversationStatelessServiceMock implements Partial<ConversationStatelessService> {
  getMultiConversationDetails = jest.fn().mockReturnValue(of([]));
  getConversationDetail = jest.fn().mockReturnValue(of(null));
  createConversation = jest.fn().mockReturnValue(of(null));
  sendMessage = jest.fn().mockReturnValue(of(null));
}

export const conversationStatelessServiceMock = new ConversationStatelessServiceMock();

class ViewServiceMock implements Partial<ViewService> {
  selectedViewId$ = of(ALL_VIEW_ID);
}

export const viewServiceMock = new ViewServiceMock();

export const accountGroupServiceMock = {
  currentAccountGroup$: of({
    accountGroup: {
      marketId: 'arbitrary-market',
      accountGroupId: accountGroupIdMock,
    },
  }),
  currentAccountGroupId$: of(accountGroupIdMock),
  get: () => {
    return of({
      externalIdentifiers: {
        socialProfileId: '123',
      },
    });
  },
};

export const conversationApiServiceMock = {
  createConversation: () => {
    return;
  },
  sendMessage: () => {
    return of({
      workflowId: '123',
    } as SendMessageResponse);
  },
  getMessage: () => {
    return of({} as Message);
  },
  getMultiMessages: () => {
    return of({ messages: [] } as GetMultiMessagesResponse);
  },
  getMultiParticipants: () => {
    return of({ participants: [] } as GetMultiParticipantsResponse);
  },
  getMultiConversationDetails: () => {
    return of({ conversations: [] } as GetMultiConversationDetailsResponse);
  },
  listWidgets: () => {
    return of({
      widgets: [],
    } as ListWidgetsResponse);
  },
};

export const inboxApiServiceMock = {
  getConfiguration: () => {
    return of({
      configuration: {
        availabilityMessage: '',
        showAvailabilityMessage: false,
      },
    } as GetConfigurationResponse);
  },
  upsertConfiguration: () => {
    return of({ configuration: {} } as UpsertConfigurationResponse);
  },
};

export const snackbarServiceMock = {
  errorSnack: () => {
    return;
  },
  openErrorSnack: () => {
    return;
  },
};

export const participantServiceMock = {
  currentParticipant$: of(participantIAMMock),
  getParticipantsByKey: () => {
    return;
  },
  getMissingParticipantsInCache: () => {
    return [] as string[];
  },
  setParticipantsInCacheFromGetParticipantResp: () => {
    return;
  },
  getOrganizationId$: () => {
    return of('AG-123');
  },
  buildIAMUserParticipant: () => {
    return of(
      new Participant({
        internalParticipantId: 'test-user-id',
        partnerId: 'ABC',
        participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER,
        isSubjectParticipant: true,
      }),
    );
  },
  buildAIAssistantParticipant: () => {
    return of(
      new Participant({
        internalParticipantId: SYSTEM_ASSISTANT_ID,
        name: 'Aurora',
        participantType: ParticipantType.PARTICIPANT_TYPE_AI_ASSISTANT,
        isSubjectParticipant: false,
        profileImageUrl: '',
      }),
    );
  },
};

export const snowplowServiceMock = {
  trackClick: () => {
    return;
  },
};

export const cookieServiceMock = {
  get: (name: string) => {
    return name;
  },
  set: () => {
    return;
  },
};

export const featureFlagServiceMock = {
  batchGetStatus: () =>
    of({
      ['hide_convos_without_message']: true,
      ['inbox_tab_pcc']: true,
      ['inbox_platform_chat']: false,
    } as FeatureFlagStatusInterface),
};

export const analyticsServiceMock = {
  trackEvent: () => {
    return;
  },
};

export const ProductAnalyticsServiceMock = {};

export const environmentServiceMock = {
  getEnvironment: () => {
    return Environment.DEMO;
  },
  getEnvironmentString: () => {
    return 'demo';
  },
};

export const customerServiceMock = {
  getCustomer: () => {
    return of({} as GetCustomerResponse);
  },
};

export const partnerServiceMock = {
  getPartnerId: () => {
    return 'ABC';
  },
  partnerBrandName$: of(''),
};

export const translateServiceMock = {
  instant: (key: string) => {
    switch (key) {
      case 'INBOX.PREVIEW_PANE.CURRENT_USER':
        return 'You';
      case 'INBOX.PREVIEW_PANE.DELETED_USER':
        return 'Deleted User';
    }
  },
  get: (key: string) => {
    return of(key);
  },
  onTranslationChange: of(),
  onLangChange: of(),
  onDefaultLangChange: of(),
};

export const partnerServiceInterfaceMock = {
  getPartnerId: () => {
    return 'ABC';
  },
};

export const partnerAccountGroupServiceMock = {
  getPartnerAccountGroupMapping: () => {
    return of({ partnerId: 'DEF' });
  },
};

export const smsServiceMock = {
  getAccountInfo: () => {
    return;
  },
};

export const appConfigServiceMock = {
  config$: of({}),
};

export const navigationServiceMock = {
  setBreadcrumbs: () => {
    return;
  },
  toggleInboxScreen: () => {
    return;
  },
  toggleInboxSideContent: () => {
    return;
  },
};

export const currentUserServiceTokenMock = {
  currentUser: of({}),
};

export const userIdTokenMock = of(userMock.userId);
export const userIsImpersonatingTokenMock = of(true);
export const accountGroupIdTokenMock = of(accountGroupIdMock);
export const companyNameTokenMock = of(accountGroupMock.companyName);
export const partnerIdTokenMock = of(accountGroupMock.partnerId);
export const marketIdTokenMock = of(accountGroupMock.marketId);
export const platformLocationsEnabledTokenMock = of(['business_app']);
export const platformLocationTokenMock = 'business_app';
export const conversationChannelsEnabledTokenMock = of(['sms']);
export const routesTokenMock = of({ root: './', sendNewMessage: '' } as RouteConfig);

export const featureFlagTokenMock = of(true);
export const inboxTermsOfServiceTokenMock = {
  getUserAgreement: () => {
    return of({});
  },
  userAgreementIsAccepted: () => {
    return true;
  },
  isNewTermsOfService: () => {
    return true;
  },
  hasTermsOfService: () => {
    return true;
  },
  showDialogTermsOfService: () => {
    return true;
  },
  getTermsOfService: () => {
    return of({});
  },
};
export const countryTokenMock = of('CA');
export const geographicalStateTokenMock = of('');
export const partnerBrandNameTokenMock = of('Partner Brand');

export const conversationChannelServiceTokenMock = new Map<ConversationChannel, ConversationChannelService>();

export const inboxNotificationServiceMock = {
  inboxNotificationStatus$: of(true),
};
