import { ConversationChannel, Message, MessageStatus, MessageType, Participant } from '@vendasta/conversation';
import { ChatMessageStatus } from '@vendasta/galaxy/chat';
import { ConversationMessage, convertMediaJSONIntoMedia, Media } from './interface/conversation.interface';
import { Event } from '@vendasta/conversation/lib/_internal/objects/event';

const ID_PREFIX = 'MESSAGE-';

export function toFirestoreId(id: string): string {
  return id.replace(ID_PREFIX, '');
}

export function fromFirestoreId(id: string): string {
  if (id && id.startsWith(ID_PREFIX)) {
    return id;
  }
  return ID_PREFIX + id;
}

/**
 * convert API message to ConversationMessage
 * @param {Message} msg - the message coming from the API
 * @param {Participant} sender - the participant that sent msg
 * @return {ConversationMessage} - the converted ConversationMessage object
 */
export function convertMessageIntoConversationMessage(
  msg: Message,
  sender: Participant | undefined,
): ConversationMessage | null {
  if (msg == null) {
    return null;
  }

  return {
    id: toFirestoreId(msg.messageId),
    messageId: msg.messageId,
    conversationId: msg.conversationId,
    channel: msg.channel,
    body: msg.body,
    media:
      msg.media
        ?.map((media) => convertMediaJSONIntoMedia(media))
        ?.filter((media): media is Media => !!media && media !== 'not-json') ?? [],
    type: msg.type ?? MessageType.MESSAGE_TYPE_MESSAGE,
    created: msg.created,
    updated: msg.updated,
    deleted: msg.deleted,
    sender: msg.type !== MessageType.MESSAGE_TYPE_SYSTEM ? sender : undefined,
    sendStatus: msg.sendStatus,
    metadata: msg.metadata,
    externalMessageId: msg.externalMessageId,
    UIComponents: msg.uiComponents,
  };
}

export function mapMessageStatus(message: ConversationMessage): ChatMessageStatus {
  switch (message.sendStatus?.status) {
    case MessageStatus.MESSAGE_STATUS_READ:
      return 'read';
    case MessageStatus.MESSAGE_STATUS_DELIVERED:
    case MessageStatus.MESSAGE_STATUS_NOT_READ:
      return 'delivered';
    case MessageStatus.MESSAGE_STATUS_FAILED:
    case MessageStatus.MESSAGE_STATUS_UNDELIVERED:
      return 'failed';
    case MessageStatus.MESSAGE_STATUS_SENT:
      return 'sent';
    case MessageStatus.MESSAGE_STATUS_SENDING:
      return 'sending';
    default:
      return undefined;
  }
}

// returns true if the message is an inbound email
export function isInboundEmailMessage(message: ConversationMessage): boolean {
  const { channel, sendStatus } = message;

  // check for inbound is weak, but works for now
  return channel === ConversationChannel.CONVERSATION_CHANNEL_EMAIL && !sendStatus;
}

export function isBookingAvailabilityMessage(item: ConversationMessage | Event): boolean {
  return 'type' in item && item.type === MessageType.MESSAGE_TYPE_BOOKING_AVAILABILITY;
}
