import { ConversationChannel, Participant } from '@vendasta/conversation';
import { InboxItem } from './interface/conversation.interface';

export type ViewMode = 'modal' | 'sidebar';

export type MessageGroupable = MessageGroup | InboxItem;

export class MessageGroup {
  readonly id: string;
  readonly channel: ConversationChannel;
  messages: InboxItem[];
  readonly sender?: Participant;
  createdAt?: Date | null;

  constructor(
    messageId: string,
    sender: Participant | undefined,
    channel: ConversationChannel,
    createdAt?: Date | null,
  ) {
    this.id = `message-group-${messageId}`;
    this.messages = [];
    this.sender = sender;
    this.channel = channel;
    this.createdAt = createdAt;
  }

  addItem(item: InboxItem): void {
    this.messages.push(item);
  }

  get lastItem(): InboxItem {
    return this.messages?.[this.messages?.length - 1];
  }
}
