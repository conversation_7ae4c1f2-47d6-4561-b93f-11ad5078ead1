@if (type() === 'primary') {
  <button
    (click)="sendMessage()"
    data-action="clicked-start-chat"
    mat-flat-button
    color="primary"
    [disabled]="creatingConversation$ | async"
  >
    <ng-container [ngTemplateOutlet]="buttonContent"></ng-container>
  </button>
} @else if (type() === 'secondary') {
  <button
    (click)="sendMessage()"
    data-action="clicked-start-chat"
    mat-flat-button
    [disabled]="creatingConversation$ | async"
  >
    <ng-container [ngTemplateOutlet]="buttonContent"></ng-container>
  </button>
} @else if (type() === 'alternative') {
  <button
    (click)="sendMessage()"
    data-action="clicked-start-chat"
    mat-stroked-button
    [disabled]="creatingConversation$ | async"
  >
    <ng-container [ngTemplateOutlet]="buttonContent"></ng-container>
  </button>
} @else if (type() === 'text') {
  <button
    (click)="sendMessage()"
    data-action="clicked-start-chat"
    mat-stroked-button
    color="primary"
    class="remove-border"
    [disabled]="creatingConversation$ | async"
  >
    <ng-container [ngTemplateOutlet]="buttonContent"></ng-container>
  </button>
} @else if (type() === 'link') {
  <a (click)="sendMessage()" data-action="clicked-start-chat">
    <ng-container [ngTemplateOutlet]="buttonContent"></ng-container>
  </a>
} @else if (type() === 'menu') {
  <button
    (click)="sendMessage()"
    data-action="clicked-start-chat"
    mat-menu-item
    [disabled]="creatingConversation$ | async"
  >
    <ng-container [ngTemplateOutlet]="buttonContent"></ng-container>
  </button>
} @else if (type() === 'icon') {
  <button
    (click)="sendMessage()"
    data-action="clicked-start-chat"
    mat-icon-button
    color="primary"
    class="remove-border"
    [disabled]="creatingConversation$ | async"
    matTooltip="{{ 'INBOX.CHAT.SEND_MESSAGE' | translate }}"
  >
    <mat-icon>forum</mat-icon>
  </button>
}

<ng-template #buttonContent>
  <div class="row">
    @if (showIcon()) {
      <mat-icon class="icon-style" [ngClass]="{ 'icon-color': primaryIcon }">forum</mat-icon>
    }
    <glxy-button-loading-indicator [isLoading]="creatingConversation$ | async">
      <span #content>
        <ng-content></ng-content>
      </span>

      @if (content.childNodes.length === 0) {
        <span>
          {{ buttonText() || ('INBOX.CHAT.SEND_MESSAGE' | translate) }}
        </span>
      }
    </glxy-button-loading-indicator>
  </div>
</ng-template>
