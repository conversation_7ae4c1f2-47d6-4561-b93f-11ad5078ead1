import { Component, ElementRef, ViewChild, inject, input, output } from '@angular/core';
import { ConversationChannel, Participant } from '@vendasta/conversation';
import { BehaviorSubject } from 'rxjs';
import { InboxNavigationService } from '../../../core/src/lib/inbox-navigation.service';
import { SubjectParticipant } from '../../../core/src/lib/interface/conversation.interface';

interface SendMessageButtonConfig {
  currentParticipant?: Participant;
  subjectParticipants: SubjectParticipant[];
  channel: ConversationChannel;
  prefilledMessage?: string;
}
@Component({
  selector: 'inbox-button-send-message',
  templateUrl: './button-send-message.component.html',
  styleUrls: ['./button-send-message.component.scss'],
  standalone: false,
})
export class ButtonSendMessageComponent {
  private readonly inboxNav = inject(InboxNavigationService);

  config = input.required<SendMessageButtonConfig>();
  type = input<'primary' | 'secondary' | 'alternative' | 'link' | 'icon' | 'menu' | 'text'>('primary');
  primaryIcon = input<boolean | undefined>(undefined);
  buttonText = input<string>('');
  showIcon = input<boolean>(true);

  closeModal = output<void>();

  @ViewChild('content', { read: ElementRef, static: false }) content!: ElementRef;

  private creatingConversation$$ = new BehaviorSubject<boolean>(false);
  creatingConversation$ = this.creatingConversation$$.asObservable();

  sendMessage(): void {
    const configValue = this.config();
    if (!configValue) return;

    this.inboxNav.gotoConversation(
      configValue.subjectParticipants,
      configValue.currentParticipant as Participant,
      configValue.channel,
      configValue.prefilledMessage,
    );
    this.closeModal.emit();
  }
}
