import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { TranslationModule } from '../../ui/src/translation-module';
import { ButtonSendMessageComponent } from './button-send-message/button-send-message.component';

@NgModule({
  declarations: [ButtonSendMessageComponent],
  imports: [
    CommonModule,
    TranslateModule,
    MatIconModule,
    MatButtonModule,
    TranslationModule,
    MatTooltipModule,
    MatMenuModule,
    GalaxyButtonLoadingIndicatorModule,
  ],
  exports: [ButtonSendMessageComponent],
})
export class InboxButtonModule {}
