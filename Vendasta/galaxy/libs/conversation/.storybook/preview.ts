import { CommonModule } from '@angular/common';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { InjectionToken, importProvidersFrom } from '@angular/core';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { CoreModule, SessionService } from '@galaxy/core';
import { LexiconModule } from '@galaxy/lexicon';
import { Preview, applicationConfig } from '@storybook/angular';
import { ConversationChannel, PlatformLocation } from '@vendasta/conversation';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { of } from 'rxjs';
import { ConversationCoreModule } from '../core/src/lib/conversation-core.module';
import { ConversationConfig } from '../core/src/lib/interface/config.interface';
import i18n from '../ui/src/i18n/assets/en_devel.json';
import { InboxUploadService } from '../ui/src/pages/inbox-chat/components/inbox-upload-file/inbox-upload.service';

const sessionId = '';

const INBOX_CONFIG_FOR_CONVERSATION = new InjectionToken<ConversationConfig>('Token for Conversation config', {
  providedIn: 'root',
  factory: function (): ConversationConfig {
    const userId$ = of('U-123');
    const accountGroupId = 'AG-123';
    const accountGroupId$ = of(accountGroupId);
    const country$ = of('US');
    const geographicalState$ = of('CA');
    const partnerId$ = of('ABC');
    const partnerBrandName$ = of('ABC');
    const companyName$ = of('Some Company');
    const marketId$ = of('default');
    const featureFlag$ = of(true);
    const platformLocation = PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP;
    const conversationChannelsEnabled$ = of([
      ConversationChannel.CONVERSATION_CHANNEL_SMS,
      ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
    ]);
    const routes$ = of({
      root: `/account/location/${accountGroupId}`,
      sendNewMessage: `/account/location/${accountGroupId}/inbox/new-message`,
      a2pForm: `/account/location/${accountGroupId}/settings/inbox/sms-registration`,
    });
    const isImpersonating$ = of(false);
    const googleBusinessMessagesSupported$ = of(true);
    const hasAccessToInboxPage$ = of(true);
    const allowSmbChatWithPartner$ = of(true);

    return {
      userId$,
      accountGroupId$,
      country$,
      geographicalState$,
      partnerId$,
      partnerBrandName$,
      companyName$,
      featureFlag$,
      platformLocation,
      conversationChannelsEnabled$,
      routes$,
      marketId$,
      isImpersonating$,
      hasAccessToInboxPage$,
      allowSmbChatWithPartner$,
      googleBusinessMessagesSupported$,
    };
  },
});

const preview: Preview = {
  decorators: [
    applicationConfig({
      providers: [
        importProvidersFrom(CommonModule),
        importProvidersFrom(BrowserAnimationsModule),
        importProvidersFrom(CoreModule),
        importProvidersFrom(LexiconModule.forRoot()),
        provideHttpClient(withInterceptorsFromDi()),
        importProvidersFrom(MatSnackBarModule),
        importProvidersFrom(TranslateTestingModule.withTranslations('en', i18n)),
        importProvidersFrom(RouterTestingModule.withRoutes([])),
        {
          provide: SessionService,
          useValue: {
            getSessionId: () => of(sessionId),
          },
        },
        { provide: InboxUploadService, useValue: {} },
        importProvidersFrom(ConversationCoreModule.forRoot({ config: INBOX_CONFIG_FOR_CONVERSATION })),
      ],
    }),
  ],

  tags: ['autodocs', 'autodocs'],
};

export default preview;
