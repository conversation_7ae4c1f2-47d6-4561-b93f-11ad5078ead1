{"name": "campaign", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/campaign/src", "prefix": "campaign", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/campaign/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "weblate-upload": {"executor": "./tools/builders/weblate-upload:upload", "options": {"filePath": "libs/campaign/src/lib/assets/i18n/en_devel.json", "weblateProject": "common", "weblateComponent": "campaign"}}, "weblate-commit": {"executor": "./tools/builders/weblate-commit:commit", "options": {"weblateProject": "common", "weblateComponent": "campaign"}}}, "tags": ["scope:shared"]}