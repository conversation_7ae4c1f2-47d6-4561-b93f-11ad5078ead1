import { Component, Inject } from '@angular/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { CommonModule } from '@angular/common';
import { HistoryTableComponent } from './history-table/history-table.component';
import { TranslateModule } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';

import { CampaignConfig } from '../dependencies';
import { PAGE_ROUTES } from '../routing-constants';
import { map } from 'rxjs/operators';
import { combineLatest } from 'rxjs';
import { CONFIG_TOKEN } from '../../../shared/src/tokens';

@Component({
  selector: 'campaign-history',
  templateUrl: './campaign-history.component.html',
  imports: [CommonModule, GalaxyPageModule, TranslateModule, HistoryTableComponent],
})
export class CampaignHistoryComponent {
  // TODO: needs to be injected instead @MAEL-493

  previousUrl$ = combineLatest([
    this.config.basePath$,
    this.route.params.pipe(map((params) => params['campaignId'])),
  ]).pipe(map(([basePath, campaignId]) => `/${basePath}/${PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.DETAILS(campaignId)}`));

  constructor(
    private readonly route: ActivatedRoute,
    @Inject(CONFIG_TOKEN) private readonly config: CampaignConfig,
  ) {}
}
