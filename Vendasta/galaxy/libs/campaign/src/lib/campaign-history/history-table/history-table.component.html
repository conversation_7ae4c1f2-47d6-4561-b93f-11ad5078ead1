<mat-card>
  <glxy-table-container
    [dataSource]="dataSource"
    [columns]="columns"
    [pageSizeOptions]="[10, 20, 100]"
    [fullWidth]="true"
  >
    <va-filter-container
      content
      [filters]="campaignHistoryFilters.filters"
      [searchable]="false"
      [savedFilters]="filterService.savedFilters"
      (savedFilterSelected)="filterService.applySavedFilter($event)"
      (saveCurrentFilter)="filterService.saveCurrentFilter($event)"
      (removeSavedFilter)="filterService.removeSavedFilter($event)"
      [filterIsOpen]="true"
    >
      <div content>
        <table mat-table>
          <tr mat-header-row *matHeaderRowDef="[]"></tr>

          <ng-container matColumnDef="recipient">
            <th mat-header-cell *matHeaderCellDef>{{ 'CAMPAIGN_ACTIVITY.TABLE.HEADERS.RECIPIENT' | translate }}</th>
            <td mat-cell *matCellDef="let element" class="narrow-cell">
              {{ element.recipientEmail }}
            </td>
          </ng-container>
          <ng-container matColumnDef="step">
            <th mat-header-cell *matHeaderCellDef>{{ 'CAMPAIGN_ACTIVITY.TABLE.HEADERS.STEP_NAME' | translate }}</th>
            <td mat-cell *matCellDef="let element" class="narrow-cell">
              {{ element | eventName: campaignStepsMapping.getValue() | async }}
            </td>
          </ng-container>
          <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef>{{ 'CAMPAIGN_ACTIVITY.TABLE.HEADERS.EVENT_TYPE' | translate }}</th>
            <td mat-cell *matCellDef="let element" class="narrow-cell">
              {{ element.activity }}
            </td>
          </ng-container>
          <ng-container matColumnDef="date">
            <th mat-header-cell *matHeaderCellDef>{{ 'CAMPAIGN_ACTIVITY.TABLE.HEADERS.DATE' | translate }}</th>
            <td mat-cell *matCellDef="let element" class="narrow-cell">
              {{ element.time | glxyDate: dateFormat }}
            </td>
          </ng-container>

          <tr mat-row *matRowDef="let row; columns: []"></tr>
        </table>
        <glxy-empty-state *ngIf="dataSource.state.totalDataMembers === 0 && !dataSource.state.loading" [size]="'small'">
          <glxy-empty-state-hero>
            <mat-icon>done_all</mat-icon>
          </glxy-empty-state-hero>
          <p>{{ 'CAMPAIGN_ACTIVITY.TABLE.EMPTY' | translate }}</p>
        </glxy-empty-state>
      </div>
    </va-filter-container>
  </glxy-table-container>
</mat-card>
