import { EmailEvent, EmailEventService, EventType, SenderInterface } from '@vendasta/email';
import { PagedListRequestInterface, PagedResponseInterface, PaginatedAPIInterface } from '@vendasta/galaxy/table';
import { utc } from 'moment/moment';
import { combineLatest, Observable } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { CampaignHistoryFilterValues } from './history-table-filters';
import { CampaignEvent } from './history-table.component';

export const CAMPAIGN_KEY = 'campaign_id';
export const STEP_KEY = 'campaign_step_id';

interface ParameterServiceInterface {
  campaignId$: Observable<string>;
  sender$: Observable<SenderInterface>;
  campaignHistoryFilterValues$: Observable<CampaignHistoryFilterValues>;
}

export class ParameterService implements ParameterServiceInterface {
  readonly campaignId$: Observable<string>;
  readonly sender$: Observable<SenderInterface>;
  readonly campaignHistoryFilterValues$: Observable<CampaignHistoryFilterValues>;

  constructor(
    campaignID$: Observable<string>,
    sender$: Observable<SenderInterface>,
    campaignHistoryFilterValues$: Observable<CampaignHistoryFilterValues>,
  ) {
    this.campaignId$ = campaignID$;
    this.sender$ = sender$;
    this.campaignHistoryFilterValues$ = campaignHistoryFilterValues$;
  }
}

export class CampaignHistoryDataService implements PaginatedAPIInterface<CampaignEvent> {
  constructor(
    private paramsService: ParameterService,
    private eventService: EmailEventService,
  ) {}

  get(r: PagedListRequestInterface): Observable<PagedResponseInterface<CampaignEvent>> {
    return combineLatest([
      this.paramsService.campaignId$,
      this.paramsService.sender$,
      this.paramsService.campaignHistoryFilterValues$,
    ]).pipe(
      switchMap(([campaignId, sender, filters]) => {
        let attr = {
          key: CAMPAIGN_KEY,
          value: campaignId,
        };
        if (filters?.campaignStepId !== '') {
          attr = {
            key: STEP_KEY,
            value: filters.campaignStepId,
          };
        }
        return this.eventService.listEventsForAttribute(
          sender,
          attr,
          filters.emailActivities,
          r.pagingOptions?.cursor,
          r.pagingOptions?.pageSize,
        );
      }),
      map((r) => {
        const campaignEvents: CampaignEvent[] = [];
        r.events?.forEach((event: EmailEvent) => {
          campaignEvents.push({
            recipientEmail: event.emailAddress,
            emailId: event.emailId,
            activity: this.getDisplayTypeFromEnum(event.eventType),
            time: utc(event.eventTime),
          });
        });
        return {
          data: campaignEvents,
          pagingMetadata: {
            nextCursor: r?.cursor,
            hasMore: r?.hasMore,
          },
        };
      }),
      catchError(() => []),
    );
  }

  private getDisplayTypeFromEnum(value: EventType): string {
    switch (value) {
      case EventType.PROCESSED:
        return 'Processed';
      case EventType.DELIVERED:
        return 'Delivered';
      case EventType.OPENED:
        return 'Opened';
      case EventType.CLICKED:
        return 'Clicked';
      case EventType.BOUNCED:
        return 'Bounced';
      case EventType.DEFERRED:
        return 'Deferred';
      case EventType.DROPPED:
        return 'Dropped';
      case EventType.SPAMREPORT:
        return 'Spamreport';
      case EventType.UNSUBSCRIBED:
        return 'Unsubscribed';
      case EventType.RESUBSCRIBED:
        return 'Resubscribed';
      default:
        return 'Unknown';
    }
  }
}
