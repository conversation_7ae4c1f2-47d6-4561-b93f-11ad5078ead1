import { CommonModule } from '@angular/common';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CampaignService, GetterCampaignData } from '@vendasta/campaigns';
import { EmailEventService, MessageService } from '@vendasta/email';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxyDataSource, GalaxyTableModule } from '@vendasta/galaxy/table';
import { GalaxyColumnDef } from '@vendasta/galaxy/table/src/table.interface';
import { DateFormat } from '@vendasta/galaxy/utility/date-utils';
import { FilterService, VaFilterModule } from '@vendasta/uikit';
import { FilterModule } from '@vendasta/va-filter2';
import { Moment } from 'moment/moment';
import { BehaviorSubject, Observable, Subscription, map } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { CampaignConfig } from '../../dependencies';
import { PipesModule } from '../../pipes/pipes.module';
import { CampaignHistoryFilterValues, CampaignHistoryFilters } from './history-table-filters';
import { CampaignHistoryDataService, ParameterService } from './history-table.service';
import { CONFIG_TOKEN } from '../../../../shared/src/tokens';

export interface CampaignEvent {
  recipientEmail: string;
  emailId: string;
  activity: string;
  time: Moment;
}

const CAMPAIGN_ID_ROUTE_PARAM = 'campaignId';

@Component({
  selector: 'campaign-history-table',
  templateUrl: 'history-table.component.html',
  imports: [
    CommonModule, //
    MatCardModule,
    TranslateModule,
    VaFilterModule,
    MatIconModule,
    GalaxyTableModule,
    MatTableModule,
    GalaxyEmptyStateModule,
    GalaxyPipesModule,
    FilterModule,
    PipesModule,
  ],
  providers: [FilterService],
})
export class HistoryTableComponent implements OnInit, OnDestroy {
  dataSource: GalaxyDataSource<CampaignEvent>;
  readonly dateFormat = DateFormat.medium;
  readonly columns: GalaxyColumnDef[] = [
    {
      id: 'recipient',
      title: this.translateService.instant('CAMPAIGN_ACTIVITY.TABLE.HEADERS.RECIPIENT'),
    },
    {
      id: 'step',
      title: this.translateService.instant('CAMPAIGN_ACTIVITY.TABLE.HEADERS.STEP_NAME'),
    },
    {
      id: 'type',
      title: this.translateService.instant('CAMPAIGN_ACTIVITY.TABLE.HEADERS.EVENT_TYPE'),
    },
    {
      id: 'date',
      title: this.translateService.instant('CAMPAIGN_ACTIVITY.TABLE.HEADERS.DATE'),
    },
  ];

  private campaignId$: Observable<string>;
  public campaignHistoryFilters: CampaignHistoryFilters = new CampaignHistoryFilters(this.translateService);
  private campaignStepsLoadedSubject: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private subscriptions: Subscription[] = [];
  private campaignHistoryFilterValues$$: BehaviorSubject<CampaignHistoryFilterValues> =
    new BehaviorSubject<CampaignHistoryFilterValues>({
      campaignStepId: '',
      emailActivities: [1, 2, 3, 4, 5, 6, 7, 8, 9],
    });
  private campaignHistoryFilterValues$ = this.campaignHistoryFilterValues$$.asObservable();
  campaignStepsMapping: BehaviorSubject<Map<string, string>> = new BehaviorSubject<Map<string, string>>(
    new Map<string, string>(),
  );

  constructor(
    private readonly route: ActivatedRoute,
    private readonly translateService: TranslateService,
    private emailEventService: EmailEventService,
    public filterService: FilterService,
    private campaignService: CampaignService,
    private messageService: MessageService,
    @Inject(CONFIG_TOKEN) private readonly campaignConfig: CampaignConfig,
  ) {}

  ngOnInit(): void {
    this.campaignId$ = this.route.params.pipe(
      map((params) => {
        let campaignId = '';
        if (params[CAMPAIGN_ID_ROUTE_PARAM]) {
          campaignId = params[CAMPAIGN_ID_ROUTE_PARAM];
        }
        return campaignId;
      }),
    );
    this.subscriptions.push(
      this.route.params
        .pipe(switchMap((params) => this.campaignService.get(params[CAMPAIGN_ID_ROUTE_PARAM])))
        .subscribe((campaign: GetterCampaignData) => {
          const mapping = new Map<string, string>();
          campaign.campaignSchedule.forEach((v) => {
            mapping.set(v.campaignStepId, v.name);
          });
          this.campaignStepsMapping.next(mapping);
          this.campaignHistoryFilters.setCampaignStepTitlesFilterOptions(
            campaign.campaignSchedule.map((v) => ({
              campaignStepId: v.campaignStepId,
              title: v.name,
            })),
          );
          this.campaignStepsLoadedSubject.next(true);
        }),

      this.campaignStepsLoadedSubject.subscribe((loaded) => {
        if (loaded) {
          this.filterService.setFilters(this.campaignHistoryFilters.filters);
        }
      }),
      this.filterService.fieldValueChanges.subscribe(() => {
        this.campaignHistoryFilterValues$$.next(this.campaignHistoryFilters.getFilterValues());
      }),
    );
    const paramsService = new ParameterService(
      this.campaignId$,
      this.campaignConfig.sender$,
      this.campaignHistoryFilterValues$,
    );
    const service = new CampaignHistoryDataService(
      paramsService, //
      this.emailEventService,
    );
    this.dataSource = new GalaxyDataSource<CampaignEvent>(service);
  }

  public ngOnDestroy(): void {
    this.subscriptions.forEach((subscriptions) => subscriptions.unsubscribe());
  }
}
