import { TranslateService } from '@ngx-translate/core';
import { EventType } from '@vendasta/email';
import { CheckboxFilterField, Filters, FilterSection, SearchSelectFilterField } from '@vendasta/uikit';

export interface CampaignHistoryFilterValues {
  campaignStepId: string;
  emailActivities: EventType[];
}

export interface CampaignStepTitle {
  campaignStepId: string;
  title: string;
}

export class CampaignHistoryFilters {
  public constructor(private translate: TranslateService) {}

  private campaignStepFilter = new SearchSelectFilterField({
    name: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EVENT'),
    value: null,
    optionDisplayProperty: 'title',
    placeholder: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EVENT_PLACEHOLDER'),
  });

  private activityProcessedFilter = new CheckboxFilterField({
    name: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.PROCESSED'),
    value: false,
    id: 'processed',
    appliedTextPrefix: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EMAIL_ACTIVITY'),
  });

  private activityDeliveredFilter = new CheckboxFilterField({
    name: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.DELIVERED'),
    value: false,
    id: 'delivered',
    appliedTextPrefix: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EMAIL_ACTIVITY'),
  });

  private activityOpenedFilter = new CheckboxFilterField({
    name: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.OPENED'),
    value: false,
    id: 'opened',
    appliedTextPrefix: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EMAIL_ACTIVITY'),
  });

  private activityClickedThroughFilter = new CheckboxFilterField({
    name: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.CLICKED_THROUGH'),
    value: false,
    id: 'clicked',
    appliedTextPrefix: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EMAIL_ACTIVITY'),
  });

  private activityBouncedFilter = new CheckboxFilterField({
    name: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.BOUNCED'),
    value: false,
    id: 'bounced',
    appliedTextPrefix: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EMAIL_ACTIVITY'),
  });

  private activityDroppedFilter = new CheckboxFilterField({
    name: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.DROPPED'),
    value: false,
    id: 'dropped',
    appliedTextPrefix: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EMAIL_ACTIVITY'),
  });

  private activityUnsubscribeFilter = new CheckboxFilterField({
    name: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.UNSUBSCRIBE'),
    value: false,
    id: 'unsubscribed',
    appliedTextPrefix: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EMAIL_ACTIVITY'),
  });

  private activitySpamFilter = new CheckboxFilterField({
    name: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.SPAM_REPORT'),
    value: false,
    id: 'spamreport',
    appliedTextPrefix: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EMAIL_ACTIVITY'),
  });

  public filters: Filters = new Filters('Filters', [
    new FilterSection({
      title: 'Event',
      type: 'or',
      fields: [this.campaignStepFilter],
    }),
    new FilterSection({
      title: this.translate.instant('CAMPAIGN_ACTIVITY.TABLE.FILTERS.EMAIL_ACTIVITY'),
      type: 'or',
      fields: [
        this.activityProcessedFilter,
        this.activityDeliveredFilter,
        this.activityOpenedFilter,
        this.activityClickedThroughFilter,
        this.activityBouncedFilter,
        this.activitySpamFilter,
        this.activityUnsubscribeFilter,
        this.activityDroppedFilter,
      ],
    }),
  ]);

  public setCampaignStepTitlesFilterOptions(titles: CampaignStepTitle[]): void {
    this.campaignStepFilter.setOptions(titles);
  }

  public getFilterValues(): CampaignHistoryFilterValues {
    let campaignStepId = '';
    if (this.filters.fields && this.filters.fields[0].value) {
      campaignStepId = this.filters.fields[0].value.campaignStepId || '';
    }
    return {
      campaignStepId: campaignStepId,
      emailActivities: this.getSelectedEmailActivities(),
    };
  }

  private getSelectedEmailActivities(): EventType[] {
    const selectedEmailActivities: EventType[] = [];
    const emailActivityFilters: CheckboxFilterField[] = [
      this.activityBouncedFilter,
      this.activityClickedThroughFilter,
      this.activityDeliveredFilter,
      this.activityDroppedFilter,
      this.activityOpenedFilter,
      this.activitySpamFilter,
      this.activityUnsubscribeFilter,
      this.activityProcessedFilter,
    ];

    emailActivityFilters.forEach((filter) => {
      if (filter.value) {
        selectedEmailActivities.push(this.getEventTypeEnum(filter.id));
      }
    });

    if (selectedEmailActivities.length === 0) {
      return [1, 2, 3, 4, 5, 6, 7, 8, 9];
    }

    return selectedEmailActivities;
  }

  private getEventTypeEnum(value: string): EventType {
    switch (value) {
      case 'processed':
        return EventType.PROCESSED;
      case 'delivered':
        return EventType.DELIVERED;
      case 'opened':
        return EventType.OPENED;
      case 'clicked':
        return EventType.CLICKED;
      case 'bounced':
        return EventType.BOUNCED;
      case 'dropped':
        return EventType.DROPPED;
      case 'spamreport':
        return EventType.SPAMREPORT;
      case 'unsubscribed':
        return EventType.UNSUBSCRIBED;
      case 'deferred':
        return EventType.DEFERRED;
      default:
        return EventType.EMPTY;
    }
  }

  public setFilters(filters: Filters): void {
    this.filters = filters;
  }
}
