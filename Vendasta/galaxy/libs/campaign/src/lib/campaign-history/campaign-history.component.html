<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button [previousPageUrl]="previousUrl$ | async"></glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title [updateTabTitle]="true">
      {{ 'CAMPAIGN_ACTIVITY.TITLE' | translate }}
    </glxy-page-title>
  </glxy-page-toolbar>

  <glxy-page-wrapper [maxWidth]="2000">
    <ng-container>
      <campaign-history-table></campaign-history-table>
    </ng-container>
  </glxy-page-wrapper>
</glxy-page>
