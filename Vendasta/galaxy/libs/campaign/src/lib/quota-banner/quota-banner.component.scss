@use 'design-tokens' as *;

.usage-banner {
  margin: 0 0 16px;
}

.usage-banner-content {
  display: flex;
  align-items: center;
}

.usage-banner-info {
  display: flex;
  align-items: center;
  flex-grow: 2;
  font-size: $font-preset-3-size;
  width: 100%;
}

.usage-progress-bar {
  margin-left: $spacing-2;
  margin-right: $spacing-2;
  min-width: 65%;
}

.usage-text {
  font-size: $font-preset-4-size;
  min-width: 12%;
  text-align: right;
  padding-right: $spacing-3;
}
.quota-title {
  min-width: 23%;
}
.bar-wrapper {
  display: flex;
  width: 100%;
  align-items: center;
}
.usage-banner-cta {
  padding: 0 0 0 $spacing-3;
}

:host .usage-progress-bar-over-quota ::ng-deep .mdc-linear-progress__bar-inner {
  border-color: $warn-icon-color;
}

@media (max-width: $media--tablet-large-minimum) {
  .usage-banner-content {
    flex-direction: column;
    justify-content: center;
  }
  .usage-banner-cta {
    margin-top: $spacing-2;
    padding: 0;
  }
  .bar-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
  }
  .quota-title {
    padding-bottom: $spacing-2;
  }
  .usage-text {
    padding: $spacing-2 0 0 0;
  }
}
