<ng-container *ngIf="emailQuota$ | async as usage">
  <mat-card class="usage-banner" *ngIf="usage.limit >= 0">
    <mat-card-content class="usage-banner-content">
      <div class="usage-banner-info">
        <div class="bar-wrapper">
          <span class="quota-title">{{ 'QUOTA_BANNER.USAGE.BANNER_TITLE' | translate }}</span>
          <mat-progress-bar
            class="usage-progress-bar"
            mode="determinate"
            [value]="usage.limit === 0 ? 100 : (usage.count / usage.limit) * 100"
            [ngClass]="{ 'usage-progress-bar-over-quota': usage.count >= usage.limit }"
          ></mat-progress-bar>
          <span class="usage-text">
            {{
              'QUOTA_BANNER.USAGE.BANNER_OF'
                | translate: { usage: (usage.count ? usage.count : 0) | number, quota: usage.limit | number }
            }}
          </span>
        </div>
      </div>
      <div *ngIf="(isBusinessSender$ | async) === false" class="usage-banner-cta">
        <button mat-stroked-button [routerLink]="config.actionURL">
          <mat-icon>trending_up</mat-icon>
          {{ 'QUOTA_BANNER.USAGE.BANNER_CTA' | translate }}
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</ng-container>

<ng-container *ngIf="smsQuota$ | async as smsUsage">
  <mat-card class="usage-banner" *ngIf="smsUsage.limit >= 0">
    <mat-card-content class="usage-banner-content">
      <div class="usage-banner-info">
        <div class="bar-wrapper">
          <span class="quota-title">{{ 'QUOTA_BANNER.USAGE.SMS_BANNER_TITLE' | translate }}</span>
          <mat-progress-bar
            class="usage-progress-bar"
            mode="determinate"
            [value]="smsUsage.limit === 0 ? 100 : (smsUsage.count / smsUsage.limit) * 100"
            [ngClass]="{ 'usage-progress-bar-over-quota': smsUsage.count >= smsUsage.limit }"
          ></mat-progress-bar>
          <span class="usage-text">
            {{
              'QUOTA_BANNER.USAGE.BANNER_OF'
                | translate: { usage: (smsUsage.count ? smsUsage.count : 0) | number, quota: smsUsage.limit | number }
            }}
          </span>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</ng-container>
