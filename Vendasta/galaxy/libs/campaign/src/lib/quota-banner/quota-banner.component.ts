import { Component, Inject } from '@angular/core';
import { QUOTA_BANNER_CONFIG, QUOTA_CONFIG } from '../dependencies';
import { switchMap, map } from 'rxjs/operators';
import { BehaviorSubject, Observable } from 'rxjs';
import { CampaignQuotaCategory, Period, QuotaService, SenderType, GetCampaignQuotaResponse } from '@vendasta/campaigns';

interface CampaignQuota {
  count: number;
  limit: number;
}

@Component({
  selector: 'campaign-quota-banner',
  templateUrl: './quota-banner.component.html',
  styleUrls: ['./quota-banner.component.scss'],
  standalone: false,
})
export class QuotaBannerComponent {
  emailQuota$: Observable<CampaignQuota>;
  smsQuota$: Observable<CampaignQuota>;
  isBusinessSender$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  constructor(
    private quotaService: QuotaService,
    @Inject(QUOTA_BANNER_CONFIG) public readonly config: QUOTA_CONFIG,
  ) {
    this.config.sender$.subscribe((sender) => {
      if (sender.type === SenderType.SENDER_TYPE_BUSINESS) {
        this.isBusinessSender$.next(true);
      }
    });

    this.emailQuota$ = config.sender$.pipe(
      switchMap((sndr) => {
        return this.quotaService.getCampaignQuota(
          sndr,
          CampaignQuotaCategory.CAMPAIGN_QUOTA_TYPE_EMAIL,
          Period.PERIOD_MONTH,
        );
      }),
      map((resp) => this.getQuotaFromResponse(resp)),
    );
    this.smsQuota$ = config.sender$.pipe(
      switchMap((sndr) => {
        return this.quotaService.getCampaignQuota(
          sndr,
          CampaignQuotaCategory.CAMPAIGN_QUOTA_TYPE_SMS,
          Period.PERIOD_MONTH,
        );
      }),
      map((resp) => this.getQuotaFromResponse(resp)),
    );
  }

  private getQuotaFromResponse(resp: GetCampaignQuotaResponse): CampaignQuota {
    const now = new Date();
    const isQuotaUpToDate =
      resp?.updated.getMonth() === now.getMonth() && resp?.updated.getFullYear() === now.getFullYear();

    return { count: isQuotaUpToDate ? resp.count : 0, limit: resp.limit };
  }
}
