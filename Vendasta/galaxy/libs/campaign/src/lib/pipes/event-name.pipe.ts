import { Pipe, PipeTransform } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { STEP_KEY } from '../campaign-history/history-table/history-table.service';
import { CampaignEvent } from '../campaign-history/history-table/history-table.component';
import { MessageService } from '@vendasta/email';

@Pipe({
  name: 'eventName',
  standalone: false,
})
export class EventNamePipe implements PipeTransform {
  constructor(private messageService: MessageService) {}

  transform(e: CampaignEvent, m: Map<string, string>): Observable<string> {
    return this.messageService.getMessage(e.emailId).pipe(
      map((message) => {
        const stepId = message.attributes.find((a) => a.key === STEP_KEY)?.value ?? '';
        return m.get(stepId) || '';
      }),
    );
  }
}
