<h2 mat-dialog-title>
  {{ 'CAMPAIGNS.CREATE_CAMPAIGN' | translate }}
</h2>
<form (ngSubmit)="create()" [formGroup]="formGroup">
  <mat-dialog-content>
    <mat-form-field appearance="outline">
      <mat-label>
        {{ 'CAMPAIGNS.CAMPAIGN_NAME' | translate }}
      </mat-label>
      <input matInput [formControl]="campaignNameControl" [errorStateMatcher]="errorMatcher" />
    </mat-form-field>
    <mat-hint *ngIf="campaignNameControl.hasError('required')">
      {{ 'CAMPAIGNS.NAME.CREATE' | translate }}
    </mat-hint>
    <mat-error *ngIf="campaignNameControl.value.length > 100">
      {{ 'CAMPAIGNS.NAME.TOO_LONG' | translate }}
    </mat-error>
    <mat-error *ngIf="campaignNameControl.value.length < 100 && !campaignNameControl.hasError('required')">
      <br />
    </mat-error>
  </mat-dialog-content>
  <mat-dialog-actions>
    <button mat-button color="primary" type="button" [mat-dialog-close]>
      {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" type="submit" [disabled]="!campaignNameControl.valid" (click)="create()">
      {{ 'COMMON.ACTION_LABELS.CREATE' | translate }}
    </button>
  </mat-dialog-actions>
</form>
