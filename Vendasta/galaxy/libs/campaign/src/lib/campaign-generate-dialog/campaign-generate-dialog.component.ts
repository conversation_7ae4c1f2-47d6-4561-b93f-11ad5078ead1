import { Component } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ControlErrorStateMatcher } from './controlErrorStateMatcher';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

@Component({
  selector: 'campaign-generate-dialog',
  templateUrl: './campaign-generate-dialog.component.html',
  styleUrls: ['./campaign-generate-dialog.component.scss'],
  imports: [
    CommonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule,
    ReactiveFormsModule,
  ],
})
export class CampaignGenerateDialogComponent {
  campaignNameControl = new UntypedFormControl('', [Validators.required, Validators.maxLength(100)]);
  errorMatcher = new ControlErrorStateMatcher();
  formGroup = this.fb.group({
    name: [this.campaignNameControl],
  });

  constructor(
    public dialogRef: MatDialogRef<CampaignGenerateDialogComponent>,
    private posthogService: ProductAnalyticsService,
    private fb: UntypedFormBuilder,
  ) {}

  create(): void {
    this.posthogService.trackEvent('user-clicked-create-campaign-in-modal', 'create-campaign-workflow', 'click');
    this.dialogRef.close(this.campaignNameControl.value);
  }
}
