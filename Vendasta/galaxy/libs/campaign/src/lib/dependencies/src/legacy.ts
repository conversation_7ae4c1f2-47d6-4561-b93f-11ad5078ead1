import { Observable, map } from 'rxjs';
import { SenderInterface, SenderType } from '@vendasta/campaigns';

export function SenderTypeFromBusinessId(businessId$: Observable<string>): Observable<SenderInterface> {
  return businessId$.pipe(
    map((v) => ({
      id: v,
      type: SenderType.SENDER_TYPE_BUSINESS,
    })),
  );
}

export function SenderTypeFromPartnerId(partnerId: Observable<string>): Observable<SenderInterface> {
  return partnerId.pipe(
    map((v) => ({
      id: v,
      type: SenderType.SENDER_TYPE_PARTNER,
    })),
  );
}

export type RecipientType = string;

export class RecipientTypes {
  public static readonly IAMUser: RecipientType = 'RecipientTypeIAMUser';
  public static readonly CampaignRecipient: RecipientType = 'RecipientTypeCampaignRecipient';
}

export function stepDataForPartnerEmail(
  recipientAccountGroupId: string,
  recipientType: RecipientType,
  recipientId: string,
): { [key: string]: string } {
  return {
    account_group_id: recipientAccountGroupId,
    recipient_external_type: recipientType,
    recipient_external_id: recipientId,
  };
}

export function stepDataForBusinessEmail(senderAccountGroupId: string): { [key: string]: string } {
  return {
    account_group_id: senderAccountGroupId,
  };
}
