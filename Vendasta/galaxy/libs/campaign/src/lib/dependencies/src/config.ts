import { Observable } from 'rxjs';
import { SenderInterface } from '@vendasta/campaigns';

export type campaignsTableFilterSection = 'Tags' | 'Status';

export interface CampaignConfig {
  // The (relative) prefix to include in URLs for routing. E.g. if the
  // complete URL is 'https://example.com/sick/campaignz/CAMPAIGN-123/details'
  // then the basePath is 'sick/campaignz' because the remainder of the path
  // was constructed by this lib.  See PAGE_ROUTES.
  basePath$: Observable<string>;
  // The owner of the campaigns.
  sender$: Observable<SenderInterface>;
  // The ID of the user who is currently using the app
  userId$: Observable<string>;
  // The location of the account group
  location$: Observable<string>;
  // The URL to get to Campaigns Pro / SMS addons in the Marketplace
  productBasePath$: Observable<string>;
  // The URL to find SMS settings at
  smsSettingsPath$?: Observable<string>;
  // Service to check if campaigns is properly configured
  // If not properly configured, it will open a modal
  campaignsConfigurationService$?: Observable<{
    isProperlyConfigured(): Promise<boolean>;
    openConfigurationModal(): Promise<boolean>;
  }>;
  enabledFilterSections: campaignsTableFilterSection[];
}
