import { Component, InjectionToken, Type } from '@angular/core';
import { CampaignConfig } from './config';
import { WorkStates } from '@vendasta/rx-utils/work-state';
import { SaveResult } from '../../campaign-email-builder/page/shared/email-template-saver';
import { EmailContentData, EmailPreviewHydrationData } from '@galaxy/email-ui/email-builder';
import { combineLatest, Observable } from 'rxjs';
import { AccountGroup } from '@vendasta/account-group/lib/_internal/objects/api';
import { map } from 'rxjs/operators';
import { SenderInterface } from '@vendasta/campaigns';

export const PREVIEW_DATA_TOKEN = new InjectionToken('com.vendasta.campaigns.preview.data');
export type PREVIEW_DATA_TYPE = Observable<{ [key: string]: string }>;

export const PREVIEW_DATA_SELECTOR_COMPONENT_TOKEN = new InjectionToken(
  'com.vendasta.campaigns.preview.data.selector_component',
);
export type PREVIEW_DATA_SELECTOR_COMPONENT_TYPE = Type<Component & PreviewDataProvider>;

export interface PreviewDataProvider {
  data$: PREVIEW_DATA_TYPE;
}

export interface TemplateExporter {
  workState: WorkStates<SaveResult>;

  updateContentData(update: EmailContentData): void;

  updateHTML(html: string): void;
}

export function buildHydrationData(
  accountGroup$: Observable<AccountGroup>,
  campaignConfig: CampaignConfig,
): Observable<EmailPreviewHydrationData> {
  return combineLatest([accountGroup$, campaignConfig.userId$]).pipe(
    map(([accountGroup, userId]: [AccountGroup, string]) => ({
      business: {
        accountGroupId: accountGroup.accountGroupId,
        name: accountGroup.napData?.companyName,
        hasSnapshot: accountGroup.snapshotReports?.snapshots?.length > 0,
        hasSalesperson: accountGroup.accountGroupExternalIdentifiers?.salesPersonId !== '',
      },
      partnerID: '',
      marketID: '',
      userID: userId,
      locale: 'en',
      useFakeData: false,
    })),
  );
}

export const SUPPORTED_LOCALES_TOKEN = 'com.vendasta.campaigns.supported_locales';
export type LOCALE_DATA_TYPE = Observable<{ value: string; name: string }[]>;

export interface QuotaConfig {
  sender$: Observable<SenderInterface>;
  actionURL?: string;
}

export type QUOTA_CONFIG = QuotaConfig;
export const QUOTA_BANNER_CONFIG = 'campaign.quota.banner.config';

export enum AddActionType {
  New = 'add-new',
  Existing = 'add-existing',
  Snapshot = 'add-snapshot',
  SMS = 'add-sms',
}

export type Sender = SenderInterface;
export const CAMPAIGN_SENDER_TOKEN = 'campaign.sender';
