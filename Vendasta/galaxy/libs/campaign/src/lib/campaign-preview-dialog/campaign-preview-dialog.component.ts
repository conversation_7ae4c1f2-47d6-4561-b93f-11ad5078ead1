import { CommonModule } from '@angular/common';
import { Component, Inject, OnDestroy, OnInit, Optional, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { AccountGroup } from '@galaxy/account-group';
import { TranslateModule } from '@ngx-translate/core';
import { CampaignService, CampaignStepType, GetterCampaignData } from '@vendasta/campaigns';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyEmailViewerModule } from '@vendasta/galaxy/email-viewer';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { GalaxyDefaultProviderOverrides } from '@vendasta/galaxy/provider-default-overrides';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BehaviorSubject, EMPTY, Observable, Subscription, combineLatest, firstValueFrom, of } from 'rxjs';
import { filter, map, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import {
  PREVIEW_DATA_SELECTOR_COMPONENT_TOKEN,
  PREVIEW_DATA_SELECTOR_COMPONENT_TYPE,
  PREVIEW_DATA_TOKEN,
  PREVIEW_DATA_TYPE,
} from '../dependencies';
import { CampaignPreviewDataSelectorDirective } from './campaign-preview-data-selector.directive';
import { EventDayPipe } from './event-day.pipe';
import { NonEmailCampaignStep, NonEmailCampaignSteps } from './non-email-steps';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../shared/src/tokens';

export interface PreviewCampaignDialogData {
  campaignId: string;
  templateId?: string;
}

export interface PreviewData {
  html: string;
  name: string;
  subject: string;
}

@Component({
  templateUrl: './campaign-preview-dialog.component.html',
  styleUrls: ['./campaign-preview-dialog.component.scss'],
  providers: [GalaxyDefaultProviderOverrides],
  imports: [
    CommonModule,
    TranslateModule,
    MatProgressSpinnerModule,
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    MatDialogModule,
    MatSelectModule,
    GalaxyEmailViewerModule,
    GalaxyInputModule,
    GalaxyAlertModule,
    EventDayPipe,
    CampaignPreviewDataSelectorDirective,
    GalaxyButtonLoadingIndicatorModule,
  ],
})
export class CampaignPreviewDialogComponent implements OnInit, OnDestroy {
  public static openAndAddToURL(
    dialog: MatDialog,
    router: Router,
    activatedRoute: ActivatedRoute,
    campaignId: string,
    templateId?: string,
  ): MatDialogRef<CampaignPreviewDialogComponent> {
    const data: PreviewCampaignDialogData = {
      campaignId: campaignId,
      templateId: templateId,
    };
    const dialogRef = dialog.open(CampaignPreviewDialogComponent, {
      data: data,
    });
    this.addToURL(router, activatedRoute, campaignId);
    dialogRef
      .afterClosed()
      .pipe(take(1))
      .subscribe(() => this.removeFromURL(router, activatedRoute));
    return dialogRef;
  }

  private static addToURL(router: Router, activatedRoute: ActivatedRoute, campaignId: string): void {
    router.navigate([], {
      relativeTo: activatedRoute,
      queryParams: {
        previewForCampaignId: campaignId,
      },
      queryParamsHandling: 'merge',
      skipLocationChange: true,
    });
  }

  private static removeFromURL(router: Router, activatedRoute: ActivatedRoute): void {
    router.navigate([], {
      relativeTo: activatedRoute,
      queryParams: {
        previewForCampaignId: null,
      },
      queryParamsHandling: 'merge',
      skipLocationChange: true,
    });
  }

  @ViewChild(CampaignPreviewDataSelectorDirective, { static: true }) stepDataHost:
    | CampaignPreviewDataSelectorDirective
    | undefined;

  campaignDetails$: Observable<GetterCampaignData> = EMPTY;

  private selectedStepIndex$$: BehaviorSubject<number> = new BehaviorSubject(0);
  private selectedStepIndex$: Observable<number> = this.selectedStepIndex$$.asObservable();

  previewData$: Observable<PreviewData> = EMPTY;

  sendingEmails = false;

  campaignStepType = CampaignStepType;
  currentStepType = CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL;
  nonEmailStepDetails: NonEmailCampaignStep | undefined;
  loadingEmail = false;

  subscriptions: Subscription[] = [];

  constructor(
    public dialogRef: MatDialogRef<CampaignPreviewDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PreviewCampaignDialogData,
    private readonly snackbarService: SnackbarService,
    private readonly campaignsSDK: CampaignService,
    @Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE,
    @Optional()
    @Inject(PREVIEW_DATA_SELECTOR_COMPONENT_TOKEN)
    private readonly dataComponent?: PREVIEW_DATA_SELECTOR_COMPONENT_TYPE,
    @Optional()
    @Inject(PREVIEW_DATA_TOKEN)
    private data$: PREVIEW_DATA_TYPE = EMPTY,
  ) {
    if (!this.dataComponent && !this.data$) {
      throw new Error(
        `No provider for: ` +
          `[${PREVIEW_DATA_TOKEN}, ${PREVIEW_DATA_SELECTOR_COMPONENT_TOKEN}]. ` +
          `Exactly one must be provided.`,
      );
    }
    if (this.dataComponent && this.data$) {
      throw new Error(
        `A provider is present for both: ` +
          `[${PREVIEW_DATA_TOKEN}, ${PREVIEW_DATA_SELECTOR_COMPONENT_TOKEN}]. ` +
          `Exactly one must be provided.`,
      );
    }

    this.campaignDetails$ = this.campaignsSDK
      .get(this.data.campaignId)
      .pipe(shareReplay({ bufferSize: 1, refCount: true }));
  }

  ngOnInit(): void {
    let stepData$: PREVIEW_DATA_TYPE;
    if (this.dataComponent) {
      const component = this.stepDataHost!.viewContainerRef.createComponent(this.dataComponent);
      stepData$ = component.instance.data$;
    } else {
      stepData$ = this.data$!;
    }
    this.data$ = stepData$.pipe(shareReplay({ bufferSize: 1, refCount: true }));

    this.previewData$ = combineLatest([
      this.campaignDetails$ || EMPTY, //
      this.selectedStepIndex$,
      this.data$,
    ]).pipe(
      filter(([details]) => !!details),
      tap(() => (this.loadingEmail = true)),
      switchMap(([details, index, stepData]) => {
        // No steps found, output that
        if (!details.campaignSchedule[index]) {
          this.loadingEmail = false;
          return of({} as PreviewData);
        }
        // Non email displaying step
        if (details.campaignSchedule[index].stepType !== CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL) {
          this.loadingEmail = false;
          this.currentStepType = CampaignStepType.CAMPAIGN_STEP_TYPE_UNSPECIFIED;
          this.nonEmailStepDetails = NonEmailCampaignSteps[details.campaignSchedule[index].stepType];
          this.nonEmailStepDetails.title = details.campaignSchedule[index].name;
          return of({} as PreviewData);
        }
        this.currentStepType = CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL;
        return this.campaignsSDK
          .getEmailStepContent(details.campaignId, details.campaignSchedule[index].campaignStepId, stepData)
          .pipe(
            map(
              (resp) =>
                ({
                  name: details.campaignSchedule[index].name,
                  subject: resp.subject,
                  html: resp.html,
                }) as PreviewData,
            ),
          );
      }),
      tap(() => {
        this.loadingEmail = false;
      }),
      shareReplay(1),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  stepChanged(index: number): void {
    this.selectedStepIndex$$.next(index);
  }

  displayFn(accountGroup?: AccountGroup): string | undefined {
    return accountGroup ? accountGroup.napData.companyName : 'Generic account';
  }

  async sendTestEmail(): Promise<void> {
    const details = await firstValueFrom(this.campaignDetails$);
    this.dialogRef.disableClose = true;

    // const selectedTemplateId = await firstValueFrom(this.selectedStepIndex$);
    // const currentTemplate = this.data.templateId || details.campaignSchedule[selectedTemplateId].templateId;
    this.sendingEmails = true;
    const sender = await firstValueFrom(this.config.sender$);
    const currentStepIndex = await firstValueFrom(this.selectedStepIndex$);
    const currentStepId = details.campaignSchedule[currentStepIndex].campaignStepId;
    const currentUserId = await firstValueFrom(this.config.userId$);
    const stepData = await firstValueFrom(this.data$);
    await firstValueFrom(
      this.campaignsSDK.sendStepTestEmail(sender, details.campaignId, currentStepId, currentUserId, stepData),
    )
      .catch((err) => {
        this.snackbarService.openErrorSnack('CAMPAIGN_GENERIC.TEST_EMAIL.FAILURE', {
          duration: 5000,
          interpolateTranslateParams: {
            message: err.error.message,
          },
        });
        throw err;
      })
      .then(() => {
        this.snackbarService.openSuccessSnack('CAMPAIGN_GENERIC.TEST_EMAIL.SUCCESS');
      })
      .finally(() => {
        this.sendingEmails = false;
      });
  }
}
