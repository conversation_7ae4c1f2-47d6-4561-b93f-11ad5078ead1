import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { CampaignStepInterface } from '@vendasta/campaigns/lib/_internal/objects/api';

const ONE_DAY_IN_SECONDS = 86400;

@Pipe({
  name: 'eventDay',
  standalone: true,
})
export class EventDayPipe implements PipeTransform {
  constructor(private translate: TranslateService) {}

  transform(schedule: CampaignStepInterface[], index: number): string {
    const secondsDelay = schedule
      .slice(0, index)
      .map((x) => x.secondsAfterLastEmail || 0)
      .reduce((a, b) => a + b, 0);
    console.log(`schedule`, schedule);
    console.log(`secondsDelay[${index}]`, secondsDelay);
    const daysDifferent = 1 + secondsDelay / ONE_DAY_IN_SECONDS;
    const day = this.translate.instant('CAMPAIGN_DETAILS.STEP.DAY');
    return `${day} ${daysDifferent}`;
  }
}
