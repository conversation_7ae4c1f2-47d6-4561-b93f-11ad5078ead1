<div mat-dialog-title>
  <mat-icon class="close-icon" (click)="dialogRef.close()">close</mat-icon>
  <h2>
    <ng-container *ngIf="campaignDetails$ | async as details; else loadingStencil">
      {{ 'CAMPAIGN_GENERIC.TEST_EMAIL.TITLE' | translate : { name: details.name } }}
    </ng-container>
  </h2>

  <div class="top-actions-container" *ngIf="campaignDetails$ | async as details">
    <div class="stage-container">
      <mat-form-field *ngIf="details.campaignSchedule.length > 0">
        <mat-select [value]="0" (selectionChange)="stepChanged($event.value)">
          <mat-option *ngFor="let schedule of details.campaignSchedule; let i = index" [value]="i">
            {{
              'CAMPAIGN_GENERIC.TEST_EMAIL.COUNT' | translate : { curr: i + 1, total: details.campaignSchedule.length }
            }}
            ({{ details.campaignSchedule | eventDay : i + 1 }})
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <button
      mat-raised-button
      color="primary"
      *ngIf="currentStepType === campaignStepType.CAMPAIGN_STEP_TYPE_EMAIL"
      (click)="sendTestEmail()"
    >
      <glxy-button-loading-indicator [isLoading]="sendingEmails">
        {{ 'CAMPAIGN_GENERIC.TEST_EMAIL.SEND_EMAIL' | translate }}
      </glxy-button-loading-indicator>
    </button>
  </div>

  <div style="margin-top: 8px">
    <ng-template campaignPreviewDataSelector></ng-template>
  </div>

  <ng-container *ngIf="currentStepType === campaignStepType.CAMPAIGN_STEP_TYPE_EMAIL">
    <h3 class="email-subject-line">
      <strong>
        {{ 'CAMPAIGN_GENERIC.TEST_EMAIL.SUBJECT' | translate }}
      </strong>
      <span *ngIf="previewData$ | async as data; else loadingStencil">
        {{ data.subject }}
      </span>
    </h3>
  </ng-container>
</div>
<mat-dialog-content>
  <ng-container *ngIf="previewData$ | async as data" class="preview-content">
    <ng-container *ngIf="!loadingEmail; else loadingEmailTemplate">
      <glxy-email-viewer
        *ngIf="currentStepType === campaignStepType.CAMPAIGN_STEP_TYPE_EMAIL && !!data.html"
        [html]="data.html"
        [showEmailInline]="true"
      ></glxy-email-viewer>

      <div class="no-events" *ngIf="currentStepType === campaignStepType.CAMPAIGN_STEP_TYPE_EMAIL && !data.html">
        {{ 'CAMPAIGN_GENERIC.TEST_EMAIL.NO_EVENTS' | translate }}
      </div>
      <div class="non-email-step" *ngIf="currentStepType !== campaignStepType.CAMPAIGN_STEP_TYPE_EMAIL">
        <mat-icon [ngStyle]="{ color: nonEmailStepDetails?.icon?.color }">
          {{ nonEmailStepDetails?.icon?.name }}
        </mat-icon>
        <div class="title">
          {{ nonEmailStepDetails?.title! }}
        </div>
        <hr />
        <div class="description" [innerHtml]="nonEmailStepDetails?.description!"></div>
      </div>
    </ng-container>
    <ng-template #loadingEmailTemplate>
      <mat-spinner [diameter]="30"></mat-spinner>
    </ng-template>
  </ng-container>
</mat-dialog-content>

<ng-template #loadingStencil>
  <div class="stencil-shimmer"></div>
</ng-template>
