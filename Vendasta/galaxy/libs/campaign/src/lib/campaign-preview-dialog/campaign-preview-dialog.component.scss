@use 'design-tokens' as *;

h2 {
  overflow: hidden;
  max-width: 650px;
  white-space: nowrap;
  text-overflow: ellipsis;

  .stencil-shimmer {
    height: 20px;
  }
}

.email-subject-line {
  margin: 16px 0;
  font-weight: normal;
  max-width: 650px;

  span {
    vertical-align: top;
  }
  .stencil-shimmer {
    vertical-align: top;
    display: inline-block;
    height: 16px;
    width: 200px;
    margin-left: 4px;
    margin-top: 2px;
  }
}

mat-dialog-content {
  position: relative;
  width: 700px;
  height: 500px;

  glxy-email-viewer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
  }
  .no-events {
    text-align: center;
    padding: 32px;
  }
  .non-email-step {
    text-align: center;
    padding: 40px 0;

    mat-icon {
      font-size: 60px;
      width: 60px;
      height: 60px;
      margin-bottom: 8px;
    }
    .title {
      font-size: 30px;
    }
    hr {
      width: 65%;
      margin: 16px auto;
    }
  }
  mat-spinner {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }
}

.stage-container {
  flex: 1;
}

mat-dialog-actions {
  max-width: 700px;
  display: contents;
}

.top-actions-container,
.send-test-email-container {
  flex: 0 0 100%;
  display: flex;
}

.send-test-email-container {
  display: block;
  margin-top: 8px;

  mat-form-field {
    flex: 3;
  }

  .input-boxes {
    display: flex;
  }

  .action-container {
    text-align: center;
    float: right;

    mat-spinner {
      float: right;
      margin-right: 16px;
    }
  }
}

.address {
  color: $tertiary-font-color;
}

.close-icon {
  display: flex;
  float: right;
  cursor: pointer;
  margin-top: 16px;
  color: $gray;
  &:hover {
    color: $dark-gray;
  }
}

.preview-error {
  margin: 20px 0;
}

.preview-error-list {
  font-size: 14px;
}

:host ::ng-deep {
  .mat-mdc-list .mat-mdc-list-item {
    color: $dark-gray;
    height: 20px;
    margin-left: -4px;
  }

  glxy-input .mat-form-field-flex {
    width: 400px;
  }
}
