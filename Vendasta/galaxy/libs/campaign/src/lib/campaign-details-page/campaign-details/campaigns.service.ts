import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { CampaignStepType } from '@vendasta/campaigns/lib/_internal';
import {
  CampaignService as CampaignsSDK,
  GetterCampaignData,
  Statuses,
  SenderInterface,
  StepDataResponse,
} from '@vendasta/campaigns';
import { FieldMaskInterface } from '@vendasta/campaigns/lib/_internal/interfaces';
import { CampaignStepInterface } from './interface';

@Injectable()
export class CampaignsService {
  constructor(private readonly campaignSDK: CampaignsSDK) {}

  get(campaignID: string): Observable<GetterCampaignData> {
    return this.campaignSDK.get(campaignID);
  }

  addStepToCampaign(
    sender: SenderInterface,
    campaignId: string,
    stepType: CampaignStepType,
    contentId: string,
    name: string,
  ): Observable<StepDataResponse> {
    return this.campaignSDK.addStep(sender, campaignId, stepType, contentId, name);
  }

  updateStepOnCampaign(
    sender: SenderInterface,
    campaignId: string,
    campaignStepId: string,
    contentId: string,
    name: string,
    secondsAfterLastStep: number,
    fm: FieldMaskInterface,
  ): Observable<StepDataResponse> {
    return this.campaignSDK.updateStep(sender, campaignId, campaignStepId, contentId, name, secondsAfterLastStep, fm);
  }

  updateCampaignSchedule(
    sender: SenderInterface,
    campaignId: string,
    campaignSchedule: CampaignStepInterface[],
  ): Observable<void> {
    return this.campaignSDK.updateCampaignSchedule(sender, campaignId, campaignSchedule);
  }

  updateStatus(sender: SenderInterface, campaignID: string, status: Statuses): Observable<void> {
    return this.campaignSDK.updateStatus(sender, campaignID, status);
  }

  updateName(sender: SenderInterface, campaignID: string, name: string): Observable<void> {
    return this.campaignSDK.updateName(sender, campaignID, name);
  }

  updateLocale(sender: SenderInterface, campaignID: string, locale: string): Observable<void> {
    return this.campaignSDK.updateCampaignLocale(sender, campaignID, locale);
  }

  pauseCampaign(campaignID: string): Observable<void> {
    return this.campaignSDK.pauseCampaign(campaignID);
  }

  unpauseCampaign(campaignID: string): Observable<void> {
    return this.campaignSDK.unpauseCampaign(campaignID);
  }

  delete(campaignID: string): Observable<null> {
    return this.campaignSDK.delete(campaignID);
  }
}
