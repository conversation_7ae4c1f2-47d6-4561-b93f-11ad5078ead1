import { Injectable } from '@angular/core';
import { SenderInterface, SenderType } from '@vendasta/campaigns';
import { EventService, OwnerType, StatusStatInterface, StatusType } from '@vendasta/smsv2';
import { SmsStepStats } from './interface';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class CampaignSmsStatsService {
  constructor(private smsEventService: EventService) {}

  getSmsStepStats(sender: SenderInterface, campaignStepID: string): Observable<SmsStepStats> {
    return this.smsEventService
      .getAttributeEventStats(
        {
          ownerId: sender.id,
          ownerType: this.senderToOwner(sender.type || 0),
          namespace: 'RM', // campaigns uses the RM namespace
        },
        {
          key: 'campaign_step_id',
          value: campaignStepID,
        },
      )
      .pipe(
        map((r) => {
          if (r.stats?.length === 0) {
            return this.emptyCampaignStatsData();
          }
          return this.responseToSmsStepStats(r.stats ?? []);
        }),
      );
  }

  responseToSmsStepStats(stats: StatusStatInterface[]): SmsStepStats {
    const result = this.emptyCampaignStatsData();
    for (const stat of stats) {
      switch (stat.status) {
        case StatusType.STATUS_TYPE_DELIVERED:
          result.delivered = stat.value || 0;
          break;
        case StatusType.STATUS_TYPE_SENT:
          result.sent = stat.value || 0;
          break;
        case StatusType.STATUS_TYPE_FAILED:
          result.failed = stat.value || 0;
          break;
        case StatusType.STATUS_TYPE_QUEUED:
          result.queued = stat.value || 0;
          break;
        case StatusType.STATUS_TYPE_UNDELIVERED:
          result.undelivered = stat.value || 0;
          break;
      }
    }
    return result;
  }

  emptyCampaignStatsData = (): SmsStepStats => ({
    queued: 0,
    sent: 0,
    delivered: 0,
    undelivered: 0,
    failed: 0,
  });

  senderToOwner(t: SenderType): OwnerType {
    switch (t) {
      case SenderType.SENDER_TYPE_PARTNER:
        return OwnerType.OWNER_TYPE_PARTNER;
      case SenderType.SENDER_TYPE_BUSINESS:
        return OwnerType.OWNER_TYPE_ACCOUNT_GROUP;
      default:
        return OwnerType.OWNER_TYPE_UNSPECIFIED;
    }
  }
}
