import { CommonModule } from '@angular/common';
import { Component, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { CampaignService, DuplicateCampaignStepStatus } from '@vendasta/campaigns';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { SubscriptionList } from '@vendasta/rx-utils';
import { EMPTY, firstValueFrom, interval } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';

// If copying this component a third time, consider implementing an abstraction.
export interface Data {
  campaignId: string;
  campaignStepId: string;
}

@Component({
  templateUrl: './campaign-step-duplicate-dialog.component.html',
  styleUrls: ['./campaign-step-duplicate-dialog.component.scss'],
  imports: [
    CommonModule, //
    MatDialogModule,
    GalaxyLoadingSpinnerModule,
    MatButtonModule,
  ],
})
export class CampaignStepDuplicateDialogComponent implements OnInit, OnDestroy {
  errored = false;

  private readonly subs = SubscriptionList.new();

  constructor(
    @Inject(MAT_DIALOG_DATA) private readonly data: Data,
    private readonly ref: MatDialogRef<CampaignStepDuplicateDialogComponent>,
    private readonly campaigns: CampaignService,
  ) {}

  async ngOnInit(): Promise<void> {
    await this.requestAndPoll();
  }

  async requestAndPoll(): Promise<void> {
    this.errored = false;
    this.subs.destroy();

    const workflowID = firstValueFrom(
      this.campaigns.duplicateCampaignStepAsync(this.data.campaignId, this.data.campaignStepId),
    );
    workflowID
      .then(
        (id: string) => this.pollForStatus(id), //
      )
      .catch(
        () => (this.errored = true), //
      );
  }

  private pollForStatus(id: string): void {
    const poll = interval(3000).pipe(
      switchMap(() => {
        return this.campaigns.checkCampaignStepDuplicateStatus(id);
      }),
      catchError((err) => {
        console.error(err);
        return EMPTY; // Keep polling
      }),
    );

    this.subs.add(poll, (v) => {
      if (v === DuplicateCampaignStepStatus.DUPLICATE_CAMPAIGN_STEP_STATUS_COMPLETE) {
        this.ref.close(true);
      }
    });
  }

  ngOnDestroy(): void {
    this.subs.destroy();
  }
}
