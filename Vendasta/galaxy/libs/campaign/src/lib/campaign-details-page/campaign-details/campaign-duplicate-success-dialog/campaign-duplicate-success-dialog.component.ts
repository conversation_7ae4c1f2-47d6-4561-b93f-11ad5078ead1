import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { firstValueFrom } from 'rxjs';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../../../shared/src/tokens';

export interface Data {
  newCampaignId: string;
}

@Component({
  templateUrl: './campaign-duplicate-success-dialog.component.html',
  styleUrls: ['./campaign-duplicate-success-dialog.component.scss'],
  imports: [CommonModule, MatDialogModule, MatButtonModule, TranslateModule],
})
export class CampaignDuplicateSuccessDialogComponent {
  private readonly campaignId: string;

  constructor(
    @Inject(MAT_DIALOG_DATA) private readonly data: Data,
    @Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE,
    private readonly ref: MatDialogRef<CampaignDuplicateSuccessDialogComponent>,
    private readonly router: Router,
  ) {
    this.campaignId = data.newCampaignId;
  }

  async goToNewCampaign(): Promise<void> {
    const basePath = await firstValueFrom(this.config.basePath$);
    await this.router
      .navigate([basePath, this.campaignId, 'details'])
      .then(() => this.ref.close())
      .catch((err) => console.error(err));
  }

  closeDialog(): void {
    this.ref.close();
  }
}
