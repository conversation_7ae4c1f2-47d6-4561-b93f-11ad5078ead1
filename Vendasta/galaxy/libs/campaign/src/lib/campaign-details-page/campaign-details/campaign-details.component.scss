@use 'design-tokens' as *;

:host {
  display: block;
  height: calc(100dvh - #{$atlas-bar-height} - env(safe-area-inset-bottom) - env(safe-area-inset-top));
}

.page-heading {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.page-heading-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.title {
  cursor: pointer;
}

.title-input {
  font-size: 24px;
  margin-right: 10px;
  width: 80%;
}

mat-form-field {
  font-size: 14px;
}

.campaign-steps--border {
  position: relative;

  &:before {
    content: '';
    position: absolute;
    top: 24px;
    left: 23px;
    height: calc(100% - 120px);
    width: 1px;
    background: $blue;
  }
}

.campaign-steps--border.published {
  &:before {
    height: calc(100% - 90px);
  }
}

.campaign-steps--step {
  display: grid;
  grid-template-columns: 1fr 90%;
  margin: 24px 0;

  & .campaign-step--day {
    height: 48px;
    width: 48px;
    margin: auto;
    padding: 6px;
    border-radius: 3px;
    border: 1px solid $blue;
    background-color: $light-blue;
    text-align: center;
    text-transform: uppercase;
    z-index: 1;
  }

  & .campaign-step--content {
    display: flex;
    align-items: center;
    height: 64px;
    background-color: $white;
  }
}

.draft-banner {
  margin-bottom: 12px;
  display: block;
}

.app-campaign-step.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drop-list-dragging .app-campaign-step:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging:active {
  cursor: grabbing;
}
