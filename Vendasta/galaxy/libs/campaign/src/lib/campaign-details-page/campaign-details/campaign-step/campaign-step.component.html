<div class="campaign-steps--step" *ngIf="stepStats$ | async as stepStats">
  <div class="campaign-step--day">
    {{ 'STEP.DAY' | translate }}
    <br />
    {{ dayNumber }}
  </div>
  <div class="campaign-step__content">
    <div
      class="campaign-step--content"
      [ngClass]="{
        snapshot: isSnapshotEvent,
        sms: isSMSEvent,
        rounded: draftMode || isSnapshotEvent,
      }"
    >
      <div class="campaign-step--header" [ngClass]="{ published: !draftMode }">
        <mat-icon
          *ngIf="draftMode && (campaignStatus$ | async) !== cs.ARCHIVED"
          class="drag"
          [ngClass]="{ snapshot: isSnapshotEvent, sms: isSMSEvent }"
        >
          drag_indicator
        </mat-icon>
        <div class="info">
          <div class="campaign-step--title">
            <a class="title" [ngClass]="{ snapshot: isSnapshotEvent, sms: isSMSEvent }" (click)="previewStep()">
              <ng-container *ngIf="isSMSEvent; else notSMS">
                {{ smsDetail?.name || step.name }}
              </ng-container>
              <ng-template #notSMS>
                {{ templateDetail?.name || step.name }}
              </ng-template>
            </a>
          </div>
          <div class="campaign-step--subject">
            <ng-container *ngIf="isSMSEvent">
              {{ 'STEP.CONTENT' | translate }}
              {{ smsDetail?.message }}
            </ng-container>
            <ng-container *ngIf="!isSMSEvent">
              {{ 'STEP.SUBJECT' | translate }}
              {{ templateDetail?.subject }}
            </ng-container>
          </div>
        </div>
      </div>
      <div class="campaign-step--stats" *ngIf="!draftMode">
        <div class="campaign-step--stat-container">
          <div class="campaign-step--stat-num">{{ stepStats.delivered }}</div>
          <div class="campaign-step--stat-name" [matTooltip]="emailDeliveredTooltip$ | async">
            {{ 'STATS.DELIVERED' | translate }}
          </div>
        </div>
        <div *ngIf="isSMSEvent" class="campaign-step--stat-container">
          <div class="campaign-step--stat-num">{{ stepStats.queued }}</div>
          <div class="campaign-step--stat-name" [matTooltip]="smsQueuedTooltip$ | async">
            {{ 'STATS.QUEUED' | translate }}
          </div>
        </div>
        <div *ngIf="!isSMSEvent" class="campaign-step--stat-container">
          <div class="campaign-step--stat-num">{{ stepStats.pending }}</div>
          <div class="campaign-step--stat-name" [matTooltip]="emailPendingTooltip$ | async">
            {{ 'STATS.PENDING' | translate }}
          </div>
        </div>
        <div *ngIf="!isSMSEvent" class="campaign-step--stat-container">
          <div class="campaign-step--stat-num">{{ stepStats.openRatio.toString() }}</div>
          <div class="campaign-step--stat-name" [matTooltip]="emailOpenRateTooltip$ | async">
            {{ 'STATS.OPEN_RATE' | translate }}
          </div>
        </div>
        <div *ngIf="!isSMSEvent" class="campaign-step--stat-container">
          <div class="campaign-step--stat-num">{{ stepStats.clickToOpenRatio.toString() }}</div>
          <div class="campaign-step--stat-name" [matTooltip]="emailClickRateTooltip$ | async">
            {{ 'STATS.CLICK_RATE' | translate }}
          </div>
        </div>
      </div>
      <campaign-step-options
        [draftMode]="draftMode"
        [isArchived]="(campaignStatus$ | async) === cs.ARCHIVED"
        [isEditable]="campaignDetails.isEditable"
        [isSnapshot]="isSnapshotEvent"
        [isSMS]="isSMSEvent"
        (deleteRequested)="emitDeleteStep(step.campaign_step_id, step?.name)"
        (editRequested)="editEmailClicked(step.campaign_step_id, templateDetail?.templateId)"
        (previewRequested)="previewStep()"
        (duplicateRequested)="duplicateStep(step.campaign_step_id)"
        (editSMSRequested)="editSMSClicked(step.campaign_step_id, step?.template_id)"
      ></campaign-step-options>
    </div>
    <div (mouseenter)="showPopovers()" (mouseleave)="hidePopovers()" cdkOverlayOrigin #popoverOrigin="cdkOverlayOrigin">
      <div class="campaign-step--stats-bar" *ngIf="!isSnapshotEvent && !draftMode">
        <div class="bar-delivered" [ngStyle]="{ 'width.%': deliveredWidth }"></div>
        <div class="bar-pending" [ngStyle]="{ 'width.%': pendingWidth, 'left.%': pendingOffset }"></div>
        <div class="bar-error" [ngStyle]="{ 'width.%': errorWidth, 'left.%': errorOffset }"></div>
      </div>
    </div>
    <glxy-popover
      *ngIf="!isSnapshotEvent && !draftMode"
      [origin]="popoverOrigin"
      [isOpen]="showPopover"
      [positions]="[PopoverPositions.Top]"
      [padding]="'small'"
    >
      <div class="stats-bar--tooltip">
        <div class="stats-bar--tooltip-color">
          <div class="delivered-block"></div>
        </div>
        <div class="stats-bar--tooltip-name">
          {{ 'STATS.DELIVERED' | translate }}
        </div>
        <div class="stats-bar--tooltip-value">{{ stepStats.delivered }}</div>
      </div>
      <ng-container *ngIf="isSMSEvent; else notSMS">
        <div class="stats-bar--tooltip">
          <div class="stats-bar--tooltip-color">
            <div class="pending-block"></div>
          </div>
          <div class="stats-bar--tooltip-name">
            {{ 'STATS.QUEUED' | translate }}
          </div>
          <div class="stats-bar--tooltip-value">{{ stepStats.queued }}</div>
        </div>
        <div class="stats-bar--tooltip">
          <div class="stats-bar--tooltip-color">
            <div class="error-block"></div>
          </div>
          <div class="stats-bar--tooltip-name">
            {{ 'STATS.FAILED' | translate }}
          </div>
          <div class="stats-bar--tooltip-value">{{ stepStats.failed }}</div>
        </div>
        <div class="stats-bar--tooltip">
          <div class="stats-bar--tooltip-color">
            <div class="error-block"></div>
          </div>
          <div class="stats-bar--tooltip-name">
            {{ 'STATS.UNDELIVERED' | translate }}
          </div>
          <div class="stats-bar--tooltip-value">{{ stepStats.undelivered }}</div>
        </div>
      </ng-container>
      <ng-template #notSMS>
        <div class="stats-bar--tooltip">
          <div class="stats-bar--tooltip-color">
            <div class="pending-block"></div>
          </div>
          <div class="stats-bar--tooltip-name">
            {{ 'STATS.PENDING' | translate }}
          </div>
          <div class="stats-bar--tooltip-value">{{ stepStats.pending }}</div>
        </div>
        <div class="stats-bar--tooltip">
          <div class="stats-bar--tooltip-color">
            <div class="error-block"></div>
          </div>
          <div class="stats-bar--tooltip-name">
            {{ 'STATS.BOUNCED' | translate }}
          </div>
          <div class="stats-bar--tooltip-value">{{ stepStats.bounced }}</div>
        </div>
        <div class="stats-bar--tooltip">
          <div class="stats-bar--tooltip-color">
            <div class="error-block"></div>
          </div>
          <div class="stats-bar--tooltip-name">
            {{ 'STATS.UNSUBSCRIBED' | translate }}
          </div>
          <div class="stats-bar--tooltip-value">{{ stepStats.unsubscribed }}</div>
        </div>
        <div class="stats-bar--tooltip">
          <div class="stats-bar--tooltip-color">
            <div class="error-block"></div>
          </div>
          <div class="stats-bar--tooltip-name">
            {{ 'STATS.SPAM' | translate }}
          </div>
          <div class="stats-bar--tooltip-value">{{ stepStats.spamReport }}</div>
        </div>
        <div class="stats-bar--tooltip">
          <div class="stats-bar--tooltip-color">
            <div class="error-block"></div>
          </div>
          <div class="stats-bar--tooltip-name">
            {{ 'STATS.DROPPED' | translate }}
          </div>
          <div class="stats-bar--tooltip-value">{{ stepStats.dropped }}</div>
        </div>
        <div class="stats-bar--tooltip">
          <div class="stats-bar--tooltip-color">
            <div class="error-block"></div>
          </div>
          <div class="stats-bar--tooltip-name">
            {{ 'STATS.RECIPIENT_STATS.STOPPED' | translate }}
          </div>
          <div class="stats-bar--tooltip-value">{{ stepStats.paused }}</div>
        </div>
      </ng-template>
    </glxy-popover>
    @if (draftMode) {
      <form [formGroup]="delayForm" class="delay">
        {{ 'STEP.WAIT' | translate }}
        <div class="delay--form">
          <glxy-form-field [size]="'small'" class="delay--input">
            <input
              type="number"
              matInput
              formControlName="delay"
              min="0"
              (change)="emitDelayChange($event.target.value)"
            />
          </glxy-form-field>
          <glxy-form-field [size]="'small'" class="delay--selector">
            <select formControlName="timePeriod" (change)="timeIntervalChanged($event.target.value)">
              <option *ngFor="let timeInterval of timeIntervals" [value]="timeInterval">
                {{ timeInterval }}
              </option>
            </select>
          </glxy-form-field>
        </div>
        <span *ngIf="stepNumber !== 0; else firstStep">
          {{ 'STEP.PREVIOUS_EVENT' | translate }}
        </span>
        <ng-template #firstStep>
          {{ 'STEP.FIRST_EVENT' | translate }}
        </ng-template>
        <div class="delay--error">
          @if (delayForm.hasError('maxDelayExceeded')) {
            <mat-error>{{ 'STEP.ERRORS.MAX_DELAY_EXCEEDED' | translate }}</mat-error>
          }
        </div>
      </form>
    }
  </div>
</div>
<ng-template #campaignStepPreview>
  <campaign-step-details-preview
    [showStats]="false"
    [campaignDetails]="campaignDetails"
    [initialStepID]="step.campaign_step_id"
    (editEmailFromPreviewClick)="editEmailFromPreviewClicked($event)"
    (editSMSFromPreviewClick)="editSMSFromPreviewClicked($event)"
  ></campaign-step-details-preview>
</ng-template>
<ng-template #campaignStepDetails>
  <campaign-step-details-preview
    [showStats]="true"
    [campaignDetails]="campaignDetails"
    [initialStepID]="step.campaign_step_id"
    (editEmailFromPreviewClick)="editEmailFromPreviewClicked($event)"
    (editSMSFromPreviewClick)="editSMSFromPreviewClicked($event)"
    [stepData]="step"
  ></campaign-step-details-preview>
</ng-template>
