import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { EmailTemplateService, SenderInterface } from '@vendasta/campaigns';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { CampaignPreviewInterface } from './interface';
import { TemplatesService } from '@vendasta/templates';
import { AppNamespace } from '@vendasta/email-builder';

interface LoadedTemplates {
  [templateId: string]: PreviewEmailType;
}

const seenTemplates: LoadedTemplates = {};

interface PreviewEmailType {
  html?: string;
  name?: string;
  subject?: string;
}

@Injectable({
  providedIn: 'root',
})
export class MarketingAutomationService {
  private templateId$$: BehaviorSubject<string> = new BehaviorSubject('');
  templateId$: Observable<string> = this.templateId$$.asObservable();

  constructor(
    private readonly http: HttpClient,
    private readonly campaignsService: EmailTemplateService,
    private templatesService: TemplatesService,
  ) {}

  getHTMLContentOfEmail(
    sender: SenderInterface,
    templateId: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    currentStep: any = null,
    accountGroupId = '',
    locale?: string,
    useCache = true,
  ): Observable<CampaignPreviewInterface> {
    this.templateId$$.next(templateId);

    if (templateId === 'snapshot-creation') {
      return of({
        html: '',
        name: '',
        subject: '',
      });
    }
    if (useCache && templateId in seenTemplates && accountGroupId === '') {
      const html = seenTemplates[templateId].html || '';
      const stepSubject = seenTemplates[templateId].subject || '';
      const stepName = seenTemplates[templateId].name || '';

      return of({
        html: html,
        name: stepName,
        subject: stepSubject,
      });
    }
    if (templateId.startsWith('SMS-BUILDER')) {
      return this.templatesService.get(AppNamespace.CAMPAIGNS, templateId).pipe(
        map((resp) => {
          return {
            content: resp.content,
            name: resp.name,
            subject: '',
            html: '',
          };
        }),
      );
    }
    return this.campaignsService
      .previewEmailTemplate(templateId, accountGroupId, '', '', locale || 'en', '', sender)
      .pipe(
        map((resp) => {
          return {
            html: resp.htmlBody,
            name: resp.name,
            subject: resp.subject,
          };
        }),
      );
  }
}
