@use 'design-tokens' as *;

$purple: #6775c3;
$transparent-green: #4caf50b2; //20% transparent green
$more-transparent-green: #4caf5032; //70% transparent green
$more-transparent-red: #e5393532; //70% transparent red
$stat-bar-height: 5px;

:host(.app-campaign-step) {
  display: block;
}

.campaign-steps--step {
  display: grid;
  grid-template-columns: 1fr 93%;
  margin: 24px 0;

  & .campaign-step--day {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    height: 48px;
    width: 48px;
    margin: auto;
    margin-left: 0;
    margin-top: 20%;
    border-radius: 2px;
    border: 1px solid $blue;
    background-color: $light-blue;
    text-align: center;
    text-transform: uppercase;
    z-index: 1;
    color: $dark-gray;
    font-size: 12px;
  }

  & .campaign-step--content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 10px;
    background-color: $card-background-color;
    border-radius: 2px 2px 0 0;
  }

  & .campaign-step--content.snapshot {
    background-color: $purple;
  }
}

.campaign-step--stats-bar {
  background-color: $lighter-gray;
  height: $stat-bar-height;
  filter: drop-shadow(0px 1px 1px $gray);
  border-radius: 0 0 2px 2px;

  & .bar-delivered {
    height: inherit;
    background-color: $transparent-green;
  }

  & .bar-pending {
    position: relative;
    top: -$stat-bar-height;
    height: inherit;
    background-color: $more-transparent-green;
  }

  & .bar-error {
    position: relative;
    top: -$stat-bar-height * 2;
    height: inherit;
    background-color: $more-transparent-red;
  }
}

.stats-bar--tooltip {
  display: flex;
  width: 200px;
  overflow: hidden;
  flex: nowrap;
  margin: 5px 8px;

  .stats-bar--tooltip-color {
    width: 7%;
  }

  .stats-bar--tooltip-name {
    width: 73%;
    color: $gray;
  }

  .stats-bar--tooltip-value {
    width: 20%;
  }

  .delivered-block {
    display: inline-flex;
    width: 4px;
    height: 10px;
    background-color: $transparent-green;
  }

  .pending-block {
    display: inline-flex;
    width: 4px;
    height: 10px;
    background-color: $more-transparent-green;
  }

  .error-block {
    display: inline-flex;
    width: 4px;
    height: 10px;
    background-color: $more-transparent-red;
  }
}

.drag {
  color: $light-gray;
}

.delay {
  color: $gray;
  font-size: 12px;
  margin-top: 1%;
  margin-bottom: 1.5%;
  margin-left: 20px;
}

.delay--form {
  display: inline-flex;
  max-height: 26px;
}

.delay--input {
  height: 30px;
  margin-left: 4px;
}

.delay--selector {
  margin: 0 4px;
}

.delay--error {
  display: block;
  margin: $spacing-2 0;
}

input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  opacity: 0.6942;
}

.campaign-step--header {
  display: flex;
  align-items: center;
  width: 70%;
}

.campaign-step--header.published {
  width: 35%;
}

.campaign-step--stats {
  display: grid;
  grid-template-columns: 25% 25% 25% 25%;
  align-items: center;
  width: 40%;
}

@media (max-width: 650px) {
  .campaign-step--stats {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 20%;
  }
}

.campaign-step--delete {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: $gray;
  margin-left: 10px;
}

.info {
  display: inline-block;
  line-height: 20px;
  width: 100%;
  padding-right: 30px;
}

.campaign-step--title {
  flex-wrap: wrap;
  font-size: 16px;
  margin-left: 10px;
  white-space: normal;
  overflow: visible;
  word-wrap: break-word;
}

.campaign-step--subject {
  color: $secondary-text-color;
  font-size: 12px;
  margin-left: 10px;
  white-space: normal;
  overflow: visible;
  word-wrap: break-word;
}

.campaign-step--stat-container {
  display: flex;
  flex-flow: column;
}

.campaign-step--stat-num {
  color: $primary-text-color;
  font-size: 16px;
}

.campaign-step--stat-name {
  color: $secondary-text-color;
  font-size: 12px;
}

.rounded {
  border-radius: 2px !important;
}

.snapshot {
  color: $white;

  :hover {
    color: $white;
  }

  .title {
    text-decoration: underline;
  }
}
