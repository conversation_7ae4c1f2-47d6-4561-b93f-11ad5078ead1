<mat-dialog-content>
  <p *ngIf="errored === false">
    <span>{{ waitingMessage }}</span>
    <glxy-loading-spinner size="large"></glxy-loading-spinner>
  </p>
  <p *ngIf="errored === true">
    <span>{{ 'CAMPAIGN_DETAILS.ACTIONS.COPY_DIALOG.ERROR' | translate }}</span>
  </p>
</mat-dialog-content>

<mat-dialog-actions *ngIf="errored">
  <button mat-raised-button color="primary" (click)="requestAndPoll()">
    <span>{{ 'CAMPAIGN_DETAILS.ACTIONS.COPY_DIALOG.TRY_AGAIN' | translate }}</span>
  </button>
</mat-dialog-actions>
