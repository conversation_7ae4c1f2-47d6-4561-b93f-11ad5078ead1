<glxy-side-drawer-container>
  <glxy-page>
    <glxy-page-toolbar>
      <glxy-page-nav>
        <glxy-page-nav-button
          [previousPageUrl]="previousPageUrl$ | async"
          [historyBackButtonTitle]="'COMMON.ACTION_LABELS.BACK' | translate"
        ></glxy-page-nav-button>
      </glxy-page-nav>
      <glxy-page-title>
        {{ 'CAMPAIGN_DETAILS.TITLE' | translate }}
      </glxy-page-title>
    </glxy-page-toolbar>
    <ng-container *ngIf="campaignDetailsV2$ | async as campaignDetails">
      <div (click)="closePreview()">
        <div class="page-heading">
          <h1 page-heading-container>
            <ng-container *ngIf="campaignName$ | async as campaignName">
              <ng-container *ngIf="!editingTitle">
                <span
                  [ngClass]="campaignDetails.isEditable ? 'title' : ''"
                  (click)="campaignDetails.isEditable ? editTitle() : null"
                >
                  {{ campaignName }}
                  <mat-icon *ngIf="campaignDetails.isEditable" class="edit-icon">create</mat-icon>
                </span>
              </ng-container>
              <mat-form-field class="title-input" *ngIf="editingTitle">
                <input
                  matInput
                  appAutofocus
                  [value]="campaignName"
                  (blur)="confirmEditing($event.target.value)"
                  (keydown.enter)="confirmEditing($event.target.value)"
                  (keydown.esc)="cancelEditing()"
                />
              </mat-form-field>
            </ng-container>
            <campaign-state
              *ngIf="campaignStatus$ | async"
              [status]="campaignStatus$ | async"
              [campaignState]="''"
            ></campaign-state>
          </h1>
          <div class="page-heading-container">
            <campaign-options
              [draftMode]="draftMode$ | async"
              [isCampaignEditable]="campaignDetails.isEditable"
              [isCampaignHidden]="false"
              [campaignStatus]="campaignStatus$ | async"
              [campaignHasChildSteps]="(campaignSteps$ | async)?.length > 0"
              [campaignId]="(campaignId$ | async) || ''"
              [partnerId]="partnerId$ | async"
              [updatingStatus]="updatingStatus"
              (resumeRequested)="resumeCampaign()"
              (deleteRequested)="deleteCampaign()"
              (archiveRequested)="archiveCampaign()"
              (pauseRequested)="pauseCampaign()"
              (addListRequested)="openAddListToCampaignModal()"
              (campaignDuplicated)="handleCampaignDuplicated($event)"
            ></campaign-options>
            <button
              mat-flat-button
              color="primary"
              *ngIf="(campaignStatus$ | async) === 'published' || (campaignStatus$ | async) === 'active'"
              (click)="openSendPreviewPage()"
            >
              {{ 'COMMON.ACTION_LABELS.ADD_RECIPIENTS' | translate }}
            </button>
            <button
              mat-flat-button
              color="primary"
              *ngIf="draftMode$ | async"
              (click)="publishCampaign()"
              [disabled]="disablePublishing$ | async"
            >
              {{ 'COMMON.ACTION_LABELS.PUBLISH' | translate }}
            </button>
          </div>
        </div>
        <div class="action-bars">
          <div class="action-bars--left">
            <mat-form-field *ngIf="emailCategoriesEnabled">
              <mat-select [value]="emailCategory$ | async" (valueChange)="updateCampaignEmailCategory($event)">
                <mat-option [disabled]="true" value="">Select an email category</mat-option>
                <mat-option value="conquer-local-newsletter">Conquer Local Newsletter</mat-option>
                <mat-option value="product-insider">Product Insider</mat-option>
                <mat-option value="blog-newsletter">Blog newsletter</mat-option>
                <mat-option value="promotions-and-offers">Promotions and offers</mat-option>
                <mat-option value="product-updates-and-announcement">Product updates and announcement</mat-option>
                <mat-option value="other">Other</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="action-bars--right" *ngIf="supportedLocales$ | async as locales">
            <mat-form-field subscriptSizing="dynamic" *ngIf="locales.length > 1">
              <mat-select [value]="locale$ | async" (valueChange)="updateCampaignLocale($event)">
                <ng-container *ngFor="let locale of locales">
                  <mat-option [value]="locale.value">{{ locale.name }}</mat-option>
                </ng-container>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <ng-container *ngIf="(draftMode$ | async) === false">
          <campaign-stats
            [campaignSchedule]="campaignSteps$ | async"
            [showRecipientStats]="nanDateFilter"
            [focus]="campaignFocus$ | async"
            [sender]="sender$ | async"
            [campaignID]="campaignId$ | async"
          ></campaign-stats>
        </ng-container>
        <campaign-details-scheduling [campaignId]="(campaignId$ | async) || ''"></campaign-details-scheduling>
        <ng-container *ngIf="templateDetails$ | async as templateDetails">
          <div class="campaign-steps">
            <div
              class="campaign-steps--border"
              [ngClass]="{ published: (draftMode$ | async) === false }"
              cdkDropList
              (cdkDropListDropped)="drop($event)"
            >
              <ng-container *ngFor="let step of campaignSteps$ | async; index as i">
                <campaign-step
                  cdkDrag
                  cdkDragLockAxis="y"
                  [cdkDragDisabled]="(draftMode$ | async) === false || (campaignStatus$ | async) === cs.ARCHIVED"
                  [cdkDragStartDelay]="isMobile() ? 200 : 0"
                  [draftMode]="draftMode$ | async"
                  [step]="step"
                  [stepNumber]="i"
                  [campaignDetails]="campaignDetailsV2$ | async"
                  [templateDetails]="templateDetails"
                  [dayNumber]="dayList[i]"
                  [campaignStatus$]="campaignStatus$"
                  [selectedMarketId]="(selectedMarketId$ | async) || ''"
                  (delayUpdated)="updateDelay($event)"
                  (deleteStep)="removeStep($event)"
                  (editEmailClick)="goToEmailEdit($event)"
                  (reloadStepsRequested)="reload$$.next(null)"
                  (editSMSClick)="goToSMSEdit($event)"
                ></campaign-step>
              </ng-container>
            </div>
            <div *ngIf="loadingStep">
              <glxy-loading-spinner></glxy-loading-spinner>
            </div>
            <campaign-details-add-actions
              [showActions]="(draftMode$ | async) && (campaignStatus$ | async) !== cs.ARCHIVED"
              [loading]="loadingStep"
              [noSteps]="isStateEmpty$ | async"
              (actionClicked)="handleAddStep($event)"
            ></campaign-details-add-actions>
          </div>
        </ng-container>
      </div>
    </ng-container>
  </glxy-page>
</glxy-side-drawer-container>
