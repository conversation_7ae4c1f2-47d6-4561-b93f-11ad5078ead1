import { CampaignSmsStatsService } from './campaign-sms-stats.service';
import { CampaignStepStats, EmailStepStats, Ratio, SmsStepStats } from './interface';
import { CampaignEmailStatsService } from './campaign-email-stats.service';
import { formatStatsPercentage } from './utils';
import { map, Observable } from 'rxjs';
import { SenderInterface } from '@vendasta/campaigns';

export class SMSStepStatsGetter implements StepStatsGetter {
  constructor(private smsEventStatsService: CampaignSmsStatsService) {}

  getStepStats(sender: SenderInterface, campaign_step_id: string): Observable<SmsStepStats> {
    return this.smsEventStatsService.getSmsStepStats(sender, campaign_step_id);
  }
}

export class EmailStepStatsGetter implements StepStatsGetter {
  constructor(private emailEventStatsService: CampaignEmailStatsService) {}
  getStepStats(sender: SenderInterface, campaign_step_id: string): Observable<EmailStepStats> {
    return this.emailEventStatsService.getEmailStepStats(sender, campaign_step_id).pipe(
      map((stats) => {
        return {
          bounced: stats.bounced.count,
          campaign_step_id: campaign_step_id,
          clickedThrough: stats.clicked.count,
          created: 0,
          delivered: stats.delivered.count,
          dropped: stats.dropped.count,
          notRequired: 0,
          onDeck: 0,
          opened: stats.opened.count,
          paused: 0,
          pending: 0,
          refreshed: 0,
          sent: 0,
          spamReport: stats.spamreport.count,
          unsubscribed: stats.unsubscribed.count,
          clickToOpenRate: 0,
          clickToOpenRatio: new Ratio(stats.clicked.unique_by_email, stats.opened.unique_by_email).withFormatter(
            formatStatsPercentage,
          ),
          openRate: 0,
          openRatio: new Ratio(stats.opened.unique_by_email, stats.delivered.unique_by_email).withFormatter(
            formatStatsPercentage,
          ),
        };
      }),
    );
  }
}

export abstract class StepStatsGetter {
  abstract getStepStats(sender: SenderInterface, campaign_step_id: string): Observable<CampaignStepStats>;
}
