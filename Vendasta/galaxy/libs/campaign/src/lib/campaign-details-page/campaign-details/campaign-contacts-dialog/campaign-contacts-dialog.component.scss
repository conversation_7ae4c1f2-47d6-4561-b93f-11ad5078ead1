@use 'design-tokens' as *;

.campaign-next-button {
  display: flex;
  justify-self: right;
}
.campaign-back-button {
  margin-right: 0.5em;
}

.campaign-button-container {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-top: 0;
}

.campaign-next-button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.contact-dialog-footer {
  display: flex;
  justify-content: space-between;
}
.mat-dialog-actions-contact-dialog {
  display: block;
}
:host ::ng-deep va-filter-container .toolbar {
  z-index: 1;
  position: sticky;
  top: 0;
  background-color: $card-background-color;
  border-bottom: 1px solid $glxy-grey-300;
}
.mat-mdc-dialog-content {
  padding: 0 !important;
}
:host ::ng-deep .content-container {
  padding: $spacing-3;
}
:host ::ng-deep va-search-box {
  min-width: 100%;
}
.list-item-checkbox,
.list-row {
  display: flex;
}
:host ::ng-deep .glxy-form-field {
  padding: $spacing-3;
}
