import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CampaignFocus, CampaignStepInterface, FullEmailStats, RecipientCampaignStats } from '../interface';
import { ConvertSteptypeStringToProto, formatStatsPercentage, formatTranslation } from '../utils';
import { CommonModule } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { UIKitModule } from '@vendasta/uikit';
import { MatIconModule } from '@angular/material/icon';
import { FormatStatPipe } from '@vendasta/galaxy/pipes';
import { RecipientCampaignStatsService } from './recipient_campaign_stats.service';
import {
  CampaignStatsService,
  CampaignStepType,
  GetCampaignDetailsStatsResponseInterface,
  SenderInterface,
} from '@vendasta/campaigns';
import { map, shareReplay } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { CampaignEmailStatsService } from '../campaign-email-stats.service';
import { EmailEventService } from '@vendasta/email';
import { PAGE_ROUTES } from '../../../routing-constants';

@Component({
  selector: 'campaign-stats',
  imports: [CommonModule, TranslateModule, MatTooltipModule, UIKitModule, MatIconModule, FormatStatPipe, RouterModule],
  providers: [RecipientCampaignStatsService, CampaignEmailStatsService, EmailEventService, CampaignStatsService],
  templateUrl: './campaign-stats.component.html',
  styleUrls: ['./campaign-stats.component.scss'],
})
export class CampaignStatsComponent implements OnChanges, OnInit {
  public historyPageRoute = '';

  @Input()
  showRecipientStats = true;
  @Input()
  focus: string = CampaignFocus.UNSPECIFIED;
  @Input()
  sender: SenderInterface = {};
  @Input()
  campaignID = '';
  @Input()
  campaignSchedule: CampaignStepInterface[] = [];
  queryParams = {};

  recipientCampaignStats$: Observable<RecipientCampaignStats> =
    this.recipientCampaignStatsService.recipientCampaignStats$.pipe(
      map((stats: GetCampaignDetailsStatsResponseInterface): RecipientCampaignStats => {
        return {
          active: stats.stats?.active || 0,
          completed: stats.stats?.completed || 0,
          stopped: stats.stats?.stopped || 0,
          total_accounts: stats.stats?.totalAccounts || 0,
          total_recipients: stats.stats?.totalRecipients || 0,
          waiting_on_rate_limit: stats.stats?.waitingOnRateLimit || 0,
          total_leads: stats.stats?.totalLeads || 0,
        };
      }),
      shareReplay(1),
    );

  emailstats$: Observable<FullEmailStats> = this.campaignEmailStatsService.emailStats$.pipe(shareReplay(1));
  hasEmailStep = false;

  hotLeadsTooltip$: Observable<string> = this.recipientCampaignStats$.pipe(
    map((stats: RecipientCampaignStats): string => {
      return formatTranslation(
        this.translate.instant('STATS.PERFORMANCE_STATS.TOTAL_LEADS_TOOLTIP_SINGLE'),
        this.translate.instant('STATS.PERFORMANCE_STATS.TOTAL_LEADS_TOOLTIP', {
          totalLeads: stats.total_leads,
        }),
        stats.total_leads,
      );
    }),
  );

  clickedTooltip$: Observable<string> = this.emailstats$.pipe(
    map((data: FullEmailStats): string => {
      return formatTranslation(
        this.translate.instant('STATS.PERFORMANCE_STATS.CLICKED_TOOLTIP_SINGLE'),
        this.translate.instant('STATS.PERFORMANCE_STATS.CLICKED_TOOLTIP', {
          clicked: data.stats.clicked.unique_by_email,
        }),
        data.stats.clicked.unique_by_email,
      );
    }),
  );

  percentBuffering$: Observable<string> = this.recipientCampaignStats$.pipe(
    map((stats: RecipientCampaignStats): string => {
      return formatStatsPercentage(stats.waiting_on_rate_limit, stats.total_recipients);
    }),
  );
  percentInProgress$: Observable<string> = this.recipientCampaignStats$.pipe(
    map((stats: RecipientCampaignStats): string => {
      return formatStatsPercentage(stats.active, stats.total_recipients);
    }),
  );
  percentStopped$: Observable<string> = this.recipientCampaignStats$.pipe(
    map((stats: RecipientCampaignStats): string => {
      return formatStatsPercentage(stats.stopped, stats.total_recipients);
    }),
  );
  percentCompleted$: Observable<string> = this.recipientCampaignStats$.pipe(
    map((stats: RecipientCampaignStats): string => {
      return formatStatsPercentage(stats.completed, stats.total_recipients);
    }),
  );
  percentClickedThroughRate$: Observable<string> = this.emailstats$.pipe(
    map((data: FullEmailStats): string => {
      return formatStatsPercentage(data.stats.clicked.unique_by_email, data.stats.delivered.unique_by_email);
    }),
  );

  constructor(
    private translate: TranslateService,
    private router: Router,
    private readonly recipientCampaignStatsService: RecipientCampaignStatsService,
    private readonly campaignEmailStatsService: CampaignEmailStatsService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['campaignID'] && changes['campaignID'].currentValue) {
      const campaignID = changes['campaignID'].currentValue;
      this.recipientCampaignStatsService.campaignID = campaignID;
      this.campaignEmailStatsService.campaignID = campaignID;

      this.historyPageRoute = '../../' + PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.HISTORY(campaignID);
      this.queryParams = {
        filters: JSON.stringify({
          opened: true,
        }),
      };
    }
    if (changes['sender'] && changes['sender'].currentValue) {
      this.recipientCampaignStatsService.sender = changes['sender'].currentValue;
      this.campaignEmailStatsService.sender = changes['sender'].currentValue;
    }

    if (changes['campaignSchedule']) {
      this.campaignSchedule.forEach((step) => {
        if (ConvertSteptypeStringToProto(step.step_type) == CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL) {
          this.hasEmailStep = true;
        }
      });
    }
  }

  navigateToAccounts(campaignId: string): void {
    this.router.navigate(['campaign', 'accounts', campaignId]);
  }

  navigateToRecipients(campaignId: string): void {
    this.router.navigate(['marketing', 'campaign', campaignId, 'history']);
  }

  ngOnInit(): void {
    this.campaignSchedule.forEach((step) => {
      if (ConvertSteptypeStringToProto(step.step_type) == CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL) {
        this.hasEmailStep = true;
      }
    });
  }
}
