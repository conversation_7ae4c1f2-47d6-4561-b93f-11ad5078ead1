<mat-expansion-panel class="scheduling-panel">
  <mat-expansion-panel-header>
    <mat-panel-title>
      {{ 'CAMPAIGN_OPTIONS.CONFIG.TITLE' | translate }}
    </mat-panel-title>
    <mat-panel-description> {{ 'CAMPAIGN_OPTIONS.CONFIG.SUBTITLE' | translate }} </mat-panel-description>
  </mat-expansion-panel-header>

  <h4>
    {{ 'CAMPAIGN_OPTIONS.SCHEDULING.TITLE' | translate }}
  </h4>

  <mat-divider></mat-divider>
  <form [formGroup]="schedulingForm">
    <mat-form-field class="dropdown">
      <mat-label>
        {{ 'CAMPAIGN_OPTIONS.SCHEDULING.TIME_ZONE.DESCRIPTION' | translate }}
      </mat-label>
      <input type="text" matInput [matAutocomplete]="timezoneAutocomplete" formControlName="timezone" />
      <mat-icon class="clear-icon" *ngIf="schedulingForm.controls.timezone.value" matSuffix (click)="clearTimezone()">
        clear
      </mat-icon>
      <mat-hint>
        {{ 'CAMPAIGN_OPTIONS.SCHEDULING.TIME_ZONE.HINT' | translate }}
      </mat-hint>
    </mat-form-field>
    <mat-autocomplete #timezoneAutocomplete="matAutocomplete">
      <mat-option *ngFor="let option of timezoneOptions$ | async" [value]="option.value">
        {{ option.label }}
      </mat-option>
    </mat-autocomplete>
    <div>
      <div class="title">
        <b>
          {{ 'CAMPAIGN_OPTIONS.SCHEDULING.INCLUDED_DAYS.TITLE' | translate }}
        </b>
      </div>
      <mat-button-toggle-group
        name="includedDays"
        multiple
        formControlName="includedDays"
        (change)="checkIncludedDays()"
      >
        <mat-button-toggle value="monday">
          {{ 'CAMPAIGN_OPTIONS.DAYS.MONDAY' | translate }}
        </mat-button-toggle>
        <mat-button-toggle value="tuesday">
          {{ 'CAMPAIGN_OPTIONS.DAYS.TUESDAY' | translate }}
        </mat-button-toggle>
        <mat-button-toggle value="wednesday">
          {{ 'CAMPAIGN_OPTIONS.DAYS.WEDNESDAY' | translate }}
        </mat-button-toggle>
        <mat-button-toggle value="thursday">
          {{ 'CAMPAIGN_OPTIONS.DAYS.THURSDAY' | translate }}
        </mat-button-toggle>
        <mat-button-toggle value="friday">
          {{ 'CAMPAIGN_OPTIONS.DAYS.FRIDAY' | translate }}
        </mat-button-toggle>
        <mat-button-toggle value="saturday">
          {{ 'CAMPAIGN_OPTIONS.DAYS.SATURDAY' | translate }}
        </mat-button-toggle>
        <mat-button-toggle value="sunday">
          {{ 'CAMPAIGN_OPTIONS.DAYS.SUNDAY' | translate }}
        </mat-button-toggle>
      </mat-button-toggle-group>
      <mat-error *ngIf="showDaysWarning">
        <span class="error-text">
          {{ 'CAMPAIGN_OPTIONS.SCHEDULING.INCLUDED_DAYS.WARNING' | translate }}
        </span>
      </mat-error>
      <div class="info-text">
        {{ 'CAMPAIGN_OPTIONS.SCHEDULING.INCLUDED_DAYS.HINT' | translate }}
      </div>
    </div>

    <button mat-flat-button color="primary" (click)="save()">
      {{ 'COMMON.ACTION_LABELS.SAVE' | translate }}
    </button>
    <button mat-button color="primary" (click)="cancel()">
      {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
    </button>
  </form>
</mat-expansion-panel>
