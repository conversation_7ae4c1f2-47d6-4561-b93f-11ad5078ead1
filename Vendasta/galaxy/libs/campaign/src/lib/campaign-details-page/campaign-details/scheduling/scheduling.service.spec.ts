import { SchedulingService } from './scheduling.service';

describe('SchedulingComponent', () => {
  let schedulingService: SchedulingService;
  beforeEach(() => {
    schedulingService = new SchedulingService(null, null, null);
  });

  describe('filteredTimezones', () => {
    it('should return timezones greater than 1 when filter value is empty', () => {
      const timezones = schedulingService.filteredTimezones('');
      expect(timezones.length).toBeGreaterThan(1);
    });

    it('should return timezone that matches filter term', () => {
      const timezones = schedulingService.filteredTimezones('kathmandu');
      expect(timezones[0].label).toEqual('Asia/Kathmandu');
    });
  });
});
