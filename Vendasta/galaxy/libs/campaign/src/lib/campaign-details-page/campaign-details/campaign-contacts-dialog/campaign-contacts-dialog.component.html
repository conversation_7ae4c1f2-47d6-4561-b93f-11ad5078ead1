<h2 mat-dialog-title>
  {{ 'CAMPAIGNS.CONTACTS.SELECT_CAMPAIGN_RECIPIENTS' | translate }}
</h2>
<mat-dialog-content>
  <va-filter-container
    *ngIf="(modalState$$ | async) === ModalState.CONTACTS"
    [showToolbar]="true"
    (searchTermChanged)="term$.next($event)"
  >
    <div content>
      <div *ngFor="let contact of dataMembers$ | async; let last = last">
        <div class="list-row">
          <mat-checkbox
            class="list-item-checkbox"
            secondary-action-item
            [checked]="contact.disabled === true || isSelected(contact)"
            [disabled]="contact.disabled === true"
            (change)="selectionChanged($event, contact)"
          ></mat-checkbox>
          <campaign-app-list-item
            class="list-item"
            [titleText]="contact.name"
            [subtitleText]="contact.email"
            [additionalSubtitleText]="contact.phoneNumber"
            [clickable]="false"
          ></campaign-app-list-item>
        </div>
      </div>
      <div>
        <ng-container *ngIf="(dataMembers$ | async).length > 0">
          <glxy-infinite-scroll-trigger
            (isVisible)="loadMore()"
            [visiblilityMargin]="0"
            [minHeight]="0"
          ></glxy-infinite-scroll-trigger>
        </ng-container>
      </div>

      <div
        *ngIf="loading$ | async"
        style="display: flex; align-items: center; justify-content: center; min-height: 61px"
      >
        <mat-spinner [diameter]="32" [strokeWidth]="4" [style.height.px]="32"></mat-spinner>
      </div>
      <glxy-empty-state
        *ngIf="(dataMembers$ | async).length === 0 && (dataServiceLoading$ | async) === false"
        [size]="'small'"
      >
        <glxy-empty-state-hero>
          <mat-icon>account_circle</mat-icon>
        </glxy-empty-state-hero>
        <p>{{ 'CAMPAIGNS.CONTACTS.MODAL.NO_CONTACTS' | translate }}</p>
      </glxy-empty-state>
    </div>
  </va-filter-container>

  <ng-container *ngIf="(modalState$$ | async) === ModalState.SCHEDULE">
    <glxy-form-field [required]="true">
      <glxy-label>{{ 'CAMPAIGNS.CONTACTS.MODAL.SCHEDULE_OR_START' | translate }}</glxy-label>
      <mat-radio-group required>
        <mat-radio-button value="1" (change)="onItemChange(ScheduleType.NOW)">
          {{ 'CAMPAIGNS.CONTACTS.MODAL.SEND_NOW' | translate }}
          <extended>{{ 'CAMPAIGNS.CONTACTS.MODAL.SEND_TIME' | translate }} {{ nowDate | date: 'fullTime' }}</extended>
        </mat-radio-button>
        <mat-radio-button value="2" (change)="onItemChange(ScheduleType.LATER)">
          {{ 'CAMPAIGNS.CONTACTS.MODAL.SCHEDULE_LATER' | translate }}
          <extended>{{ 'CAMPAIGNS.CONTACTS.MODAL.SCHEDULE_LATER_LABEL' | translate }}</extended>
        </mat-radio-button>
      </mat-radio-group>

      <ng-container *ngIf="this.scheduleOption === ScheduleType.LATER">
        <glxy-form-field size="default">
          <glxy-label>{{ 'CAMPAIGNS.CONTACTS.MODAL.PICK_DATE' | translate }}</glxy-label>

          <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle>
          <input
            matInput
            placeholder="Select a date"
            class="click-datepicker"
            #dateInput
            [matDatepicker]="picker"
            [value]="selectedDate"
            (dateChange)="dateChanged(dateInput.value)"
          />
          <mat-datepicker #picker></mat-datepicker>
        </glxy-form-field>
        <glxy-form-field>
          <glxy-label>{{ 'CAMPAIGNS.CONTACTS.MODAL.PICK_TIME' | translate }}</glxy-label>
          <forms-time-picker
            [control]="startTime"
            [placeholder]="'8:00 AM' | translate"
            appearance="outline"
          ></forms-time-picker>
        </glxy-form-field>
      </ng-container>
    </glxy-form-field>
  </ng-container>
</mat-dialog-content>
<mat-dialog-actions class="mat-dialog-actions-contact-dialog">
  <ng-container class="contact-dialog-footer">
    <div class="contact-dialog-footer">
      <div>
        <span>
          <p>{{ selectedContacts.length }} {{ 'CAMPAIGNS.CONTACTS.MODAL.NUM_RECIPIENTS' | translate }}</p>
        </span>
      </div>
      <ng-container *ngIf="(modalState$$ | async) === ModalState.CONTACTS">
        <div class="campaign-next-button-container">
          <button mat-stroked-button class="campaign-back-button" color="primary" (click)="close()">
            {{ 'CAMPAIGNS.CONTACTS.MODAL.CANCEL' | translate }}
          </button>
          <button
            mat-flat-button
            class="campaign-next-button"
            color="primary"
            (click)="changeState(ModalState.SCHEDULE)"
            [disabled]="selectedContacts.length > 20"
          >
            {{ 'CAMPAIGNS.CONTACTS.MODAL.NEXT' | translate }}
          </button>
        </div>
      </ng-container>
      <ng-container *ngIf="(modalState$$ | async) === ModalState.SCHEDULE">
        <div class="campaign-button-container">
          <button
            mat-stroked-button
            class="campaign-back-button"
            color="accent"
            (click)="changeState(ModalState.CONTACTS)"
          >
            {{ 'CAMPAIGNS.CONTACTS.MODAL.BACK' | translate }}
          </button>
          <button
            mat-flat-button
            class="campaign-next-button"
            color="primary"
            (click)="startCampaign()"
            [disabled]="loading$ | async"
          >
            <glxy-button-loading-indicator [isLoading]="loading$ | async">
              {{ 'CAMPAIGNS.CONTACTS.MODAL.START_CAMPAIGN' | translate }}
            </glxy-button-loading-indicator>
          </button>
        </div>
      </ng-container>
    </div>
  </ng-container>
</mat-dialog-actions>
