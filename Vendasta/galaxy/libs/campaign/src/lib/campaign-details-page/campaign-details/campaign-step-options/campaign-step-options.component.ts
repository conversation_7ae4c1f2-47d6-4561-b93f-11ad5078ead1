import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { MatMenuModule } from '@angular/material/menu';

@Component({
  selector: 'campaign-step-options',
  imports: [CommonModule, TranslateModule, MatIconModule, MatButtonModule, GalaxyTooltipModule, MatMenuModule],
  templateUrl: './campaign-step-options.component.html',
  styleUrls: ['./campaign-step-options.component.scss'],
})
export class CampaignStepOptionsComponent {
  @Input() draftMode = false;
  @Input() isSnapshot = false;
  @Input() isSMS = false;
  @Input() isEditable = false;
  @Input() isArchived = false;

  @Output() readonly previewRequested = new EventEmitter<null>();
  @Output() readonly editRequested = new EventEmitter<null>();
  @Output() readonly deleteRequested = new EventEmitter<null>();
  @Output() readonly duplicateRequested = new EventEmitter<null>();
  @Output() readonly editSMSRequested = new EventEmitter<null>();

  requestPreview(): void {
    this.previewRequested.emit();
  }

  requestEdit(): void {
    if (this.isSMS) {
      this.editSMSRequested.emit();
    } else {
      this.editRequested.emit();
    }
  }

  requestDelete(): void {
    this.deleteRequested.emit();
  }

  requestDuplicate(): void {
    this.duplicateRequested.emit();
  }
}
