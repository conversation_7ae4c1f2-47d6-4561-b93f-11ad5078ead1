import { Component, EventEmitter, Inject, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { Observable, take, switchMap, map, Subscription } from 'rxjs';
import { SENDER_INJECTION_TOKEN } from '@galaxy/email-ui/email-builder/src/shared';
import { Sender } from '@vendasta/email';
import { Feature, ActivationService, SenderInterface, CampaignService } from '@vendasta/campaigns';
import { AddActionType, CampaignConfig } from '../../../dependencies';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { FeatureFlagService } from '@galaxy/partner';
import { CampaignStateService } from '../campaign-state.service';
import { MatDialog, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { UpsellSmsDialogComponent } from '../upsell-sms-dialog/upsell-sms-dialog.component';
import { CONFIG_TOKEN } from '../../../../../shared/src/tokens';

@Component({
  selector: 'campaign-details-add-actions',
  templateUrl: './add-actions.component.html',
  styleUrls: ['./add-actions.component.scss'],
  imports: [CommonModule, TranslateModule, MatIconModule, MatButtonModule, MatMenuModule, MatDialogModule],
  providers: [FeatureFlagService],
})
export class AddActionsComponent implements OnInit, OnDestroy {
  @Input() showActions = false;
  @Input() loading = false;
  @Input() noSteps = false;
  @Output() actionClicked: EventEmitter<AddActionType> = new EventEmitter();
  actionTypes = AddActionType;
  isLocationValid = false;
  isSMSAvailable$: Observable<boolean>;
  availableSteps$: Observable<Feature[]>;
  features = Feature;
  sender: SenderInterface;
  subscriptions: Subscription[] = [];
  sender$: Observable<SenderInterface> = this.campaignConfig.sender$;

  constructor(
    @Inject(CONFIG_TOKEN) private readonly campaignConfig: CampaignConfig,
    @Inject(SENDER_INJECTION_TOKEN) public readonly sndr: Observable<Sender>,
    private activation: ActivationService,
    private snackbarService: SnackbarService,
    private readonly campaignStateService: CampaignStateService,
    public dialogRef: MatDialogRef<UpsellSmsDialogComponent>,
    public dialog: MatDialog,
    private campaigns: CampaignService,
    @Inject('PARTNER_ID') readonly partnerId$: Observable<string>,
  ) {
    this.subscriptions.push(
      this.campaignConfig.sender$.subscribe((sender) => {
        this.campaignStateService.setSender(sender);
      }),
    );

    this.subscriptions.push(
      this.sender$.subscribe((sender) => {
        this.sender = sender;
      }),
    );

    this.availableSteps$ = sndr.pipe(
      take(1),
      switchMap((sndr) => {
        return campaigns.getAvailableStepTypesForSender(sndr);
      }),
      map((resp) => resp.availableStepTypes),
    );
    this.isSMSAvailable$ = this.availableSteps$.pipe(map((steps) => steps.includes(Feature.SEND_SMS)));
  }
  ngOnInit() {
    this.campaignConfig.location$.subscribe((location) => {
      if (location === 'US' || location === 'CA') {
        this.isLocationValid = true;
      }
    });
  }

  openDialogOrEmitAction() {
    this.isSMSAvailable$.pipe(take(1)).subscribe((smsAvailable) => {
      if (smsAvailable) {
        this.actionClicked.emit(AddActionType.SMS);
      } else {
        this.openSMSUpsellDialog();
      }
    });
  }

  openSMSUpsellDialog() {
    this.dialog.open(UpsellSmsDialogComponent, {
      width: '450px',
      data: { sender: this.sender },
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }
}
