import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  HostBinding,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { AbstractControl, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { EmailStatsModule } from '@galaxy/email-ui/email-activity';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CampaignStepInterfaceInterface } from '@vendasta/campaigns';
import { AppNamespace } from '@vendasta/email-builder';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyEmailViewerModule } from '@vendasta/galaxy/email-viewer';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxyPopoverModule, PopoverPositions } from '@vendasta/galaxy/popover';
import { DynamicOpenCloseTemplateRefService } from '@vendasta/galaxy/side-drawer/src/dynamic-open-close-template-ref.service';
import { GalaxySideDrawerModule } from '@vendasta/galaxy/side-drawer/src/side-drawer.module';
import { GalaxyStatisticModule } from '@vendasta/galaxy/statistic';
import { TemplatesService } from '@vendasta/templates';
import { Observable, Subscription } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';
import { CampaignConfig } from '../../../dependencies';
import { CampaignEmailStatsService } from '../campaign-email-stats.service';
import { CampaignStepDetailsPreviewComponent } from '../campaign-step-details-preview/campaign-step-details-preview.component';
import {
  CampaignStepDuplicateDialogComponent,
  Data,
} from '../campaign-step-duplicate-dialog/campaign-step-duplicate-dialog.component';
import { CampaignStepOptionsComponent } from '../campaign-step-options/campaign-step-options.component';
import {
  CampaignStatus,
  CampaignStepInterface,
  CampaignStepStats,
  SMSContentData,
  TemplateData,
  isSmsStepStats,
} from '../interface';
import { MarketingAutomationService } from '../marketing-automation.service';
import { formatTranslation } from '../utils';
import { TimeConvertor, TimeInterval } from './time-convertor.service';
import { CampaignSmsStatsService } from '../campaign-sms-stats.service';
import { EmailStepStatsGetter, SMSStepStatsGetter, StepStatsGetter } from '../campaign-stats';
import { CONFIG_TOKEN } from '../../../../../shared/src/tokens';

export interface StepDetailInterface {
  stepId: string;
  templateId: string;
}

@Component({
  selector: 'campaign-step',
  imports: [
    CommonModule,
    TranslateModule,
    CampaignStepDetailsPreviewComponent,
    CampaignStepOptionsComponent,
    GalaxyPopoverModule,
    OverlayModule,
    MatTooltipModule,
    MatIconModule,
    RouterModule,
    GalaxyEmailViewerModule,
    MatProgressSpinnerModule,
    GalaxyInputModule,
    MatOptionModule,
    MatAutocompleteModule,
    MatFormFieldModule,
    GalaxyAlertModule,
    GalaxyPipesModule,
    GalaxyStatisticModule,
    MatGridListModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    EmailStatsModule,
    MatTooltipModule,
    GalaxySideDrawerModule,
    GalaxyFormFieldModule,
  ],
  providers: [CampaignEmailStatsService, CampaignSmsStatsService, TimeConvertor],
  templateUrl: './campaign-step.component.html',
  styleUrls: ['./campaign-step.component.scss'],
})
export class CampaignStepComponent implements OnInit, AfterViewInit, OnDestroy {
  @HostBinding('class.app-campaign-step') componentClass = true;
  templateDetail: TemplateData;
  smsDetail: SMSContentData = { message: '', name: '' };
  @Input()
  step: CampaignStepInterface;
  @Input()
  stepNumber: number;
  preview: boolean;

  @Input()
  set templateDetails(data: TemplateData[]) {
    for (let i = 0; i < data.length; i++) {
      if (data[i].templateId === this.step.template_id) {
        this.templateDetail = data[i];
        return;
      }
    }
  }

  @Input()
  campaignDetails: {
    campaignId: string;
    locale: string;
    isEditable: boolean;
    campaignSchedule: CampaignStepInterfaceInterface[];
  };
  @Input()
  dayNumber: number;
  @Input()
  draftMode: boolean;
  @Input()
  campaignStatus$: Observable<CampaignStatus>;
  @Input()
  selectedMarketId: string;

  @Output()
  delayUpdated = new EventEmitter<[string, number]>();
  @Output()
  editEmailClick = new EventEmitter<[string, string]>();
  @Output()
  deleteStep = new EventEmitter<[string, string]>();
  @Output()
  reloadStepsRequested = new EventEmitter<null>();
  @Output()
  editSMSClick = new EventEmitter<[string, string]>();

  @ViewChild('campaignStepPreview') campaignStepPreview: TemplateRef<any>;
  @ViewChild('campaignStepDetails') campaignStepDetails: TemplateRef<any>;
  subscriptions: Subscription[] = [];
  isSnapshotEvent = false;
  isSMSEvent = false;
  cs: typeof CampaignStatus = CampaignStatus;
  locale: string;

  public emailDeliveredTooltip$: Observable<string>;
  public emailPendingTooltip$: Observable<string>;
  public smsQueuedTooltip$: Observable<string>;
  public emailOpenRateTooltip$: Observable<string>;
  public emailClickRateTooltip$: Observable<string>;

  deliveredWidth = 0;
  pendingWidth = 0;
  errorWidth = 0;
  pendingOffset = 0;
  errorOffset = 0;
  campaignSMSstep = 'CAMPAIGN_STEP_TYPE_SMS';

  PopoverPositions = PopoverPositions;
  showPopover = false;

  campaignSteps: CampaignStepInterfaceInterface[];
  delayForm: FormGroup;
  timeIntervals = [TimeInterval.HOURS, TimeInterval.DAYS, TimeInterval.WEEKS];

  public stepStats$: Observable<CampaignStepStats>;

  private statsGetter: StepStatsGetter = new EmailStepStatsGetter(this.emailEventStatsService);

  constructor(
    @Inject(CONFIG_TOKEN) private readonly config: CampaignConfig,
    private translate: TranslateService,
    private dynamicOpenCloseService: DynamicOpenCloseTemplateRefService,
    private marketingAutomationService: MarketingAutomationService,
    private readonly dialog: MatDialog,
    private route: ActivatedRoute,
    private emailEventStatsService: CampaignEmailStatsService,
    private timeConvertor: TimeConvertor,
    private templateService: TemplatesService,
    private smsEventStatsService: CampaignSmsStatsService,
  ) {}

  ngOnInit(): void {
    this.campaignSteps = this.campaignDetails.campaignSchedule;
    this.locale = this.campaignDetails.locale ? this.campaignDetails.locale : 'en';
    this.isSnapshotEvent = this.step.name === 'Snapshot Creation';
    this.isSMSEvent = this.step.step_type === this.campaignSMSstep;
    if (this.isSMSEvent) {
      this.statsGetter = new SMSStepStatsGetter(this.smsEventStatsService);
    } else {
      this.statsGetter = new EmailStepStatsGetter(this.emailEventStatsService);
    }

    if (this.isSMSEvent) {
      this.templateService.get(AppNamespace.CAMPAIGNS, this.step.template_id).subscribe((template) => {
        this.smsDetail.message = template.content;
        this.smsDetail.name = template.name;
      });
    }

    if (this.route.snapshot.queryParams['stepId'] && this.route.snapshot.queryParams['templateId']) {
      this.step.campaign_step_id = this.route.snapshot.queryParams['stepId'];
      this.step.template_id = this.route.snapshot.queryParams['templateId'];
      this.previewStep();
    }

    const existingDelay = this.timeConvertor.convertFromSeconds(this.step.seconds_after_last_email);
    this.delayForm = new FormGroup(
      {
        timePeriod: new FormControl(existingDelay[1]),
        delay: new FormControl(existingDelay[0]),
      },
      [
        (formGroup: AbstractControl) => {
          formGroup = formGroup as FormGroup;
          const delay = formGroup.get('delay')?.value;
          const timePeriod = formGroup.get('timePeriod')?.value;
          if (delay && timePeriod) {
            const totalDelay = this.timeConvertor.convertToSeconds(delay, timePeriod);
            const thirtyDaysInSeconds = 60 * 60 * 24 * 30;
            if (totalDelay > thirtyDaysInSeconds) {
              return { maxDelayExceeded: true };
            }
          }
          return null;
        },
      ],
    );

    this.stepStats$ = this.config.sender$.pipe(
      take(1),
      switchMap((sender) => {
        return this.statsGetter.getStepStats(sender, this.step.campaign_step_id);
      }),
    );
    this.setupTooltips();
    this.setupStatBarPercentages();
  }

  ngAfterViewInit(): void {
    this.dynamicOpenCloseService.registerTemplate('campaignStepPreview-' + this.stepNumber, this.campaignStepPreview);
    this.dynamicOpenCloseService.registerTemplate('campaignStepDetails-' + this.stepNumber, this.campaignStepDetails);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  emitDeleteStep(stepId: string, templateName: string): void {
    if (!templateName) {
      this.deleteStep.emit([stepId, '']);
    } else {
      this.deleteStep.emit([stepId, templateName]);
    }
  }

  editEmailClicked(stepId: string, templateId: string): void {
    this.editEmailClick.emit([stepId, templateId]);
  }

  editSMSClicked(stepId: string, templateId: string): void {
    this.editSMSClick.emit([stepId, templateId]);
  }

  emitDelayChange(delay: number): void {
    if (!this.delayForm.valid) {
      return;
    }
    this.delayUpdated.emit([
      this.step.campaign_step_id,
      this.timeConvertor.convertToSeconds(delay, this.delayForm.value.timePeriod),
    ]);
  }

  timeIntervalChanged(interval: TimeInterval): void {
    if (!this.delayForm.valid) {
      return;
    }
    this.delayUpdated.emit([
      this.step.campaign_step_id,
      this.timeConvertor.convertToSeconds(this.delayForm.value.delay, interval),
    ]);
  }

  setupTooltips(): void {
    this.emailDeliveredTooltip$ = this.stepStats$.pipe(
      map((stats) =>
        formatTranslation(
          this.translate.instant('STATS.EMAIL.DELIVERED_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.DELIVERED_TOOLTIP', {
            delivered: stats?.delivered,
          }),
          stats?.delivered,
        ),
      ),
    );

    this.smsQueuedTooltip$ = this.stepStats$.pipe(
      map((stats) => {
        if (!isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.SMS.QUEUED_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.SMS.QUEUED_TOOLTIP', {
            queued: stats?.queued,
          }),
          stats?.queued,
        );
      }),
    );

    this.emailPendingTooltip$ = this.stepStats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.PENDING_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.PENDING_TOOLTIP', {
            pending: stats?.pending,
          }),
          stats?.pending,
        );
      }),
    );
    this.emailOpenRateTooltip$ = this.stepStats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.OPEN_RATE_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.OPEN_RATE_TOOLTIP', {
            openRate: stats?.openRatio ? Math.floor(stats.openRatio.toNumber() * 100) : 0,
          }),
          stats?.openRatio?.numerator || 0,
        );
      }),
    );
    this.emailClickRateTooltip$ = this.stepStats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.CLICK_RATE_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.CLICK_RATE_TOOLTIP', {
            clickRate: stats?.clickToOpenRatio ? Math.floor(stats.clickToOpenRatio.toNumber() * 100) : 0,
          }),
          stats?.clickToOpenRatio?.numerator || 0,
        );
      }),
    );
  }

  setupStatBarPercentages(): void {
    this.subscriptions.push(
      this.stepStats$.subscribe((stats) => {
        if (isSmsStepStats(stats)) {
          const total = stats?.delivered + stats?.queued + stats?.failed + stats?.undelivered;
          if (total) {
            this.deliveredWidth = ((stats.delivered - stats.failed) / total) * 100;
            this.pendingWidth = (stats.queued / total) * 100;
            this.errorWidth = (stats.failed / total) * 100 + (stats.undelivered / total) * 100;
            this.pendingOffset = this.deliveredWidth;
            this.errorOffset = this.deliveredWidth + this.pendingWidth;
          }
        } else {
          const total = stats?.delivered + stats?.pending + stats?.bounced + stats?.dropped + stats?.paused;
          if (total) {
            this.deliveredWidth = ((stats.delivered - stats.unsubscribed - stats.spamReport) / total) * 100;
            this.pendingWidth = (stats.pending / total) * 100;
            this.errorWidth =
              (stats.unsubscribed / total) * 100 +
              (stats.spamReport / total) * 100 +
              (stats.dropped / total) * 100 +
              (stats.bounced / total) * 100 +
              (stats.paused / total) * 100;
            this.pendingOffset = this.deliveredWidth;
            this.errorOffset = this.deliveredWidth + this.pendingWidth;
          }
        }
      }),
    );
  }

  showPopovers(): void {
    this.showPopover = true;
  }

  hidePopovers(): void {
    this.showPopover = false;
  }

  previewStep(): void {
    if (this.draftMode) {
      this.dynamicOpenCloseService.open('campaignStepPreview-' + this.stepNumber);
    } else {
      this.dynamicOpenCloseService.open('campaignStepDetails-' + this.stepNumber);
    }
  }

  duplicateStep(campaignStepId: string): void {
    const data: Data = {
      campaignId: this.campaignDetails.campaignId || '',
      campaignStepId: campaignStepId,
    };
    const ref = this.dialog.open(CampaignStepDuplicateDialogComponent, {
      data: data,
    });
    ref.afterClosed().subscribe((complete: boolean) => {
      if (complete) {
        this.reloadStepsRequested.next(null);
      }
    });
  }

  editEmailFromPreviewClicked(event: StepDetailInterface): void {
    this.editEmailClicked(event.stepId, event.templateId);
  }

  editSMSFromPreviewClicked(event: StepDetailInterface): void {
    this.editSMSClicked(event.stepId, event.templateId);
  }
}
