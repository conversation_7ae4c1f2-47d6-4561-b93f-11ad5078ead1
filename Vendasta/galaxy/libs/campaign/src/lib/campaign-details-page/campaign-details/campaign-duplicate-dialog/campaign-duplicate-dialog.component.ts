import { CommonModule } from '@angular/common';
import { Component, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AsyncWorkStatus, CampaignService, SenderInterface } from '@vendasta/campaigns';
import { CheckDuplicateCampaignStatusResponse } from '@vendasta/campaigns/lib/_internal/objects/api';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { SubscriptionList } from '@vendasta/rx-utils';
import { EMPTY, firstValueFrom, interval } from 'rxjs';
import { catchError, filter, shareReplay, switchMap, take, takeUntil } from 'rxjs/operators';

// If copying this component a third time, consider implementing an abstraction.
export interface Data {
  campaignId: string;
  sender: SenderInterface;
}

export interface DialogResult {
  newCampaignId: string;
}

@Component({
  imports: [CommonModule, MatDialogModule, MatButtonModule, TranslateModule, GalaxyLoadingSpinnerModule],
  templateUrl: './campaign-duplicate-dialog.component.html',
  styleUrls: ['./campaign-duplicate-dialog.component.scss'],
})
export class CampaignDuplicateDialogComponent implements OnInit, OnDestroy {
  errored = false;
  completed = false;
  waitingMessage = '';

  private readonly subs = SubscriptionList.new();

  constructor(
    @Inject(MAT_DIALOG_DATA) private readonly data: Data,
    readonly ref: MatDialogRef<CampaignDuplicateDialogComponent>,
    private readonly campaigns: CampaignService,
    private readonly translate: TranslateService,
  ) {}

  async ngOnInit(): Promise<void> {
    this.waitingMessage = this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.COPY_DIALOG.PLEASE_WAIT');
    window.setTimeout(() => {
      this.waitingMessage = this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.COPY_DIALOG.LONG_WAIT');
    }, 10000);
    await this.requestAndPoll();
  }

  async requestAndPoll(): Promise<void> {
    this.reset();

    const workflowID = firstValueFrom(this.campaigns.duplicateCampaignAsyncV2(this.data.campaignId, this.data.sender));
    workflowID
      .then(
        (id: string) => this.pollForStatus(id), //
      )
      .catch(
        () => (this.errored = true), //
      );
  }

  private reset(): void {
    this.errored = false;
    this.completed = false;
    this.subs.destroy();
  }

  private pollForStatus(id: string): void {
    const poll = interval(3000).pipe(
      switchMap(() => {
        return this.campaigns.checkCampaignDuplicateStatus(id);
      }),
      catchError((err) => {
        console.error(err);
        return EMPTY; // Keep polling
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    const error = poll.pipe(
      filter((v) => v.status === AsyncWorkStatus.ASYNC_WORK_STATUS_ERROR),
      take(1),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    const pollUntilErr = poll.pipe(takeUntil(error));

    this.subs.add(pollUntilErr, (v: CheckDuplicateCampaignStatusResponse) => {
      if (v.status === AsyncWorkStatus.ASYNC_WORK_STATUS_COMPLETE) {
        const res: DialogResult = { newCampaignId: v.campaignId };
        this.ref.close(res);
      }
    });
    this.subs.add(error, () => (this.errored = true));
  }

  ngOnDestroy(): void {
    this.subs.destroy();
  }
}
