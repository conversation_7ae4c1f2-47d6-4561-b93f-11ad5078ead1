@use 'design-tokens' as *;

glxy-statistic-value {
  font-size: $spacing-4;
}

mat-card {
  margin: 20px 0;
}

.preview-with-stats {
  display: flex;
  @media screen and (max-width: calc($media--desktop-medium-minimum - 1px)) {
    flex-direction: column;
  }
}

.step-position {
  font-size: 14px;
  color: $dark-gray;
}

.stats-container {
  background-color: $primary-background-color;
  display: inline-block;
  width: 100%;
  border-bottom: thin solid $border-color;
  border-right: thin solid $border-color;
  padding: $spacing-3 $spacing-4;

  @media screen and (min-width: $media--desktop-medium-minimum) {
    width: 42%;
    overflow-y: scroll;
    min-height: 880px;
    padding: $spacing-3 $spacing-4 42px;
  }
}

.page-subtitle {
  font-size: $spacing-3;
  font-weight: bold;
}

.email-activity-btn {
  display: flex;
  justify-content: center;
  padding: $spacing-5;
}

.stats-header {
  font-size: 20px;
  line-height: 36px;
  width: 90%;
}

.step-preview-header {
  margin: $spacing-3 $spacing-4;
  font-size: 20px;
  line-height: 36px;

  .edit-step-btn {
    background-color: $primary-background-color;
    float: right;
    margin-left: $spacing-2;
  }

  .display-test-btn {
    float: right;
  }
}

.address {
  color: $tertiary-font-color;
}

.send-test-email-container {
  display: block;
  padding-right: $spacing-4;

  .input-boxes {
    display: flex;
    align-items: center;

    .account-selector {
      width: 200px;
      margin: 0 $spacing-2 0 $spacing-4;
      display: block;
    }

    .account-option {
      height: auto;
      font-size: 14px;
    }

    .email-input {
      width: 70%;
    }
  }

  .action-container {
    margin: 0 0 28px 0;
    float: right;

    .sending-spinner {
      margin-right: 16px;
    }
  }
}

.step-subject-line {
  margin: $spacing-4;
  font-size: 16px;
  color: $secondary-text-color;

  @media screen and (min-width: $media--desktop-medium-minimum) {
    margin: 60px $spacing-4 0;
  }
}

.email-preview-content {
  margin: 15px 30px;
}

.non-email-step {
  text-align: center;
  padding: $spacing-5 0;

  .title {
    font-size: 30px;
  }

  mat-icon {
    font-size: 60px;
    width: 60px;
    height: 60px;
    margin-bottom: $spacing-2;
  }

  hr {
    width: 65%;
    margin: 16px auto;
  }
}

.preview-step {
  display: block;
  background-color: $secondary-background-color;
  padding-bottom: 20px;
  flex-grow: 3;
  width: 100%;
  @media screen and (min-width: $media--desktop-medium-minimum) {
    overflow-y: scroll;
  }
}

.preview-error {
  margin: 0 0 $spacing-4 30px;
}

.preview-error-list {
  font-size: 14px;
}

:host ::ng-deep {
  .side-drawer-container {
    min-height: 100%;
    display: flex;
    flex-direction: column;
  }

  .side-drawer-content {
    background-color: $secondary-background-color;
  }

  .side-drawer-toolbar-title {
    flex: 1;
    margin-left: $spacing-2;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .mat-autocomplete-panel {
    min-width: 690px;
  }

  .mat-expansion-panel-body {
    padding: 0;
  }

  .mat-mdc-list .mat-mdc-list-item {
    color: $dark-gray;
    height: 20px;
    margin-left: -4px;
  }

  .mat-toolbar-row {
    background-color: $primary-background-color;
    border-bottom: thin solid $border-color;

    .mat-mdc-button {
      color: $primary-accent-color;
    }
  }

  .searchbox {
    width: 100%;
    margin: -20px 0;
  }
}

:host ::ng-deep .sms-message {
  margin-top: $spacing-4;
  width: 350px;
  height: 600px;
  justify-self: center;
  background-color: $primary-background-color;

  .content-message {
    margin: $spacing-4;
  }
}

:host ::ng-deep .account-selector > .mat-form-field-wrapper > .mat-form-field-flex > .mat-form-field-infix {
  padding: 10px 0 $spacing-3 0;
  font-size: 14px;
}

:host ::ng-deep .with-stats-panel > .side-drawer-container {
  width: 100%;
  min-width: $media--phone-minimum;
  @media screen and (min-width: $media--desktop-medium-minimum) {
    max-width: 1110px;
    min-width: calc($media--desktop-medium-minimum - $new-panel-width - $spacing-5);
  }

  .side-drawer-content {
    max-height: 90vh;

    @media screen and (min-width: $media--desktop-medium-minimum) {
      overflow-y: hidden;
    }
  }
}

:host ::ng-deep .no-stats-panel > .side-drawer-container {
  @media screen and (min-width: $media--desktop-medium-minimum) {
    width: 750px;
  }
}

:host ::ng-deep .mat-grid-tile-content {
  justify-content: space-between;
}

.glxy-email-viewer {
  margin-top: 15px;
  @media screen and (min-width: $media--desktop-medium-minimum) {
    min-width: 700px;
  }
}
