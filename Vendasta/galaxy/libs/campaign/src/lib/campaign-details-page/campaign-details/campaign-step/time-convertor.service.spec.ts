import { TimeConvertor, TimeInterval } from './time-convertor.service';

describe('TimeConvertor', () => {
  let timeConvertor: TimeConvertor;
  beforeEach(() => {
    timeConvertor = new TimeConvertor();
  });

  describe('convertToSeconds', () => {
    it('should return the correct conversion to seconds for each time interval type', () => {
      const secondsFromHours = timeConvertor.convertToSeconds(5, TimeInterval.HOURS);
      expect(secondsFromHours).toEqual(18000);
      const secondsFromDays = timeConvertor.convertToSeconds(5, TimeInterval.DAYS);
      expect(secondsFromDays).toEqual(432000);
      const secondsFromWeeks = timeConvertor.convertToSeconds(5, TimeInterval.WEEKS);
      expect(secondsFromWeeks).toEqual(3024000);
    });

    it('should throw error for invalid interval', () => {
      expect(() => {
        timeConvertor.convertToSeconds(0, null);
      }).toThrow('Invalid interval');
    });
  });

  describe('convertFromSeconds', () => {
    it('should return the correct value for each time interval', () => {
      let result = timeConvertor.convertFromSeconds(604800);
      expect(result[0]).toEqual(1);
      expect(result[1]).toEqual(TimeInterval.WEEKS);
      result = timeConvertor.convertFromSeconds(86400);
      expect(result[0]).toEqual(1);
      expect(result[1]).toEqual(TimeInterval.DAYS);
      result = timeConvertor.convertFromSeconds(3600);
      expect(result[0]).toEqual(1);
      expect(result[1]).toEqual(TimeInterval.HOURS);
    });
    it('should return result in days if the duration exceeds a week but cannot be rounded to another whole week', () => {
      const result = timeConvertor.convertFromSeconds(777600);
      expect(result[0]).toEqual(9);
      expect(result[1]).toEqual(TimeInterval.DAYS);
    });
    it('should return result in hours if the duration exceeds a day but cannot be rounded to another whole day', () => {
      const result = timeConvertor.convertFromSeconds(97200);
      expect(result[0]).toEqual(27);
      expect(result[1]).toEqual(TimeInterval.HOURS);
    });
  });
});
