import { Injectable } from '@angular/core';

import { EmailEventService, EventType } from '@vendasta/email';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { CampaignStats, EmailStat, EmailStatsData, FullEmailStats } from './interface';
import { SenderInterface, SenderType } from '@vendasta/campaigns';
import { GetAttributeEventStatsResponseEventStats } from '@vendasta/email/lib/_internal/objects/api';
import { map, startWith, switchMap } from 'rxjs/operators';

@Injectable()
export class CampaignEmailStatsService {
  constructor(private emailEventService: EmailEventService) {}

  set sender(sender: SenderInterface) {
    this.sender$$.next(sender);
  }

  set campaignID(campaignID: string) {
    this.campaignID$$.next(campaignID);
  }
  //
  private sender$$: BehaviorSubject<SenderInterface> = new BehaviorSubject<SenderInterface>({
    type: SenderType.SENDER_TYPE_INVALID,
    id: '',
  });
  private campaignID$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  //
  public emailStats$: Observable<FullEmailStats> = combineLatest([this.sender$$, this.campaignID$$]).pipe(
    switchMap(([sender, campaignID]: [SenderInterface, string]) => {
      return this.getEmailStats(sender, campaignID);
    }),
  );

  getEmailStats(sender: SenderInterface, campaignID: string): Observable<FullEmailStats> {
    return this.emailEventService
      .getAttributeEventStats(
        {
          id: sender.id,
          type: sender.type,
        },
        {
          key: 'campaign_id',
          value: campaignID,
        },
      )
      .pipe(
        map((r) => {
          return {
            stats: this.convertEventStatsToEmailStatsData(r?.data),
            deliveryRate: r?.deliveryRate ?? 0,
            openRate: r?.openRate ?? 0,
            clickToOpenRate: r?.clickToOpenRate ?? 0,
          };
        }),
        startWith(this.createFullEmailStats({})),
      );
  }

  getEmailStepStats(sender: SenderInterface, campaignStepID: string): Observable<EmailStatsData> {
    return this.emailEventService
      .getAttributeEventStats(
        {
          id: sender.id,
          type: sender.type,
        },
        {
          key: 'campaign_step_id',
          value: campaignStepID,
        },
      )
      .pipe(
        map((r) => this.convertEventStatsToEmailStatsData(r?.data)),
        startWith(this.createEmailStatsData({})),
      );
  }

  convertEventStatsToEmailStatsData(data: GetAttributeEventStatsResponseEventStats[]): EmailStatsData {
    const statsData = this.emptyEmailStatsData().stats;
    if (data?.length > 0) {
      for (const d of data) {
        switch (d.eventType) {
          case EventType.DELIVERED:
            statsData.delivered = {
              count: d.count,
              unique: d.uniqueCount,
              unique_by_email: d.uniqueRecipients,
            };
            break;
          case EventType.OPENED:
            statsData.opened = {
              count: d.count,
              unique: d.uniqueCount,
              unique_by_email: d.uniqueRecipients,
            };
            break;
          case EventType.PROCESSED:
            statsData.processed = {
              count: d.count,
              unique: d.uniqueCount,
              unique_by_email: d.uniqueRecipients,
            };
            break;
          case EventType.CLICKED:
            statsData.clicked = {
              count: d.count,
              unique: d.uniqueCount,
              unique_by_email: d.uniqueRecipients,
            };
            break;
          case EventType.BOUNCED:
            statsData.bounced = {
              count: d.count,
              unique: d.uniqueCount,
              unique_by_email: d.uniqueRecipients,
            };
            break;
          case EventType.DEFERRED:
            statsData.deferred = {
              count: d.count,
              unique: d.uniqueCount,
              unique_by_email: d.uniqueRecipients,
            };
            break;
          case EventType.DROPPED:
            statsData.dropped = {
              count: d.count,
              unique: d.uniqueCount,
              unique_by_email: d.uniqueRecipients,
            };
            break;
          case EventType.SPAMREPORT:
            statsData.spamreport = {
              count: d.count,
              unique: d.uniqueCount,
              unique_by_email: d.uniqueRecipients,
            };
            break;
          case EventType.UNSUBSCRIBED:
            statsData.unsubscribed = {
              count: d.count,
              unique: d.uniqueCount,
              unique_by_email: d.uniqueRecipients,
            };
            break;
          case EventType.RESUBSCRIBED:
            statsData.resubscribed = {
              count: d.count,
              unique: d.uniqueCount,
              unique_by_email: d.uniqueRecipients,
            };
            break;
        }
      }
    }
    return statsData;
  }

  createCampaignStatsData = <T extends Partial<CampaignStats>>(initialValues: T): CampaignStats & T => {
    return Object.assign(this.emptyCampaignStatsData(), initialValues);
  };

  createFullEmailStats = <T extends Partial<FullEmailStats>>(initialValues: T): FullEmailStats & T => {
    return Object.assign(this.emptyEmailStatsData(), initialValues);
  };

  createEmailStatsData = <T extends Partial<EmailStatsData>>(initialValues: T): EmailStatsData & T => {
    return Object.assign(this.emptyEmailStatsData().stats, initialValues);
  };

  emptyEmailStat = (): EmailStat => ({
    count: 0,
    unique: 0,
    unique_by_email: 0,
  });

  emptyEmailStatsData = (): FullEmailStats => ({
    stats: {
      processed: this.emptyEmailStat(),
      delivered: this.emptyEmailStat(),
      opened: this.emptyEmailStat(),
      clicked: this.emptyEmailStat(),
      bounced: this.emptyEmailStat(),
      deferred: this.emptyEmailStat(),
      dropped: this.emptyEmailStat(),
      spamreport: this.emptyEmailStat(),
      unsubscribed: this.emptyEmailStat(),
      resubscribed: this.emptyEmailStat(),
    },
    deliveryRate: 0,
    openRate: 0,
    clickToOpenRate: 0,
  });

  emptyCampaignStatsData = (): CampaignStats => ({
    active: 0,
    clicked_through: 0,
    completed: 0,
    delivered: 0,
    opened: 0,
    sent: 0,
    stopped: 0,
    total_accounts: 0,
    total_leads: 0,
    total_recipients: 0,
    undeliverable: 0,
    waiting_on_rate_limit: 0,
  });
}
