import { Injectable } from '@angular/core';

export enum TimeInterval {
  HOURS = 'hours',
  DAYS = 'days',
  WEEKS = 'weeks',
}

@Injectable()
export class TimeConvertor {
  readonly secondsPerMinute = 60;
  readonly secondsPerHour = this.secondsPerMinute * 60;
  readonly secondsPerDay = this.secondsPerHour * 24;
  readonly secondsPerWeek = this.secondsPerDay * 7;

  convertToSeconds(value: number, interval: TimeInterval): number {
    let seconds = 0;
    switch (interval) {
      case TimeInterval.HOURS:
        seconds = value * this.secondsPerHour;
        break;
      case TimeInterval.DAYS:
        seconds = value * this.secondsPerDay;
        break;
      case TimeInterval.WEEKS:
        seconds = value * this.secondsPerWeek;
        break;
      default:
        // Should never happen
        throw new Error('Invalid interval');
    }
    return seconds;
  }

  convertFromSeconds(seconds: number): [number, TimeInterval] {
    if (seconds >= this.secondsPerWeek && seconds % this.secondsPerWeek === 0) {
      return [seconds / this.secondsPerWeek, TimeInterval.WEEKS];
    }

    if (seconds >= this.secondsPerDay && seconds % this.secondsPerDay === 0) {
      return [seconds / this.secondsPerDay, TimeInterval.DAYS];
    }

    const hours = Math.floor(seconds / this.secondsPerHour);
    return [hours, TimeInterval.HOURS];
  }
}
