import { CommonModule } from '@angular/common';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SenderInterface, SenderType } from '@vendasta/campaigns';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { UIKitModule } from '@vendasta/uikit';
import { Observable, Subscription } from 'rxjs';
import { map, startWith, take } from 'rxjs/operators';
import { MyCampaignsTableService } from '../../../campaign-list-page/my-campaigns-table.service';
import { CampaignConfig } from '../interface';
import { SchedulingService, TimezoneOption } from './scheduling.service';

@Component({
  imports: [
    CommonModule,
    TranslateModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatOptionModule,
    MatAutocompleteModule,
    ReactiveFormsModule,
    MatDividerModule,
    UIKitModule,
    MatExpansionModule,
    MatInputModule,
    MatIconModule,
    MatButtonToggleModule,
    MatButtonModule,
    GalaxyAlertModule,
  ],
  providers: [SchedulingService, MyCampaignsTableService],
  selector: 'campaign-details-scheduling',
  templateUrl: './scheduling.component.html',
  styleUrls: ['./scheduling.component.scss'],
})
export class SchedulingComponent implements OnInit, OnDestroy {
  @Input() campaignId = '';
  senderId = '';
  schedulingForm: UntypedFormGroup;
  timezoneOptions$: Observable<TimezoneOption[]>;
  subscriptions: Subscription[] = [];
  showDaysWarning = false;

  constructor(
    private schedulingService: SchedulingService,
    private mycampaignsTableService: MyCampaignsTableService,
  ) {
    this.schedulingForm = new UntypedFormGroup({
      timezone: new UntypedFormControl(),
      includedDays: new UntypedFormControl([]),
      rateLimitEnabled: new UntypedFormControl(true),
      rateLimit: new UntypedFormControl(0),
    });

    this.timezoneOptions$ = this.schedulingForm.controls['timezone'].valueChanges.pipe(
      startWith(''),
      map((val) => this.schedulingService.filteredTimezones(val)),
    );
  }

  ngOnInit(): void {
    this.schedulingService.initCampaignConfig(this.campaignId);
    this.patchForm();
    this.subscriptions.push(this.schedulingService.config$.subscribe(() => this.checkIncludedDays()));

    this.subscriptions.push(
      this.mycampaignsTableService
        .getSender()
        .pipe(
          take(1),
          map((sender: SenderInterface) => {
            this.senderId = sender.id || '';
          }),
        )
        .subscribe(),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  patchForm(): void {
    this.subscriptions.push(
      this.schedulingService.config$
        .pipe(
          map((config: CampaignConfig | null) => {
            this.schedulingForm.patchValue({
              timezone: config?.timezone,
              includedDays: config?.validDays,
              rateLimitEnabled: config?.rateLimitEnabled,
              rateLimit: config?.rateLimit,
            });
          }),
        )
        .subscribe(),
    );
  }

  clearTimezone(): void {
    this.schedulingForm.controls['timezone'].patchValue('');
  }

  checkIncludedDays(): void {
    const includedDays = this.schedulingForm.controls['includedDays'].value;
    this.showDaysWarning = includedDays.length === 0;
  }

  save(): void {
    const senderType = this.senderId.startsWith('AG-')
      ? SenderType.SENDER_TYPE_BUSINESS
      : SenderType.SENDER_TYPE_PARTNER;
    this.schedulingService
      .validateTimezone(this.schedulingForm.controls['timezone'].value, this.timezoneOptions$)
      .pipe(take(1))
      .subscribe({
        next: (res) => {
          if (res) {
            this.schedulingService.updateCampaignConfig(
              this.campaignId,
              {
                timezone: this.schedulingForm.controls['timezone'].value,
                rateLimitEnabled: this.schedulingForm.controls['rateLimitEnabled'].value,
                rateLimit: this.schedulingForm.controls['rateLimit'].value,
                validDays: this.schedulingForm.controls['includedDays'].value,
              },
              { id: this.senderId, type: senderType },
            );
          } else {
            this.schedulingService.displayTimezoneErrorSnack();
          }
        },
      });
  }

  cancel(): void {
    this.schedulingService.resetConfig();
  }
}
