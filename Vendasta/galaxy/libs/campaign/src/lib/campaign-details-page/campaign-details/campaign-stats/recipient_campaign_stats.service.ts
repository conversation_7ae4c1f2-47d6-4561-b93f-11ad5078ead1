import { Injectable } from '@angular/core';
import {
  CampaignStatsService,
  GetCampaignDetailsStatsResponseInterface,
  SenderInterface,
  SenderType,
} from '@vendasta/campaigns';
import { BehaviorSubject, combineLatest, distinct, Observable } from 'rxjs';
import { filter, map, switchMap } from 'rxjs/operators';
import { CampaignEmailStatsService } from '../campaign-email-stats.service';

@Injectable()
export class RecipientCampaignStatsService {
  set campaignID(campaignID: string) {
    this.campaignID$$.next(campaignID);
  }

  set sender(sender: SenderInterface) {
    this.sender$$.next(sender);
  }

  public recipientCampaignStats$: Observable<GetCampaignDetailsStatsResponseInterface>;

  private campaignID$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  private sender$$: BehaviorSubject<SenderInterface> = new BehaviorSubject<SenderInterface>({
    type: SenderType.SENDER_TYPE_INVALID,
    id: '',
  });
  private readonly senderID$ = this.sender$$.pipe(
    filter((sender: SenderInterface) => sender.type !== SenderType.SENDER_TYPE_INVALID),
    filter((sender: SenderInterface) => sender.id !== ''),
    map((sender: SenderInterface): string => {
      if (sender.type === SenderType.SENDER_TYPE_BUSINESS) {
        return 'business/' + sender.id;
      }
      return sender.id || '';
    }),
  );

  constructor(
    private readonly campaignStatsService: CampaignStatsService,
    private readonly emailStatsService: CampaignEmailStatsService,
  ) {
    this.recipientCampaignStats$ = combineLatest([this.campaignID$$, this.senderID$]).pipe(
      distinct(),
      switchMap(([campaignID, senderId]: [string, string]) => {
        return this.campaignStatsService.getCampaignDetailsStats({ partnerId: senderId, campaignId: campaignID });
      }),
    );
  }
}
