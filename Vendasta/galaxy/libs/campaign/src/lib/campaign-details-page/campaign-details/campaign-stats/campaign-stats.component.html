<ng-container *ngIf="recipientCampaignStats$ | async as recipientCampaignStats">
  <ng-container *ngIf="emailstats$ | async as emailStats">
    <div class="container padding">
      <va-card size="half" horizontal>
        <va-card-break-group>
          <va-card-header [title]="{ key: 'STATS.RECIPIENT_STATS.TITLE' }"></va-card-header>
          <va-card-content class="big-stats">
            <div>
              <div class="stat-title" [matTooltip]="'STATS.RECIPIENT_STATS.RECIPIENTS_TOOLTIP' | translate">
                {{ 'STATS.RECIPIENT_STATS.RECIPIENTS' | translate }}
              </div>
              @if (hasEmailStep) {
                <a [routerLink]="[historyPageRoute]">
                  <span class="stat">{{ recipientCampaignStats.total_recipients | formatStat }}</span>
                </a>
              } @else {
                <span class="stat">{{ recipientCampaignStats.total_recipients | formatStat }}</span>
              }
            </div>
          </va-card-content>
        </va-card-break-group>
        <va-card-break-line vertical></va-card-break-line>
        <va-card-break-group>
          <va-card-content class="small-stats">
            <!-- currently only Business App uses this component and the Buffering stat doesn't work for SMBs -->
            <!-- <div>
              <span [matTooltip]="'STATS.RECIPIENT_STATS.BUFFERING_TOOLTIP' | translate">
                {{ 'STATS.RECIPIENT_STATS.BUFFERING' | translate }}
              </span>
              <span class="stat">
                <span class="percentage-stat">{{ percentBuffering$ | async }}</span>
                {{ recipientCampaignStats.waiting_on_rate_limit | formatStat: true }}
              </span>
            </div> -->
            <div>
              <span [matTooltip]="'STATS.RECIPIENT_STATS.IN_PROGRESS_TOOLTIP' | translate">
                {{ 'STATS.RECIPIENT_STATS.IN_PROGRESS' | translate }}
              </span>
              <span class="stat">
                <span class="percentage-stat">{{ percentInProgress$ | async }}</span>
                {{ recipientCampaignStats.active | formatStat: true }}
              </span>
            </div>
            <div>
              <span [matTooltip]="'STATS.RECIPIENT_STATS.STOPPED_TOOLTIP' | translate">
                {{ 'STATS.RECIPIENT_STATS.STOPPED' | translate }}
              </span>
              <span class="stat">
                <span class="percentage-stat">{{ percentStopped$ | async }}</span>
                {{ recipientCampaignStats.stopped | formatStat: true }}
              </span>
            </div>
            <div>
              <span [matTooltip]="'STATS.RECIPIENT_STATS.COMPLETED_TOOLTIP' | translate">
                {{ 'STATS.RECIPIENT_STATS.COMPLETED' | translate }}
              </span>
              <span class="stat">
                <span class="percentage-stat">{{ percentCompleted$ | async }}</span>
                {{ recipientCampaignStats.completed | formatStat: true }}
              </span>
            </div>
          </va-card-content>
        </va-card-break-group>
      </va-card>
      <va-card size="half" horizontal>
        <va-card-break-group>
          <va-card-header [title]="{ key: 'STATS.PERFORMANCE_STATS.TITLE' }"></va-card-header>
          <va-card-content class="big-stats">
            <div *ngIf="focus !== 'adopt'; else adoptStats">
              <div class="stat-title" [matTooltip]="hotLeadsTooltip$ | async">
                {{ 'STATS.PERFORMANCE_STATS.TOTAL_LEADS' | translate }}
              </div>
              @if (hasEmailStep) {
                <a [routerLink]="historyPageRoute" [queryParams]="queryParams">
                  <span class="stat">{{ emailStats.stats.opened.unique_by_email }}</span>
                </a>
              } @else {
                <span class="stat">{{ emailStats.stats.opened.unique_by_email }}</span>
              }

              <mat-icon class="fec-icon fec-icon-hotness hotness-icon"></mat-icon>
            </div>
            <ng-template #adoptStats>
              <div>
                <div class="stat-title" [matTooltip]="clickedTooltip$ | async">
                  {{ 'STATS.EMAIL.CLICKED' | translate }}
                </div>
                <span class="stat">{{ emailStats.stats.clicked.unique_by_email }}</span>
              </div>
              <div>
                <div class="stat-title" [matTooltip]="'STATS.PERFORMANCE_STATS.CLICKED_THROUGH_TOOLTIP' | translate">
                  {{ 'STATS.PERFORMANCE_STATS.CTOR' | translate }}
                </div>
                <span class="stat">{{ percentClickedThroughRate$ | async }}</span>
              </div>
            </ng-template>
          </va-card-content>
        </va-card-break-group>
        <va-card-break-line vertical></va-card-break-line>
        <va-card-break-group>
          <va-card-content class="small-stats">
            <div>
              <span [matTooltip]="'STATS.PERFORMANCE_STATS.DELIVERY_RATE_TOOLTIP' | translate">
                {{ 'STATS.PERFORMANCE_STATS.DELIVERY_RATE' | translate }}
              </span>
              <span class="stat">
                <span class="percentage-stat">{{
                  'STATS.PERFORMANCE_STATS.STAT_PERCENTAGE' | translate: { stat: emailStats.deliveryRate }
                }}</span>
                {{ emailStats.stats.delivered.unique_by_email | formatStat: true }}
              </span>
            </div>
            <div>
              <span [matTooltip]="'STATS.PERFORMANCE_STATS.OPEN_RATE_TOOLTIP' | translate">
                {{ 'STATS.PERFORMANCE_STATS.OPEN_RATE' | translate }}
              </span>
              <span class="stat">
                <span class="percentage-stat">{{
                  'STATS.PERFORMANCE_STATS.STAT_PERCENTAGE' | translate: { stat: emailStats.openRate }
                }}</span>
                {{ emailStats.stats.opened.unique_by_email | formatStat: true }}
              </span>
            </div>
            <div *ngIf="focus !== 'adopt'">
              <span [matTooltip]="'STATS.PERFORMANCE_STATS.CLICKED_THROUGH_TOOLTIP' | translate">
                {{ 'STATS.PERFORMANCE_STATS.CTOR' | translate }}
              </span>
              <span class="stat">
                <span class="percentage-stat">{{
                  'STATS.PERFORMANCE_STATS.STAT_PERCENTAGE' | translate: { stat: emailStats.clickToOpenRate }
                }}</span>
                {{ emailStats.stats.clicked.unique_by_email }}
              </span>
            </div>
            <div>
              <span [matTooltip]="'STATS.PERFORMANCE_STATS.DROPPED_TOOLTIP' | translate">
                {{ 'STATS.PERFORMANCE_STATS.DROPPED' | translate }}
              </span>
              <span class="stat">
                {{ emailStats.stats.dropped.unique_by_email }}
              </span>
            </div>
            <div>
              <span [matTooltip]="'STATS.PERFORMANCE_STATS.BOUNCED_TOOLTIP' | translate">
                {{ 'STATS.PERFORMANCE_STATS.BOUNCED' | translate }}
              </span>
              <span class="stat">
                {{ emailStats.stats.bounced.unique_by_email }}
              </span>
            </div>
          </va-card-content>
        </va-card-break-group>
      </va-card>
    </div>
  </ng-container>
</ng-container>
