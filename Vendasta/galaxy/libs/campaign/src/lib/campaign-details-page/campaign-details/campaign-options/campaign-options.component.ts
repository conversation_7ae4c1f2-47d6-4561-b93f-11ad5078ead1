import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Inject, Input, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { Statuses } from '@vendasta/campaigns';
import { firstValueFrom } from 'rxjs';
import {
  CampaignDuplicateDialogComponent,
  Data,
  DialogResult,
} from '../campaign-duplicate-dialog/campaign-duplicate-dialog.component';
import { CampaignStateService } from '../campaign-state.service';
import { CampaignStatus } from '../interface';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../../../shared/src/tokens';

export type CampaignID = string;

@Component({
  selector: 'campaign-options',
  imports: [CommonModule, TranslateModule, MatMenuModule, MatIconModule, MatButtonModule],
  templateUrl: './campaign-options.component.html',
  styleUrls: ['./campaign-options.component.scss'],
})
export class CampaignOptionsComponent {
  @Input() campaignId: string;
  @Input() partnerId: string;
  @Input() draftMode = false;
  @Input() isCampaignEditable = false;
  @Input() isCampaignHidden = false;
  @Input() campaignHasChildSteps = false;
  @Input() campaignStatus: string | null;
  @Input() updatingStatus = false;

  @Output() campaignDuplicated = new EventEmitter<CampaignID>();

  // TODO: Move the code for these actions into this component to reduce duplication
  @Output() pauseRequested = new EventEmitter<null>();
  @Output() resumeRequested = new EventEmitter<null>();
  @Output() addListRequested = new EventEmitter<null>();
  @Output() archiveRequested = new EventEmitter<null>();
  @Output() deleteRequested = new EventEmitter<null>();

  cs: typeof CampaignStatus = CampaignStatus;
  isPremade = this.campaignStateService.state.campaignDetails.isPremade;

  constructor(
    private readonly dialog: MatDialog,
    private readonly campaignStateService: CampaignStateService,
    @Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE,
  ) {}

  async duplicateCampaign(): Promise<void> {
    const sender = await firstValueFrom(this.config.sender$);
    const data: Data = {
      campaignId: this.campaignId,
      sender: sender,
    };

    const ref = this.dialog.open(CampaignDuplicateDialogComponent, { data: data });
    const result: DialogResult = await firstValueFrom(ref.afterClosed());
    this.campaignDuplicated.emit(result.newCampaignId);
  }

  unpublishCampaign(): void {
    this.campaignStateService.updateStatus(Statuses.STATUSES_DRAFT);
  }
}
