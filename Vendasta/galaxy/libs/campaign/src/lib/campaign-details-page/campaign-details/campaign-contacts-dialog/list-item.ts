import { NgClass, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { RouterLink } from '@angular/router';

import { IconDescriptor, VaIconModule } from '@vendasta/uikit';

@Component({
  selector: 'campaign-app-list-item',
  styleUrl: './list-item.scss',
  template: `
    <div class="va-item-container">
      <i
        *ngIf="icon"
        class="material-icons"
        (click)="itemWasClicked()"
        [style.color]="clickable ? '#1e88e5' : '#6666'"
        [style.cursor]="clickable ? 'pointer' : 'auto'"
      >
        {{ icon }}
      </i>
      <i
        *ngIf="iconClass"
        [ngClass]="iconClass"
        (click)="itemWasClicked()"
        [style.color]="clickable ? '#1e88e5' : '#6666'"
        [style.cursor]="clickable ? 'pointer' : 'auto'"
      ></i>
      <va-icon
        *ngIf="iconDescriptor"
        [iconUrl]="iconDescriptor.iconUrl"
        [iconName]="iconDescriptor.name"
        [diameter]="32"
        [style.color]="clickable ? '#1e88e5' : '#6666'"
        [style.cursor]="clickable ? 'pointer' : 'auto'"
        (click)="itemWasClicked()"
        [routerLink]="targetUrl"
      ></va-icon>
      <div class="va-item-content-container">
        <a
          class="va-item-title"
          [routerLink]="targetUrl"
          (click)="itemWasClicked()"
          [style.color]="clickable ? '#1e88e5' : '#6666'"
          [style.cursor]="clickable ? 'pointer' : 'auto'"
        >
          {{ titleText }}
        </a>
        <div *ngIf="subtitleText" class="va-item-subtitle">{{ subtitleText }}</div>
        <div *ngIf="additionalSubtitleText" class="va-item-subtitle">{{ additionalSubtitleText }}</div>
      </div>
      <span class="fill-remaining-space"></span>
      <div class="va-item-accessory">
        <ng-content select="[secondary-action-item]"></ng-content>
      </div>
    </div>
  `,
  imports: [NgClass, NgIf, RouterLink, VaIconModule],
})
export class CampaignListItemComponent {
  @Input() icon: string;
  @Input() iconDescriptor: IconDescriptor;
  @Input() iconClass: string;
  @Input() titleText: string;
  @Input() subtitleText: string;
  @Input() additionalSubtitleText: string;
  @Input() clickable = true;
  @Input() targetUrl: string = null;

  @Output() itemClicked = new EventEmitter();

  itemWasClicked(): void {
    if (this.clickable && !this.targetUrl) {
      this.itemClicked.emit();
    } else if (this.targetUrl) {
      window.open(this.targetUrl, '_blank');
    }
  }
}
