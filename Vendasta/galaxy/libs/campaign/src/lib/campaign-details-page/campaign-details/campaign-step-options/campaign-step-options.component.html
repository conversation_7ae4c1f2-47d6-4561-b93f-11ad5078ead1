<div class="campaign-step--options">
  <mat-menu #menu>
    <button mat-menu-item (click)="requestPreview()">
      <span *ngIf="draftMode === true">
        {{ 'CAMPAIGN_DETAILS.STEP.PREVIEW' | translate }}
      </span>
      <span *ngIf="draftMode !== true">
        {{ 'CAMPAIGN_DETAILS.STEP.DETAILS' | translate }}
      </span>
    </button>
    <button *ngIf="draftMode === true" mat-menu-item (click)="requestDuplicate()">
      {{ 'CAMPAIGN_DETAILS.STEP.MAKE_A_COPY' | translate }}
    </button>
    <button *ngIf="draftMode && !isArchived" mat-menu-item (click)="requestDelete()">
      {{ 'CAMPAIGN_DETAILS.STEP.DELETE' | translate }}
    </button>
  </mat-menu>
  <button
    *ngIf="isSnapshot === false && isEditable === true"
    mat-button
    color="primary"
    class="edit"
    (click)="requestEdit()"
  >
    <ng-container *ngIf="isSMS; else notSMS">
      {{ 'CAMPAIGN_DETAILS.TEMPLATE.EDIT_SMS' | translate }}
    </ng-container>
    <ng-template #notSMS>
      {{ 'CAMPAIGN_DETAILS.STEP.EDIT' | translate }}
    </ng-template>
  </button>
  <button
    mat-button
    class="menu-button"
    [glxyTooltip]="'CAMPAIGN_DETAILS.STEP.MORE_ACTIONS' | translate"
    [matMenuTriggerFor]="menu"
  >
    <mat-icon [class.snapshot]="isSnapshot">more_vert</mat-icon>
  </button>
</div>
