<mat-icon class="chat-icon">chat</mat-icon>
<h2 mat-dialog-title>{{ 'SMS.TITLE' | translate }}</h2>

<div class="mat-dialog-subtitle">
  <p>
    <i>{{ 'SMS.SUBTITLE' | translate }}</i>
  </p>
</div>
<mat-dialog-content>
  <p>
    <b> {{ 'SMS.DESCRIPTION' | translate }}</b>
  </p>
  <p>{{ 'SMS.DESCRIPTION_2' | translate }}</p>
</mat-dialog-content>

<mat-dialog-actions>
  @if ((smsAddOnPath$ | async) !== null) {
    <button mat-flat-button color="primary" (click)="gotoAddOns()" mat-dialog-close>
      {{ 'SMS.PURCHASE' | translate }}
    </button>
  }
</mat-dialog-actions>
