@use 'design-tokens' as *;

:host {
  display: block;
}

.mat-dialog-subtitle {
  color: #8b8b8b;
  margin-top: $negative-4;
  margin-left: 90px;
  font-size: 12px;
}

.chat-icon {
  color: #8b8b8b75;
  font-size: 49px;
  overflow: visible;
  margin: 20px;
}
.mat-mdc-dialog-actions {
  justify-content: space-between;
  border-top: none;
  margin-top: -15px;
}
.mdc-dialog__title {
  margin-top: -65px;
  margin-left: 60px;
  font-family: Roboto, sans-serif;
  font-size: 22px;
}
