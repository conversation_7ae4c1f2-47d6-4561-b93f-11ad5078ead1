import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Inject, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router, RouterLink } from '@angular/router';
import { EmailStatsModule } from '@galaxy/email-ui/email-activity';
import { DisplayLinkStats } from '@galaxy/email-ui/email-activity/src/email-stats/email-stats';
import { LinkActivityService } from '@galaxy/email-ui/email-activity/src/link-activity/link-activity.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CampaignStepInterfaceInterface, CampaignStepType } from '@vendasta/campaigns';
import { AppNamespace } from '@vendasta/email-builder';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyChatModule } from '@vendasta/galaxy/chat';
import { EmailViewerComponent, GalaxyEmailViewerModule } from '@vendasta/galaxy/email-viewer';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxySideDrawerModule } from '@vendasta/galaxy/side-drawer/src/side-drawer.module';
import { GalaxyStatisticModule } from '@vendasta/galaxy/statistic';
import { Template, TemplatesService, TemplateType } from '@vendasta/templates';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import { distinctUntilChanged, map, switchMap, take } from 'rxjs/operators';
import { CampaignConfig } from '../../../dependencies';
import { PAGE_ROUTES } from '../../../routing-constants';
import { CampaignEmailStatsService } from '../campaign-email-stats.service';
import { StepDetailInterface } from '../campaign-step/campaign-step.component';
import { CampaignStepInterface, CampaignStepStats, isSmsStepStats, StepNumberInterface } from '../interface';
import { MarketingAutomationService } from '../marketing-automation.service';
import { formatTranslation } from '../utils';
import { CampaignSmsStatsService } from '../campaign-sms-stats.service';
import { EmailStepStatsGetter, SMSStepStatsGetter } from '../campaign-stats';
import { CONFIG_TOKEN } from '../../../../../shared/src/tokens';

enum ActivityTypeFilter {
  CLICKED = 'clicked',
  OPENED = 'opened',
  DELIVERED = 'delivered',
  BOUNCED = 'bounced',
  SPAMREPORT = 'spamreport',
  UNSUBSCRIBED = 'unsubscribed',
  DROPPED = 'dropped',
}

interface state {
  stepId: string;
  stepTemplateID: string;
  stepType: CampaignStepType;

  previewStepName: string;
  previewSubject: string;
  previewHTML: string;

  loadingPreview?: boolean;
}

const initialState: state = {
  stepId: '',
  stepTemplateID: '',
  stepType: CampaignStepType.CAMPAIGN_STEP_TYPE_UNSPECIFIED,

  previewStepName: '',
  previewSubject: '',
  previewHTML: '',
};

@Component({
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyEmailViewerModule,
    MatProgressSpinnerModule,
    GalaxyInputModule,
    MatOptionModule,
    MatAutocompleteModule,
    MatFormFieldModule,
    GalaxyAlertModule,
    MatIconModule,
    GalaxyPipesModule,
    GalaxyStatisticModule,
    MatGridListModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    EmailStatsModule,
    MatTooltipModule,
    GalaxySideDrawerModule,
    RouterLink,
    MatButtonModule,
    GalaxyLoadingSpinnerModule,
    GalaxyChatModule,
  ],
  selector: 'campaign-step-details-preview',
  templateUrl: './campaign-step-details-preview.component.html',
  styleUrls: ['./campaign-step-details-preview.component.scss'],
})
export class CampaignStepDetailsPreviewComponent implements OnInit, OnDestroy {
  @ViewChild('preview') emailViewer: EmailViewerComponent;
  @Input() campaignDetails: {
    campaignId: string;
    locale: string;
    isEditable: boolean;
    campaignSchedule: CampaignStepInterfaceInterface[];
  };
  @Input() showStats: boolean;
  @Input() stepData: CampaignStepInterface;
  @Input() initialStepID: string;

  @Output() editEmailFromPreviewClick: EventEmitter<StepDetailInterface> = new EventEmitter();
  @Output() editSMSFromPreviewClick: EventEmitter<StepDetailInterface> = new EventEmitter();

  subscriptions: Subscription[] = [];

  private state$$ = new BehaviorSubject<state>(initialState);

  set state(state: state) {
    this.state$$.next(state);
  }

  get state(): state {
    return this.state$$.getValue();
  }

  activityTypeFilter = ActivityTypeFilter;

  public templatePreview$: Observable<{
    previewSubject: string;
    previewBodyHTML: string;
    previewName: string;
  }>;

  public smsPreview$: Observable<Template> = new Observable<Template>();
  public stepName$: Observable<string>;
  public stepSubject$: Observable<string>;
  public smsSubject$: Observable<string>;

  public emailPreviewWithHighlight$: Observable<string>;
  public smsPreviewWithHighlight$: Observable<string>;

  public stats$: Observable<CampaignStepStats>;
  public stepNumber$: Observable<StepNumberInterface>;

  public openRateTooltip$: Observable<string>;
  public ctrTooltip$: Observable<string>;
  public openedTooltip$: Observable<string>;
  public clickedTooltip$: Observable<string>;
  public deliveredTooltip$: Observable<string>;
  public bouncedTooltip$: Observable<string>;
  public spamTooltip$: Observable<string>;
  public unsubscribedTooltip$: Observable<string>;
  public droppedTooltip$: Observable<string>;
  public pendingTooltip$: Observable<string>;

  public queuedTooltip$: Observable<string>;
  public failedTooltip$: Observable<string>;
  public undeliveredTooltip$: Observable<string>;
  public sentTooltip$: Observable<string>;

  public locale = '';

  linkActivity$: Observable<DisplayLinkStats[]>;
  linkMapping$: Observable<Map<string, string>>;
  shouldDisplayLinkActivity$: Observable<boolean>;

  statsUrl$: Observable<string>;
  backUrl$: Observable<string>;
  isPreviewingSMS$: Observable<boolean>;

  // set state specific observables
  stepID$: Observable<string> = this.state$$.pipe(
    distinctUntilChanged(),
    map((state) => state.stepId),
  );

  templateId$: Observable<string> = this.state$$.pipe(
    distinctUntilChanged(),
    map((state) => state.stepTemplateID),
  );

  stepType$: Observable<CampaignStepType> = this.state$$.pipe(
    distinctUntilChanged(),
    map((state) => state.stepType),
  );

  constructor(
    private translate: TranslateService,
    private router: Router,
    private linkActivityService: LinkActivityService,
    private emailEventStatsService: CampaignEmailStatsService,
    private marketingAutomationService: MarketingAutomationService,
    private templatesService: TemplatesService,
    @Inject(CONFIG_TOKEN) private readonly config: CampaignConfig,
    private smsEventStatsService: CampaignSmsStatsService,
  ) {}

  ngOnInit(): void {
    const currStep = this.getCurrentStepFromInitialStepID();
    this.state = {
      ...this.state,
      stepId: this.initialStepID,
      stepTemplateID: currStep?.templateId || '',
      stepType: currStep?.stepType || CampaignStepType.CAMPAIGN_STEP_TYPE_UNSPECIFIED,
    };
    this.setBackUrl();
    this.setStatsUrl();
    this.locale = this.campaignDetails.locale ? this.campaignDetails.locale : 'en';

    this.templatePreview$ = combineLatest([this.config.sender$, this.templateId$]).pipe(
      switchMap(([sender, templateId]) => {
        return this.marketingAutomationService.getHTMLContentOfEmail(sender, templateId, null, '');
      }),
      map((template) => {
        return {
          previewSubject: template.subject,
          previewBodyHTML: template.html,
          previewName: template.name,
        };
      }),
    );

    this.smsPreview$ = this.templateId$.pipe(
      switchMap((templateId) => {
        return this.templatesService.get(AppNamespace.CAMPAIGNS, templateId);
      }),
    );

    this.stepName$ = this.templatePreview$.pipe(map((templatePreview) => templatePreview.previewName));

    this.stepSubject$ = this.templatePreview$.pipe(map((templatePreview) => templatePreview.previewSubject));
    this.smsSubject$ = this.smsPreview$.pipe(map((smsPreview) => smsPreview.name));
    this.stepNumber$ = this.stepID$.pipe(
      map((stepId) => {
        return {
          totalSteps: this.campaignDetails.campaignSchedule.length,
          currentStep: this.campaignDetails.campaignSchedule.findIndex((step) => step.campaignStepId === stepId),
        };
      }),
    );
    this.linkActivity$ = this.stepID$.pipe(
      switchMap((stepID) =>
        this.linkActivityService.loadLinkActivity({
          key: 'recipient_campaign_step_id',
          value: stepID,
        }),
      ),
    );

    this.stats$ = combineLatest([this.config.sender$, this.stepID$, this.stepType$]).pipe(
      switchMap(([sender, stepId, stepType]) => {
        const statsGetter =
          stepType === CampaignStepType.CAMPAIGN_STEP_TYPE_SMS
            ? new SMSStepStatsGetter(this.smsEventStatsService)
            : new EmailStepStatsGetter(this.emailEventStatsService);
        return statsGetter.getStepStats(sender, stepId);
      }),
    );

    this.isPreviewingSMS$ = this.state$$.pipe(
      map((state) => state.stepType === CampaignStepType.CAMPAIGN_STEP_TYPE_SMS),
    );

    this.setupTooltips();

    this.emailPreviewWithHighlight$ = this.templatePreview$.pipe(
      map((template) => {
        return template.previewBodyHTML;
      }),
    );

    this.smsPreviewWithHighlight$ = this.smsPreview$.pipe(
      switchMap((template) =>
        this.templatesService.render(
          template.content,
          {
            account_group_id: 'want-fake-value-displayed',
            partner_id: 'want-fake-value-displayed',
            contact_id: 'want-fake-value-displayed',
          },
          TemplateType.TEMPLATE_TYPE_GOLANG_TEXT,
          {
            accountGroupId: 'account_group_id',
            partnerId: 'partner_id',
            contactId: 'contact_id',
          },
          undefined,
          undefined,
          undefined,
          true,
        ),
      ),
    );

    this.shouldDisplayLinkActivity$ = this.linkActivity$.pipe(
      map((activity) => activity.length > 0 && activity[0].url !== ''),
    );
  }

  goToStep(stepNumber: number) {
    if (this.campaignDetails.campaignSchedule) {
      const newStep = this.campaignDetails.campaignSchedule.at(stepNumber);
      if (newStep?.campaignStepId && newStep?.templateId) {
        this.state = {
          ...this.state,
          stepId: newStep.campaignStepId,
          stepTemplateID: newStep.templateId,
          stepType: newStep.stepType ?? CampaignStepType.CAMPAIGN_STEP_TYPE_UNSPECIFIED,
        };
      }
    }
  }

  editEmailClicked(): void {
    this.subscriptions.push(
      combineLatest([this.stepID$, this.templateId$])
        .pipe(
          take(1),
          map(([stepId, templateId]) => {
            this.editEmailFromPreviewClick.emit({
              stepId: stepId,
              templateId: templateId,
            });
          }),
        )
        .subscribe(),
    );
  }

  editSMSClicked(): void {
    this.subscriptions.push(
      combineLatest([this.stepID$, this.templateId$])
        .pipe(
          take(1),
          map(([stepId, templateId]) => {
            this.editSMSFromPreviewClick.emit({
              stepId: stepId,
              templateId: templateId,
            });
          }),
        )
        .subscribe(),
    );
  }

  getCurrentStepFromInitialStepID(): CampaignStepInterfaceInterface | undefined {
    if (!this.campaignDetails.campaignSchedule) {
      console.log('no campaign schedule');
      return undefined;
    }
    return this.campaignDetails.campaignSchedule.find((step) => step.campaignStepId === this.initialStepID);
  }

  // toggleHighlightPreview(url: string, expanded: boolean): void {
  //   // ToDO: put this back in
  //   if (!expanded) {
  //     this.subscriptions.push(
  //       this.linkMapping$
  //         .pipe(take(1), startWith(new Map<string, string>()))
  //         .subscribe(linkMapping => {
  //           const escapedUrl = linkMapping.get(url).replace(/[/\-\\^$*+?.()|[\]{}]/g, '\\$&');
  //           const regex = new RegExp(`<a[^>]*href="${escapedUrl}"(.|\\s)*?</a>`);
  //           const htmlWithHighlight = this.emailPreviewHtml.replace(
  //             regex,
  //             (match) =>
  //               `<span class="highlighted-url" style="background:yellow; display:inline-block;">${match}</span>`,
  //           );
  //           this.emailPreviewWithHighlight$ = of(htmlWithHighlight);
  //         }),
  //       fromEvent(this.emailViewer.iframePreview.nativeElement, 'load')
  //         .pipe(take(1))
  //         .subscribe(() => {
  //           const highlightedElement =
  //             this.emailViewer.iframePreview.nativeElement.contentWindow.document.getElementsByClassName(
  //               'highlighted-url',
  //             )[0];
  //           highlightedElement?.scrollIntoView({
  //             behavior: 'smooth',
  //             block: 'center',
  //           });
  //         }),
  //     );
  //   } else {
  //     this.emailPreviewWithHighlight$ = of(this.emailPreviewHtml);
  //   }
  // }

  linkActivityPath(stepId: string, templateId: string): string {
    return `../../../link-activity/${this.campaignDetails.campaignId}/${stepId}/${templateId}`;
  }

  emailActivityPath(): string {
    return `../../${this.campaignDetails.campaignId}/history`;
  }

  setStatsUrl(): void {
    this.statsUrl$ = this.config.basePath$.pipe(
      map(
        (path) =>
          `/${path}/${PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.LINKACTIVITY(
            'recipient_campaign_step_id',
            this.stepData.campaign_step_id,
          )}`,
      ),
    );
  }

  setBackUrl(): void {
    this.backUrl$ = this.config.basePath$.pipe(
      map((path) => {
        return `/${path}/${PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.DETAILS(this.campaignDetails.campaignId)}?templateId=${
          this.stepData.template_id
        }&stepId=${this.stepData.campaign_step_id}`;
      }),
    );
  }

  emailActivityFilters(activityType: ActivityTypeFilter): Observable<string> {
    return this.stepID$.pipe(
      map((stepId) => {
        let filters = `{"${activityType}":"true","event":"${stepId}"`;
        filters += `}`;
        return filters;
      }),
    );
  }

  navigateToEmailActivity(): void {
    const campaignId = this.campaignDetails.campaignId;
    this.subscriptions.push(
      this.config.basePath$.subscribe((basePath) => {
        const url = `${basePath}/${PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.HISTORY(campaignId)}`;
        this.router.navigate([url], {
          queryParams: { opened: true },
        });
      }),
    );
  }

  setupTooltips(): void {
    this.openRateTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.OPEN_RATE_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.OPEN_RATE_TOOLTIP', {
            openRate: stats?.openRatio ? Math.floor(stats?.openRatio?.toNumber() * 100) : 0,
          }),
          stats?.openRatio?.numerator || 0,
        );
      }),
    );
    this.ctrTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        const uniqueClicks = stats?.clickToOpenRatio ? Math.floor(stats.clickToOpenRatio.toNumber() * 100) : 0;
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.CLICK_RATE_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.CLICK_RATE_TOOLTIP', {
            clickRate: uniqueClicks,
          }),
          stats?.clickToOpenRatio?.numerator || 0,
        );
      }),
    );
    this.openedTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.OPEN_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.OPEN_TOOLTIP', {
            opens: stats?.openRatio?.numerator || 0,
          }),
          stats?.openRatio?.numerator || 0,
        );
      }),
    );
    this.clickedTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.CLICK_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.CLICK_TOOLTIP', {
            clicks: stats.clickToOpenRatio?.numerator || 0,
          }),
          stats?.clickToOpenRatio?.numerator || 0,
        );
      }),
    );
    this.pendingTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.PENDING_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.PENDING_TOOLTIP', {
            pending: stats?.pending,
          }),
          stats?.pending,
        );
      }),
    );
    this.bouncedTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.BOUNCED_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.BOUNCED_TOOLTIP', {
            bounces: stats?.bounced,
          }),
          stats?.bounced,
        );
      }),
    );
    this.spamTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.SPAM_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.SPAM_TOOLTIP', {
            spamReport: stats?.spamReport,
          }),
          stats?.spamReport,
        );
      }),
    );
    this.unsubscribedTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.UNSUBSCRIBED_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.UNSUBSCRIBED_TOOLTIP', {
            unsubscribes: stats?.unsubscribed,
          }),
          stats?.unsubscribed,
        );
      }),
    );
    this.droppedTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.EMAIL.DROPPED_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.DROPPED_TOOLTIP', {
            drops: stats?.dropped,
          }),
          stats?.dropped,
        );
      }),
    );
    this.deliveredTooltip$ = this.stats$.pipe(
      map((stats) =>
        formatTranslation(
          this.translate.instant('STATS.EMAIL.DELIVERED_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.EMAIL.DELIVERED_TOOLTIP', {
            delivered: stats?.delivered,
          }),
          stats?.delivered,
        ),
      ),
    );
    this.sentTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (!isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.SMS.SENT_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.SMS.SENT_TOOLTIP', {
            sent: stats?.sent,
          }),
          stats?.sent,
        );
      }),
    );
    this.failedTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (!isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.SMS.FAILED_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.SMS.FAILED_TOOLTIP', {
            failed: stats?.failed,
          }),
          stats?.failed,
        );
      }),
    );
    this.failedTooltip$ = this.stats$.pipe(
      map((stats) => {
        if (!isSmsStepStats(stats)) {
          return '';
        }
        return formatTranslation(
          this.translate.instant('STATS.SMS.UNDELIVERED_TOOLTIP_SINGLE'),
          this.translate.instant('STATS.SMS.UNDELIVERED_TOOLTIP', {
            undelivered: stats?.undelivered,
          }),
          stats?.undelivered,
        );
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
    this.router.navigate([], {
      queryParams: { stepId: null, templateId: null },
      queryParamsHandling: 'merge',
    });
  }
}
