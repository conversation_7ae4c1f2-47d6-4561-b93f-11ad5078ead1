import { SenderInterface } from '@vendasta/campaigns';
import { Context } from '../../campaign-email-builder/page/shared/context';

export interface CampaignData {
  is_enabled: boolean;
  status: string;
  updated: string;
  open_rate: number;
  hidden_for_partners: any[];
  events_count: number;
  emails_delivered: number;
  created: string;
  campaign_id: string;
  campaign_focus: string;
  is_editable: boolean;
  created_user: string;
  accounts_count: number;
  click_through_rate: number;
  has_automations: boolean;
  parent_campaign_id: string;
  active_accounts_count: number;
  is_hidden: boolean;
  is_draft: boolean;
  updated_user: string;
  name: string;
  campaign_schedule: CampaignStepInterface[];
  partner_id: string;
  market_id: string;
  campaign_schedule_version: number;
  locale: string;
  email_category_id: string;
  is_premade: boolean;
}

export interface CampaignPreviewInterface {
  html: string;
  name: string;
  subject: string;
  content?: string;
}

export interface CampaignStepInterface {
  campaign_step_id: string;
  step_type: string;
  template_id: string;
  seconds_after_last_email: number;
  name: string;
}

export interface TemplateData {
  fullHtml: string;
  htmlBody: string;
  name: string;
  subject: string;
  templateId: string;
  useFullHtml: boolean;
}

export interface SMSContentData {
  campaignStepId?: string;
  templateId?: string;
  name?: string;
  message?: string;
  ownerId?: string;
  appNamespace?: string;
  context?: Context;
  stepType?: string;
}

export interface CampaignDelay {
  delay: number;
  validDays: string[];
  timezone: string;
  rateLimitEnabled: boolean;
  rateLimit: number;
}

export interface CampaignConfig {
  validDays: string[];
  timezone: string;
  rateLimitEnabled?: boolean;
  rateLimit?: number;
}

export interface CampaignStepPreviewData {
  html: string;
  name: string;
  subject: string;
}

export type CampaignStepStats = EmailStepStats | SmsStepStats;

export class Ratio {
  numerator: number;
  denominator: number;

  private formatter: (n: number, d: number) => string = (n, d) => {
    return (n / d).toString();
  };

  constructor(numerator: number, denominator: number) {
    this.numerator = numerator;
    this.denominator = denominator;
  }

  withFormatter(f: (n: number, d: number) => string): Ratio {
    this.formatter = f;
    return this;
  }

  toString(): string {
    return this.formatter(this.numerator, this.denominator);
  }

  toNumber(): number {
    return this.numerator / this.denominator;
  }
}

export interface EmailStepStats {
  bounced: number;
  campaign_step_id: string;
  clickedThrough: number;
  created: number;
  delivered: number;
  dropped: number;
  notRequired: number;
  onDeck: number;
  opened: number;
  paused: number;
  pending: number;
  refreshed: number;
  sent: number;
  spamReport: number;
  unsubscribed: number;
  clickToOpenRate: number;
  clickToOpenRatio?: Ratio;
  openRate: number;
  openRatio?: Ratio;
}

export function isSmsStepStats(s: SmsStepStats | EmailStepStats): s is SmsStepStats {
  return (s as SmsStepStats).queued !== undefined;
}

export interface SmsStepStats {
  queued: number;
  sent: number;
  delivered: number;
  undelivered: number;
  failed: number;
}

export interface DuplicateCampaignData {
  campaignId: string;
  sender: SenderInterface;
}

export interface DuplicateCampaignSuccessData {
  newCampaignId: string;
}

export interface EmailStats {
  active: number;
  clicked_through: number;
  completed: number;
  delivered: number;
  opened: number;
  sent: number;
  stopped: number;
  total_accounts: number;
  total_leads: number;
  total_recipients: number;
  undeliverable: number;
  waiting_on_rate_limit: number;
}

export interface RecipientCampaignStats {
  active: number;
  completed: number;
  stopped: number;
  total_accounts: number;
  total_recipients: number;
  waiting_on_rate_limit: number;
  total_leads: number;
}

export interface CampaignStats extends EmailStats, RecipientCampaignStats {}

export interface EmailStat {
  count: number;
  unique: number;
  unique_by_email: number;
}

export interface EmailStatsData {
  processed: EmailStat;
  // processed: number;
  // unique_processed: number;
  // unique_processed_by_email: number;

  delivered: EmailStat;

  // delivered: number;
  // unique_delivered: number;
  // unique_delivered_by_email: number;

  opened: EmailStat;
  // opened: number;
  // unique_opened: number;
  // unique_opened_by_email: number;

  clicked: EmailStat;
  // clicked: number;
  // unique_clicked: number;
  // unique_clicked_by_email: number;

  bounced: EmailStat;
  // bounced: number;
  // unique_bounced: number;
  // unique_bounced_by_email: number;

  deferred: EmailStat;
  // deferred: number;
  // unique_deferred: number;
  // unique_deferred_by_email: number;

  dropped: EmailStat;
  // dropped: number;
  // unique_dropped: number;
  // unique_dropped_by_email: number;

  spamreport: EmailStat;
  // spamreport: number;
  // unique_spamreport: number;
  // unique_spamreport_by_email: number;

  unsubscribed: EmailStat;
  // unsubscribed: number;
  // unique_unsubscribed:
  resubscribed: EmailStat;
  // resubscribed: number;
}

export interface FullEmailStats {
  stats: EmailStatsData;
  deliveryRate?: number;
  openRate?: number;
  clickToOpenRate?: number;
}

export enum CampaignStatus {
  ACTIVE = 'active',
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  UNSPECIFIED = '',
}

export interface StepNumberInterface {
  currentStep: number;
  totalSteps: number;
}

export interface MarketingAutomationPageData {
  key: string;
  title: string;
  imageHeader: {
    title: string;
    subtitle: string;
  };
  showNewPage?: boolean;
  manageTagsCampaignId?: string;
}

export interface Contact {
  id: string;
  name: string;
  email: string;
  disabled?: boolean;
  phoneNumber?: string;
}

export interface ContactsState {
  contacts: Contact[];
}

export interface PagedResponseMetadataInterface {
  nextCursor: string;
  hasMore: boolean;
  totalResults: number;
}

export enum ModalState {
  CONTACTS = 'contacts',
  SCHEDULE = 'schedule',
}

export enum CampaignFocus {
  ACQUIRE = 'acquire',
  ADOPT = 'adopt',
  UPSELL = 'upsell',
  NONE = '',
  UNSPECIFIED = '',
}

export enum ScheduleType {
  NOW = 'now',
  LATER = 'later',
}

export class Market {
  name = '';
  partner_id = '';
  market_id = '';

  constructor(data: any) {
    Object.assign(this, data);
    if (data.pid) {
      this.partner_id = data.pid;
    }
  }
}
