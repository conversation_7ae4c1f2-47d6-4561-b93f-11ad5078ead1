<button class="action-menu" mat-stroked-button color="primary" [matMenuTriggerFor]="menu">
  {{ 'COMMON.ACTION_LABELS.ACTIONS' | translate }}
  <mat-icon iconPositionEnd>arrow_drop_down</mat-icon>
</button>
<mat-menu #menu="matMenu">
  <button
    mat-menu-item
    *ngIf="campaignStatus === cs.PUBLISHED && !isPremade"
    (click)="unpublishCampaign()"
    [disabled]="updatingStatus"
  >
    {{ 'COMMON.ACTION_LABELS.UNPUBLISH' | translate }}
  </button>
  <button mat-menu-item (click)="duplicateCampaign()">
    {{ 'COMMON.ACTION_LABELS.COPY' | translate }}
  </button>
  <button mat-menu-item *ngIf="!draftMode" (click)="pauseRequested.emit()">
    {{ 'COMMON.ACTION_LABELS.PAUSE' | translate }}
  </button>
  <button
    mat-menu-item
    *ngIf="!draftMode && (campaignStatus === cs.PUBLISHED || campaignStatus === cs.ACTIVE)"
    (click)="resumeRequested.emit()"
  >
    {{ 'COMMON.ACTION_LABELS.RESUME' | translate }}
  </button>
  <button
    mat-menu-item
    (click)="archiveRequested.emit()"
    *ngIf="
      (isCampaignEditable && campaignHasChildSteps && campaignStatus !== cs.ARCHIVED) ||
      (!isCampaignEditable && !isCampaignHidden)
    "
  >
    {{ 'COMMON.ACTION_LABELS.ARCHIVE' | translate }}
  </button>
  <button mat-menu-item *ngIf="draftMode && !campaignHasChildSteps" (click)="deleteRequested.emit()">
    {{ 'COMMON.ACTION_LABELS.DELETE' | translate }}
  </button>
</mat-menu>
