<mat-dialog-content>
  <p *ngIf="errored === false">
    <span>Please wait while a copy of this campaign step is created...</span>
    <glxy-loading-spinner size="large"></glxy-loading-spinner>
  </p>
  <p *ngIf="errored === true">
    <span>There was a problem making a copy of the campaign step.</span>
  </p>
</mat-dialog-content>

<mat-dialog-actions *ngIf="errored">
  <button mat-raised-button color="primary" (click)="requestAndPoll()">
    <span>Try Again</span>
  </button>
</mat-dialog-actions>
