@use 'design-tokens' as *;

.scheduling-panel {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.draft-banner {
  width: 69%;
  margin-bottom: 12px;
  display: block;
}

.title {
  margin-top: 28px;
  margin-bottom: 4px;
}
.mat-expansion-panel-header-title,
.mat-expansion-panel-header-description {
  flex-grow: 0;
  flex-basis: auto;
}

.dropdown {
  width: 60%;
  margin-top: 16px;
}

.mat-button-toggle-checked {
  background-color: $primary-color;
  color: $white;
}

.toggle {
  margin-left: 40px;
}

.info-text {
  white-space: pre-line;
  margin-top: 4px;
  font-size: 12px;
  color: $secondary-font-color;
  max-width: 560px;
  line-height: normal;
}

.error-text {
  font-size: $font-preset-5-size;
}

button {
  margin: 30px 0 12px;
}

.clear-icon {
  cursor: pointer;
}

.rate-limit {
  margin-top: 4px;
}
