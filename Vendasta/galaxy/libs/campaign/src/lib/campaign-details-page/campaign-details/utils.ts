import { CampaignStepType } from '@vendasta/campaigns';

/**
 * This will take a numerator and denominator and return a string percentage formatted to
 * at most one decimal point
 * @param numerator
 * @param denominator
 * @return formatted percentage
 */
export function formatStatsPercentage(numerator: number, denominator: number): string {
  let formattedValue = 'N/A';
  if (denominator) {
    // convert to a percentage then round to 1 decimal point
    const percentage = Math.round((numerator / denominator) * 100 * 10) / 10;
    formattedValue = percentage % 1 === 0 ? percentage.toFixed(0) + '%' : percentage.toFixed(1) + '%';
  }
  return formattedValue;
}

/**
 * Takes in a singular translation and a non-singular translation and returns the proper translation depending
 * on the comparison value
 * @param singularTranslation
 * @param pluralTranslation
 * @param comparisonValue
 * @return the singular or plural translation
 */
export function formatTranslation(
  singularTranslation: string,
  pluralTranslation: string,
  comparisonValue: number,
): string {
  return comparisonValue === 1 ? singularTranslation : pluralTranslation;
}

/**
 * This will take a value and format it according to en-US locale. If truncate is set to true, values 1000 and up will
 * be abbreviated
 * @param value
 * @param truncate
 * @return formatted number
 */
export function formatStatsNum(value: number, truncate = false): string {
  if (truncate && value >= 1000) {
    let denominator = 1000;
    let suffix = 'k';

    if (value >= 1000000) {
      denominator = 1000000;
      suffix = 'm';
    }
    const truncated = Math.round((value / denominator) * 10) / 10;
    return truncated % 1 === 0 ? truncated.toFixed(0) + suffix : truncated.toFixed(1) + suffix;
  }
  return value.toLocaleString('en-US');
}

/**
 * Takes a number value and formats it so whole values have no decimal and one decimal place otherwise
 * @param percent
 * @return formatted percentage
 */
export function formatPercentage(percent: number): string {
  return percent % 1 === 0 ? percent.toFixed(0) + '%' : percent.toFixed(1) + '%';
}

export function ConvertSteptypeStringToProto(step: string): CampaignStepType {
  switch (step) {
    case 'CAMPAIGN_STEP_TYPE_UNSPECIFIED':
      return CampaignStepType.CAMPAIGN_STEP_TYPE_UNSPECIFIED;
    case 'CAMPAIGN_STEP_TYPE_EMAIL':
      return CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL;
    case 'CAMPAIGN_STEP_TYPE_SNAPSHOT_CREATION':
      return CampaignStepType.CAMPAIGN_STEP_TYPE_SNAPSHOT_CREATION;
    case 'CAMPAIGN_STEP_TYPE_SMS':
      return CampaignStepType.CAMPAIGN_STEP_TYPE_SMS;
  }
}
