import { BehaviorSubject, catchError, distinct, map, mergeMap, Observable, of, Subscription } from 'rxjs';
import { STANDARD_CRM_FIELD_EXTERNAL_IDS } from '@galaxy/conversation/core';
import { CRMApiService, CrmObject, CrmObjectSearch, FieldValue, ListCrmObjectsRequest } from '@vendasta/crm';
import { Inject, Injectable } from '@angular/core';
import { Contact } from '../interface';
import { distinctUntilChanged, filter, share, switchMap, tap } from 'rxjs/operators';
import {
  ContactType,
  PagedResponseMetadata,
  RecipientCampaignService,
  RecipientType,
  SenderType,
} from '@vendasta/campaigns';
import { CampaignData } from './campaign-contacts-dialog.component';
import { SENDER_ID_TOKEN } from '@galaxy/email-ui/email-activity/src/dependencies';

export interface State<Contact> {
  searchTerm?: string;
  pagingOptions?: PagedResponseMetadata;
  dataMembers?: Contact[];
  pageSize: number;
  searchFields?: string[];
  loading?: boolean;
  shouldRefetch?: boolean;
}

export interface ScrollEvent {
  pageSize: number;
}

@Injectable()
export class CampaignContactsDataService {
  private stateSubscription: Subscription | null;
  private state$$ = new BehaviorSubject<State<Contact>>(this.defaultState());
  state$ = this.state$$.asObservable();
  private searchTerm: string;
  loading$ = this.state$.pipe(map(({ loading }) => loading));
  dataMembers$ = this.state$.pipe(map(({ dataMembers = [] }) => dataMembers));
  campaignData: CampaignData;

  constructor(
    @Inject(SENDER_ID_TOKEN) readonly senderId$: Observable<string>,
    private readonly crmService: CRMApiService,
    private readonly recipientCampaignService: RecipientCampaignService,
  ) {}

  private getContactsFromCrmService(senderID: string, term: string): Observable<Array<Contact>> {
    return this.crmService
      .listContacts({
        namespace: senderID,
        search: { searchTerm: term } as CrmObjectSearch,
        pagingOptions: {
          pageSize: this.state.pageSize,
          cursor: this.state.pagingOptions?.hasMore ? this.state.pagingOptions.nextCursor : undefined,
        },
      } as ListCrmObjectsRequest)
      .pipe(
        tap((response) => (this.state.pagingOptions = response.pagingMetadata)),
        map(
          (response) => {
            return response?.crmObjects.map((contact: CrmObject) => this.getContactToContact(contact));
          },
          catchError(() => of([] as Array<Contact>)),
        ),
        share(),
      );
  }

  private createContactFieldMap(contactFields: CrmObject): Map<string, FieldValue> {
    const contactFieldMap = new Map<string, FieldValue>();
    contactFields.fields.forEach((field) => {
      if (field?.fieldId) contactFieldMap.set(field.externalId, field);
    });
    return contactFieldMap;
  }

  private getContactToContact(contactFields: CrmObject): Contact {
    const contactFieldMap = this.createContactFieldMap(contactFields);
    return {
      id: contactFields.crmObjectId,
      name:
        [
          contactFieldMap.get(STANDARD_CRM_FIELD_EXTERNAL_IDS.firstName)?.stringValue ?? '',
          contactFieldMap.get(STANDARD_CRM_FIELD_EXTERNAL_IDS.lastName)?.stringValue ?? '',
        ].join(' ') ?? '',
      email: contactFieldMap.get(STANDARD_CRM_FIELD_EXTERNAL_IDS.email)?.stringValue ?? '',
      phoneNumber: contactFieldMap.get(STANDARD_CRM_FIELD_EXTERNAL_IDS.phoneNumber)?.stringValue ?? '',
    };
  }

  getContacts(search: string): Observable<Array<Contact>> {
    return this.senderId$.pipe(
      distinct(),
      switchMap((senderId: string) => this.getContactsFromCrmService(senderId, search).pipe(share())),
    );
  }

  private defaultState(): State<Contact> {
    return {
      dataMembers: [],
      searchTerm: this.searchTerm,
      pageSize: 10,
      pagingOptions: undefined,
      loading: false,
      shouldRefetch: true,
    };
  }

  get state(): State<Contact> {
    return this.state$$.getValue();
  }

  set state(newState: State<Contact>) {
    this.state$$.next({ ...this.state, ...newState });
  }

  connect(): void {
    this.stateSubscription = this.state$
      .pipe(
        filter((state) => !!state.shouldRefetch),
        distinctUntilChanged(),
        switchMap(({ searchTerm }) => {
          return this.getContacts(searchTerm || '').pipe(
            map((rows) => {
              let filteredRows = rows.filter((row: Contact) => row.email !== '');
              if (this.campaignData.senderType === SenderType.SENDER_TYPE_BUSINESS) {
                filteredRows = rows.filter((row: Contact) => row.email !== '' || row.phoneNumber !== '');
              }
              const recipients = filteredRows.map((row: Contact) => {
                return {
                  id: row.id,
                  type: RecipientType.RECIPIENT_TYPE_CRM_CONTACT,
                  contact: {
                    type: ContactType.CONTACT_TYPE_EMAIL,
                    value: row.email,
                  },
                  emailAddress: row.email,
                };
              });
              return { rows: filteredRows, recipients };
            }),
            catchError(() => of({ rows: [], recipients: [] })),
          );
        }),
        mergeMap(({ rows, recipients }) => {
          return this.recipientCampaignService
            .lookupRecipientCampaigns(
              this.campaignData.campaignId,
              {
                type: this.campaignData.senderType,
                id: this.campaignData.senderId,
              },
              recipients,
            )
            .pipe(
              map((resp) => {
                return rows.map((row) => {
                  if (
                    resp?.recipientCampaigns?.some(
                      (recipient) => recipient.accountGroupId.replace('crmcontact/', '') === row.id,
                    )
                  ) {
                    row.disabled = true;
                  }
                  return row;
                });
              }),
            );
        }),
      )
      .subscribe({
        next: (response) => this.pageDataReceived(response),
      });
  }

  disconnect(): void {
    if (this.stateSubscription) {
      this.stateSubscription.unsubscribe();
    }
  }

  pageDataReceived(response: Contact[]): void {
    requestAnimationFrame(() => {
      this.state = {
        ...this.state,
        dataMembers: this.state.dataMembers?.concat(response),
        loading: false,
        shouldRefetch: false,
      };
    });
  }

  resetPagingAndSearch() {
    this.state = {
      ...this.state,
      pagingOptions: undefined,
      searchTerm: '',
    };
  }

  setSearchTerm(searchTerm: string): void {
    const state = this.state;
    this.searchTerm = searchTerm;

    this.state = {
      ...this.defaultState(),
      searchTerm,
      pageSize: state.pageSize,
      loading: true,
      shouldRefetch: true,
    };
  }

  loadNext(page: ScrollEvent): void {
    if (this.state.pagingOptions?.hasMore) {
      this.state = {
        ...this.state,
        pageSize: page.pageSize,
        loading: true,
        shouldRefetch: true,
      };
    }
  }
}
