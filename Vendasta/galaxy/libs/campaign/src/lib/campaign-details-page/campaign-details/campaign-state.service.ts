import { CampaignsService } from './campaigns.service';
import { Injectable } from '@angular/core';
import { CampaignStepType, GetterCampaignDataInterface, SenderInterface, Statuses } from '@vendasta/campaigns';
import { CampaignStepInterface } from './interface';
import { BehaviorSubject, Observable, Subscription, firstValueFrom } from 'rxjs';
import { distinctUntilChanged, filter, map, switchMap, take } from 'rxjs/operators';
import { Context } from '../../campaign-email-builder/page/shared/context';

@Injectable()
export class CampaignStateService {
  private store$$: BehaviorSubject<State> = new BehaviorSubject<State>(InitialState);

  private campaignId$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  private sender$$: BehaviorSubject<SenderInterface> = new BehaviorSubject<SenderInterface>({});

  private serverSyncSubscriptions: Subscription[] = [];

  public setCampaignId(campaignId: string): void {
    this.campaignId$$.next(campaignId);
  }

  public setSender(sender: SenderInterface): void {
    this.sender$$.next(sender);
  }

  constructor(private readonly campaignsService: CampaignsService) {
    this.campaignId$$
      .pipe(
        filter((campaignId) => !!campaignId),
        switchMap((campaignId) => this.campaignsService.get(campaignId)),
      )
      .subscribe((campaign) => {
        const campaignSteps: CampaignStepInterface[] = [];
        for (const step of campaign.campaignSchedule) {
          const newStep: CampaignStepInterface = {
            campaign_step_id: step.campaignStepId,
            step_type: stepTypeToString(step.stepType),
            template_id: step.templateId,
            seconds_after_last_email: step.secondsAfterLastEmail || 0,
            name: step.name,
          };
          campaignSteps.push(newStep);
        }

        this.updateState({ campaignDetails: campaign });
        this.updateState({ campaignStatus: campaign.status });
        this.updateState({ name: campaign.name });
        this.updateState({ campaignSteps: campaignSteps });
        this.updateState({ locale: campaign.locale });
      });
  }

  //TODO: make this private
  get state(): State {
    return this.store$$.getValue();
  }

  updateStatus(status: Statuses): void {
    this.campaignsService.updateStatus(this.sender$$.getValue(), this.campaignId$$.getValue(), status).subscribe(() => {
      this.updateState({ campaignStatus: status });
    });
  }

  async pauseCampaign(): Promise<void> {
    const campaignId = await firstValueFrom(this.campaignId$$);
    this.campaignsService.pauseCampaign(campaignId).subscribe();
  }

  async unpauseCampaign(): Promise<void> {
    const campaignId = await firstValueFrom(this.campaignId$$);
    this.campaignsService.unpauseCampaign(campaignId).subscribe();
  }

  updateName(name: string): void {
    this.campaignsService.updateName(this.sender$$.getValue(), this.campaignId$$.getValue(), name).subscribe(() => {
      this.updateState({ name: name });
    });
  }

  updateLocale(locale: string): void {
    this.campaignsService.updateLocale(this.sender$$.getValue(), this.campaignId$$.getValue(), locale).subscribe(() => {
      this.updateState({ locale: locale });
    });
  }

  updateCampaignSchedule(campaignSteps: CampaignStepInterface[]): void {
    this.updateState({ campaignSteps });
  }

  updateCampaignDelay(campaignSteps: CampaignStepInterface[]): void {
    this.campaignsService
      .updateCampaignSchedule(this.sender$$.getValue(), this.campaignId$$.getValue(), campaignSteps)
      .subscribe(() => {
        this.updateState({ campaignSteps });
      });
  }

  private updateState(newState: StateInterface): void {
    this.store$$.next({ ...this.state, ...newState });
  }

  removeCampaignStep(sender: SenderInterface, campaignId: string, stepId: string): void {
    const events = [...this.state.campaignSteps].filter((step) => step.campaign_step_id !== stepId);
    this.campaignsService.updateCampaignSchedule(sender, campaignId, events).subscribe({
      next: () => {
        this.updateCampaignSchedule(events);
      },
    });
  }

  getCampaignSteps(): number {
    return this.state.campaignSteps.length;
  }

  addCampaignStep(
    ctx: Context,
    campaignId: string,
    templateId: string,
    templateName: string,
    stepType: CampaignStepType = CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL,
  ): void {
    const stringStepType = stepTypeToString(stepType);
    this.campaignsService
      .addStepToCampaign(
        {
          type: ctx.getSenderType(),
          id: ctx.getOwnerId(),
        },
        campaignId,
        stepType,
        templateId,
        templateName,
      )
      .pipe(
        switchMap((resp) =>
          this.campaignSteps$.pipe(
            take(1),
            map((campaignSteps) => {
              const secondsAfterLastEmail = campaignSteps.length > 0 ? 604800 : 0;
              const newStep: CampaignStepInterface = {
                campaign_step_id: resp.campaignStepId,
                step_type: stringStepType,
                template_id: resp.templateId,
                seconds_after_last_email: secondsAfterLastEmail,
                name: templateName,
              };
              return [...campaignSteps, newStep];
            }),
          ),
        ),
        map((updatedCampaignSteps) => this.updateCampaignSchedule(updatedCampaignSteps)),
        take(1),
      )
      .subscribe();
  }

  updateCampaignStep(
    ctx: Context,
    campaignId: string,
    campaignStepId: string,
    templateId: string,
    templateName: string,
    secondsAfterLastStep?: number,
    stepType: CampaignStepType = CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL,
  ): void {
    const stringStepType = stepTypeToString(stepType);
    this.campaignsService
      .updateStepOnCampaign(
        {
          type: ctx.getSenderType(),
          id: ctx.getOwnerId(),
        },
        campaignId,
        campaignStepId,
        templateId,
        templateName,
        secondsAfterLastStep,
        {
          paths: this.buildFieldMask(templateName, secondsAfterLastStep),
        },
      )
      .pipe(
        switchMap((resp) =>
          this.campaignSteps$.pipe(
            map((campaignSteps) => {
              const updatedStep: CampaignStepInterface = {
                campaign_step_id: resp.campaignStepId,
                step_type: stringStepType,
                template_id: resp.templateId,
                seconds_after_last_email: secondsAfterLastStep,
                name: templateName,
              };
              return campaignSteps.map((step) => {
                if (step.campaign_step_id === campaignStepId) {
                  return updatedStep;
                }
                return step;
              });
            }),
          ),
        ),
        map((updatedCampaignSteps) => this.updateCampaignSchedule(updatedCampaignSteps)),
        take(1),
      )
      .subscribe();
  }

  private buildFieldMask(name: string | undefined, secondsAfterLastStep: number | undefined): string[] {
    const paths: string[] = [];
    if (name !== undefined) {
      paths.push('Name');
    }
    if (secondsAfterLastStep !== undefined) {
      paths.push('SecondsAfterLastStep');
    }
    return paths;
  }

  campaignStatus$ = this.store$$.pipe(
    map((state) => state.campaignStatus),
    distinctUntilChanged(),
  );

  selectedMarketId$ = this.store$$.pipe(
    map((state) => state.selectedMarketId),
    distinctUntilChanged(),
  );

  campaignSteps$ = this.store$$.pipe(
    map((state) => state.campaignSteps),
    distinctUntilChanged(),
  );

  campaignState$ = this.store$$.pipe(
    map((state) => state.campaignState),
    distinctUntilChanged(),
  );

  locale$ = this.store$$.pipe(
    map((state) => state.locale),
    distinctUntilChanged(),
  );

  name$: Observable<string> = this.store$$.pipe(
    map((state: State): string => state.name || ''), // TODO: do I need this  || ''?
    distinctUntilChanged(),
  );

  emailCategory$: Observable<string> = this.store$$.pipe(
    map((state: State) => state.emailCategory || ''), // TODO: do I need this  || ''?
    distinctUntilChanged(),
  );

  // TODO: deprecate this
  campaignDetailsV2$: Observable<GetterCampaignDataInterface> = this.store$$.pipe(
    map((state: State) => state.campaignDetails || {}),
    filter((campaignDetails) => !!campaignDetails),
    distinctUntilChanged(),
  );
}

class State {
  campaignDetails: GetterCampaignDataInterface; // TODO: deprecate this
  selectedMarketId: string;
  campaignSteps: CampaignStepInterface[];
  campaignStatus: Statuses;
  campaignFocus: string;
  name: string;
  emailCategory: string;
  campaignState: string;
  locale: string;

  constructor(state: StateInterface) {
    this.campaignDetails = state.campaignDetails || InitialState.campaignDetails; //TODO: deprecate this
    this.selectedMarketId = state.selectedMarketId || InitialState.selectedMarketId;
    this.campaignSteps = state.campaignSteps || InitialState.campaignSteps;
    this.campaignStatus = state.campaignStatus || InitialState.campaignStatus;
    this.campaignFocus = state.campaignFocus || InitialState.campaignFocus;
    this.name = state.name || InitialState.name;
    this.emailCategory = state.emailCategory || InitialState.emailCategory;
    this.campaignState = state.campaignState || InitialState.campaignState;
    this.locale = state.locale || InitialState.locale;
  }
}

interface StateInterface {
  campaignDetails?: GetterCampaignDataInterface; // TODO: deprecate this
  selectedMarketId?: string;
  campaignSteps?: CampaignStepInterface[];
  campaignStatus?: Statuses;
  campaignFocus?: string;
  name?: string;
  emailCategory?: string;
  campaignState?: string;
  locale?: string;
}

const InitialState: State = {
  campaignDetails: {},
  selectedMarketId: '',
  campaignSteps: [],
  campaignStatus: Statuses.STATUSES_UNSPECIFIED,
  campaignFocus: '',
  name: '',
  emailCategory: '',
  campaignState: '',
  locale: 'en',
};

function stepTypeToString(stepType: CampaignStepType): string {
  switch (stepType) {
    case CampaignStepType.CAMPAIGN_STEP_TYPE_UNSPECIFIED:
      return 'CAMPAIGN_STEP_TYPE_UNSPECIFIED';
    case CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL:
      return 'CAMPAIGN_STEP_TYPE_EMAIL';
    case CampaignStepType.CAMPAIGN_STEP_TYPE_SNAPSHOT_CREATION:
      return 'CAMPAIGN_STEP_TYPE_SNAPSHOT_CREATION';
    case CampaignStepType.CAMPAIGN_STEP_TYPE_SMS:
      return 'CAMPAIGN_STEP_TYPE_SMS';
    default:
      return 'CAMPAIGN_STEP_TYPE_UNSPECIFIED';
  }
}
