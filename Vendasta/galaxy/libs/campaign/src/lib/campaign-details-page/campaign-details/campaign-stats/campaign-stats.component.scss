@use 'design-tokens' as *;

.container {
  display: flex;
  flex-flow: row;
  flex-wrap: wrap;
}

.half {
  flex-basis: 50%;
}

.padding {
  padding-bottom: 15px;
}

va-card:nth-of-type(1) {
  padding-right: 8px;
  @media (max-width: 800px) {
    padding-right: 0;
  }
}

va-card:nth-of-type(2) {
  padding-left: 8px;
  @media (max-width: 800px) {
    padding-left: 0;
    margin-top: 16px;
  }
}

va-card-break-group:first-of-type {
  padding-left: 8px;
}

.big-stats {
  display: flex;
  padding-bottom: 50px;
  @media (max-width: 800px) {
    padding-bottom: 16px;
  }

  & > div {
    flex: 0 0 50%;
  }

  .stat {
    font-size: 30px;
  }

  .stat-title {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 10px;
    color: $dark-gray;
  }
}

.small-stats {
  padding: 0;
  color: $secondary-text-color;
  @media (max-width: 800px) {
    padding-top: 0;
  }

  & div:nth-child(even) {
    background-color: $secondary-background-color;
  }

  & > div {
    display: block;
    justify-content: space-between;
    padding: 8px 24px 8px 10px;
  }

  .percentage-stat {
    color: $primary-text-color;
    font-weight: bold;
  }

  .stat {
    justify-content: space-between;
    display: flex;
    min-width: 60px;
    padding-top: 2px;
  }
}

.fec-icon-hotness:before {
  vertical-align: middle;
  padding-bottom: 22px;
}

.hotness-icon {
  padding-left: 8px;
  color: $red;
}

:host ::ng-deep .va-card-header mat-card-title {
  padding-right: 0 !important;
}
