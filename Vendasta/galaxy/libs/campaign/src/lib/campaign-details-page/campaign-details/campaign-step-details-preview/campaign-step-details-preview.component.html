<glxy-closing-side-drawer
  [ngClass]="showStats ? 'with-stats-panel' : 'no-stats-panel'"
  [title]="(stepName$ | async) || (smsSubject$ | async)"
>
  <glxy-side-drawer-title-actions *ngIf="stepNumber$ | async as stepNumber">
    <button mat-button *ngIf="stepNumber.currentStep !== 0" (click)="goToStep(stepNumber.currentStep - 1)">
      <mat-icon>chevron_left</mat-icon>
      {{ 'PREVIEW.PREVIOUS' | translate }}
    </button>
    <span class="step-position">
      {{
        'PREVIEW.COUNT'
          | translate
            : {
                curr: (stepNumber$ | async)?.currentStep + 1,
                total: (stepNumber$ | async)?.totalSteps
              }
      }}
    </span>
    <button
      mat-button
      *ngIf="stepNumber.currentStep !== stepNumber.totalSteps - 1"
      (click)="goToStep(stepNumber.currentStep + 1)"
    >
      {{ 'PREVIEW.NEXT' | translate }}
      <mat-icon>chevron_right</mat-icon>
    </button>
  </glxy-side-drawer-title-actions>
  <glxy-side-drawer-content [ngClass]="{ 'preview-with-stats': showStats }">
    <ng-container *ngIf="showStats">
      <div *ngIf="stats$ | async as stepStats" class="stats-container">
        <div class="stats-header">
          <ng-container *ngIf="isPreviewingSMS$ | async; else isEmail">
            {{ 'CAMPAIGN_DETAILS.STEP.SMS_PERFORMANCE' | translate }}
          </ng-container>
          <ng-template #isEmail>
            {{ 'CAMPAIGN_DETAILS.STEP.EMAIL_PERFORMANCE' | translate }}
          </ng-template>
        </div>

        <ng-container *ngIf="(isPreviewingSMS$ | async) === false">
          <mat-card appearance="outlined">
            <mat-card-content>
              <mat-grid-list cols="2" rowHeight="4rem">
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="openRateTooltip$ | async">
                      {{ 'STATS.OPEN_RATE' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>{{ stepStats.openRatio.toString() }}</glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="ctrTooltip$ | async">
                      {{ 'STATS.CLICK_RATE' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>{{ stepStats.clickToOpenRatio.toString() }}</glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="openedTooltip$ | async">
                      {{ 'STATS.OPENED' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>
                      <a
                        [routerLink]="emailActivityPath()"
                        [queryParams]="{ filters: (this.emailActivityFilters(activityTypeFilter.OPENED) | async) }"
                      >
                        {{ stepStats.openRatio?.numerator || 0 }}
                      </a>
                    </glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="clickedTooltip$ | async">
                      {{ 'STATS.CLICKS' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>
                      <a
                        [routerLink]="emailActivityPath()"
                        [queryParams]="{ filters: (this.emailActivityFilters(activityTypeFilter.CLICKED) | async) }"
                      >
                        {{ stepStats.clickToOpenRatio?.numerator || 0 }}
                      </a>
                    </glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
              </mat-grid-list>
            </mat-card-content>
          </mat-card>
          <div *ngIf="shouldDisplayLinkActivity$ | async">
            <div>
              <email-link-performance
                [linkActivityPath]="statsUrl$ | async"
                [linkActivity]="linkActivity$ | async"
                [backUrl]="backUrl$ | async"
              ></email-link-performance>
              <!--        (linkExpansionClicked)="toggleHighlightPreview($event.normalizedUrl, $event.expanded)"-->
            </div>
          </div>
        </ng-container>

        <div class="page-subtitle">{{ 'STATS.DELIVERY_STATUS' | translate }}</div>
        <mat-card appearance="outlined">
          <mat-card-content>
            <mat-grid-list cols="2" rowHeight="4rem">
              <mat-grid-tile>
                <glxy-statistic>
                  <glxy-statistic-title [matTooltip]="deliveredTooltip$ | async">
                    {{ 'STATS.DELIVERED' | translate }}
                  </glxy-statistic-title>
                  <glxy-statistic-value>{{ stepStats.delivered }}</glxy-statistic-value>
                </glxy-statistic>
              </mat-grid-tile>
              <ng-container *ngIf="isPreviewingSMS$ | async; else showPending">
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="queuedTooltip$ | async">
                      {{ 'STATS.QUEUED' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>{{ stepStats.queued }}</glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
              </ng-container>
              <ng-template #showPending>
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="pendingTooltip$ | async">
                      {{ 'STATS.PENDING' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>{{ stepStats.pending }}</glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
              </ng-template>
            </mat-grid-list>
          </mat-card-content>
        </mat-card>

        <mat-card appearance="outlined">
          <mat-card-content>
            <mat-grid-list cols="2" rowHeight="4rem">
              <ng-container *ngIf="isPreviewingSMS$ | async; else showEmailStats">
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="sentTooltip$ | async">
                      {{ 'STATS.SENT' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>
                      {{ stepStats.sent }}
                    </glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="undeliveredTooltip$ | async">
                      {{ 'STATS.UNDELIVERED' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>
                      {{ stepStats.undelivered }}
                    </glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="failedTooltip$ | async">
                      {{ 'STATS.FAILED' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>
                      {{ stepStats.failed }}
                    </glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
              </ng-container>
              <ng-template #showEmailStats>
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="bouncedTooltip$ | async">
                      {{ 'STATS.BOUNCED' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>
                      <a
                        [routerLink]="emailActivityPath()"
                        [queryParams]="{ filters: (this.emailActivityFilters(activityTypeFilter.BOUNCED) | async) }"
                      >
                        {{ stepStats.bounced }}
                      </a>
                    </glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="spamTooltip$ | async">
                      {{ 'STATS.SPAM' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>
                      <a
                        [routerLink]="emailActivityPath()"
                        [queryParams]="{ filters: (this.emailActivityFilters(activityTypeFilter.SPAMREPORT) | async) }"
                      >
                        {{ stepStats.spamReport }}
                      </a>
                    </glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="unsubscribedTooltip$ | async">
                      {{ 'STATS.UNSUBSCRIBED' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>
                      <a
                        [routerLink]="emailActivityPath()"
                        [queryParams]="{
                          filters: (this.emailActivityFilters(activityTypeFilter.UNSUBSCRIBED) | async)
                        }"
                      >
                        {{ stepStats.unsubscribed }}
                      </a>
                    </glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
                <mat-grid-tile>
                  <glxy-statistic>
                    <glxy-statistic-title [matTooltip]="droppedTooltip$ | async">
                      {{ 'STATS.DROPPED' | translate }}
                    </glxy-statistic-title>
                    <glxy-statistic-value>
                      <a
                        [routerLink]="emailActivityPath()"
                        [queryParams]="{ filters: (this.emailActivityFilters(activityTypeFilter.DROPPED) | async) }"
                      >
                        {{ stepStats.dropped }}
                      </a>
                    </glxy-statistic-value>
                  </glxy-statistic>
                </mat-grid-tile>
              </ng-template>
            </mat-grid-list>
          </mat-card-content>
        </mat-card>

        <ng-container *ngIf="(isPreviewingSMS$ | async) === false">
          <div class="email-activity-btn">
            <button mat-flat-button color="primary" (click)="navigateToEmailActivity()">
              {{ 'CAMPAIGN_DETAILS.STEP.VIEW_EMAIL_ACTIVITY' | translate }}
            </button>
          </div>
        </ng-container>
      </div>
    </ng-container>

    <div *ngIf="emailPreviewWithHighlight$ | async as emailContent; else smsPreview" class="preview-step">
      <div class="step-preview-header">
        {{ 'CAMPAIGN_DETAILS.STEP.EMAIL_PREVIEW' | translate }}
        <button
          mat-stroked-button
          *ngIf="campaignDetails.isEditable"
          color="primary"
          class="edit-step-btn"
          (click)="editEmailClicked()"
        >
          {{ 'CAMPAIGN_DETAILS.STEP.EDIT' | translate }}
        </button>
      </div>
      <div class="step-subject-line">
        {{ 'CAMPAIGN_DETAILS.STEP.SUBJECT' | translate }}
        <strong><span [innerHtml]="stepSubject$ | async"></span></strong>
      </div>
      <glxy-email-viewer #preview [html]="emailContent" [showEmailInline]="true"></glxy-email-viewer>
    </div>
    <ng-template #smsPreview>
      <div *ngIf="smsPreviewWithHighlight$ | async as smsContent" class="preview-step">
        <div class="step-preview-header">
          {{ 'CAMPAIGN_DETAILS.STEP.SMS_PREVIEW' | translate }}
          <button
            mat-stroked-button
            *ngIf="campaignDetails.isEditable"
            color="primary"
            class="edit-step-btn"
            (click)="editSMSClicked()"
          >
            {{ 'CAMPAIGN_DETAILS.STEP.EDIT_SMS' | translate }}
          </button>
        </div>
        <div class="step-subject-line">
          {{ 'CAMPAIGN_DETAILS.STEP.NAME' | translate }}
          <strong>
            <span>{{ smsSubject$ | async }}</span>
          </strong>
        </div>
        <glxy-chat-message [itemContent]="'message'" [messageText]="smsContent" class="sms-message"></glxy-chat-message>
      </div>
    </ng-template>
  </glxy-side-drawer-content>
</glxy-closing-side-drawer>
<ng-template #loading>
  <glxy-loading-spinner [fullHeight]="true" [fullWidth]="true"></glxy-loading-spinner>
</ng-template>
