import { CommonModule } from '@angular/common';
import { Component, Inject, On<PERSON>estroy, OnInit } from '@angular/core';
import { FormsModule, UntypedFormControl } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ContactType, RecipientCampaignService, RecipientType, SenderType } from '@vendasta/campaigns';
import { VaFormsModule } from '@vendasta/forms';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyTableModule } from '@vendasta/galaxy/table';
import { VaFilterModule } from '@vendasta/uikit';
import { BehaviorSubject, forkJoin, Observable, of, Subject } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Contact, ModalState, ScheduleType } from '../interface';
import { CampaignContactsDataService } from './campaign-contacts-data-service';
import { CampaignListItemComponent } from './list-item';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

//  TODO: potential to remove this dialog with the release of the new campaign preview modal
export interface CampaignData {
  campaignId: string;
  senderType: SenderType;
  senderId: string;
}

@Component({
  imports: [
    CommonModule,
    MatButtonModule,
    TranslateModule,
    GalaxyLoadingSpinnerModule,
    MatDialogModule,
    GalaxyTableModule,
    MatTableModule,
    GalaxyEmptyStateModule,
    MatIconModule,
    GalaxyFormFieldModule,
    MatRadioModule,
    FormsModule,
    VaFormsModule,
    MatDatepickerModule,
    MatInputModule,
    GalaxyButtonLoadingIndicatorModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    VaFilterModule,
    GalaxyInfiniteScrollTriggerModule,
    CampaignListItemComponent,
  ],
  templateUrl: './campaign-contacts-dialog.component.html',
  styleUrls: ['./campaign-contacts-dialog.component.scss'],
  providers: [CampaignContactsDataService, RecipientCampaignService],
})
export class CampaignContactsDialogComponent implements OnInit, OnDestroy {
  constructor(
    private readonly translate: TranslateService,
    private campaignContactDataSvc: CampaignContactsDataService,
    @Inject(MAT_DIALOG_DATA) private readonly data: CampaignData,
    private readonly recipientCampaignService: RecipientCampaignService,
    readonly ref: MatDialogRef<CampaignContactsDialogComponent>,
    private snackbarService: SnackbarService,
    private posthogService: ProductAnalyticsService,
  ) {}

  Contacts$ = new Observable<Contact[]>();

  modalState$$: BehaviorSubject<string> = new BehaviorSubject<string>(ModalState.CONTACTS);
  scheduleOption: ScheduleType;
  selectedDate: Date;
  nowDate: string;
  startTime: UntypedFormControl = new UntypedFormControl();
  loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  loading$: Observable<boolean> = this.loading$$.asObservable();
  dataMembers$ = this.campaignContactDataSvc.dataMembers$;
  dataServiceLoading$ = this.campaignContactDataSvc.loading$;
  selectedContacts: Contact[] = [];
  subscriptions: any = [];
  term$ = new Subject<string>();

  ngOnInit(): void {
    this.campaignContactDataSvc.campaignData = this.data;
    this.nowDate = Date?.now()?.toString();
    this.campaignContactDataSvc.connect();
    this.subscriptions.push(
      this.term$.subscribe((term) => {
        this.campaignContactDataSvc.setSearchTerm(term);
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.campaignContactDataSvc.disconnect();
  }

  selectionChanged(event: MatCheckboxChange, contact: Contact): void {
    if (event.checked) {
      this.selectedContacts.push(contact);
      if (this.selectedContacts.length > 20) {
        this.snackbarService.openErrorSnack('CAMPAIGNS.CONTACTS.MODAL.ERRORS.MAX_CONTACTS', { duration: 1000 });
        return;
      }
    } else {
      const index = this.selectedContacts.indexOf(contact, 0);
      if (index > -1) {
        this.selectedContacts.splice(index, 1);
      }
    }
  }

  close() {
    this.ref.close();
  }

  changeState(modalState: string): void {
    this.modalState$$.next(modalState);
    this.campaignContactDataSvc.resetPagingAndSearch();
  }

  isSelected(contact: Contact): boolean {
    return !!this.selectedContacts.find((c) => c.id === contact.id);
  }

  onItemChange(value: ScheduleType) {
    this.scheduleOption = value;
    if (value === ScheduleType.NOW) {
      this.selectedDate = new Date(Date.now());
    }
  }

  public loadMore() {
    this.campaignContactDataSvc.loadNext({
      pageSize: this.campaignContactDataSvc.state.pageSize,
    });
  }

  dateChanged(value: string): void {
    this.selectedDate = new Date(value);
  }

  startCampaign() {
    this.posthogService.trackEvent('user-clicked-send-campaign', 'send-campaign-workflow', 'click');
    if (this.validate()) {
      const date = new Date(this.getDateToSend());
      const campaignId = this.data.campaignId;
      const contacts = this.getContactsToSend();
      const sender = {
        type: this.data.senderType,
        id: this.data.senderId,
      };
      const recipients = contacts.map((contact) => {
        return {
          id: contact.id,
          type: RecipientType.RECIPIENT_TYPE_CRM_CONTACT,
          contact: {
            type: ContactType.CONTACT_TYPE_EMAIL,
            value: contact.email,
          },
          emailAddress: contact.email,
          phoneNumber: contact.phone,
        };
      });
      this.loading$$.next(true);
      this.subscriptions.push(
        forkJoin(
          recipients.map((recipient) => {
            return this.recipientCampaignService
              .addToCampaign(campaignId, sender, recipient, date, this.scheduleOption === ScheduleType.NOW)
              .pipe(
                map(() => ''),
                catchError(() => {
                  const failedContact = recipient.id;
                  return of(failedContact);
                }),
              );
          }),
        ).subscribe((resp: string[]) => {
          const failedContacts = resp.filter((contact) => contact !== '');
          this.loading$$.next(false);
          if (failedContacts.length > 0) {
            if (failedContacts.length === 1) {
              this.snackbarService.openErrorSnack(
                this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.ERRORS.FAILED_CONTACT_ERROR'),
              );
            } else {
              this.snackbarService.openErrorSnack(
                this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.ERRORS.FAILED_CONTACTS_COUNT_ERROR', {
                  count: failedContacts.length,
                }),
              );
            }
          } else {
            this.snackbarService.openSuccessSnack('CAMPAIGNS.CONTACTS.MODAL.SUCCESS');
            this.close();
          }
        }),
      );
    }
  }

  getContactsToSend() {
    return this.selectedContacts.map((row) => {
      return {
        id: row.id,
        name: row['name'],
        email: row['email'],
        phone: row['phoneNumber'],
      };
    });
  }

  validate() {
    if (this.scheduleOption === undefined) {
      this.snackbarService.openErrorSnack('CAMPAIGNS.CONTACTS.MODAL.ERRORS.SELECT_DATE');
      return false;
    }
    if (
      (this.scheduleOption === ScheduleType.LATER && this.selectedDate === undefined) ||
      (this.scheduleOption === ScheduleType.LATER && this.selectedDate === null)
    ) {
      this.snackbarService.openErrorSnack('CAMPAIGNS.CONTACTS.MODAL.ERRORS.SELECT_DATE');
      return false;
    }
    if (this.selectedContacts.length === 0) {
      this.snackbarService.openErrorSnack('CAMPAIGNS.CONTACTS.MODAL.ERRORS.SELECT_CONTACTS');
      return false;
    }
    return true;
  }

  getDateToSend() {
    if (this.scheduleOption === ScheduleType.LATER) {
      this.selectedDate.setHours(this.startTime?.value.split(':')[0] || 0);
      this.selectedDate.setMinutes(this.startTime?.value.split(':')[1] || 0);
    }
    return this.selectedDate;
  }

  protected readonly ModalState = ModalState;
  protected readonly Date = Date;
  protected readonly ScheduleType = ScheduleType;
}
