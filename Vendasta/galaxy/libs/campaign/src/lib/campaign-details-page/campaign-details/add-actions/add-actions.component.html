<div *ngIf="noSteps" class="no-steps">
  <img
    class="empty-campaign"
    src="https://storage.cloud.google.com/galaxy-libs-public-images/campaign-details-empty-state.png"
  />
  <h2>{{ 'CAMPAIGN_DETAILS.EMPTY_STATE.TITLE' | translate }}</h2>
  <p>
    {{ 'CAMPAIGN_DETAILS.EMPTY_STATE.DESCRIPTION_1' | translate }}<br />
    {{ 'CAMPAIGN_DETAILS.EMPTY_STATE.DESCRIPTION_2' | translate }}
  </p>
</div>
<div *ngIf="showActions" class="add-campaign-step">
  <div class="button-container">
    <button mat-stroked-button color="primary" [disabled]="loading" [matMenuTriggerFor]="menu">
      <mat-icon>email</mat-icon>
      {{ 'CAMPAIGN_DETAILS.ADD_EMAIL' | translate }}
      <mat-icon iconPositionEnd>arrow_drop_down</mat-icon>
    </button>
    <button
      mat-stroked-button
      color="primary"
      class="add-sms-step"
      [disabled]="loading"
      *ngIf="isLocationValid"
      (click)="openDialogOrEmitAction()"
    >
      {{ 'CAMPAIGN_DETAILS.TEMPLATE.ADD_SMS' | translate }}
      <mat-icon
        [ngClass]="{ 'sms-icon': (isSMSAvailable$ | async), 'lock-icon': (isSMSAvailable$ | async) === false }"
      ></mat-icon>
    </button>
  </div>
  <mat-menu #menu="matMenu">
    <ng-container *ngIf="availableSteps$ | async as enabled">
      <button mat-menu-item *ngIf="enabled.includes(features.SEND_EMAIL)" (click)="actionClicked.emit(actionTypes.New)">
        {{ 'CAMPAIGN_DETAILS.TEMPLATE.CREATE_NEW' | translate }}
      </button>
      <button
        mat-menu-item
        *ngIf="enabled.includes(features.SEND_EMAIL)"
        (click)="actionClicked.emit(actionTypes.Existing)"
      >
        {{ 'CAMPAIGN_DETAILS.TEMPLATE.ADD_EXISTING' | translate }}
      </button>
      <button
        mat-menu-item
        *ngIf="enabled.includes(features.SEND_SNAPSHOT)"
        (click)="actionClicked.emit(actionTypes.Snapshot)"
      >
        {{ 'CAMPAIGN_DETAILS.TEMPLATE.ADD_SNAPSHOT_REPORT' | translate }}
      </button>
    </ng-container>
  </mat-menu>
</div>
