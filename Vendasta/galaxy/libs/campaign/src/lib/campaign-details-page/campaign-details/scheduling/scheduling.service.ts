import { Injectable } from '@angular/core';
import { CampaignService, ConfigService, GetConfigResponseInterface, SenderInterface } from '@vendasta/campaigns';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import moment from 'moment-timezone';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, skipWhile, take } from 'rxjs/operators';
import { CampaignConfig } from '../interface';

export interface TimezoneOption {
  value: string;
  label: string;
}

@Injectable()
export class SchedulingService {
  private allTimezones: TimezoneOption[] = [];
  config$$: BehaviorSubject<CampaignConfig | null> = new BehaviorSubject<CampaignConfig | null>(null);
  config$: Observable<CampaignConfig | null> = this.config$$.asObservable().pipe(skipWhile((c) => !c));

  constructor(
    private readonly campaignService: CampaignService,
    private snackbarService: SnackbarService,
    private campaignConfigService: ConfigService,
  ) {
    this.setAllTimezones();
  }

  private setAllTimezones(): void {
    const timezones = moment.tz.names();
    this.allTimezones = timezones.map((t: string) => ({
      value: t,
      label: t,
    }));
  }

  filteredTimezones(filterValue: string): TimezoneOption[] {
    filterValue = filterValue.toLowerCase();
    return this.allTimezones.filter((option) => {
      const displayWithSpaces = option.label.replace('_', ' ').toLowerCase();
      return displayWithSpaces.indexOf(filterValue) >= 0;
    });
  }

  initCampaignConfig(campaignId: string): void {
    this.campaignConfigService.getConfig(campaignId).subscribe((config: GetConfigResponseInterface) => {
      this.config$$.next({
        validDays: config.days || [],
        timezone: config.timezone || '',
      });
    });
  }

  validateTimezone(timezone: string, options: Observable<TimezoneOption[]>): Observable<boolean> {
    return options.pipe(map((res) => res.some((o) => o.value === timezone)));
  }

  updateCampaignConfig(campaignId: string, config: CampaignConfig, senderId: SenderInterface): void {
    this.campaignConfigService
      .updateConfig(
        campaignId,
        config.timezone,
        [],
        config.rateLimitEnabled || false,
        config.rateLimit || 0,
        senderId,
        config.validDays,
      )
      .pipe(take(1))
      .subscribe({
        next: () => {
          this.config$$.next(config);
          this.snackbarService.openSuccessSnack('CAMPAIGN_OPTIONS.CONFIG.SUCCESS');
        },
        error: () => {
          this.snackbarService.openErrorSnack('CAMPAIGN_OPTIONS.CONFIG.FAILURE');
        },
      });
  }

  displayTimezoneErrorSnack(): void {
    this.snackbarService.openErrorSnack('CAMPAIGN_OPTIONS.CONFIG.TIMEZONE_FAILURE');
  }

  resetConfig(): void {
    this.config$$.next(this.config$$.value);
  }
}
