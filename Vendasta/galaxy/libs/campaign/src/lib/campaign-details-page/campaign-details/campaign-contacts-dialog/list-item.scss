@use 'design-tokens' as *;

:host {
  display: block;
}

.va-item-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 61px;
}

i {
  padding-left: 10px;
  font-size: 32px;
}

va-icon {
  padding-left: 10px;
}

.fill-remaining-space {
  flex: 1 1 auto;
}

.va-item-content-container {
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.va-item-title {
  font-size: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  color: $primary-text-color !important;
}

.va-item-subtitle {
  margin-top: 5px;
  color: $secondary-text-color;
}

.va-item-accessory {
  padding-right: 10px;
}
