import { Component, Inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { CampaignConfig } from '@galaxy/campaign/dependencies';
import { Router } from '@angular/router';
import { CONFIG_TOKEN } from '../../../../../shared/src/tokens';

@Component({
  selector: 'campaign-upsell-sms-dialog',
  templateUrl: './upsell-sms-dialog.component.html',
  styleUrls: ['./upsell-sms-dialog.component.scss'],
  standalone: false,
})
export class UpsellSmsDialogComponent {
  constructor(
    @Inject(CONFIG_TOKEN) private readonly campaignConfig: CampaignConfig,
    private router: Router,
  ) {}

  protected smsAddOnPath$ = this.campaignConfig.productBasePath$;

  async gotoAddOns(): Promise<boolean> {
    const smsAddOnPath = await firstValueFrom(this.smsAddOnPath$);
    return this.router.navigateByUrl(`${smsAddOnPath}`);
  }
}
