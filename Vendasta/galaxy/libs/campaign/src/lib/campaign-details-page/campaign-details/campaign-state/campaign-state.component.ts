import { Component, Input, OnChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { CampaignStatus } from '../interface';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';

@Component({
  selector: 'campaign-state',
  imports: [CommonModule, GalaxyBadgeModule],
  templateUrl: './campaign-state.component.html',
})
export class CampaignStateComponent implements OnChanges {
  state: string;
  colour: string;
  @Input() campaignState: string;
  @Input() status: CampaignStatus;

  constructor(private translate: TranslateService) {}

  ngOnChanges(): void {
    switch (this.status) {
      case CampaignStatus.PUBLISHED:
      case CampaignStatus.ACTIVE: {
        if (this.campaignState === 'ongoing') {
          this.state = this.translate.instant('CAMPAIGN_STATUS.ONGOING');
          this.colour = 'primary'; //blue
          break;
        }
        this.state = this.translate.instant('CAMPAIGN_STATUS.PUBLISHED');
        this.colour = 'green';
        break;
      }
      case CampaignStatus.ARCHIVED: {
        this.state = this.translate.instant('CAMPAIGN_STATUS.ARCHIVED');
        this.colour = 'gray';
        break;
      }
      case CampaignStatus.DRAFT:
      default: {
        this.state = this.translate.instant('CAMPAIGN_STATUS.DRAFT');
        this.colour = 'yellow';
        break;
      }
    }
  }
}
