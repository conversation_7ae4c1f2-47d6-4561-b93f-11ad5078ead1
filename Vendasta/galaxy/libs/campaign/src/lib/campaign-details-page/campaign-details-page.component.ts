import { Component } from '@angular/core';
import { CampaignDetailsComponent } from './campaign-details/campaign-details.component';
import { CommonModule } from '@angular/common';
import { EmailTemplateSaver } from '../campaign-email-builder/page/shared/email-template-saver';
import { SMSTemplateSaver } from '../campaign-email-builder/page/shared/sms-template-saver';

@Component({
  template: ` <campaign-details></campaign-details> `,
  imports: [CommonModule, CampaignDetailsComponent],
  providers: [EmailTemplateSaver, SMSTemplateSaver],
})
export class CampaignDetailsPageComponent {}
