import {
  DeletedTagsPolicy,
  GetAggregatedCampaignStatsResponse,
  ListCampaignStatsForSenderRequestInterface,
  ListCampaignStatsForSenderResponse,
  ListCampaignStatsResponse,
  ListCampaignStatsStruct,
  ListCampaignStatsStructInterface,
  SenderType,
} from '@vendasta/campaigns';
import { JestScheduler } from '@vendasta/rx-utils';
import { LoadRequest } from '@vendasta/va-filter2-table';
import { EMPTY, Observable, ReplaySubject, of, throwError } from 'rxjs';
import { map } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import {
  CampaignStatsSource,
  InMemoryTags,
  MyCampaignsTableService,
  preferInMemoryTags,
  preferInMemoryTags$,
} from './my-campaigns-table.service';

/**
 * IdentityTranslator returns the translation key, unchanged.
 */
class IdentityTranslator {
  instant(key: string): string {
    return key;
  }
}

describe('MyCampaignsTableService', () => {
  let sched: TestScheduler;
  const testSuiteSenderType = SenderType.SENDER_TYPE_BUSINESS;
  const testSuiteSenderId = 'sender ID defined in test suite';

  beforeEach(() => (sched = new JestScheduler()));
  afterEach(() => sched.flush());

  describe('load: outgoing requests', () => {
    class RequestCapturingService implements CampaignStatsSource {
      readonly requests$$ = new ReplaySubject(2);
      get(): Observable<GetAggregatedCampaignStatsResponse> {
        return throwError('unexpected call');
      }

      listStatsForSender(
        data: ListCampaignStatsForSenderRequestInterface,
      ): Observable<ListCampaignStatsForSenderResponse> {
        this.requests$$.next(data);
        return of(new ListCampaignStatsResponse({}));
      }
    }

    let statsService: RequestCapturingService;
    let service: MyCampaignsTableService;

    const testPageSize = 11;
    const loadRequest: LoadRequest = {
      filters: undefined,
      sortingOptions: undefined,
      pageIndex: undefined,
      pageSize: testPageSize,
    };

    beforeEach(() => {
      statsService = new RequestCapturingService();
      service = new MyCampaignsTableService(
        {
          basePath$: EMPTY,
          sender$: of({ id: testSuiteSenderId, type: testSuiteSenderType }),
        },
        statsService,
        new IdentityTranslator(),
      );
    });

    it('should make one request with the correct parameters', () => {
      const expectedRequest: ListCampaignStatsForSenderRequestInterface = {
        sender: { id: testSuiteSenderId, type: testSuiteSenderType },
        marketId: '',
        statuses: [],
        states: [],
        searchTerm: undefined,
        pagingOptions: {
          cursor: '',
          pageSize: testPageSize,
        },
        tagIds: [],
        deletedTagsPolicy: DeletedTagsPolicy.DELETED_TAGS_POLICY_EXCLUDE_FROM_RESPONSE,
      };

      service.load(loadRequest).subscribe();

      sched.expectObservable(statsService.requests$$).toBe('x', { x: expectedRequest });
    });
  });
  describe('load: internal state updates', () => {
    class MockStatsSource implements CampaignStatsSource {
      listResponse: ListCampaignStatsResponse;
      readonly usedCursors$$ = new ReplaySubject<string>(2);

      get(): Observable<GetAggregatedCampaignStatsResponse> {
        return undefined;
      }

      listStatsForSender(
        data: ListCampaignStatsForSenderRequestInterface,
      ): Observable<ListCampaignStatsForSenderResponse> {
        this.usedCursors$$.next(data.pagingOptions?.cursor);
        return of(this.listResponse);
      }
    }

    let source: MockStatsSource;

    beforeEach(() => {
      source = new MockStatsSource();
    });

    it('should use cursor from first page response when loading second page [hasMore==true]', () => {
      const service = new MyCampaignsTableService(
        {
          basePath$: EMPTY,
          sender$: of({ id: testSuiteSenderId, type: testSuiteSenderType }),
        },
        source,
        new IdentityTranslator(),
      );

      const nextPageCursor = 'next page cursor defined in test setup';

      source.listResponse = new ListCampaignStatsForSenderResponse({
        pagingMetadata: {
          hasMore: true,
          nextCursor: nextPageCursor,
        },
      });

      const firstPageRequest: LoadRequest = {
        pageIndex: 0,
        pageSize: undefined,
        sortingOptions: undefined,
        filters: undefined,
      };

      const secondPageRequest: LoadRequest = {
        pageIndex: 1,
        pageSize: undefined,
        sortingOptions: undefined,
        filters: undefined,
      };

      service.load(firstPageRequest).subscribe();
      service.load(secondPageRequest).subscribe();

      sched.expectObservable(source.usedCursors$$).toBe('(xy)', {
        x: '',
        y: nextPageCursor,
      });
    });

    // Skipped because this service does not behave as expected. It falls back to loading page 0 as if it were page N.
    // TODO: Reject requests when a cursor is not present for the requested page (don't just load page 0)
    test.skip('it should not load second page if hasMore is false on first page response', () => {
      const service = new MyCampaignsTableService(
        {
          basePath$: EMPTY,
          sender$: of({ id: testSuiteSenderId, type: testSuiteSenderType }),
        },
        source,
        new IdentityTranslator(),
      );

      source.listResponse = new ListCampaignStatsForSenderResponse({
        pagingMetadata: {
          hasMore: false,
          nextCursor: undefined,
        },
      });

      const firstPageRequest: LoadRequest = {
        pageIndex: 0,
        pageSize: undefined,
        sortingOptions: undefined,
        filters: undefined,
      };

      const secondPageRequest: LoadRequest = {
        pageIndex: 1,
        pageSize: undefined,
        sortingOptions: undefined,
        filters: undefined,
      };

      service.load(firstPageRequest).subscribe();
      service.load(secondPageRequest).subscribe();

      sched.expectObservable(source.usedCursors$$).toBe('x', {
        x: '',
      });
    });

    it('should generate an estimated total from page REQUEST [hasMore==false]', () => {
      const service = new MyCampaignsTableService(
        {
          basePath$: EMPTY,
          sender$: of({ id: testSuiteSenderId, type: testSuiteSenderType }),
        },
        source,
        new IdentityTranslator(),
      );

      source.listResponse = new ListCampaignStatsForSenderResponse({
        pagingMetadata: {
          totalResults: undefined, // Microservice does not return a value for this
          hasMore: false,
        },
      });

      const testPageIndex = 0;
      const testPageSize = 10;
      const expectedTotalEstimate = 10;

      const pageRequest: LoadRequest = {
        pageIndex: testPageIndex,
        pageSize: testPageSize,
        sortingOptions: undefined,
        filters: undefined,
      };

      service.load(pageRequest).subscribe();

      sched.expectObservable(service.totalResults$).toBe('x', {
        x: expectedTotalEstimate,
      });
    });
    it('should generate an estimated total from page REQUEST [hasMore==true]', () => {
      const service = new MyCampaignsTableService(
        {
          basePath$: EMPTY,
          sender$: of({ id: testSuiteSenderId, type: testSuiteSenderType }),
        },
        source,
        new IdentityTranslator(),
      );

      source.listResponse = new ListCampaignStatsForSenderResponse({
        pagingMetadata: {
          totalResults: undefined, // Microservice does not return a value for this
          hasMore: true,
        },
      });

      const testPageIndex = 0;
      const testPageSize = 10;
      const expectedTotalEstimate = 20;

      const pageRequest: LoadRequest = {
        pageIndex: testPageIndex,
        pageSize: testPageSize,
        sortingOptions: undefined,
        filters: undefined,
      };

      service.load(pageRequest).subscribe();

      sched.expectObservable(service.totalResults$).toBe('x', {
        x: expectedTotalEstimate,
      });
    });
  });
});
describe('preferInMemoryTags', () => {
  it('should return loaded tags if in-memory is empty', () => {
    const campaignId = 'CAMPAIGN-1';
    const loadedStats: ListCampaignStatsStructInterface[] = [
      {
        campaignId: campaignId,
        tags: [
          {
            tagId: 'tag-1',
            text: 'loaded tag one',
          },
          {
            tagId: 'tag-2',
            text: 'loaded tag two',
          },
        ],
      },
    ];
    const loadedTagIds = loadedStats.filter((c) => (c.campaignId = campaignId))[0].tags.map((t) => t.tagId);
    const result = preferInMemoryTags({}, loadedStats);
    const resultTagIds = result.filter((c) => c.campaignId === campaignId)[0].tags.map((t) => t.tagId);
    expect(resultTagIds).toEqual(loadedTagIds);
  });

  it('should return in-memory tags if in-memory has tags for same campaign', () => {
    const campaignId = 'CAMPAIGN-1';
    const loadedStats: ListCampaignStatsStructInterface[] = [
      {
        campaignId: campaignId,
        tags: [
          {
            tagId: 'tag-1',
            text: 'loaded tag one',
          },
          {
            tagId: 'tag-2',
            text: 'loaded tag two',
          },
        ],
      },
    ];
    const inMemoryTags: InMemoryTags = {};
    inMemoryTags[campaignId] = [
      {
        tagId: 'tag-3',
        tagName: 'in-memory tag three',
        selected: true,
      },
      {
        tagId: 'tag-4',
        tagName: 'in-memory tag four',
        selected: true,
      },
    ];
    const result = preferInMemoryTags(inMemoryTags, loadedStats);
    const resultTagIds = result.filter((r) => r.campaignId === campaignId)[0].tags.map((t) => t.tagId);
    const expectedTagIds = ['tag-3', 'tag-4'];
    expect(resultTagIds).toEqual(expectedTagIds);
  });

  it('should only return TRUE in-memory tags', () => {
    const campaignId = 'CAMPAIGN-1';
    const loadedStats: ListCampaignStatsStructInterface[] = [
      {
        campaignId: campaignId,
        tags: [
          {
            tagId: 'tag-1',
            text: 'loaded tag one',
          },
          {
            tagId: 'tag-2',
            text: 'loaded tag two',
          },
        ],
      },
    ];
    const inMemoryTags: InMemoryTags = {};
    inMemoryTags[campaignId] = [
      {
        tagId: 'tag-3',
        tagName: 'in-memory tag three',
        selected: true,
      },
      {
        tagId: 'tag-4',
        tagName: 'in-memory tag four',
        selected: false,
      },
      {
        tagId: 'tag-5',
        tagName: 'in-memory tag five',
        selected: true,
      },
    ];
    const result = preferInMemoryTags(inMemoryTags, loadedStats);
    const resultTagIds = result.filter((r) => r.campaignId === campaignId)[0].tags.map((t) => t.tagId);
    const expectedTagIds = ['tag-3', 'tag-5'];
    expect(resultTagIds).toEqual(expectedTagIds);
  });

  it('should return loaded tags if in-memory has tags BUT not for same campaign', () => {
    const testCampaignId = 'CAMPAIGN-1';
    const otherCampaignId = `NOT-${testCampaignId}`;
    const loadedStats: ListCampaignStatsStructInterface[] = [
      {
        campaignId: testCampaignId,
        tags: [
          {
            tagId: 'tag-1',
            text: 'loaded tag one',
          },
          {
            tagId: 'tag-2',
            text: 'loaded tag two',
          },
        ],
      },
    ];
    const inMemoryTags: InMemoryTags = {};
    inMemoryTags[otherCampaignId] = [
      {
        tagId: 'tag-3',
        tagName: 'in-memory tag three',
        selected: true,
      },
      {
        tagId: 'tag-4',
        tagName: 'in-memory tag four',
        selected: true,
      },
    ];
    const loadedTagIds = loadedStats.filter((s) => s.campaignId === testCampaignId)[0].tags.map((t) => t.tagId);
    const result = preferInMemoryTags({}, loadedStats);
    const resultTagIds = result.filter((r) => r.campaignId === testCampaignId)[0].tags.map((t) => t.tagId);
    expect(resultTagIds).toEqual(loadedTagIds);
  });
});
describe('preferInMemoryTags# (Observable)', () => {
  let sched: TestScheduler;
  beforeEach(() => {
    sched = new JestScheduler();
  });
  afterEach(() => sched.flush());
  it('should use in-memory tags if they are added after load', () => {
    const loadedStats$ = sched.createColdObservable('-x', {
      x: [
        new ListCampaignStatsStruct({
          campaignId: 'CAMPAIGN',
          tags: [{ tagId: 'loaded' }],
        }),
      ],
    });
    const inMemoryTags$ = sched.createColdObservable('--x', {
      x: {
        CAMPAIGN: [{ tagId: 'memory', tagName: 'whatever', selected: true }],
      } as InMemoryTags,
    });
    const result$ = preferInMemoryTags$(loadedStats$, inMemoryTags$);
    const resultTagIds$ = result$.pipe(map((r) => r.map((s) => s.tags.map((t) => t.tagId))));
    sched.expectObservable(resultTagIds$).toBe('-xy', {
      x: [['loaded']],
      y: [['memory']],
    });
  });
  it('should use loaded tags if another load happens after memory tags are added', () => {
    const inMemoryTags$ = sched.createColdObservable('-x-', {
      x: {
        CAMPAIGN: [{ tagId: 'memory', tagName: 'whatever', selected: true }],
      } as InMemoryTags,
    });
    const loadedStats$ = sched.createColdObservable('x-x', {
      x: [
        new ListCampaignStatsStruct({
          campaignId: 'CAMPAIGN',
          tags: [{ tagId: 'loaded' }],
        }),
      ],
    });
    const result$ = preferInMemoryTags$(loadedStats$, inMemoryTags$);
    const resultTagIds$ = result$.pipe(map((r) => r.map((s) => s.tags.map((t) => t.tagId))));
    sched.expectObservable(resultTagIds$).toBe('xyz', {
      x: [['loaded']],
      y: [['memory']],
      z: [['loaded']],
    });
  });
});
