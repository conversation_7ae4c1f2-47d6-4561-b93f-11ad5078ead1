import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
  AllDates,
  CampaignService,
  CampaignState,
  CampaignStatsService,
  DateFilter,
  DeletedTagsPolicy,
  Focuses,
  GetAggregatedCampaignStatsRequestInterface,
  GetAggregatedCampaignStatsResponse,
  ListCampaignStatsForSenderRequestInterface,
  ListCampaignStatsForSenderResponse,
  ListCampaignStatsForSenderResponseInterface,
  ListCampaignStatsStructInterface,
  ListCampaignTagDetails,
  PagedResponseMetadata,
  Sender,
  SenderInterface,
  Statuses,
} from '@vendasta/campaigns';
import { LoadRequest, TableDataService } from '@vendasta/va-filter2-table';
import { BehaviorSubject, Observable, Subject, combineLatest, merge } from 'rxjs';
import { distinctUntilChanged, map, scan, shareReplay, switchMap, take, tap, withLatestFrom } from 'rxjs/operators';
import { TagChangesListener } from '../manage-tags/manage-tags.component';
import { TagData } from '../manage-tags/tag-toggle/tag-toggle.component';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../shared/src/tokens';

export interface CampaignStatsSource {
  listStatsForSender(data: ListCampaignStatsForSenderRequestInterface): Observable<ListCampaignStatsForSenderResponse>;

  get(statsRequest: GetAggregatedCampaignStatsRequestInterface): Observable<GetAggregatedCampaignStatsResponse>;
}

interface Translator {
  instant(key: string): string;
}

interface TagUpdate {
  campaignId: string;
  allTags: TagData[];
}

export interface InMemoryTags {
  [campaignId: string]: TagData[];
}

@Injectable()
export class MyCampaignsTableService implements TableDataService<ListCampaignStatsStructInterface>, TagChangesListener {
  private totalResults$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  public readonly totalResults$: Observable<number> = this.totalResults$$.asObservable();
  private readonly campaignFocus$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  private readonly campaignDateFilter$$: BehaviorSubject<DateFilter> = new BehaviorSubject<DateFilter>(AllDates);
  public readonly campaignDateFilter$: Observable<DateFilter> = this.campaignDateFilter$$.asObservable();
  private readonly inMemoryTagUpdates$$ = new Subject<TagUpdate>();
  private readonly inMemoryTags$: Observable<InMemoryTags> = this.inMemoryTagUpdates$$.pipe(
    scan<TagUpdate, InMemoryTags>((acc, value) => {
      acc[value.campaignId] = value.allTags;
      return acc;
    }, {}),
  );

  private cursors: { [id: number]: string | undefined } = { 0: '' };

  /**
   * @deprecated Use isLoading$
   */
  public isLoading$$: BehaviorSubject<boolean> = new BehaviorSubject(true);
  public readonly isLoading$ = this.isLoading$$.pipe(distinctUntilChanged());
  public reload$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  constructor(
    @Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE,
    @Inject(CampaignStatsService) private campaignStatsService: CampaignStatsSource,
    @Inject(TranslateService) private translateService: Translator,
    @Inject(CampaignService) private campaignService: CampaignService,
    private readonly http: HttpClient,
  ) {}

  createNewCampaign(name: string): Observable<string> {
    return this.config.sender$.pipe(
      take(1),
      map((sender) => {
        return { type: sender.type, id: sender.id };
      }),
      switchMap((senderData) => this.campaignService.create(<Sender>senderData, name)),
    );
  }

  getSender(): Observable<SenderInterface> {
    return this.config.sender$;
  }

  load(request: LoadRequest): Observable<ListCampaignStatsStructInterface[]> {
    const loadedStats$ = combineLatest([
      this.config.sender$,
      this.reload$$.asObservable(),
      this.campaignDateFilter$,
    ]).pipe(
      switchMap(([sender, ,]) => this.fetchCampaignStats(sender, request)),
      tap((resp) => this.updateTotalResults(request, resp)),
      map((resp) => resp?.stats || []),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
    return preferInMemoryTags$(loadedStats$, this.inMemoryTags$);
  }

  private fetchCampaignStats(
    sender: SenderInterface,
    request: LoadRequest,
  ): Observable<ListCampaignStatsForSenderResponse> {
    const lcsRequest: ListCampaignStatsForSenderRequestInterface = {
      sender: sender,
      tagIds: request.filters?.tag ? [request.filters?.tag?.tagId] : [],
      marketId: request.filters?.marketId ?? '',
      statuses: this.getStatuses(request),
      states: this.getStates(request),
      searchTerm: request.filters?.search,
      pagingOptions: {
        cursor: this.cursors[request.pageIndex || 0],
        pageSize: request.pageSize,
      },
      deletedTagsPolicy: DeletedTagsPolicy.DELETED_TAGS_POLICY_EXCLUDE_FROM_RESPONSE,
    };

    return this.campaignStatsService.listStatsForSender(lcsRequest);
  }

  setFocus(focus: string): void {
    this.campaignFocus$$.next(focus);
  }

  getStates(request: LoadRequest): CampaignState[] {
    const stateList: CampaignState[] = [];
    if (request.filters?.Published) {
      stateList.push(convertCampaignStateToProto('idle'));
    }
    if (request.filters?.Ongoing) {
      stateList.push(convertCampaignStateToProto('active'));
    }
    return stateList;
  }

  getStatuses(request: LoadRequest): Statuses[] {
    const statusList: Statuses[] = [];
    if (request.filters?.Published) {
      statusList.push(convertStatusToProto('published'));
      statusList.push(convertStatusToProto('active'));
    }
    if (request.filters?.Ongoing) {
      statusList.push(convertStatusToProto('active'));
      statusList.push(convertStatusToProto('published'));
    }
    if (request.filters?.Draft) {
      statusList.push(convertStatusToProto('draft'));
    }
    if (request.filters?.Archived) {
      statusList.push(convertStatusToProto('archived'));
    }

    return statusList;
  }

  reloadData(): void {
    // When we force the table to reload after an action from my-campaigns-actions-column has been clicked/requested
    // there is a significant delay to fetching the updated data; The timeout is to account for this as with no delay,
    // the results for our table will be the same as the previous request
    window.setTimeout(() => this.reload$$.next(true), 4500);
  }

  private updateTotalResults(request: LoadRequest, resp: ListCampaignStatsForSenderResponseInterface): void {
    if (!resp.pagingMetadata) {
      resp.pagingMetadata = new PagedResponseMetadata({});
    }
    resp.pagingMetadata.totalResults = request.pageSize * (request.pageIndex + 1);
    if (resp.pagingMetadata.hasMore) {
      resp.pagingMetadata.totalResults += request.pageSize;
      this.cursors[request.pageIndex + 1] = resp.pagingMetadata.nextCursor;
    }
    this.totalResults$$.next(resp.pagingMetadata.totalResults);
  }

  handleTagsChanged(campaignId: string, allTags: TagData[]): void {
    const newVar: TagUpdate = {
      campaignId: campaignId,
      allTags: allTags,
    } as TagUpdate;
    this.inMemoryTagUpdates$$.next(newVar);
  }
}

export function convertFocusToProto(focus: string): Focuses {
  switch (focus) {
    case 'acquire':
      return Focuses.FOCUSES_ACQUIRE;
    case 'adopt':
      return Focuses.FOCUSES_ADOPT;
    case 'upsell':
      return Focuses.FOCUSES_UPSELL;
    case 'none':
      return Focuses.FOCUSES_NONE;
    default:
      return Focuses.FOCUSES_ACQUIRE;
  }
}

function convertStatusToProto(status: string): Statuses {
  switch (status) {
    case 'draft':
      return Statuses.STATUSES_DRAFT;
    case 'published':
      return Statuses.STATUSES_PUBLISHED;
    case 'active':
      return Statuses.STATUSES_ACTIVE;
    case 'archived':
      return Statuses.STATUSES_ARCHIVED;
    default:
      return Statuses.STATUSES_UNSPECIFIED;
  }
}

function convertCampaignStateToProto(state: string): CampaignState {
  switch (state) {
    case 'active':
      return CampaignState.CAMPAIGN_STATE_ACTIVE;
    case 'idle':
      return CampaignState.CAMPAIGN_STATE_IDLE;
    default:
      return CampaignState.CAMPAIGN_STATE_UNSPECIFIED;
  }
}

export function preferInMemoryTags$(
  loadedStats$: Observable<ListCampaignStatsStructInterface[]>,
  inMemoryTags$: Observable<InMemoryTags>,
): Observable<ListCampaignStatsStructInterface[]> {
  const inMemoryWithLoaded$ = inMemoryTags$.pipe(
    withLatestFrom(loadedStats$),
    map(([imt, ls]) => preferInMemoryTags(imt, ls)),
  );
  return merge(inMemoryWithLoaded$, loadedStats$);
}

export function preferInMemoryTags(
  inMemoryTags: InMemoryTags,
  loadedStats: ListCampaignStatsStructInterface[],
): ListCampaignStatsStructInterface[] {
  return loadedStats.map((s) => {
    if (!Object.keys(inMemoryTags).includes(s.campaignId!)) {
      return s;
    }
    const newStats = { ...s };
    const imt = inMemoryTags[s.campaignId!]?.filter((v) => v.selected);
    newStats.tags = imt?.map(
      (t) =>
        new ListCampaignTagDetails({
          tagId: t.tagId,
          text: t.tagName,
        }),
    );
    return newStats;
  });
}
