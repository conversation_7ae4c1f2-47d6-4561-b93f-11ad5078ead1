<div *ngIf="tags?.length === 0">
  <glxy-badge class="add" (click)="manageTags()">
    <mat-icon size="small">add</mat-icon>
    <span>{{ 'TABLE.COLUMNS.TAGS.ADD_TAG_BUTTON_TEXT' | translate }}</span>
  </glxy-badge>
</div>
<div [glxyTooltip]="tooltipText">
  <div *ngIf="tags?.length > 0">
    <glxy-badge color="light-grey">
      <div>{{ tags[0].text }}</div>
    </glxy-badge>
  </div>
  <div *ngIf="tags?.length > 1">
    <glxy-badge color="light-grey">+ {{ tags.length - 1 }} more</glxy-badge>
  </div>
</div>
