import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { NgIf } from '@angular/common';

@Component({
  imports: [NgIf, MatButtonModule, MatDialogModule, TranslateModule],
  templateUrl: './confirmation-dialog.component.html',
  styleUrls: ['./confirmation-dialog.component.scss'],
})
export class ConfirmationDialogComponent {
  titlePre = '';
  title = 'CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DEFAULT.TITLE';
  titlePost = '';
  boldPre = '';
  message = '';
  boldPost = '';
  confirmButtonText = 'CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DEFAULT.CONFIRM';
  cancelButtonText = 'COMMON.ACTION_LABELS.CANCEL';
  warn = false;

  constructor(
    @Inject(MAT_DIALOG_DATA) private data: any,
    private dialogRef: MatDialogRef<ConfirmationDialogComponent>,
  ) {
    if (data) {
      this.titlePre = data.titlePre || this.titlePre;
      this.title = data.title || this.title;
      this.titlePost = data.titlePost || this.titlePost;
      this.boldPre = data.pre || this.boldPre;
      this.message = data.message || this.message;
      this.boldPost = data.post || this.boldPost;
      this.warn = data.warn || this.warn;
      if (data.buttonText) {
        this.confirmButtonText = data.buttonText.confirm || this.confirmButtonText;
        this.cancelButtonText = data.buttonText.decline || this.cancelButtonText;
      }
    }
  }

  onConfirmClick(): void {
    this.dialogRef.close(true);
  }
}
