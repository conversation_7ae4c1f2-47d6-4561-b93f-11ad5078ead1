import { Component, OnInit } from '@angular/core';
import { MyCampaignsColumnDirective } from './my-campaigns-column.directive';

@Component({
  template: `
    <div class="my-campaigns-number-column">
      {{ activeRecipients }}
    </div>
  `,
  styleUrls: ['../campaign-list-page.component.scss'],
  standalone: false,
})
export class MyCampaignsActiveRecipientsColumnComponent extends MyCampaignsColumnDirective implements OnInit {
  activeRecipients = 0;

  ngOnInit(): void {
    const recipients = this.element[this.columnDefinition.field];
    this.activeRecipients = recipients ? recipients : 0;
  }
}
