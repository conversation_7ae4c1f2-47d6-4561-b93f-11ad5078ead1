import { Component, OnInit } from '@angular/core';
import { MyCampaignsColumnDirective } from './my-campaigns-column.directive';
import { CampaignState, Statuses } from '@vendasta/campaigns';
import { VaBadgeModule } from '@vendasta/uikit';
import { CommonModule } from '@angular/common';

@Component({
  template: `
    <div>
      <va-badge color="primary" *ngIf="status === 'Ongoing'">
        {{ status }}
      </va-badge>
      <va-badge color="green" *ngIf="status === 'Published'">
        {{ status }}
      </va-badge>
      <va-badge *ngIf="status === 'Archived'">
        {{ status }}
      </va-badge>
      <va-badge color="yellow" *ngIf="status === 'Draft'">
        {{ status }}
      </va-badge>
    </div>
  `,
  styleUrls: ['../campaign-list-page.component.scss'],
  imports: [CommonModule, VaBadgeModule],
})
export class MyCampaignsStatusColumnComponent extends MyCampaignsColumnDirective implements OnInit {
  status: string;

  constructor() {
    super();
  }

  ngOnInit(): void {
    this.status = this.convertStatus(this.element.status, this.element.state);
  }

  convertStatus(status: Statuses, state: CampaignState): string {
    switch (status) {
      case Statuses.STATUSES_ACTIVE:
      case Statuses.STATUSES_PUBLISHED:
        if (state === CampaignState.CAMPAIGN_STATE_ACTIVE) {
          return 'Ongoing';
        }
        return 'Published';
      case Statuses.STATUSES_ARCHIVED:
        return 'Archived';
      case Statuses.STATUSES_DRAFT:
        return 'Draft';
      default:
        return '';
    }
  }
}
