import { Component, OnInit } from '@angular/core';
import { MyCampaignsColumnDirective } from './my-campaigns-column.directive';
import { CampaignState } from '@vendasta/campaigns';

@Component({
  template: `
    <div class="my-campaigns-state-column">
      {{ state }}
    </div>
  `,
  styleUrls: ['../campaign-list-page.component.scss'],
})
export class MyCampaignsStateColumnComponent extends MyCampaignsColumnDirective implements OnInit {
  state: string;

  constructor() {
    super();
  }

  ngOnInit(): void {
    this.state = this.convertState(this.element.state);
  }

  convertState(state: CampaignState): string {
    switch (state) {
      case CampaignState.CAMPAIGN_STATE_ACTIVE:
        return 'Active';
      case CampaignState.CAMPAIGN_STATE_IDLE:
        return 'Idle';
      default:
        return '';
    }
  }
}
