import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { PAGE_ROUTES } from '../../routing-constants';
import { MyCampaignsColumnDirective } from './my-campaigns-column.directive';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../../shared/src/tokens';

@Component({
  template: `
    <div class="my-campaigns-name-column">
      <a [routerLink]="detailsUrl$ | async">{{ element.name }}</a>
    </div>
  `,
  styleUrls: ['../campaign-list-page.component.scss'],
  imports: [CommonModule, RouterModule],
})
export class MyCampaignsNameColumnComponent extends MyCampaignsColumnDirective implements OnInit {
  detailsUrl$: Observable<string> | undefined;

  constructor(@Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE) {
    super();
  }

  ngOnInit(): void {
    this.detailsUrl$ = this.config.basePath$.pipe(
      map((v) => `/${v}/${PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.DETAILS(this.element.campaignId!)}`),
    );
  }
}
