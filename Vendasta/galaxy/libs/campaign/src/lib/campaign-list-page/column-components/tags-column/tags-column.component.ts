import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { GetterCampaignData, ListCampaignTagDetailsInterface } from '@vendasta/campaigns';
import { Observable, firstValueFrom } from 'rxjs';
import { ManageTagsComponent } from '../../../manage-tags/manage-tags.component';
import { MyCampaignsColumnDirective } from '../my-campaigns-column.directive';

@Component({
  templateUrl: './tags-column.component.html',
  styleUrls: ['./tags-column.component.scss'],
  standalone: false,
})
export class TagsColumnComponent extends MyCampaignsColumnDirective implements OnInit {
  tags: ListCampaignTagDetailsInterface[];
  tooltipText: string;

  constructor(
    @Inject('PARTNER_ID') private readonly partnerId$: Observable<string>,
    private readonly dialog: MatDialog,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
  ) {
    super();
  }

  ngOnInit(): void {
    this.tags =
      (this.element[this.columnDefinition.field as keyof GetterCampaignData] as ListCampaignTagDetailsInterface[]) ||
      [];
    this.tooltipText = this.tags
      .filter(Boolean)
      .map((v) => v.text)
      .join(', ');
  }

  async manageTags(): Promise<void> {
    await firstValueFrom(this.partnerId$); // idk if this si doing something so just removing the const assign
    ManageTagsComponent.openAndAddToURL(this.dialog, this.router, this.activatedRoute, this.element.campaignId);
  }
}
