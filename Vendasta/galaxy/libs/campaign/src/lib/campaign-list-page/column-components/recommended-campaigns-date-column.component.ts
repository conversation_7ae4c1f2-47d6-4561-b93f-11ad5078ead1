import { Component, OnInit } from '@angular/core';
import { RecommendedCampaignsColumnDirective } from './recommended-campaigns-column.directive';
import { GetterCampaignData } from '@vendasta/campaigns';

@Component({
  template: `
    <div class="recommended-campaigns-date-column">
      <span>{{ date | glxyDate }}</span>
    </div>
  `,
  styleUrls: ['../recommended-campaigns-table-tab/recommended-campaigns-table-tab.component.scss'],
})
export class RecommendedCampaignsDateColumnComponent extends RecommendedCampaignsColumnDirective implements OnInit {
  date: string;

  constructor() {
    super();
  }

  ngOnInit(): void {
    this.date = this.element[this.columnDefinition.field as keyof GetterCampaignData] as string;
  }
}
