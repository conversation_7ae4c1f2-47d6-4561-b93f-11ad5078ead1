<span mat-dialog-title>
  {{ titlePre | translate }}
  <span class="bold-title">{{ title | translate }}</span>
  {{ titlePost | translate }}
</span>
<mat-dialog-content class="message">
  <p>
    <b>{{ boldPre | translate }}</b>
    <ng-container *ngIf="boldPre">
      <br />
      <br />
    </ng-container>
    <span [innerHtml]="message | translate"></span>
    <ng-container *ngIf="boldPost">
      <br />
      <br />
    </ng-container>
    <b>{{ boldPost | translate }}</b>
  </p>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-button color="primary" mat-dialog-close tabindex="-1">
    {{ cancelButtonText | translate }}
  </button>
  <button mat-flat-button color="primary" (click)="onConfirmClick()" tabindex="1" *ngIf="!warn">
    {{ confirmButtonText | translate }}
  </button>
  <button mat-flat-button color="warn" (click)="onConfirmClick()" tabindex="1" *ngIf="warn">
    {{ confirmButtonText | translate }}
  </button>
</mat-dialog-actions>
