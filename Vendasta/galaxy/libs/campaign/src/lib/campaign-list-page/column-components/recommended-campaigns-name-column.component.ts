import { Component } from '@angular/core';
import { RecommendedCampaignsColumnDirective } from './recommended-campaigns-column.directive';
import { Router } from '@angular/router';

@Component({
  template: `
    <div class="recommended-campaigns-name-column">
      <a (click)="viewCampaignDetails()">{{ element.name }}</a>
    </div>
  `,
  styleUrls: ['../recommended-campaigns-table-tab/recommended-campaigns-table-tab.component.scss'],
})
export class RecommendedCampaignsNameColumnComponent extends RecommendedCampaignsColumnDirective {
  constructor(private router: Router) {
    super();
  }

  viewCampaignDetails(): void {
    this.router.navigate(['marketing', 'campaign', 'details', this.element.campaignId]);
  }
}
