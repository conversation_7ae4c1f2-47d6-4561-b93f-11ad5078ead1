import { Component, OnInit } from '@angular/core';
import { MyCampaignsColumnDirective } from './my-campaigns-column.directive';
@Component({
  template: `
    <div class="my-campaigns-number-column">
      {{ rateDisplay }}
    </div>
  `,
  styleUrls: ['../campaign-list-page.component.scss'],
  standalone: false,
})
export class MyCampaignsOpenRateColumnComponent extends MyCampaignsColumnDirective implements OnInit {
  rateDisplay: string;

  constructor() {
    super();
  }

  ngOnInit(): void {
    const rate = this.element[this.columnDefinition.field] as number;
    if (rate) {
      this.rateDisplay = rate > 0 ? formatPercentage(rate) : this.translateService.instant('COMMON.ACTION_LABELS.NA');
    } else {
      this.rateDisplay = this.translateService.instant('COMMON.ACTION_LABELS.NA');
    }
  }
}

function formatPercentage(percent: number): string {
  return percent % 1 === 0 ? percent.toFixed(0) + '%' : percent.toFixed(1) + '%';
}
