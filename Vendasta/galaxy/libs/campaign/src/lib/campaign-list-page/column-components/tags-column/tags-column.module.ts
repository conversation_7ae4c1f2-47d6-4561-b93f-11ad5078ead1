import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TagsColumnComponent } from './tags-column.component';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';

@NgModule({
  declarations: [
    TagsColumnComponent, //
  ],
  imports: [
    CommonModule, //
    GalaxyBadgeModule,
    GalaxyTooltipModule,
    TranslateModule,
    MatIconModule,
  ],
})
export class TagsColumnModule {}
