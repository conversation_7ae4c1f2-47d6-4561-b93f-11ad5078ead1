import { Component, OnInit } from '@angular/core';
import { MyCampaignsColumnDirective } from './my-campaigns-column.directive';

@Component({
  template: `
    <div class="my-campaigns-number-column">
      {{ emailsDelivered }}
    </div>
  `,
  styleUrls: ['../campaign-list-page.component.scss'],
  standalone: false,
})
export class MyCampaignsEmailsDeliveredColumnComponent extends MyCampaignsColumnDirective implements OnInit {
  emailsDelivered: number;

  constructor() {
    super();
  }

  ngOnInit(): void {
    const emails = this.element[this.columnDefinition.field];
    this.emailsDelivered = emails ? emails : 0;
  }
}
