import { Input, Directive } from '@angular/core';
import { ColumnDefinition, CustomCellComponent } from '@vendasta/va-filter2-table';
import { TranslateService } from '@ngx-translate/core';
import { GetterCampaignData } from '@vendasta/campaigns';

@Directive()
export class RecommendedCampaignsColumnDirective implements CustomCellComponent<GetterCampaignData> {
  @Input() columnDefinition: ColumnDefinition;
  @Input() translateService: TranslateService;
  @Input() element: GetterCampaignData;
}
