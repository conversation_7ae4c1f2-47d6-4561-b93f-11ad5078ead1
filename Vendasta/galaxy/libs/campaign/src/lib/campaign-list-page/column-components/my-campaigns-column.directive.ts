import { Input, Directive } from '@angular/core';
import { ColumnDefinition, CustomCellComponent } from '@vendasta/va-filter2-table';
import { TranslateService } from '@ngx-translate/core';
import { ListCampaignStatsStructInterface } from '@vendasta/campaigns/lib/_internal/interfaces/api.interface';

@Directive()
export class MyCampaignsColumnDirective implements CustomCellComponent<ListCampaignStatsStructInterface> {
  @Input() columnDefinition: ColumnDefinition;
  @Input() translateService: TranslateService;
  @Input() element: ListCampaignStatsStructInterface;
}
