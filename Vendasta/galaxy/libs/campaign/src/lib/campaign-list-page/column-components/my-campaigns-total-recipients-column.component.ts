import { Component, OnInit } from '@angular/core';
import { MyCampaignsColumnDirective } from './my-campaigns-column.directive';
import { CommonModule } from '@angular/common';

@Component({
  template: `
    <div class="my-campaigns-number-column">
      {{ totalRecipients }}
    </div>
  `,
  styleUrls: ['../campaign-list-page.component.scss'],
  imports: [CommonModule],
})
export class MyCampaignsTotalRecipientsColumnComponent extends MyCampaignsColumnDirective implements OnInit {
  totalRecipients: number | undefined;

  ngOnInit(): void {
    const total = this.element[this.columnDefinition.field];
    this.totalRecipients = total ? total : 0;
  }
}
