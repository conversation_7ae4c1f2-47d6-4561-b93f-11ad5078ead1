import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Inject, OnDestroy, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { Statuses } from '@vendasta/campaigns';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Subscription, firstValueFrom } from 'rxjs';
import { take } from 'rxjs/operators';
import {
  CampaignDuplicateDialogComponent,
  DialogResult,
} from '../../campaign-details-page/campaign-details/campaign-duplicate-dialog/campaign-duplicate-dialog.component';
import { CampaignDuplicateSuccessDialogComponent } from '../../campaign-details-page/campaign-details/campaign-duplicate-success-dialog/campaign-duplicate-success-dialog.component';
import { CampaignsService } from '../../campaign-details-page/campaign-details/campaigns.service';
import {
  DuplicateCampaignData,
  DuplicateCampaignSuccessData,
} from '../../campaign-details-page/campaign-details/interface';
import { CampaignPreviewDialogComponent } from '../../campaign-preview-dialog/campaign-preview-dialog.component';
import { ManageTagsComponent } from '../../manage-tags/manage-tags.component';
import { ConfirmationDialogComponent } from './dialog/confirmation-dialog.component';
import { MyCampaignsColumnDirective } from './my-campaigns-column.directive';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../../shared/src/tokens';

@Component({
  templateUrl: './my-campaigns-actions-column.component.html',
  styleUrls: ['../campaign-list-page.component.scss'],
  imports: [CommonModule, MatIconModule, MatMenuModule, TranslateModule, MatButtonModule],
})
export class MyCampaignsActionsColumnComponent extends MyCampaignsColumnDirective implements OnDestroy {
  @Output() eventEmitter = new EventEmitter<any>();
  subscriptions: Subscription[] = [];
  Statuses = Statuses;
  isHidden = false;

  constructor(
    private readonly matDialog: MatDialog,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly snackbarService: SnackbarService,
    @Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE,
    private readonly campaignsService: CampaignsService,
  ) {
    super();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  previewCampaign(campaignId: string): void {
    CampaignPreviewDialogComponent.openAndAddToURL(this.matDialog, this.router, this.route, campaignId);
  }

  async duplicateCampaign(campaignId: string): Promise<void> {
    const data: DuplicateCampaignData = {
      campaignId: campaignId,
      sender: await firstValueFrom(this.config.sender$),
    };
    const ref = this.matDialog.open(CampaignDuplicateDialogComponent, { data: data });
    const result: DialogResult = await firstValueFrom(ref.afterClosed());
    if (result?.newCampaignId) {
      const successData: DuplicateCampaignSuccessData = {
        newCampaignId: result.newCampaignId,
      };
      this.matDialog.open(CampaignDuplicateSuccessDialogComponent, { data: successData });
    }
  }

  async pauseCampaign(campaignId: string, campaignName: string): Promise<void> {
    const dialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translateService.instant('CAMPAIGN_DETAILS.PAUSE.TITLE', { title: campaignName }),
        message: this.translateService.instant('CAMPAIGN_DETAILS.PAUSE.MESSAGE'),
        buttonText: {
          confirm: this.translateService.instant('CAMPAIGN_DETAILS.PAUSE.CONFIRM'),
          decline: this.translateService.instant('CAMPAIGN_DETAILS.PAUSE.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    const dialogResult = await dialogRef.afterClosed().toPromise();
    if (dialogResult) {
      this.campaignsService.pauseCampaign(campaignId).subscribe({
        next: () => {
          this.snackbarService.openSuccessSnack('CAMPAIGN_DETAILS.PAUSE.SUCCESS');
        },
        error: () => {
          this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.PAUSE.FAILURE');
        },
      });
    }
  }

  async resumeCampaign(campaignId: string, campaignName: string): Promise<void> {
    const dialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translateService.instant('CAMPAIGN_DETAILS.RESUME.TITLE', { title: campaignName }),
        message: this.translateService.instant('CAMPAIGN_DETAILS.RESUME.MESSAGE'),
        buttonText: {
          confirm: this.translateService.instant('CAMPAIGN_DETAILS.RESUME.CONFIRM'),
          decline: this.translateService.instant('CAMPAIGN_DETAILS.RESUME.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    const dialogResult = await firstValueFrom(dialogRef.afterClosed());
    if (dialogResult) {
      this.campaignsService.unpauseCampaign(campaignId).subscribe({
        next: () => {
          this.snackbarService.openSuccessSnack('CAMPAIGN_DETAILS.RESUME.SUCCESS');
        },
        error: () => {
          this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.RESUME.FAILURE');
        },
      });
    }
  }

  async archiveCampaign(campaignId: string, campaignName: string): Promise<void> {
    const dialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translateService.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.TITLE', {
          title: campaignName,
        }),
        message: this.translateService.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.MESSAGE'),
        warn: true,
        buttonText: {
          confirm: this.translateService.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.CONFIRM'),
          decline: this.translateService.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    const dialogResult = await firstValueFrom(dialogRef.afterClosed());
    if (dialogResult) {
      // TODO: Update campaign status via campaigns microservice [MAEL-405] [MAEL-406]
      this.snackbarService.openErrorSnack('Not implemented');
      // const pauseResult = await firstValueFrom(
      //     return this.marketingAutomationService.archiveCampaign(campaignId, this.partnerService.partnerId);
      // ).catch(err => {
      //   console.error(err);
      //   this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.ACTIONS.ARCHIVE_FAILURE');
      // }).then(() => {
      //   this.translateService.instant('CAMPAIGN_DETAILS.ACTIONS.ARCHIVE_SUCCESS'),
      // });
    }
  }

  async deleteCampaign(campaignId: string, campaignName: string): Promise<void> {
    const dialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translateService.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_CAMPAIGN.TITLE', {
          title: campaignName,
        }),
        message: this.translateService.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_CAMPAIGN.MESSAGE'),
        warn: true,
        buttonText: {
          confirm: this.translateService.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_CAMPAIGN.CONFIRM'),
          decline: this.translateService.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_CAMPAIGN.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    const dialogResult = await firstValueFrom(dialogRef.afterClosed());
    if (dialogResult) {
      // TODO: Update campaign status via campaigns microservice [MAEL-405] [MAEL-406]
      this.snackbarService.openErrorSnack('Not implemented');
      // const pauseResult = await firstValueFrom(
      //     return this.marketingAutomationService.deleteCampaign(campaignId);
      // ).catch(err => {
      //   console.error(err);
      //   this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.ACTIONS.DELETE_FAILURE');
      // }).then(() => {
      //   this.snackbarService.openSuccessSnack('CAMPAIGN_DETAILS.ACTIONS.DELETE_SUCCESS');
      //   this.eventEmitter.emit(true);
      // });
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  openAddListToCampaignModal(campaignId: string): void {
    // TODO: Implement "add recipients to campaign"
    this.snackbarService.openErrorSnack('Not implemented');
  }

  viewCampaignHistory(campaignId: string): void {
    this.router.navigate(['marketing', 'campaign', campaignId, 'history']);
  }

  manageTags(campaignId: string): void {
    const diaglogRef = ManageTagsComponent.openAndAddToURL(this.matDialog, this.router, this.route, campaignId);
    diaglogRef
      .afterClosed()
      .pipe(take(1))
      .subscribe({
        next: () => {
          this.eventEmitter.emit(true);
        },
        error: (err) => {
          console.error(err);
        },
      });
  }
}
