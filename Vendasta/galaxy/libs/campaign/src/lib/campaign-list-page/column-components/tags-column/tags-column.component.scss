@use 'design-tokens' as *;

:host {
  display: block;
  div {
    margin-bottom: $spacing-2;
    cursor: default;
    &:last-child {
      margin-bottom: 0;
    }
    glxy-badge {
      max-width: 88px;
      div {
        width: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      &.add {
        cursor: pointer;
        display: flex;
        align-items: center;
        mat-icon {
          font-size: $font-preset-3-size;
          line-height: $font-preset-5-size;
          height: $font-preset-5-size;
          width: $font-preset-3-size;
        }
      }
    }
  }
}
