<div class="my-campaigns-actions-column">
  <button mat-icon-button [matMenuTriggerFor]="actionsMenu">
    <mat-icon>more_vert</mat-icon>
  </button>
  <mat-menu #actionsMenu="matMenu">
    <!-- TODO: Replace this with app-campaign-options (confirm it has all the same actions / add some if necessary) -->
    <button mat-menu-item (click)="previewCampaign(element.campaignId)">
      {{ 'PREVIEW.ACTION' | translate }}
    </button>
    <button mat-menu-item (click)="duplicateCampaign(element.campaignId)">
      {{ 'CAMPAIGN_DETAILS.ACTIONS.COPY' | translate }}
    </button>
    <button
      mat-menu-item
      *ngIf="element.status !== Statuses.STATUSES_DRAFT"
      (click)="pauseCampaign(element.campaignId, element.name)"
    >
      {{ 'CAMPAIGN_DETAILS.PAUSE.ACTION' | translate }}
    </button>

    <button
      mat-menu-item
      *ngIf="element.status !== Statuses.STATUSES_DRAFT && element.status !== Statuses.STATUSES_ARCHIVED"
      (click)="resumeCampaign(element.campaignId, element.name)"
    >
      {{ 'CAMPAIGN_DETAILS.RESUME.ACTION' | translate }}
    </button>
    <!--    <button-->
    <!--      mat-menu-item-->
    <!--      *ngIf="element.status !== Statuses.STATUSES_DRAFT"-->
    <!--      (click)="viewCampaignHistory(element.campaignId)"-->
    <!--    >-->
    <!--      {{ 'COMMON.ACTION_LABELS.VIEW_HISTORY' | translate }}-->
    <!--    </button>-->
    <!--    <button-->
    <!--      mat-menu-item-->
    <!--      *ngIf="element.isEditable && element.eventsCount > 0 && element.status !== Statuses.STATUSES_ARCHIVED"-->
    <!--      (click)="archiveCampaign(element.campaignId, element.name)"-->
    <!--    >-->
    <!--      {{ 'COMMON.ACTION_LABELS.ARCHIVE' | translate }}-->
    <!--    </button>-->
    <!--    <button-->
    <!--      mat-menu-item-->
    <!--      *ngIf="element.isEditable && element.eventsCount === 0"-->
    <!--      (click)="deleteCampaign(element.campaignId, element.name)"-->
    <!--    >-->
    <!--      {{ 'COMMON.ACTION_LABELS.DELETE' | translate }}-->
    <!--    </button>-->

    <!--    <button mat-menu-item (click)="manageTags(element.campaignId)">-->
    <!--      {{ 'MANAGE_TAGS.MANAGE_TAGS' | translate }}-->
    <!--    </button>-->
  </mat-menu>
</div>
