import { Component, Input, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import {
  Filter2TableModule,
  TABLE_DEFINITION,
  VaFilteredMatTableService,
  VaTableSortService,
} from '@vendasta/va-filter2-table';
import { ManageTagsComponent } from '../manage-tags/manage-tags.component';
import { MyCampaignsTableFilterService, SENDER_TAGS_TOKEN } from './my-campaigns-table-filter.service';
import { tableDefinitionFactory } from './my-campaigns-table-model';
import { MyCampaignsTableService } from './my-campaigns-table.service';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { TagsModule } from '../tags/tags.module';
import { SenderTagsService } from '../tags/sender-tags.service';
import { TagsColumnModule } from './column-components/tags-column/tags-column.module';
import { ManageTagsModule } from '../manage-tags/manage-tags.module';
import { CommonModule } from '@angular/common';
import { CampaignPreviewDialogComponent } from '../campaign-preview-dialog/campaign-preview-dialog.component';
import { SmsConfigurationAlertComponent } from '../sms-configuration-alert/sms-configuration-alert.component';

@Component({
  selector: 'campaign-list-page',
  templateUrl: './campaign-list-page.component.html',
  styleUrls: ['./campaign-list-page.component.scss'],
  imports: [
    CommonModule,
    GalaxyPageModule,
    Filter2TableModule,
    TagsModule,
    TagsColumnModule,
    ManageTagsModule,
    SmsConfigurationAlertComponent,
  ],
  providers: [
    VaFilteredMatTableService,
    VaTableSortService,
    {
      provide: SENDER_TAGS_TOKEN,
      useFactory: SenderTagsService.getObservable$,
      deps: [SenderTagsService],
    },
    MyCampaignsTableFilterService,
    {
      provide: TABLE_DEFINITION,
      useFactory: tableDefinitionFactory,
      deps: [MyCampaignsTableFilterService],
    },
  ],
})
export class CampaignListComponent implements OnInit {
  @Input() key: string | undefined;
  private manageTagsForCampaignId: string | undefined;
  private previewForCampaignId: string | undefined;

  constructor(
    readonly myCampaignsTableService: MyCampaignsTableService,
    private readonly dialog: MatDialog,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
  ) {}

  async ngOnInit(): Promise<void> {
    this.myCampaignsTableService.setFocus(this.key || '');

    return this.restoreLastActivityViaURLQuery();
  }

  private async restoreLastActivityViaURLQuery(): Promise<void> {
    if (await this.openManageTagsIfPresentInURL()) {
      return Promise.resolve();
    }
    if (await this.openPreviewIfPresentInURL()) {
      return Promise.resolve();
    }
  }
  async openManageTagsIfPresentInURL(): Promise<boolean> {
    this.manageTagsForCampaignId = this.activatedRoute.snapshot.queryParams['manageTagsForCampaignId'];
    if (!this.manageTagsForCampaignId) {
      return Promise.resolve(false);
    }
    ManageTagsComponent.openAndAddToURL(this.dialog, this.router, this.activatedRoute, this.manageTagsForCampaignId);
    return Promise.resolve(true);
  }

  async openPreviewIfPresentInURL(): Promise<boolean> {
    this.previewForCampaignId = this.activatedRoute.snapshot.queryParams['previewForCampaignId'];
    if (!this.previewForCampaignId) {
      return Promise.resolve(false);
    }

    CampaignPreviewDialogComponent.openAndAddToURL(
      this.dialog,
      this.router,
      this.activatedRoute,
      this.previewForCampaignId,
    );
    return Promise.resolve(true);
  }

  reloadTable(): void {
    this.myCampaignsTableService.reloadData();
  }
}
