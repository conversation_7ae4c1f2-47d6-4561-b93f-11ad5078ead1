import { Inject, Injectable } from '@angular/core';
import {
  CheckboxFilterControl,
  FilterControl,
  FilterGroup,
  SearchFilterControl,
  SearchSelectFilterControl,
} from '@vendasta/va-filter2';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { SenderTag } from '../manage-tags/tag-toggle/tag-toggle.component';
import { CONFIG_TOKEN } from '../../../shared/src/tokens';
import { CampaignConfig, campaignsTableFilterSection } from '@galaxy/campaign/dependencies';

export const SENDER_TAGS_TOKEN = 'com.vendasta.partner_center.campaigns.table_filter.sender_tags';

@Injectable()
export class MyCampaignsTableFilterService {
  filters: FilterGroup = new FilterGroup('my-campaigns-table');

  constructor(
    @Inject(SENDER_TAGS_TOKEN) senderTags$: Observable<SenderTag[]>,
    @Inject(CONFIG_TOKEN) config: CampaignConfig,
  ) {
    this.enableFilterSections(config, senderTags$);
  }

  tagFilterOptions(senderTags$: Observable<SenderTag[]>): Observable<Map<string, SenderTag>> {
    return senderTags$.pipe(
      map((st) => {
        const tagsMap = new Map<string, SenderTag>();
        tagsMap.set('All tags', null);
        st.forEach((t) => tagsMap.set(t.tagName, t));
        return tagsMap;
      }),
    );
  }

  private enableFilterSections(config: CampaignConfig, senderTags$: Observable<SenderTag[]>): void {
    const sectionToControlMapping: Map<campaignsTableFilterSection, FilterControl[]> = new Map([
      [
        'Tags',
        [
          new SearchSelectFilterControl('tag', 'Tag', this.tagFilterOptions(senderTags$), null, {
            searchDisplay: (v: SenderTag) => v?.tagName,
            appliedValueMapper: (name: string, value: SenderTag) => {
              const uppercaseName = name.charAt(0).toUpperCase() + name.slice(1);
              return {
                name: name,
                value: value,
                label: value ? `${uppercaseName}: ${value.tagName}` : null,
              };
            },
          }),
        ],
      ],
      [
        'Status',
        [
          new CheckboxFilterControl('Published', 'Published', true),
          new CheckboxFilterControl('Draft', 'Draft', true),
          new CheckboxFilterControl('Ongoing', 'Ongoing', true),
          new CheckboxFilterControl('Archived', 'Archived', false),
        ],
      ],
    ]);

    const sections = config.enabledFilterSections;
    sections.forEach((section) => {
      const control = sectionToControlMapping.get(section);
      if (control) {
        this.filters.addSection(section, control);
      } else {
        console.warn(`No control found for ${section}. This section will not show up in the campaigns table view.`);
      }
    });

    this.filters.addToolbarSection(new SearchFilterControl('search', 'Search Campaigns'));
  }
}
