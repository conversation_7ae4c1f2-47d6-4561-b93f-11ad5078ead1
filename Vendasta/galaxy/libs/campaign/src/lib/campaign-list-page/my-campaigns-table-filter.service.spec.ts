import { MyCampaignsTableFilterService } from './my-campaigns-table-filter.service';
import { CampaignConfig } from '@galaxy/campaign/dependencies';
import { Observable, of } from 'rxjs';
import { SenderTag } from '../manage-tags/tag-toggle/tag-toggle.component';

describe('MyCampaignsTableFilterService', () => {
  it('should set initial filter sections', () => {
    const config = <CampaignConfig>{
      enabledFilterSections: ['Tags', 'Status'],
    };

    const tags$: Observable<SenderTag[]> = of([]);

    const svc = new MyCampaignsTableFilterService(tags$, config);

    const expectedSections = ['Tags', 'Status'];

    const actualSections: string[] = [];
    svc.filters.sections.forEach((section) => actualSections.push(section.name));

    const containsAll = expectedSections.every((section) => actualSections.includes(section));

    expect(containsAll).toEqual(true);
  });
});
