import { ColumnType, ColumnWidth, Pinned, TableDefinition } from '@vendasta/va-filter2-table';
import { MyCampaignsActionsColumnComponent } from './column-components/my-campaigns-actions-column.component';
import { MyCampaignsActiveRecipientsColumnComponent } from './column-components/my-campaigns-active-recipients-column.component';
import { MyCampaignsEmailsDeliveredColumnComponent } from './column-components/my-campaigns-emails-delivered-column.component';
import { MyCampaignsNameColumnComponent } from './column-components/my-campaigns-name-column.component';
import { MyCampaignsOpenRateColumnComponent } from './column-components/my-campaigns-open-rate-column.component';
import { MyCampaignsStatusColumnComponent } from './column-components/my-campaigns-status-column.component';
import { MyCampaignsTotalRecipientsColumnComponent } from './column-components/my-campaigns-total-recipients-column.component';
import { MyCampaignsTableFilterService } from './my-campaigns-table-filter.service';

const MY_CAMPAIGNS_TABLE_DEF: TableDefinition = {
  id: 'my-campaigns-table',
  labels: {
    noResults: 'No results',
    cancel: 'Cancel',
    none: 'None',
    update: 'Update',
    sortBy: 'Sort By',
    thenSortBy: 'Then Sort By',
    addAnotherSortColumn: 'Add another column to sort on',
    chooseColumnToSortBy: 'Choose a column to sort on',
    columnSorting: {
      date: {
        ascending: 'Earliest to Latest',
        descending: 'Latest to Earliest',
      },
      number: {
        ascending: '1 to 9',
        descending: '9 to 1',
      },
      string: {
        ascending: 'A to Z',
        descending: 'Z to A',
      },
      default: {
        ascending: 'ascending',
        descending: 'descending',
      },
    },
    export: {
      buttonText: 'Export',
      buttonTooltipAfterCount: '',
      buttonTooltipBeforeCount: '',
    },
  },
  options: {
    selectableRows: false,
  },
  columns: [
    {
      cellComponent: MyCampaignsNameColumnComponent,
      id: 'campaignName',
      displayName: 'Name',
      field: 'name',
      translateCell: false,
      type: ColumnType.COLUMN_TYPE_STRING,
      pinned: Pinned.PINNED_LEFT,
    },
    // TODO: MAEL-861 - Add the tag column back in once tags are supported for BCC users
    // {
    //   cellComponent: TagsColumnComponent,
    //   id: 'tags',
    //   displayName: 'Tags',
    //   field: 'tags',
    //   translateCell: false,
    // },
    {
      cellComponent: MyCampaignsTotalRecipientsColumnComponent,
      id: 'totalRecipients',
      displayName: 'Total Recipients',
      field: 'totalAccounts',
      translateCell: false,
      type: ColumnType.COLUMN_TYPE_NUMBER,
      width: ColumnWidth.WIDTH_NARROW,
    },
    {
      cellComponent: MyCampaignsActiveRecipientsColumnComponent,
      id: 'activeRecipients',
      displayName: 'Active Recipients',
      field: 'activeAccounts',
      translateCell: false,
      type: ColumnType.COLUMN_TYPE_NUMBER,
      width: ColumnWidth.WIDTH_NARROW,
    },
    {
      cellComponent: MyCampaignsEmailsDeliveredColumnComponent,
      id: 'emailsDelivered',
      displayName: 'Emails Delivered',
      field: 'emailsDelivered',
      translateCell: false,
      type: ColumnType.COLUMN_TYPE_NUMBER,
      width: ColumnWidth.WIDTH_NARROW,
    },
    {
      cellComponent: MyCampaignsOpenRateColumnComponent,
      id: 'openRate',
      displayName: 'Open Rate',
      field: 'openRate',
      translateCell: false,
      type: ColumnType.COLUMN_TYPE_NUMBER,
      width: ColumnWidth.WIDTH_NARROW,
    },
    {
      cellComponent: MyCampaignsOpenRateColumnComponent,
      id: 'clickThroughRate',
      displayName: 'CTOR',
      field: 'clickThroughRate',
      translateCell: false,
      type: ColumnType.COLUMN_TYPE_NUMBER,
      width: ColumnWidth.WIDTH_NARROW,
    },
    {
      cellComponent: MyCampaignsStatusColumnComponent,
      id: 'status',
      displayName: 'Status',
      field: 'status',
      translateCell: false,
      type: ColumnType.COLUMN_TYPE_STRING,
      width: ColumnWidth.WIDTH_NARROW,
    },
    {
      cellComponent: MyCampaignsActionsColumnComponent,
      id: 'actions',
      displayName: '',
      translateCell: false,
      width: ColumnWidth.WIDTH_ACTION,
    },
  ],
};

export const tableDefinitionFactory = (filterService: MyCampaignsTableFilterService) => {
  const tableDefinition = MY_CAMPAIGNS_TABLE_DEF;
  tableDefinition.filters = filterService.filters;
  return tableDefinition;
};
