import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { HasUnsavedChangesGuard } from '@galaxy/email-ui/email-builder';
import { CampaignSMSBuilderComponent } from './campaign-sms-builder.component';

@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: '',
        component: CampaignSMSBuilderComponent,
      },
      {
        path: 'new',
        pathMatch: 'full',
        component: CampaignSMSBuilderComponent,
        data: {
          pageType: 'create',
        },
        canDeactivate: [HasUnsavedChangesGuard],
      },
      {
        path: ':templateId',
        pathMatch: 'full',
        redirectTo: ':templateId/edit',
      },
      {
        path: ':templateId/edit',
        pathMatch: 'full',
        component: CampaignSMSBuilderComponent,
        data: {
          pageType: 'edit',
        },
        canDeactivate: [HasUnsavedChangesGuard],
      },
    ]),
  ],
  providers: [],
  exports: [RouterModule],
})
export class CampaignSMSBuilderRouter {}
