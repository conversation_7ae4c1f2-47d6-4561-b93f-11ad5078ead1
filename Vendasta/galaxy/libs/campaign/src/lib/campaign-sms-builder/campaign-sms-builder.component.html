<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-nav>
      <button (click)="goToPreviousPage()" color="primary" mat-icon-button>
        <mat-icon>arrow_back</mat-icon>
      </button>
      <glxy-page-title> {{ 'SMS_BUILDER.PAGE.TITLE' | translate }} </glxy-page-title>
      <div></div>
    </glxy-page-nav>
    <glxy-page-actions>
      <button mat-stroked-button (click)="goToPreviousPage()">Cancel</button>
      <button mat-flat-button color="primary" (click)="saveClicked()" [disabled]="saving$ | async">
        <glxy-button-loading-indicator [isLoading]="saving$ | async">Save</glxy-button-loading-indicator>
      </button>
    </glxy-page-actions>
  </glxy-page-toolbar>
  <glxy-page-wrapper>
    <sms-builder
      [allowDynamic]="true"
      (sms)="handleSMSOutput($event)"
      (valid)="isValid = $event"
      [setMessageBuilderText]="smsForm"
    ></sms-builder>
  </glxy-page-wrapper>
</glxy-page>
