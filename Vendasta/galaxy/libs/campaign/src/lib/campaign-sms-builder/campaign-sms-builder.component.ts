import { Component, Inject, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { SmsTextLimitToken, TextMessageBuilderComponent, TextMessageBuilderForm } from '@galaxy/sms';
import { MatButtonModule } from '@angular/material/button';
import { BehaviorSubject, combineLatest, Observable, of, Subscription, take } from 'rxjs';
import { map, shareReplay, startWith, switchMap } from 'rxjs/operators';
import { PAGE_ROUTES } from '../routing-constants';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule, Location } from '@angular/common';
import { CampaignStateService } from '../campaign-details-page/campaign-details/campaign-state.service';
import { Context } from '../campaign-email-builder/page/shared/context';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { SENDER_ID_TOKEN } from '@galaxy/email-ui/email-activity/src/dependencies';
import { SMSTemplateSaver } from '../campaign-email-builder/page/shared/sms-template-saver';
import { TEMPLATE_EXPORTER_KEY } from '../campaign-email-builder/page/shared/dependencies';
import { SMSContentData } from '../campaign-details-page/campaign-details/interface';
import { AppNamespace } from '@vendasta/email-builder';
import { Template, TemplatesService } from '@vendasta/templates';
import { ConfirmationDialogComponent } from '../campaign-list-page/column-components/dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { CampaignStepType } from '@vendasta/campaigns';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../shared/src/tokens';

const SMS_TEXT_LIMIT = 160;

@Component({
  selector: 'campaign-sms-builder',
  templateUrl: 'campaign-sms-builder.component.html',
  styleUrls: ['./campaign-sms-builder.component.scss'],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatInputModule,
    GalaxyFormFieldModule,
    GalaxyPageModule,
    TextMessageBuilderComponent,
    MatButtonModule,
    GalaxyButtonLoadingIndicatorModule,
    TranslateModule,
    MatIconModule,
  ],
  providers: [
    {
      provide: TEMPLATE_EXPORTER_KEY,
      useExisting: SMSTemplateSaver,
    },
    {
      provide: SmsTextLimitToken,
      useValue: SMS_TEXT_LIMIT,
    },
  ],
})
export class CampaignSMSBuilderComponent implements OnInit, OnDestroy {
  backPageURL$: Observable<string> = new Observable<string>();
  campaignID$: Observable<string> = new Observable<string>();
  templateID$: Observable<string> = new Observable<string>();
  senderId: string;
  campaignStepId: string | undefined;
  @Input() campaignId = '';
  @Input() context: Context | undefined = undefined;
  @Input() smsContent: SMSContentData | undefined = undefined;

  readonly saving$: Observable<boolean>;
  smsForm: TextMessageBuilderForm = { message: '', name: '' };
  smsOutput: TextMessageBuilderForm = { message: '', name: '' };
  isValid = true;
  private subscriptions: Subscription[] = [];
  latestSMSData?: SMSContentData;
  template$: Observable<Template> = new Observable<Template>();
  saved$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly location: Location,
    private readonly alerts: SnackbarService,
    private readonly i18n: TranslateService,
    private readonly campaignStateService: CampaignStateService,
    private smsTemplateSaver: SMSTemplateSaver,
    private templatesService: TemplatesService,
    private readonly snackbarService: SnackbarService,
    private dialog: MatDialog,
    private translate: TranslateService,
    private router: Router,
    @Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE,
    @Inject(SENDER_ID_TOKEN) readonly senderId$: Observable<string>,
  ) {
    this.saving$ = this.smsTemplateSaver.workState.isLoading$.pipe(startWith(true));
  }

  ngOnInit(): void {
    this.campaignID$ = this.route.queryParams.pipe(map((params) => params['campaignId']));
    this.backPageURL$ = combineLatest([this.campaignID$, this.config.basePath$]).pipe(
      map(([campaignId, basePath]) => `${basePath}/${PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.DETAILS(campaignId)}`),
    );
    this.templateID$ = this.route.params.pipe(map((params) => params['templateId']));
    this.subscriptions.push(
      this.senderId$.subscribe((senderId) => {
        this.senderId = senderId;
      }),
    );
    this.template$ = this.templateID$.pipe(
      switchMap((templateId) => {
        if (!templateId) {
          this.context = Context.forNewTemplate(this.senderId);
          return of();
        } else {
          return this.templatesService.get(AppNamespace.CAMPAIGNS, templateId);
        }
      }),
      shareReplay(1),
    );

    this.template$.subscribe((template) => {
      if (template) {
        this.smsForm = { message: template.content, name: template.name };
        this.context = template
          ? Context.forExistingTemplate(this.senderId, template.templateId)
          : Context.forNewTemplate(this.senderId);
      }
    });
  }

  handleSMSOutput(out: TextMessageBuilderForm): void {
    this.saved$$.next(false);
    this.smsOutput = out;
  }

  saveClicked(): void {
    const senderId = this.senderId;
    const campaignId = this.campaignId;

    const template = {
      name: this.smsOutput.name,
      message: this.smsOutput.message,
      appNamespace: AppNamespace.CAMPAIGNS,
      context: this.context,
      stepType: 'sms',
    } as SMSContentData;

    if (!this.isValid) {
      this.snackbarService.openErrorSnack('EMAIL_BUILDER.SAVING.TEMPLATE_INVALID_ERROR');
      return;
    }

    this.smsTemplateSaver.save$(senderId, this.context, campaignId, template, '').subscribe({
      next: (s: SMSContentData | undefined) => {
        if (s !== undefined) {
          this.saved$$.next(true);
          this.handleSaveSuccess(senderId, campaignId, s);
        }
      },
      error: () => {
        this.saved$$.next(false);
        this.alerts.openErrorSnack(this.i18n.instant('EMAIL_BUILDER.SAVING.GENERIC_ERROR'));
      },
    });
  }

  handleSaveSuccess(ownerId: string, campaignId: string, resp: SMSContentData): void {
    this.alerts.openSuccessSnack(this.i18n.instant('EMAIL_BUILDER.PAGE.ACTIONS.SAVE_SUCCESS'));
    if (this.context?.isNew()) {
      const tempID = resp.templateId ?? '';
      this.campaignStateService.addCampaignStep(
        this.context,
        this.campaignId,
        tempID,
        this.smsOutput.name || '',
        CampaignStepType.CAMPAIGN_STEP_TYPE_SMS,
      );
      this.context = Context.forExistingTemplate(ownerId, tempID);
    } else {
      this.campaignStateService.updateCampaignStep(
        this.context,
        this.campaignId,
        resp.campaignStepId,
        resp.templateId,
        resp.name || undefined,
        undefined,
        CampaignStepType.CAMPAIGN_STEP_TYPE_SMS,
      );
    }
  }

  goToPreviousPage(): void {
    const isSaved = this.saved$$.getValue();
    const isFormUnchanged =
      this.smsForm.message === this.smsOutput.message && this.smsForm.name === this.smsOutput.name;
    const isNewContext = this.context?.isNew();

    if (!isFormUnchanged && !isSaved) {
      this.showEditConfirmationDialog();
    } else if (isFormUnchanged || isNewContext || isSaved) {
      this.navigateBack();
    }
  }

  private showEditConfirmationDialog(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.EDIT.TITLE', {
          title: this.campaignStateService.state.name,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.EDIT.MESSAGE'),
        warn: true,
        buttonText: {
          confirm: this.translate.instant('COMMON.ACTION_LABELS.LEAVE'),
          decline: this.translate.instant('COMMON.ACTION_LABELS.CANCEL'),
        },
      },
      maxWidth: '600px',
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.navigateBack();
      }
    });
  }

  private navigateBack(): void {
    this.backPageURL$.pipe(take(1)).subscribe((url) => {
      this.router.navigateByUrl(url);
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
