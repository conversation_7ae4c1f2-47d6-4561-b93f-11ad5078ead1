import { CommonModule } from '@angular/common';
import { PageActionsComponent } from './page/page-actions/page-actions.component';
import { PageComponent } from './page/page.component';
import { TranslateModule } from '@ngx-translate/core';
import { CampaignEmailBuilderRouting } from './campaign-email-builder.routing';
import { CampaignEmailBuilderComponent } from './campaign-email-builder.component';
import { NgModule } from '@angular/core';
import { EmailTemplateSaver } from './page/shared/email-template-saver';
import { CampaignStateService } from '../campaign-details-page/campaign-details/campaign-state.service';
import { CampaignsService } from '../campaign-details-page/campaign-details/campaigns.service';
import { DynamicComponentSelectorModule } from '@galaxy/email-ui/email-builder/src/components/dynamic-component-selector';
import { PreviewContextSelectorModule } from '@galaxy/email-ui/email-builder/src/components/preview-context-selector/preview-context-selector.module';

@NgModule({
  declarations: [CampaignEmailBuilderComponent],
  imports: [
    CommonModule, //
    PageActionsComponent,
    PageComponent,
    TranslateModule,
    CampaignEmailBuilderRouting,
    DynamicComponentSelectorModule,
    PreviewContextSelectorModule,
  ],
  exports: [CampaignEmailBuilderComponent],
  providers: [EmailTemplateSaver, CampaignStateService, CampaignsService],
})
export class CampaignEmailBuilderModule {}
