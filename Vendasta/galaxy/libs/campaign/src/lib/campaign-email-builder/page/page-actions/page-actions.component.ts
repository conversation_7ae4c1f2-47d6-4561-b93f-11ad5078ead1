import { Component, Inject, Input, OnDestroy, OnInit } from '@angular/core';
import { firstValueFrom, Observable, Subscription } from 'rxjs';
import { startWith } from 'rxjs/operators';
import { EmailTemplateSaver, SaveResult } from '../shared/email-template-saver';
import { Context } from '../shared/context';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule, Location } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { CampaignStateService } from '../../../campaign-details-page/campaign-details/campaign-state.service';
import { EmailPreviewHydrationData, TEMPLATE_HYDRATION_DATA_TOKEN } from '@galaxy/email-ui/email-builder';
import { MatDialog } from '@angular/material/dialog';
import {
  SendPreviewDialogComponent,
  SendPreviewDialogData,
} from '../send-preview-dialog/send-preview-dialog.component';

@Component({
  selector: 'campaign-editor-actions',
  templateUrl: './page-actions.component.html',
  styleUrls: ['./page-actions.component.scss'],
  imports: [
    CommonModule, //
    MatButtonModule,
    TranslateModule,
    GalaxyButtonLoadingIndicatorModule,
  ],
})
export class PageActionsComponent implements OnDestroy, OnInit {
  @Input() ownerId = '';
  @Input() context: Context | undefined = undefined;
  @Input() campaignStepId = '';
  @Input() campaignId = '';
  readonly saving$: Observable<boolean>;
  private subscriptions: Subscription[] = [];

  constructor(
    @Inject(TEMPLATE_HYDRATION_DATA_TOKEN) private readonly hydrationData$: Observable<EmailPreviewHydrationData>,
    private readonly location: Location,
    private readonly alerts: SnackbarService,
    private readonly i18n: TranslateService,
    private readonly saver: EmailTemplateSaver,
    private readonly campaignStateService: CampaignStateService,
    private dialog: MatDialog,
  ) {
    this.saving$ = this.saver.workState.isLoading$.pipe(startWith(true));
  }

  ngOnInit(): void {
    this.saver.initialize();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  async openPreviewEmailDialog(): Promise<void> {
    const contentData = this.saver.getLatestData();
    const htmlContent = this.saver.getLatestHTML();
    const data: EmailPreviewHydrationData = await firstValueFrom(this.hydrationData$);
    this.dialog.open(SendPreviewDialogComponent, {
      data: {
        partnerID: data.partnerID,
        marketID: data.marketID,
        business: data.business,
        subjectLine: contentData.subjectLine,
        emailHtmlContent: htmlContent,
        locale: data.locale,
      } as SendPreviewDialogData,
      width: '400px',
    });
  }

  handleSaveClicked(): void {
    this.subscriptions.push(
      this.saver.saveWithWorkState$(this.ownerId, this.context, this.campaignId, this.campaignStepId).subscribe({
        next: (res: SaveResult) => this.handleSaveSuccess(this.ownerId, this.campaignId, res),
        error: (err: Error) => this.handleSaveError(err),
      }),
    );
  }

  private handleSaveSuccess(ownerId: string, campaignId: string, res: SaveResult): void {
    this.alerts.openSuccessSnack(this.i18n.instant('EMAIL_BUILDER.PAGE.ACTIONS.SAVE_SUCCESS'));
    if (this.context?.isNew()) {
      this.campaignStateService.addCampaignStep(
        this.context,
        this.campaignId,
        res.templateId,
        this.saver.getLatestData().name || '',
      );
      this.context = Context.forExistingTemplate(ownerId, res.templateId);
    } else {
      this.campaignStateService.updateCampaignStep(
        this.context,
        this.campaignId,
        res.campaignStepId,
        res.templateId,
        res.campaign?.name || undefined,
        undefined,
      );
    }

    this.campaignStepId = res.campaignStepId;
    const reloadURL = `/template/${res.templateId}/edit?campaignId=${campaignId}&campaignStepId=${res.campaignStepId}`;
    this.location.go(reloadURL);
  }

  private handleSaveError(err: Error): void {
    this.alerts.openErrorSnack(this.i18n.instant('EMAIL_BUILDER.PAGE.ACTIONS.ERROR_NOTICE'));
    console.error(err);
  }
}
