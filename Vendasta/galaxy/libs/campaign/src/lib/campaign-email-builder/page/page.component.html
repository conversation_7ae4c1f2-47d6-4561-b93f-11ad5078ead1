<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-nav *ngIf="!!previousPageTitle">
      <glxy-page-nav-button
        [previousPageUrl]="previousPageURL"
        [previousPageTitle]="previousPageTitle"
      ></glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>{{ 'EMAIL_BUILDER.PAGE.TITLE' | translate }}</glxy-page-title>
    <glxy-page-actions>
      <ng-content #toolbar_actions></ng-content>
    </glxy-page-actions>
  </glxy-page-toolbar>
  <uikit-async-ui
    [loading]="fetchState.isLoading$ | async"
    [error]="(fetchState.isSuccess$ | async) === false"
    [data]="fetchState.workResults$ | async"
    loadingMargin="auto"
    errorMargin="auto"
  >
    <ng-container *successData="let data">
      <email-builder
        #emailBuilder
        [emailContent]="data"
        [placeholderLogoUrl]="placeholderLogo$ | async"
        [advancedSections]="advancedSections"
        [locale]="locale"
        [dynamicComponents]="dynamicComponents"
        [previewHydrationParams]="previewHydrationData$$ | async"
        (emailContentChanged)="this.handleContentChanges($event)"
        (emailRenderUpdated)="this.handleRenderUpdated($event)"
        (emailRenderFailed)="this.handleRenderFailed()"
      >
        <div class="container" preview_header>
          <email-preview-selector
            [namespace]="namespace$ | async"
            (contextSelected)="handlePreviewContextChanges($event)"
          ></email-preview-selector>
        </div>
      </email-builder>
    </ng-container>
  </uikit-async-ui>
</glxy-page>
