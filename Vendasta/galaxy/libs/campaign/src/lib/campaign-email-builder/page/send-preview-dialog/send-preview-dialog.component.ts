import { Component, Inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogClose,
  MatDialogContent,
  MatDialogRef,
} from '@angular/material/dialog';
import { PreviewContextSelectorModule, PreviewRenderingService } from '@galaxy/email-ui/email-builder';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BehaviorSubject, combineLatest, firstValueFrom, Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { SelectedPreviewContext } from '@galaxy/email-ui/email-builder/src/components/preview-context-selector/preview-context-selector.component';
import { GalaxyWrapModule } from '@vendasta/galaxy/galaxy-wrap';
import { MatButton } from '@angular/material/button';
import { EmailTemplateService } from '@vendasta/campaigns';
import { TranslateModule } from '@ngx-translate/core';
import { SENDER_INJECTION_TOKEN } from '@galaxy/email-ui/email-builder/src/shared';
import { Sender } from '@vendasta/email';
import { AsyncPipe } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

export interface SendPreviewDialogData {
  business: SelectedPreviewContext;
  subjectLine: string;
  emailHtmlContent: string;
  partnerID: string;
  marketID: string;
  locale?: string;
}

@Component({
  templateUrl: './send-preview-dialog.component.html',
  imports: [
    FormsModule,
    GalaxyWrapModule,
    MatButton,
    MatDialogActions,
    MatDialogClose,
    MatDialogContent,
    PreviewContextSelectorModule,
    TranslateModule,
    AsyncPipe,
  ],
})
export class SendPreviewDialogComponent {
  protected readonly namespace$ = this.sender$.pipe(map((sender) => sender.id));
  private readonly contactForPreview$$ = new BehaviorSubject('default');
  private readonly hydratedContent$ = this.emailPreviewService.hydrateTemplate(this.data.emailHtmlContent);
  private readonly hydratedSubject$ = this.emailPreviewService.hydrateSubject(
    this.data.subjectLine || 'Preview for unnamed template',
  );

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: SendPreviewDialogData,
    public dialogRef: MatDialogRef<SendPreviewDialogComponent>,
    private emailPreviewService: PreviewRenderingService,
    private readonly campaignsService: EmailTemplateService,
    private readonly alerts: SnackbarService,
    @Inject('USER_ID') private userId$: Observable<string>,
    @Inject(SENDER_INJECTION_TOKEN) private readonly sender$: Observable<Sender>,
  ) {
    combineLatest([this.sender$, this.userId$, this.contactForPreview$$])
      .pipe(
        takeUntilDestroyed(),
        map(([_, userId, contactId]) => {
          return {
            business: this.data.business,
            contactID: contactId,
            userID: userId,
            partnerID: this.data.partnerID,
            marketID: this.data.marketID,
            businessID: this.data.business.accountGroupId,
          };
        }),
      )
      .subscribe((hydrationData) => {
        this.emailPreviewService.setPreviewHydrationParams(hydrationData);
      });

    this.emailPreviewService.setLocale(data?.locale || '');
  }

  async sendPreviewEmail(): Promise<void> {
    const response = combineLatest([this.hydratedContent$, this.hydratedSubject$, this.userId$, this.sender$]).pipe(
      switchMap(([content, subject, userId, sender]) => {
        return this.campaignsService.sendPreview(
          {
            id: sender.id,
            type: sender.type,
          },
          content,
          subject,
          userId,
        );
      }),
      catchError((error) => {
        this.handleSendError(error);
        return of(false);
      }),
    );

    const sent = await firstValueFrom(response);
    if (sent) {
      this.handleSendSuccess();
    }
  }

  handleSendSuccess(): void {
    this.alerts.openSuccessSnack('CAMPAIGN_PREVIEW.SEND_SUCCESS');
    this.dialogRef.close();
  }

  handleSendError(error: Error): void {
    this.alerts.openErrorSnack('CAMPAIGN_PREVIEW.SEND_FAILED');
    console.error(error);
  }

  updateSelectedContact($event: SelectedPreviewContext): void {
    if ($event.contactID) {
      this.contactForPreview$$.next($event.contactID);
    }
  }
}
