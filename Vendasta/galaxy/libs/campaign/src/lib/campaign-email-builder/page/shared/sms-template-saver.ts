import { Inject, Injectable } from '@angular/core';
import { TemplateReferenceInterface, TemplatesService as CampaignsTemplateService } from '@vendasta/campaigns';
import { AppNamespace, OwnerType } from '@vendasta/email-builder';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ObservableWorkState, WorkStates } from '@vendasta/rx-utils/work-state';
import { Observable, catchError, of, throwError } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { CampaignData, SMSContentData } from '../../../campaign-details-page/campaign-details/interface';
import { Context } from './context';
import { TemplatesService } from '@vendasta/templates';

export interface SaveResult {
  templateId: string;
  campaignStepId: string;
  campaign?: SMSContentData;
}

export interface TemplateService {
  createNewTemplate$(
    ownerType: OwnerType,
    ownerId: string,
    namespace: AppNamespace,
    templateId: string,
    data: SMSContentData,
    html: string,
    parent?: TemplateReferenceInterface,
  ): Observable<boolean>;

  updateExistingTemplate$(
    ownerType: OwnerType,
    ownerId: string,
    namespace: AppNamespace,
    templateId: string,
    data: SMSContentData,
  ): Observable<boolean>;

  validateTemplate(data: SMSContentData): boolean;
}

export interface TemplateIDGenerator {
  generateSMS(): string;
}

@Injectable({ providedIn: 'root' })
class UUIDTemplateIDGenerator implements TemplateIDGenerator {
  generateSMS(): string {
    return `SMS-BUILDER-${uuidv4()}`;
  }
}

@Injectable()
export class SMSTemplateSaver {
  private readonly workState$ = new ObservableWorkState<SaveResult>();
  public readonly workState: WorkStates<SaveResult> = this.workState$;

  constructor(
    @Inject(UUIDTemplateIDGenerator) private readonly idGenerator: TemplateIDGenerator = new UUIDTemplateIDGenerator(),
    private readonly snackbarService: SnackbarService,
    private templatesService: TemplatesService,
    private campaignsTemplateService: CampaignsTemplateService,
  ) {}

  save$(
    ownerId: string,
    smsTemplateContext: Context,
    campaignId: string,
    template: SMSContentData,
    campaignStepId?: string,
  ): Observable<SMSContentData> {
    return this.doSave(template, campaignId, campaignStepId);
  }

  private doSave(template: SMSContentData, campaignId: string, campaignStepId?: string): Observable<SMSContentData> {
    if (!template) {
      return throwError('no sms content data');
    }
    if (template.context?.isNew()) {
      return this.createNew(template);
    } else {
      return this.updateExisting(campaignId, campaignStepId, template);
    }
  }

  private updateExisting(
    campaignId: string,
    campaignStepID: string,
    updateArgs: SMSContentData,
  ): Observable<SMSContentData> {
    return this.campaignsTemplateService
      .updateSMSTemplate(
        campaignId,
        updateArgs.context.getExistingTemplateId(),
        updateArgs.name || '',
        updateArgs.message || '',
      )
      .pipe(
        catchError((err) => {
          this.snackbarService.openErrorSnack('EMAIL_BUILDER.SAVING.GENERIC_ERROR');
          throw new Error(err);
        }),
        switchMap(() => {
          const campaignSMSData: CampaignData = {} as CampaignData;
          campaignSMSData.name = updateArgs.name || '';
          return of({
            templateId: updateArgs.context.getExistingTemplateId(),
            campaignStepId: campaignStepID,
            campaign: campaignSMSData,
          } as SMSContentData);
        }),
      );
  }

  private createNew(createArgs: SMSContentData): Observable<SMSContentData> {
    const templateId = this.idGenerator.generateSMS();
    return this.templatesService
      .create(AppNamespace.CAMPAIGNS, {
        templateId: templateId,
        name: createArgs.name || '',
        content: createArgs.message || '',
      })
      .pipe(
        take(1),
        catchError((err) => {
          this.snackbarService.openErrorSnack('EMAIL_BUILDER.SAVING.GENERIC_ERROR');
          throw new Error(err);
        }),
        map((ebTemplateSucceeded) => {
          if (!ebTemplateSucceeded) {
            return {} as SMSContentData;
          }

          return {
            templateId: templateId || '',
          } as SMSContentData;
        }),
      );
  }
}
