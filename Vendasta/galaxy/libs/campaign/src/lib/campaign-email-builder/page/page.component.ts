import { CommonModule } from '@angular/common';
import { Component, Inject, Input, OnDestroy, Optional, ViewChild } from '@angular/core';
import {
  AdvancedSections,
  DYNAMIC_COMPONENT_DATA_TOKEN,
  EMAIL_FOOTER_TOKEN,
  EMAIL_TOP_LOGO_TOKEN,
  EmailBuilderComponent,
  EmailBuilderDataService,
  EmailBuilderModule,
  EmailContentData,
  EmailPreviewHydrationData,
  PLACEHOLDER_LOGO_URL_TOKEN,
  PreviewContextSelectorModule,
  TEMPLATE_HYDRATION_DATA_TOKEN,
  VariableMenuItem,
} from '@galaxy/email-ui/email-builder';
import { TranslateModule } from '@ngx-translate/core';
import { AppNamespace, OwnerType } from '@vendasta/email-builder';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { SwitchingWorkState, WorkStates } from '@vendasta/rx-utils/work-state';
import { AsyncUiModule } from '@vendasta/uikit';
import { BehaviorSubject, combineLatest, EMPTY, Observable, of } from 'rxjs';
import { skipWhile, take, map } from 'rxjs/operators';

import { Context } from './shared/context';
import { TEMPLATE_EXPORTER_KEY, TemplateExporter } from './shared/dependencies';
import { EmailTemplateSaver } from './shared/email-template-saver';
import {
  Footer,
  TopLogo,
} from '@galaxy/email-ui/email-builder/src/components/email-advanced-settings/email-advanced-settings.component';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SubscriptionList } from '@vendasta/rx-utils';
import { SelectedPreviewContext } from '@galaxy/email-ui/email-builder/src/components/preview-context-selector/preview-context-selector.component';
import { MatButtonModule } from '@angular/material/button';
import { BreakpointObserver } from '@angular/cdk/layout';
import { SENDER_INJECTION_TOKEN } from '@galaxy/email-ui/email-builder/src/shared';
import { Sender } from '@vendasta/email';

const initialContent: EmailContentData = {
  subjectLine: '',
  name: '',
  previewText: '',
  emailSetup: {
    pageSetup: {
      collapsed: true,
    },
    topLogo: {
      showLogo: 'show',
      collapsed: true,
    },
    colors: {
      collapsed: true,
    },
    footer: {
      collapsed: true,
    },
  },
  emailContentBlocks: [],
};

interface Fetch {
  templateId: string;
  ownerType: OwnerType;
  ownerId: string;
}

@Component({
  selector: 'campaign-editor-page',
  templateUrl: './page.component.html',
  styleUrls: ['./page.component.scss'],
  imports: [
    CommonModule,
    GalaxyPageModule,
    AsyncUiModule,
    TranslateModule,
    EmailBuilderModule,
    PreviewContextSelectorModule,
    MatButtonModule,
  ],
  providers: [{ provide: TEMPLATE_EXPORTER_KEY, useExisting: EmailTemplateSaver }],
})
export class PageComponent implements OnDestroy {
  private templateId = '';
  content$: Observable<EmailContentData> = EMPTY;

  readonly previewHydrationData$$: BehaviorSubject<EmailPreviewHydrationData> =
    new BehaviorSubject<EmailPreviewHydrationData>({} as EmailPreviewHydrationData);

  @Input() previousPageTitle = '';
  @Input() previousPageURL = '';
  @Input() locale = '';

  readonly advancedSections: AdvancedSections = {
    topLogo: true,
    pageSetup: true,
    footer: true,
    colors: true,
  };

  @Input() set context(ctx: Context) {
    let templateId: string;
    if (ctx.isNew()) {
      templateId = ''; // Don't try to load existing content
    } else {
      templateId = ctx.getExistingTemplateId();
    }
    this.fetchState$.startWork({
      ownerType: ctx.getOwnerType(),
      ownerId: ctx.getOwnerId(),
      templateId: templateId,
    });
    this.templateId = templateId;
  }

  private readonly fetchState$ = new SwitchingWorkState<Fetch, EmailContentData>((fetch: Fetch) => {
    const emptyContent: EmailContentData = { ...initialContent, emailContentBlocks: [] };
    if (!fetch.templateId) {
      this.content$ = of(emptyContent);
    } else {
      this.content$ = this.dataSvc
        .loadTemplate$(fetch.ownerType, fetch.ownerId, AppNamespace.CAMPAIGNS, fetch.templateId)
        .pipe(take(1));
    }
    const footer$ = this.footer$ ? this.footer$ : of(null);
    return combineLatest([this.content$, footer$]).pipe(
      map(([content, footer]: [EmailContentData, Footer | null]) => {
        if (footer) {
          const userInputtedDisclaimer = content?.emailSetup?.footer?.disclaimer ?? '';

          content.emailSetup.footer = footer;
          content.emailSetup.footer.disclaimer = userInputtedDisclaimer;
        }
        if (this.defaultTopLogo?.defaultSourceUrl && content.emailSetup.topLogo?.showLogo === 'show') {
          content.emailSetup.topLogo = { ...this.defaultTopLogo, ...content.emailSetup.topLogo };
        }

        return content;
      }),
    );
  });

  readonly fetchState: WorkStates<EmailContentData> = this.fetchState$;
  private readonly subs = SubscriptionList.new();

  @ViewChild('emailBuilder') private readonly emailBuilderChild: EmailBuilderComponent;

  protected readonly namespace$ = this.sender$.pipe(map((sender) => sender.id));

  constructor(
    private readonly dataSvc: EmailBuilderDataService,
    @Inject(TEMPLATE_EXPORTER_KEY) private readonly exporter: TemplateExporter,
    @Inject(TEMPLATE_HYDRATION_DATA_TOKEN)
    @Optional()
    readonly initialPreviewHydrationData$: Observable<EmailPreviewHydrationData>,
    @Inject(PLACEHOLDER_LOGO_URL_TOKEN) @Optional() readonly placeholderLogo$: Observable<string>,
    @Inject(EMAIL_FOOTER_TOKEN) @Optional() private readonly footer$: Observable<Footer>,
    @Inject(EMAIL_TOP_LOGO_TOKEN) @Optional() readonly defaultTopLogo: TopLogo,
    @Inject(SENDER_INJECTION_TOKEN) private readonly sender$: Observable<Sender>,
    @Inject(DYNAMIC_COMPONENT_DATA_TOKEN) @Optional() readonly dynamicComponents: VariableMenuItem[],
    private readonly alerts: SnackbarService,
    private readonly breakpointObserver: BreakpointObserver,
  ) {
    this.subs.add(this.exporter.workState.successEvents$, (success) => {
      if (success) {
        this.emailBuilderChild.saveEmail();
      }
    });
    if (initialPreviewHydrationData$) {
      this.subs.addSub(
        initialPreviewHydrationData$
          .pipe(
            skipWhile((val) => !val),
            take(1),
          )
          .subscribe((data) => this.previewHydrationData$$.next(data)),
      );
    }
  }

  handlePreviewContextChanges(event: SelectedPreviewContext): void {
    const updatedParams = {
      ...this.previewHydrationData$$.value,
      accountGroupId: event.accountGroupId,
      contactID: event.contactID,
      useFakeData: event.useFakeData,
    };
    this.previewHydrationData$$.next(updatedParams);
  }

  handleContentChanges(event: EmailContentData): void {
    this.exporter.updateContentData(event);
  }

  handleRenderUpdated(html: string): void {
    this.exporter.updateHTML(html);
  }

  handleRenderFailed(): void {
    this.alerts.openErrorSnack('EMAIL_BUILDER.PAGE.HANDLE_RENDER_ERROR');
  }

  ngOnDestroy(): void {
    this.subs.destroy();
  }
}
