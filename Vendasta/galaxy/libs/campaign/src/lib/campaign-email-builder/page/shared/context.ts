import { SenderType } from '@vendasta/campaigns';
import { OwnerType } from '@vendasta/email-builder';

export class Context {
  public static forExistingTemplate(ownerId: string, templateId: string): Context {
    return new Context(ownerId, templateId);
  }

  public static forNewTemplate(ownerId: string): Context {
    return new Context(ownerId, null);
  }

  private constructor(private readonly ownerId: string, private readonly templateId: string | null) {}

  isNew(): boolean {
    return this.templateId === null;
  }

  getOwnerId(): string {
    return this.ownerId;
  }

  getExistingTemplateId(): string {
    if (this.templateId === null) {
      throw new Error('get called on context with no template ID');
    }
    return this.templateId;
  }

  getSenderType(): SenderType {
    if (this.ownerId.startsWith('AG-')) {
      return SenderType.SENDER_TYPE_BUSINESS;
    }
    return SenderType.SENDER_TYPE_PARTNER;
  }

  getOwnerType(): OwnerType {
    if (this.ownerId.startsWith('AG-')) {
      return OwnerType.OWNER_TYPE_ACCOUNT_GROUP;
    }
    return OwnerType.OWNER_TYPE_PARTNER;
  }
}
