<mat-dialog-content>
  <email-preview-selector
    [namespace]="namespace$ | async"
    (contextSelected)="updateSelectedContact($event)"
  ></email-preview-selector>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-flat-button mat-dialog-close="">{{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}</button>
  <button mat-button color="primary" (click)="sendPreviewEmail()">{{ 'EMAIL_EDITOR_PREVIEW.SEND' | translate }}</button>
</mat-dialog-actions>
