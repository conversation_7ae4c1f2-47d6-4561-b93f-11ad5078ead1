import { Inject, Injectable } from '@angular/core';
import { EmailBuilderDataService, EmailContentData } from '@galaxy/email-ui/email-builder';
import { TemplateReferenceInterface } from '@vendasta/campaigns';
import { AppNamespace, OwnerType } from '@vendasta/email-builder';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ObservableWorkState, WorkStates } from '@vendasta/rx-utils/work-state';
import { Observable, catchError, of, throwError } from 'rxjs';
import { shareReplay } from 'rxjs/internal/operators/shareReplay';
import { map, switchMap, take } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { CampaignData } from '../../../campaign-details-page/campaign-details/interface';
import { Context } from './context';
import { TemplateExporter } from './dependencies';

export interface SaveResult {
  templateId: string;
  campaignStepId: string;
  campaign?: CampaignData;
}

export interface TemplateService {
  createNewTemplate$(
    ownerType: OwnerType,
    ownerId: string,
    namespace: AppNamespace,
    templateId: string,
    data: EmailContentData,
    html: string,
    parent?: TemplateReferenceInterface,
  ): Observable<boolean>;

  updateExistingTemplate$(
    ownerType: OwnerType,
    ownerId: string,
    namespace: AppNamespace,
    templateId: string,
    data: EmailContentData,
    html: string,
  ): Observable<boolean>;

  validateTemplate(data: EmailContentData): boolean;
}

export interface TemplateIDGenerator {
  generate(): string;
}

@Injectable({ providedIn: 'root' })
class UUIDTemplateIDGenerator implements TemplateIDGenerator {
  generate(): string {
    return `EMAIL-BUILDER-${uuidv4()}`;
  }
}

@Injectable()
export class EmailTemplateSaver implements TemplateExporter {
  private latestData?: EmailContentData;
  private latestHTML?: string;

  private readonly workState$ = new ObservableWorkState<SaveResult>();
  public readonly workState: WorkStates<SaveResult> = this.workState$;
  constructor(
    @Inject(EmailBuilderDataService) private readonly emailBuilderDataService: TemplateService,
    @Inject(UUIDTemplateIDGenerator) private readonly idGenerator: TemplateIDGenerator = new UUIDTemplateIDGenerator(),
    private readonly snackbarService: SnackbarService,
  ) {
    this.initialize();
  }

  initialize(): void {
    this.latestData = {} as EmailContentData;
    this.latestHTML = '';
  }

  saveWithWorkState$(
    ownerId: string,
    emailTemplateContext: Context,
    campaignId: string,
    campaignStepId?: string,
    parent?: TemplateReferenceInterface,
  ): Observable<SaveResult> {
    const o$ = this.doSave(ownerId, emailTemplateContext, campaignId, campaignStepId, parent).pipe(
      shareReplay({
        bufferSize: 1,
        refCount: true,
      }),
    );
    this.workState$.startWork(o$);
    return o$;
  }

  save$(
    ownerId: string,
    emailTemplateContext: Context,
    campaignId: string,
    campaignStepId?: string,
    parent?: TemplateReferenceInterface,
  ): Observable<SaveResult> {
    return this.doSave(ownerId, emailTemplateContext, campaignId, campaignStepId, parent);
  }

  private doSave(
    ownerId: string,
    emailTemplateContext: Context,
    campaignId: string,
    campaignStepId?: string,
    parent?: TemplateReferenceInterface,
  ): Observable<SaveResult> {
    if (!this.latestData) {
      return throwError('no email content data');
    }
    if (!this.latestHTML) {
      return throwError('no email HTML');
    }

    const createOrUpdateArgs = {
      ownerId: ownerId,
      data: this.latestData,
      html: this.latestHTML,
    };

    if (emailTemplateContext.isNew()) {
      return this.createNew(createOrUpdateArgs, parent);
    } else {
      return this.updateExisting(campaignId, campaignStepId, createOrUpdateArgs, emailTemplateContext);
    }
  }

  private updateExisting(
    campaignID: string,
    campaignStepID: string,
    updateArgs: {
      ownerId: string;
      data: EmailContentData;
      html: string;
    },
    ctx: Context,
  ): Observable<SaveResult> {
    return this.emailBuilderDataService
      .updateExistingTemplate$(
        OwnerType.OWNER_TYPE_ACCOUNT_GROUP,
        updateArgs.ownerId,
        AppNamespace.CAMPAIGNS,
        ctx.getExistingTemplateId(),
        updateArgs.data,
        updateArgs.html,
      )
      .pipe(
        catchError((err) => {
          this.snackbarService.openErrorSnack('EMAIL_BUILDER.SAVING.GENERIC_ERROR');
          throw new Error(err);
        }),
        switchMap((ebTemplateSucceeded) => {
          if (!ebTemplateSucceeded) {
            return of({} as SaveResult);
          }
          const campaignData: CampaignData = {} as CampaignData;
          campaignData.name = updateArgs.data.name || '';
          return of({
            templateId: ctx.getExistingTemplateId(),
            campaignStepId: campaignStepID,
            campaign: campaignData,
          } as SaveResult);
        }),
      );
  }

  private createNew(
    createArgs: {
      ownerId: string;
      data: EmailContentData;
      html: string;
    },
    parent?: TemplateReferenceInterface,
  ): Observable<SaveResult> {
    const templateId = this.idGenerator.generate();
    return this.emailBuilderDataService
      .createNewTemplate$(
        OwnerType.OWNER_TYPE_ACCOUNT_GROUP,
        createArgs.ownerId,
        AppNamespace.CAMPAIGNS,
        templateId,
        createArgs.data,
        createArgs.html,
        parent,
      )
      .pipe(
        take(1),
        catchError((err) => {
          this.snackbarService.openErrorSnack('EMAIL_BUILDER.SAVING.GENERIC_ERROR');
          throw new Error(err);
        }),
        map((ebTemplateSucceeded) => {
          if (!ebTemplateSucceeded) {
            return {} as SaveResult;
          }
          return {
            templateId: templateId || '',
          } as SaveResult;
        }),
      );
  }

  updateContentData(data: EmailContentData): void {
    this.latestData = data;
  }

  updateHTML(html: string): void {
    this.latestHTML = html;
  }

  getLatestData(): EmailContentData {
    return this.latestData || ({} as EmailContentData);
  }

  getLatestHTML(): string {
    return this.latestHTML || '';
  }
}

export function getStepIdFromCreateResponse(templateId: string, response: string): string {
  const parsed = JSON.parse(response);
  const steps: Array<any> = parsed['campaign_schedule'];
  const lastSteps = steps.reverse();
  for (let i = 0; i < lastSteps.length; i++) {
    if (lastSteps[i]?.template_id === templateId) {
      return lastSteps[i].campaign_step_id;
    }
  }
  throw new Error('Could not extract campaign step ID from response');
}
