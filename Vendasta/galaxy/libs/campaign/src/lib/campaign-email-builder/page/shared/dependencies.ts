import { EmailContentData } from '@galaxy/email-ui/email-builder';
import { WorkStates } from '@vendasta/rx-utils/work-state';
import { SaveResult } from './email-template-saver';

export const TEMPLATE_EXPORTER_KEY = 'campaigns.template_exporter';

export interface TemplateExporter {
  workState: WorkStates<SaveResult>;

  updateContentData(update: EmailContentData): void;

  updateHTML(html: string): void;
}
