import { Component, Inject } from '@angular/core';
import { ActivatedRoute, Data } from '@angular/router';
import { combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Title } from '@angular/platform-browser';
import { Context } from './page/shared/context';
import { PAGE_ROUTES } from '../routing-constants';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../shared/src/tokens';

export enum PageType {
  CREATE = 'create',
  EDIT = 'edit',
}

@Component({
  templateUrl: './campaign-email-builder.component.html',
  standalone: false,
})
export class CampaignEmailBuilderComponent {
  backPageUrl$: Observable<string>;
  context$: Observable<Context>;
  campaignId$: Observable<string>;
  campaignStepId$: Observable<string>;
  templateId$: Observable<string>;
  locale$: Observable<string>;
  ownerId$: Observable<string>;

  constructor(
    private readonly titleService: Title,
    private readonly route: ActivatedRoute,
    @Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE,
  ) {
    this.campaignId$ = this.route.queryParams.pipe(map((p) => p['campaignId']));
    this.campaignStepId$ = this.route.queryParams.pipe(map((p) => p['campaignStepId']));
    this.templateId$ = this.route.params.pipe(map((p) => p['templateId']));
    this.locale$ = this.route.queryParams.pipe(map((p) => p['locale']));
    this.ownerId$ = config.sender$.pipe(map((sender) => sender.id));

    this.context$ = combineLatest([this.route.data, this.ownerId$, this.templateId$]).pipe(
      map(([data, ownerId, templateId]: [Data, string, string]) => {
        switch (data['pageType']) {
          case PageType.CREATE:
            return Context.forNewTemplate(ownerId);
          case PageType.EDIT:
            return Context.forExistingTemplate(ownerId, templateId);
          default:
            throw new Error(`Unexpected pageType: ${data['pageType']}`);
        }
      }),
    );
    this.backPageUrl$ = combineLatest([this.campaignId$, this.config.basePath$]).pipe(
      map(([campaignId, basePath]) => `${basePath}/${PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.DETAILS(campaignId)}`),
    );
  }
}
