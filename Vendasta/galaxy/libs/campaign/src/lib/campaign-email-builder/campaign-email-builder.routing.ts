import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { HasUnsavedChangesGuard } from '@galaxy/email-ui/email-builder';
import { CampaignEmailBuilderComponent, PageType } from './campaign-email-builder.component';

@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: '',
        component: CampaignEmailBuilderComponent,
      },
      {
        path: 'new',
        pathMatch: 'full',
        component: CampaignEmailBuilderComponent,
        data: {
          pageType: PageType.CREATE,
        },
        canDeactivate: [HasUnsavedChangesGuard],
      },
      {
        path: ':templateId',
        pathMatch: 'full',
        redirectTo: ':templateId/edit',
      },
      {
        path: ':templateId/edit',
        pathMatch: 'full',
        component: CampaignEmailBuilderComponent,
        data: {
          pageType: PageType.EDIT,
        },
        canDeactivate: [HasUnsavedChangesGuard],
      },
    ]),
  ],
  providers: [],
  exports: [RouterModule],
})
export class CampaignEmailBuilderRouting {}
