import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { SmsModule } from '@galaxy/sms';
import { Router } from '@angular/router';
import { CampaignConfig } from '@galaxy/campaign/dependencies';
import { CONFIG_TOKEN } from '../../../shared/src/tokens';

const alertDismissedKey = 'smsConfigurationAlertDismissed';

@Component({
  selector: 'campaign-sms-configuration-alert',
  imports: [CommonModule, SmsModule],
  templateUrl: './sms-configuration-alert.component.html',
  styleUrls: [],
})
export class SmsConfigurationAlertComponent implements OnInit {
  showAlert$: Observable<boolean> = new Observable<boolean>();
  alertDismissed$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
    localStorage.getItem(alertDismissedKey) === 'true',
  );

  constructor(
    private router: Router,
    @Inject(CONFIG_TOKEN) private readonly campaignConfig: CampaignConfig,
    @Inject('PARTNER_ID') readonly partnerId$: Observable<string>,
  ) {}

  ngOnInit(): void {
    this.showAlert$ = combineLatest([
      this.campaignConfig.location$,
      this.campaignConfig.smsSettingsPath$ || of(null),
      this.alertDismissed$$,
    ]).pipe(
      map(([location, smsSettingsPath, alertDismissed]) => {
        if (alertDismissed) {
          return false;
        }
        if (!smsSettingsPath) {
          return false;
        }
        return location === 'US';
      }),
    );
  }

  navigateToSmsConfiguration() {
    if (this.campaignConfig.smsSettingsPath$) {
      this.campaignConfig.smsSettingsPath$.pipe(take(1)).subscribe((smsSettingsPath) => {
        this.router.navigateByUrl(smsSettingsPath);
      });
    }
  }

  dismissAlert() {
    this.alertDismissed$$.next(true);
    localStorage.setItem('smsConfigurationAlertDismissed', 'true');
  }
}
