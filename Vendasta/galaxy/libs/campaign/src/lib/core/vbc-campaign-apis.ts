import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { CampaignSDKService } from '@vendasta/sales';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { RecipientCampaign } from './interfaces';

export const VBC_CAMPAIGN_SERVICE_TOKEN = 'CAMPAIGN_SERVICE';

export interface ListCampaignsResponse {
  recipient_campaigns: RecipientCampaign[];
}

export interface VbcCampaignService {
  listCampaigns(businessId: string): Observable<ListCampaignsResponse>;
}

@Injectable()
export class VbcCampaignApis implements VbcCampaignService {
  constructor(
    private readonly http: HttpClient,
    private readonly campaignService: CampaignSDKService,
    private readonly environmentService: EnvironmentService,
  ) {}

  listCampaigns(businessId: string): Observable<ListCampaignsResponse> {
    const url = `${this.getVbcDomain()}/api/v1/business/campaign/list/`;
    const body = {
      accountGroupId: businessId,
    };
    return this.http
      .post(url, body, { withCredentials: true })
      .pipe(map((response) => response['data'] as ListCampaignsResponse));
  }

  private getVbcDomain(): string {
    let env = 'demo';
    if (this.environmentService.getEnvironment() === Environment.PROD) {
      env = 'prod';
    }
    return `https://vbc-${env}.appspot.com`;
  }
}
