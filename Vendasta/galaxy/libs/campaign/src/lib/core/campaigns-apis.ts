import { Injectable } from '@angular/core';
import { CampaignService as CampaignApiService } from '@vendasta/campaigns';
import { GetterCampaignData } from '@vendasta/campaigns/lib/_internal/objects/api';
import { Observable } from 'rxjs';

@Injectable()
export class CampaignService {
  constructor(private readonly campaignsService: CampaignApiService) {}

  getMultiCampaigns(campaignIDs: string[]): Observable<GetterCampaignData[]> {
    return this.campaignsService.getMulti(campaignIDs);
  }
}
