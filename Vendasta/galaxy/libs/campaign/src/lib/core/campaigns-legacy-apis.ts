import { Observable } from 'rxjs';
import { SetStatusResponse } from './interfaces';
import { LegacyAppengineService } from '@vendasta/campaigns';
import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';

@Injectable()
export class CampaignsLegacyService {
  constructor(private readonly campaignsLegacyService: LegacyAppengineService) {}

  setStatus(recipientCampaignId: string, active: boolean): Observable<SetStatusResponse> {
    const statusChange = active ? 1 : 2;
    return this.campaignsLegacyService.updateRecipientCampaignStatus(recipientCampaignId, statusChange).pipe(
      map(() => ({
        recipientCampaignId: recipientCampaignId,
        status: active ? 'active' : 'stopped',
      })),
    );
  }
}
