import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AddNewTagDialogComponent } from './add-new-tag-dialog.component';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GalaxyInputModule } from '@vendasta/galaxy/input';

@NgModule({
  declarations: [AddNewTagDialogComponent],
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatDialogModule,
    FormsModule,
    ReactiveFormsModule,
    GalaxyInputModule,
  ],
})
export class TagsModule {}
