import { Component, Inject } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { CampaignTag, CampaignTaggingService } from '@vendasta/campaigns';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { firstValueFrom } from 'rxjs';
import { SenderTagsService } from './sender-tags.service';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../shared/src/tokens';

const defaultColour = '#000000';

@Component({
  templateUrl: 'add-new-tag-dialog.component.html',
  styleUrls: ['./add-new-tag-dialog.component.scss'],
  standalone: false,
})
export class AddNewTagDialogComponent {
  formControl: UntypedFormControl = new UntypedFormControl();

  constructor(
    public readonly dialogRef: MatDialogRef<AddNewTagDialogComponent>,
    private readonly campaignTaggingService: CampaignTaggingService,
    private readonly senderTagsService: SenderTagsService,
    private readonly snackbarService: SnackbarService,
    @Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE,
  ) {}

  async save(newTagName: string): Promise<void> {
    const sender = await firstValueFrom(this.config.sender$);
    return firstValueFrom(this.campaignTaggingService.create(sender.type, sender.id, newTagName, defaultColour))
      .then((newTag: CampaignTag) => {
        this.snackbarService.openSuccessSnack('CAMPAIGN_DETAILS.ACTIONS.MANAGE_TAGS.TAG_CREATE_SUCCESS');
        this.senderTagsService.addTag({ tagId: newTag.tagId, tagName: newTag.text });
        this.dialogRef.close(newTag);
      })
      .catch((err) => {
        console.log(err);
        this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.ACTIONS.MANAGE_TAGS.TAG_CREATE_FAILURE');
      });
  }

  cancel(): void {
    this.dialogRef.close(null);
  }
}
