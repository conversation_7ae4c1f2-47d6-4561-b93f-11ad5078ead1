import { Inject, Injectable } from '@angular/core';
import { CampaignTaggingService, SenderInterface, SenderType } from '@vendasta/campaigns';
import { ListTagsForSenderResponse } from '@vendasta/campaigns/lib/_internal/objects/api';
import { Observable, Subject, combineLatest } from 'rxjs';
import { map, scan, shareReplay, startWith, switchMap } from 'rxjs/operators';
import { SenderTag } from '../manage-tags/tag-toggle/tag-toggle.component';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../shared/src/tokens';

// Senders will eventually have more than 100. We'll have to handle that.
const MAX_SENDER_TAGS = 100;

interface SenderTagsSource {
  listTagsForSender(
    senderType: SenderType,
    senderId: string,
    cursor: string,
    pageSize: number, //
  ): Observable<ListTagsForSenderResponse>;
}

@Injectable()
export class SenderTagsService {
  private readonly senderTags$: Observable<SenderTag[]>;
  private readonly userAddedTagEvents$$ = new Subject<SenderTag>();
  private readonly userAddedTags$ = this.userAddedTagEvents$$.pipe(
    scan((allTags, newTag) => {
      return allTags.concat(newTag);
    }, []),
    startWith([]),
  );
  private readonly deleteTagEvents$$ = new Subject<SenderTag>();
  private readonly deletedTags$ = this.deleteTagEvents$$.pipe(
    scan((allTags, removeTag) => {
      return allTags.concat(removeTag);
    }, []),
    startWith([]),
  );

  public static getObservable$(svc: SenderTagsService): Observable<SenderTag[]> {
    return svc.senderTags$;
  }

  constructor(
    @Inject(CampaignTaggingService) private readonly campaignTaggingService: SenderTagsSource,
    @Inject(CONFIG_TOKEN) config: CONFIG_TYPE,
  ) {
    const loadedTags$ = config.sender$.pipe(switchMap((s) => this.loadSenderTags(s)));
    this.senderTags$ = combineLatest([loadedTags$, this.userAddedTags$, this.deletedTags$]).pipe(
      map(([loaded, userAdded, deleted]) => {
        const allTags = loaded.concat(userAdded);
        return allTags.filter(function (el) {
          return deleted.findIndex((d) => d.tagId === el.tagId) < 0;
        });
      }),
      shareReplay({ bufferSize: 1, refCount: false }),
    );
  }

  addTag(newTag: SenderTag): void {
    this.userAddedTagEvents$$.next(newTag);
  }

  deleteTag(removeTag: SenderTag): void {
    this.deleteTagEvents$$.next(removeTag);
  }

  private loadSenderTags(sender: SenderInterface): Observable<SenderTag[]> {
    return this.campaignTaggingService
      .listTagsForSender(
        sender.type!,
        sender.id!,
        '',
        MAX_SENDER_TAGS, //
      )
      .pipe(
        map((st) =>
          st.campaignTags.map((t) => ({
            tagId: t.tagId,
            tagName: t.text,
            isBackCompatTag: t.tagId.startsWith('FOCUS-'),
          })),
        ),
      );
  }
}
