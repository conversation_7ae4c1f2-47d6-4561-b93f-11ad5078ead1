<mat-card appearance="outlined" class="mat-card--no-content-padding">
  <mat-card-header>
    <mat-card-title>
      {{ 'CAMPAIGNS.TITLE' | translate }}
    </mat-card-title>
  </mat-card-header>
  <uikit-async-ui [data]="recipientCampaigns$ | async" [loading]="loading$ | async" [error]="error$ | async">
    <ng-container *ngIf="recipientCampaigns$ | async as recipientCampaigns">
      <campaign-card-list
        *ngIf="recipientCampaigns.length > 0; else empty"
        [recipientCampaigns]="recipientCampaigns"
        (statusChanged)="handleStatusChange($event)"
        [campaignDetailsUrlPrefix]="campaignDetailsUrlPrefix"
      ></campaign-card-list>
    </ng-container>
  </uikit-async-ui>
  <ng-container *ngIf="hasMany$ | async">
    <mat-card-actions align="end">
      <button color="primary" mat-button *ngIf="(showingMore$ | async) === false" (click)="showMore()">
        {{ 'COMMON.ACTION_LABELS.VIEW_MORE' | translate }}
      </button>
      <button color="primary" mat-button *ngIf="showingMore$ | async" (click)="showLess()">
        {{ 'COMMON.ACTION_LABELS.VIEW_LESS' | translate }}
      </button>
    </mat-card-actions>
  </ng-container>
</mat-card>

<ng-template #empty>
  <glxy-empty-state [size]="'small'">
    <glxy-empty-state-hero>
      <mat-icon>mail_outline</mat-icon>
    </glxy-empty-state-hero>
    <p>
      {{ 'CAMPAIGNS.NO_CAMPAIGNS' | translate }}
    </p>
  </glxy-empty-state>
</ng-template>
