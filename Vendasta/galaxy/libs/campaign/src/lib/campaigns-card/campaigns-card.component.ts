import { Component, Input } from '@angular/core';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { UiRecipientCampaign } from '../core/interfaces';
import { StatusChangeEvent } from './campaigns-card.ui-service';
import { map } from 'rxjs/operators';
import { CampaignsCardUiService } from './campaigns-card.ui-service';

const COLLAPSED_SIZE = 3;

@Component({
  selector: 'campaign-card',
  templateUrl: './campaigns-card.component.html',
  styleUrls: ['./campaigns-card.component.scss'],
  standalone: false,
})
export class CampaignsCardComponent {
  readonly loading$: Observable<boolean>;
  readonly error$: Observable<boolean>;
  readonly recipientCampaigns$: Observable<UiRecipientCampaign[]>;
  _businessId: string;
  readonly hasMany$: Observable<boolean>;
  private readonly showMore$$ = new BehaviorSubject(false);
  readonly showingMore$ = this.showMore$$.asObservable();

  @Input() partnerId: string;
  @Input() marketId: string;
  @Input() campaignDetailsUrlPrefix: string;

  constructor(private readonly service: CampaignsCardUiService) {
    this.loading$ = service.isLoading$;
    this.error$ = service.isFailed$;

    this.recipientCampaigns$ = combineLatest([service.recipientCampaigns$, this.showMore$$]).pipe(
      map(([allCampaigns, showMore]) => {
        if (showMore) {
          return allCampaigns;
        }
        return allCampaigns.slice(0, COLLAPSED_SIZE);
      }),
    );
    this.hasMany$ = service.recipientCampaigns.workResults$.pipe(map((c) => c.length > COLLAPSED_SIZE));
  }

  @Input()
  set businessId(businessId: string) {
    this._businessId = businessId;
    this.service.initializeForBusiness(businessId);
  }

  get businessId(): string {
    return this._businessId;
  }

  showMore(): void {
    this.showMore$$.next(true);
  }

  showLess(): void {
    this.showMore$$.next(false);
  }

  handleStatusChange($event: StatusChangeEvent): void {
    this.service.setStatus($event);
  }
}
