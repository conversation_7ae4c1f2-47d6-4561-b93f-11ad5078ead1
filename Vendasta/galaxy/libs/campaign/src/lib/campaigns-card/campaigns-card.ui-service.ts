import { Inject, Injectable } from '@angular/core';
import { Observable, ReplaySubject } from 'rxjs';
import {
  getStates,
  isFailed$,
  ObservableWorkStateMap,
  startWorkWith,
  SwitchingWorkState,
  WorkStateMap,
  WorkStates,
} from '@vendasta/rx-utils/work-state';
import { VBC_CAMPAIGN_SERVICE_TOKEN, VbcCampaignService } from '../core/vbc-campaign-apis';
import { map } from 'rxjs/operators';
import { CampaignStatus, RecipientCampaign, UiRecipientCampaign } from '../core/interfaces'; //StatusChangeEvent
import { RecipientCampaignsUiModel } from './recipient-campaigns-ui.model';
import { CampaignsLegacyService } from '../core/campaigns-legacy-apis';

export interface StatusChangeEvent {
  recipientCampaignId: string;
  active: boolean;
}

function AddStatusStateToCampaigns(
  workResults$: Observable<RecipientCampaign[]>,
  statusStateMap: ObservableWorkStateMap<string, CampaignStatus>,
): Observable<UiRecipientCampaign[]> {
  return workResults$.pipe(map((campaigns) => campaigns.map((c) => AddStatusStateToCampaign(c, statusStateMap))));
}

function AddStatusStateToCampaign(
  campaign: RecipientCampaign,
  statusStateMap: WorkStateMap<string, CampaignStatus>,
): UiRecipientCampaign {
  const state = getStates(statusStateMap, campaign.recipient_campaign_id);
  return <UiRecipientCampaign>{
    recipientCampaignId: campaign.recipient_campaign_id,
    campaignId: campaign.campaign_id,
    statusState: startWorkWith(state, campaign.status),
  };
}

@Injectable()
export class CampaignsCardUiService {
  private readonly model: RecipientCampaignsUiModel;
  readonly recipientCampaigns$: Observable<UiRecipientCampaign[]>;
  private readonly fetchState = new SwitchingWorkState<string, RecipientCampaign[]>((i: string) =>
    this.fetchRecipientCampaigns$(i),
  );
  private readonly statusStateMap = new ObservableWorkStateMap<string, CampaignStatus>();
  readonly isLoading$: Observable<boolean> = this.fetchState.isLoading$;
  readonly isFailed$: Observable<boolean> = isFailed$(this.fetchState);
  private readonly businessId$$ = new ReplaySubject<string>(1);

  constructor(
    @Inject(VBC_CAMPAIGN_SERVICE_TOKEN) private readonly vbcService: VbcCampaignService,
    private readonly campaignsLegacyService: CampaignsLegacyService,
  ) {
    this.businessId$$.subscribe((businessId) => {
      this.fetchState.startWork(businessId);
    });

    this.model = new RecipientCampaignsUiModel(this.fetchState.workResults$);
    this.recipientCampaigns$ = AddStatusStateToCampaigns(this.model.asObservable(), this.statusStateMap);
  }

  get recipientCampaigns(): WorkStates<RecipientCampaign[]> {
    return this.fetchState;
  }

  initializeForBusiness(businessId: string): void {
    this.businessId$$.next(businessId);
  }

  setStatus(changes: StatusChangeEvent): void {
    this.statusStateMap.startWork(
      changes.recipientCampaignId,
      this.campaignsLegacyService
        .setStatus(changes.recipientCampaignId, changes.active)
        .pipe(map((response) => response.status)),
    );
  }

  private fetchRecipientCampaigns$(businessId: string): Observable<RecipientCampaign[]> {
    return this.vbcService.listCampaigns(businessId).pipe(map((r) => r.recipient_campaigns));
  }
}
