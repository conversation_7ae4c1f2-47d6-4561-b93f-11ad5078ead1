import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { GetterCampaignData } from '@vendasta/campaigns/lib/_internal/objects/api';
import { ObservableWorkState, isFailed$ } from '@vendasta/rx-utils/work-state';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CampaignService } from '../../core/campaigns-apis';
import { UiCampaign, UiRecipientCampaign } from '../../core/interfaces';
import { StatusChangeEvent } from '../campaigns-card.ui-service';

@Component({
  selector: 'campaign-card-list',
  templateUrl: 'campaigns-card-list.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class CampaignsCardListComponent implements OnChanges {
  @Input() recipientCampaigns: UiRecipientCampaign[];
  readonly campaigns$: Observable<UiCampaign[]>;
  readonly error$: Observable<boolean>;
  readonly loading$: Observable<boolean>;
  @Input() campaignDetailsUrlPrefix: string;
  @Output()
  readonly statusChanged = new EventEmitter<StatusChangeEvent>();
  private readonly state = new ObservableWorkState<UiCampaign[]>();

  constructor(private readonly campaignService: CampaignService) {
    this.campaigns$ = this.state.workResults$;
    this.error$ = isFailed$(this.state);
    this.loading$ = this.state.isLoading$;
  }

  ngOnChanges(): void {
    if (this.recipientCampaigns) {
      const campaignIds = [...new Set(this.recipientCampaigns.map((c) => c.campaignId))];
      this.state.startWork(
        this.campaignService
          .getMultiCampaigns(campaignIds)
          .pipe(
            map((response: GetterCampaignData[]) => UiCampaignsFromGetMultiResponse(this.recipientCampaigns, response)),
          ),
      );
    }
  }

  handleStatusChanged($event: StatusChangeEvent): void {
    this.statusChanged.emit($event);
  }
}

function UiCampaignsFromGetMultiResponse(
  recipientCampaigns: UiRecipientCampaign[],
  campaigns: GetterCampaignData[],
): UiCampaign[] {
  return recipientCampaigns.map(
    (c) =>
      <UiCampaign>{
        name: campaigns.find((cam) => cam?.campaignId === c.campaignId)?.name,
        statusState: c.statusState,
        recipientCampaignId: c.recipientCampaignId,
        campaignId: c.campaignId,
      },
  );
}
