<ng-container [ngSwitch]="status">
  <va-badge
    *ngSwitchCase="'active'"
    small
    color="positive"
    matTooltip="{{ 'CAMPAIGNS.CAMPAIGN_ACTIVE_TOOLTIP' | translate }}"
  >
    {{ status | titlecase }}
  </va-badge>
  <va-badge
    *ngSwitchCase="'stopped'"
    small
    color="gray"
    matTooltip="{{ 'CAMPAIGNS.CAMPAIGN_PAUSED_TOOLTIP' | translate }}"
  >
    {{ status | titlecase }}
  </va-badge>
  <va-badge
    *ngSwitchCase="'completed'"
    small
    color="negative"
    matTooltip="{{ 'CAMPAIGNS.CAMPAIGN_ENDED_TOOLTIP' | translate }}"
  >
    {{ status | titlecase }}
  </va-badge>
  <va-badge
    *ngSwitchCase="'active-pending'"
    small
    color="positive"
    matTooltip="{{ 'CAMPAIGNS.CAMPAIGN_CREATING_TOOLTIP' | translate }}"
  >
    Creating
  </va-badge>
</ng-container>
