<mat-list>
  <mat-list-item *ngFor="let campaign of campaigns">
    <div matListItemLine class="campaign-container">
      <div class="campaign-info">
        <div class="campaign-name">
          <button
            [routerLink]="campaignDetailsUrlPrefix + campaign.campaignId"
            data-action="click-navigate-campaign"
            mat-button
            color="primary"
            matTooltip="{{ campaign.name }}"
          >
            {{ campaign.name }}
          </button>
        </div>
        <campaign-status-icon [status]="campaign.statusState.workResults$ | async"></campaign-status-icon>
      </div>
    </div>
    <div matListItemMeta>
      <campaign-status-toggle
        [statusState]="campaign.statusState"
        (paused)="pause(campaign.recipientCampaignId)"
        (resumed)="resume(campaign.recipientCampaignId)"
      ></campaign-status-toggle>
    </div>
  </mat-list-item>
</mat-list>
