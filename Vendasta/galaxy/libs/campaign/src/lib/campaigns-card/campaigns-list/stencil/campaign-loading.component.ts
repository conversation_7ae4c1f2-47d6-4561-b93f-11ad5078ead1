import { Component, Input } from '@angular/core';
import { UiRecipientCampaign } from '../../../core/interfaces';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'campaign-loading',
  template: `
    <mat-list>
      <mat-list-item *ngFor="let campaign of recipientCampaigns">
        <div mat-list-text class="campaign-name-stencil stencil-shimmer"></div>
        <campaign-status-icon [status]="campaign.statusState.workResults$ | async"></campaign-status-icon>
      </mat-list-item>
    </mat-list>
  `,
  styleUrls: ['./campaign-loading.component.scss'],
  standalone: false,
})
export class CampaignLoadingComponent {
  constructor(private readonly translate: TranslateService) {}

  @Input() recipientCampaigns: UiRecipientCampaign[];
  @Input() placeholderText: string = this.translate.instant('CAMPAIGNS.LOADING_PLACEHOLDER');
}
