import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { UiCampaign } from '../../../core/interfaces';
import { StatusChangeEvent } from '../../campaigns-card.ui-service';

@Component({
  selector: 'campaign-list',
  templateUrl: './campaigns-list.component.html',
  styleUrls: ['./campaigns-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class CampaignsListComponent {
  @Input() campaigns: UiCampaign[];

  @Output() statusChanged = new EventEmitter<StatusChangeEvent>();
  @Input() campaignDetailsUrlPrefix: string;

  pause(recipientCampaignId: string): void {
    this.statusChanged.emit(<StatusChangeEvent>{
      recipientCampaignId: recipientCampaignId,
      active: false,
    });
  }

  resume(recipientCampaignId: string): void {
    this.statusChanged.emit(<StatusChangeEvent>{
      recipientCampaignId: recipientCampaignId,
      active: true,
    });
  }
}
