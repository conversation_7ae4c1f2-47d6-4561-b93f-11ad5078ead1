<ng-container *ngIf="showStatusToggle$ | async">
  <button
    *ngIf="status$ | async as status"
    mat-icon-button
    [disabled]="status === 'completed' || status === 'active-pending'"
    [matMenuTriggerFor]="actions"
  >
    <mat-icon>more_vert</mat-icon>
  </button>
</ng-container>

<ng-container *ngIf="isFailed$ | async as error">
  <button mat-icon-button matTooltip="{{ 'CAMPAIGNS.FAILED_STATUS_UPDATE' | translate }}" [matMenuTriggerFor]="actions">
    <mat-icon color="warn">error</mat-icon>
  </button>
</ng-container>

<mat-menu #actions="matMenu">
  <ng-container [ngSwitch]="status$ | async">
    <ng-container *ngSwitchCase="'active'">
      <button data-action="toggled-campaign-status-pause" mat-menu-item (click)="pause()">
        {{ 'COMMON.ACTION_LABELS.PAUSE' | translate }}
      </button>
    </ng-container>
    <ng-container *ngSwitchCase="'stopped'">
      <button data-action="toggled-campaign-status-resume" mat-menu-item (click)="resume()">
        {{ 'COMMON.ACTION_LABELS.RESUME' | translate }}
      </button>
    </ng-container>
  </ng-container>
</mat-menu>
