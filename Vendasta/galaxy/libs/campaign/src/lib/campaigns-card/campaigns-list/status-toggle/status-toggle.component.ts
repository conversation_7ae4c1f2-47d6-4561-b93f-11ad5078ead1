import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CampaignStatus } from '../../../core/interfaces';
import { WorkStates, isFailed$ } from '@vendasta/rx-utils/work-state';
import { Observable, combineLatest, map, startWith } from 'rxjs';

@Component({
  selector: 'campaign-status-toggle',
  templateUrl: './status-toggle.component.html',
  styleUrls: ['./status-toggle.component.scss'],
  standalone: false,
})
export class StatusToggleComponent {
  status$: Observable<CampaignStatus>;
  isLoading$: Observable<boolean>;
  isFailed$: Observable<boolean>;
  showStatusToggle$: Observable<boolean>;

  @Input() set statusState(status: WorkStates<CampaignStatus>) {
    this.status$ = status.workResults$;
    this.isLoading$ = status.isLoading$;
    this.isFailed$ = isFailed$(status);
    this.showStatusToggle$ = combineLatest([this.isFailed$.pipe(startWith(false)), this.status$]).pipe(
      map(([isFailed, status]) => !isFailed && !!status),
    );
  }
  @Output() paused = new EventEmitter<null>();
  @Output() resumed = new EventEmitter<null>();

  pause(): void {
    this.paused.emit(null);
  }
  resume(): void {
    this.resumed.emit(null);
  }
}
