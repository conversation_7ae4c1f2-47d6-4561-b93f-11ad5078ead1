<uikit-async-ui [data]="campaigns$ | async" [loading]="loading$ | async" [error]="error$ | async">
  <ng-container *ngIf="campaigns$ | async as campaigns">
    <campaign-list
      [campaigns]="campaigns"
      (statusChanged)="handleStatusChanged($event)"
      [campaignDetailsUrlPrefix]="campaignDetailsUrlPrefix"
    ></campaign-list>
  </ng-container>
  <ng-container loading>
    <campaign-loading [recipientCampaigns]="recipientCampaigns"></campaign-loading>
  </ng-container>
</uikit-async-ui>
