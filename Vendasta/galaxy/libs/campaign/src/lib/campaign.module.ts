import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule, Routes } from '@angular/router';
import { LinkActivityComponent } from '@galaxy/email-ui/email-activity/src/link-activity/link-activity.component';
import { TranslateModule } from '@ngx-translate/core';
import { ActivationService } from '@vendasta/campaigns';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { TemplatesService } from '@vendasta/templates';
import { AsyncUiModule, VaBadgeModule, VaStencilsModule } from '@vendasta/uikit';
import { CampaignI18nModule } from './assets/i18n/campaign-i18n.module';
import { CampaignDashboardComponent } from './campaign-dashboard/campaign-dashboard.component';
import { CampaignDetailsPageComponent } from './campaign-details-page/campaign-details-page.component';
import { CampaignContactsDialogComponent } from './campaign-details-page/campaign-details/campaign-contacts-dialog/campaign-contacts-dialog.component';
import { CampaignListItemComponent } from './campaign-details-page/campaign-details/campaign-contacts-dialog/list-item';
import { CampaignStateService } from './campaign-details-page/campaign-details/campaign-state.service';
import { CampaignsService } from './campaign-details-page/campaign-details/campaigns.service';
import { UpsellSmsDialogComponent } from './campaign-details-page/campaign-details/upsell-sms-dialog/upsell-sms-dialog.component';
import { EmailTemplateSaver } from './campaign-email-builder/page/shared/email-template-saver';
import { SMSTemplateSaver } from './campaign-email-builder/page/shared/sms-template-saver';
import { CampaignHistoryComponent } from './campaign-history/campaign-history.component';
import { CampaignListComponent } from './campaign-list-page/campaign-list-page.component';
import { MyCampaignsTableService } from './campaign-list-page/my-campaigns-table.service';
import { CampaignsCardComponent } from './campaigns-card/campaigns-card.component';
import { CampaignsCardUiService } from './campaigns-card/campaigns-card.ui-service';
import { CampaignsCardListComponent } from './campaigns-card/campaigns-list/campaigns-card-list.component';
import { CampaignsListComponent } from './campaigns-card/campaigns-list/list/campaigns-list.component';
import { StatusIconComponent } from './campaigns-card/campaigns-list/status-icon/status-icon.component';
import { StatusToggleComponent } from './campaigns-card/campaigns-list/status-toggle/status-toggle.component';
import { CampaignLoadingComponent } from './campaigns-card/campaigns-list/stencil/campaign-loading.component';
import { CampaignService } from './core/campaigns-apis';
import { CampaignsLegacyService } from './core/campaigns-legacy-apis';
import { VBC_CAMPAIGN_SERVICE_TOKEN, VbcCampaignApis } from './core/vbc-campaign-apis';
import { QuotaBannerComponent } from './quota-banner/quota-banner.component';
import { PAGE_ROUTES } from './routing-constants';
import { SmsConfigurationAlertComponent } from './sms-configuration-alert/sms-configuration-alert.component';
import { CampaignPreviewSendComponent } from '../../shared/src/campaign-preview-send/campaign-preview-send.component';
import { SenderTagsService } from './tags/sender-tags.service';

const CAMPAIGN_ROUTES: Routes = [
  {
    path: '',
    redirectTo: `${PAGE_ROUTES.ROOT.SUBROUTES.LIST}`,
    pathMatch: 'full',
  },
  {
    path: PAGE_ROUTES.ROOT.SUBROUTES.LIST,
    component: CampaignDashboardComponent,
  },

  {
    path: PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.DETAILS(':campaignId'),
    component: CampaignDetailsPageComponent,
  },
  {
    path: PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.HISTORY(':campaignId'),
    component: CampaignHistoryComponent,
  },
  {
    path: PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.LINKACTIVITY(':attributeKey', ':attributeValue'),
    component: LinkActivityComponent,
  },
  {
    path: PAGE_ROUTES.ROOT.TEMPLATE,
    loadChildren: () =>
      import('./campaign-email-builder/campaign-email-builder.module').then((m) => m.CampaignEmailBuilderModule),
  },
  {
    path: PAGE_ROUTES.ROOT.SMS.TEMPLATE,
    loadChildren: () => import('./campaign-sms-builder/sms-builder.module').then((m) => m.CampaignSMSBuilderModule),
  },
  {
    path: PAGE_ROUTES.ROOT.SMS.EDIT(':templateId'),
    loadChildren: () => import('./campaign-sms-builder/sms-builder.module').then((m) => m.CampaignSMSBuilderModule),
  },
  {
    path: 'preview-send',
    component: CampaignPreviewSendComponent,
  },
];

@NgModule({
  declarations: [
    CampaignsCardComponent,
    CampaignsListComponent,
    StatusIconComponent,
    CampaignsCardListComponent,
    CampaignLoadingComponent,
    StatusToggleComponent,
    CampaignDashboardComponent,
    QuotaBannerComponent,
    UpsellSmsDialogComponent,
  ],
  providers: [
    CampaignsLegacyService,
    CampaignsCardUiService,
    { provide: VBC_CAMPAIGN_SERVICE_TOKEN, useClass: VbcCampaignApis },
    CampaignService,
    SenderTagsService,
    MyCampaignsTableService,
    EmailTemplateSaver,
    CampaignStateService,
    CampaignsService,
    SMSTemplateSaver,
    ActivationService,
    TemplatesService,
  ],
  imports: [
    MatProgressBarModule,
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatListModule,
    MatDividerModule,
    MatButtonModule,
    MatTooltipModule,
    MatMenuModule,
    VaStencilsModule,
    GalaxyEmptyStateModule,
    TranslateModule,
    CampaignI18nModule,
    AsyncUiModule,
    VaBadgeModule,
    GalaxyPipesModule,
    RouterModule,
    RouterModule.forChild(CAMPAIGN_ROUTES),
    GalaxyPageModule,
    CampaignListComponent,
    CampaignDetailsPageComponent,
    LinkActivityComponent,
    CampaignListItemComponent,
    MatProgressBarModule,
    GalaxyAlertModule,
    CampaignContactsDialogComponent,
    MatDialogModule,
    SmsConfigurationAlertComponent,
  ],
  exports: [CampaignsCardComponent, QuotaBannerComponent, CampaignContactsDialogComponent],
})
export class CampaignModule {}
