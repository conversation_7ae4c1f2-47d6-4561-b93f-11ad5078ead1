<glxy-page *ngIf="pageData$ | async as pageData">
  <glxy-page-toolbar>
    <glxy-page-title>
      <span>{{ 'CAMPAIGNS.TITLE' | translate }}</span>
    </glxy-page-title>
    <glxy-page-actions>
      <button mat-flat-button [routerLink]="templatesUrl">
        {{ 'NAVIGATION.TABS.TEMPLATES_BREADCRUMB' | translate }}
      </button>
      <button mat-flat-button color="primary" (click)="openCreatecampaignDialog()">
        {{ 'CAMPAIGNS.CREATE_CAMPAIGN' | translate }}
      </button>
    </glxy-page-actions>
  </glxy-page-toolbar>

  <glxy-page-wrapper [maxWidth]="2000">
    <ng-container>
      <campaign-quota-banner></campaign-quota-banner>
      <campaign-list-page></campaign-list-page>
    </ng-container>
  </glxy-page-wrapper>
</glxy-page>
