import { Component, inject, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { CampaignQuotaCategory, Period, QuotaService, SenderInterface } from '@vendasta/campaigns';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { EMPTY, Observable, of, Subscription } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';
import { CampaignGenerateDialogComponent } from '../campaign-generate-dialog/campaign-generate-dialog.component';
import { MyCampaignsTableService } from '../campaign-list-page/my-campaigns-table.service';
import { CampaignListPageData } from '../core/interfaces';
import { QUOTA_BANNER_CONFIG, QUOTA_CONFIG } from '../dependencies';
import { PAGE_ROUTES } from '../routing-constants';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../shared/src/tokens';

interface EmailQuota {
  count: number;
  limit: number;
}
@Component({
  selector: 'campaign-dashboard',
  templateUrl: './campaign-dashboard.component.html',
  styleUrls: ['./campaign-dashboard.component.scss'],
  standalone: false,
})
export class CampaignDashboardComponent implements OnInit, OnDestroy {
  public pageData$: Observable<CampaignListPageData> = of({
    key: 'none',
    title: 'Campaigns',
  } as CampaignListPageData);

  private readonly activatedRoute = inject(ActivatedRoute);
  protected readonly templatesUrl = this.router.createUrlTree(['../../emails'], {
    relativeTo: this.activatedRoute,
  });

  private senderId = '';
  private subscriptions: Subscription[] = [];
  quota$: Observable<EmailQuota>;

  constructor(
    private readonly dialog: MatDialog,
    private readonly myCampaignsTableService: MyCampaignsTableService,
    private readonly router: Router,
    private readonly snackbarService: SnackbarService,
    @Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE,
    @Inject(QUOTA_BANNER_CONFIG) public readonly quotaConfig: QUOTA_CONFIG,
    private posthogService: ProductAnalyticsService,
    private quotaService: QuotaService,
  ) {
    this.quota$ = quotaConfig.sender$.pipe(
      switchMap((sndr) => {
        return this.quotaService.getCampaignQuota(
          sndr,
          CampaignQuotaCategory.CAMPAIGN_QUOTA_TYPE_EMAIL,
          Period.PERIOD_MONTH,
        );
      }),
      map((resp) => {
        return { count: resp.count, limit: resp.limit };
      }),
    );
  }

  ngOnInit(): void {
    this.subscriptions.push(
      this.myCampaignsTableService
        .getSender()
        .pipe(
          take(1),
          map((sender: SenderInterface) => {
            this.senderId = sender.id || '';
          }),
        )
        .subscribe(),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  navigateToCampaignDetails(campaignId: string): void {
    this.subscriptions.push(
      this.config.basePath$
        .pipe(map((v) => this.router.navigate([`/${v}/${PAGE_ROUTES.ROOT.CAMPAIGN_SUBROUTES.DETAILS(campaignId)}`])))
        .subscribe(),
    );
  }

  openCreatecampaignDialog(): void {
    this.posthogService.trackEvent('user-clicked-create-campaign', 'create-campaign-workflow', 'click');
    const dialogRef = this.dialog.open(CampaignGenerateDialogComponent);
    dialogRef
      .afterClosed()
      .pipe(
        switchMap((name) => {
          if (!name || name.trim() === '') {
            return EMPTY;
          }
          return this.myCampaignsTableService.createNewCampaign(name.trim());
        }),
      )
      .subscribe({
        next: (campaignId) => {
          if (campaignId) {
            this.navigateToCampaignDetails(campaignId);
          }
        },
        error: (err) => {
          this.snackbarService.openErrorSnack(err.error.message);
        },
      });
  }
}
