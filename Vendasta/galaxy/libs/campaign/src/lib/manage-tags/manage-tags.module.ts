import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyCheckboxModule } from '@vendasta/galaxy/checkbox';
import { GalaxyWrapModule } from '@vendasta/galaxy/galaxy-wrap';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxyPopoverModule } from '@vendasta/galaxy/popover';
import { MyCampaignsTableService } from '../campaign-list-page/my-campaigns-table.service';
import { SenderTagsService } from '../tags/sender-tags.service';
import { TagsModule } from '../tags/tags.module';
import { ManageTagsComponent, SENDER_TAGS_TOKEN, TAG_CHANGES_LISTENER_TOKEN } from './manage-tags.component';
import { TagToggleComponent } from './tag-toggle/tag-toggle.component';

@NgModule({
  imports: [
    GalaxyCheckboxModule,
    GalaxyWrapModule,
    GalaxyPipesModule,
    GalaxyLoadingSpinnerModule,
    MatDialogModule,
    GalaxyInputModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule,
    MatButtonModule,
    MatCheckboxModule,
    // TODO: Hook up lexicon
    // LexiconModule.forChild({
    //     componentName: WEBLATE_COMPONENT_NAME,
    //     baseTranslation: baseTranslation,
    // }),
    TagsModule,
    GalaxyPopoverModule,
    MatIconModule,
    TranslateModule,
  ],
  declarations: [
    ManageTagsComponent, //
    TagToggleComponent,
  ],
  providers: [
    {
      provide: TAG_CHANGES_LISTENER_TOKEN,
      useExisting: MyCampaignsTableService,
    },
    {
      provide: SENDER_TAGS_TOKEN,
      useFactory: SenderTagsService.getObservable$,
      deps: [SenderTagsService],
    },
  ],
})
export class ManageTagsModule {}
