<h2 mat-dialog-title>{{ 'MANAGE_TAGS.MANAGE_TAGS' | translate }}</h2>

<mat-dialog-content>
  <glxy-input
    placeholder="MANAGE_TAGS.TAG_NAME"
    label="MANAGE_TAGS.SEARCH_FOR_TAG"
    trailingIcon="search"
    [formControl]="searchBarControl"
  ></glxy-input>
  <ng-container *ngIf="tags$ | async as tags">
    <ng-container *ngFor="let tag of tags">
      <div class="tag-row">
        <campaign-tag-toggle
          [sender]="sender$ | async"
          [campaignId]="data.campaignId"
          [senderMode]="data.senderMode"
          [tag]="tag"
          (tagSelectionChanged)="handleTagUpdated($event)"
        ></campaign-tag-toggle>
        <mat-icon *ngIf="data.senderMode && !tag.isBackCompatTag" class="icon-action" (click)="deleteTag(tag)">
          delete
        </mat-icon>
      </div>
    </ng-container>
  </ng-container>
</mat-dialog-content>
<mat-dialog-actions class="action-buttons">
  <button mat-stroked-button (click)="openNewTagDialog()">
    {{ 'MANAGE_TAGS.NEW_TAG' | translate }}
  </button>
  <button mat-stroked-button mat-dialog-close>
    {{ 'MANAGE_TAGS.CLOSE' | translate }}
  </button>
</mat-dialog-actions>
