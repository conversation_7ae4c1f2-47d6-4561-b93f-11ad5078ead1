import { Component, Inject, On<PERSON><PERSON>roy, OnInit, Optional } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { CampaignTag, CampaignTagInterface, CampaignTaggingService, SenderInterface } from '@vendasta/campaigns';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import Fuse from 'fuse.js';
import { Observable, Subject, Subscription, combineLatest, firstValueFrom, of } from 'rxjs';
import { debounceTime, map, scan, startWith, take, tap } from 'rxjs/operators';
import { AddNewTagDialogComponent } from '../tags/add-new-tag-dialog.component';
import { SenderTagsService } from '../tags/sender-tags.service';
import { SenderTag, TagData } from './tag-toggle/tag-toggle.component';
import { CONFIG_TOKEN, CONFIG_TYPE } from '../../../shared/src/tokens';

export interface DialogData {
  campaignId: string;
  senderMode: boolean;
}

export const TAG_CHANGES_LISTENER_TOKEN = 'com.vendasta.campaigns.actions.change_tags';

export interface TagChangesListener {
  handleTagsChanged(campaignId: string, allTags: TagData[]): void;
}

export const SENDER_TAGS_TOKEN = 'com.vendasta.campaigns.manage_tags.sender_tags';

export interface UserToggledTagsMap {
  [tagId: string]: boolean; // boolean value of "selected"
}

@Component({
  templateUrl: 'manage-tags.component.html',
  styleUrls: ['manage-tags.component.scss'],
  standalone: false,
})
export class ManageTagsComponent implements OnInit, OnDestroy {
  userModifiedTagEvents$$: Subject<TagData> = new Subject<TagData>();
  tags$: Observable<TagData[]> = new Observable<TagData[]>();
  searchBarControl: FormControl = new FormControl();
  subscriptions: Subscription[] = [];
  readonly sender$: Observable<SenderInterface>;

  public static openAndAddToURL(
    dialog: MatDialog,
    router: Router,
    activatedRoute: ActivatedRoute,
    campaignId: string,
  ): MatDialogRef<ManageTagsComponent> {
    const dialogRef = dialog.open(ManageTagsComponent, {
      data: {
        campaignId: campaignId,
      },
      width: '800px',
    });
    this.addToURL(router, activatedRoute, campaignId);
    dialogRef
      .afterClosed()
      .pipe(take(1))
      .subscribe(() => this.removeFromURL(router, activatedRoute));
    return dialogRef;
  }

  private static addToURL(router: Router, activatedRoute: ActivatedRoute, campaignId: string): void {
    router.navigate([], {
      relativeTo: activatedRoute,
      queryParams: {
        manageTagsForCampaignId: campaignId,
      },
      queryParamsHandling: 'merge',
      skipLocationChange: true,
    });
  }

  private static removeFromURL(router: Router, activatedRoute: ActivatedRoute): void {
    router.navigate([], {
      relativeTo: activatedRoute,
      queryParams: {
        manageTagsForCampaignId: null,
      },
      queryParamsHandling: 'merge',
      skipLocationChange: true,
    });
  }

  constructor(
    @Inject(MAT_DIALOG_DATA) public readonly data: DialogData,
    public dialogRef: MatDialogRef<ManageTagsComponent>,
    private readonly campaignTaggingService: CampaignTaggingService,
    private readonly dialog: MatDialog,
    private senderTagService: SenderTagsService,
    private snackbarService: SnackbarService,
    @Inject(SENDER_TAGS_TOKEN) private readonly senderTags$: Observable<SenderTag[]>,
    @Inject(CONFIG_TOKEN) private readonly config: CONFIG_TYPE,
    @Optional() @Inject(TAG_CHANGES_LISTENER_TOKEN) private readonly tagChangesListener?: TagChangesListener,
  ) {
    this.sender$ = config.sender$;
  }

  ngOnInit(): void {
    const allTagsFromServer$ = this.loadAllTagsFromServer();

    const userToggledTags$ = this.userModifiedTagEvents$$.pipe(
      // builds up a map of selected/deselected tags from each toggle event
      scan((acc: UserToggledTagsMap, value: TagData) => {
        acc[value.tagId] = value.selected;
        return acc;
      }, {}),
      startWith({}),
    );

    const allTagsToggledByUser$ = combineLatest([allTagsFromServer$, userToggledTags$]).pipe(
      map(([senderTags, toggled]) => applyUserSelectionToTags(senderTags, toggled)),
      tap((allTags) => this.tagChangesListener?.handleTagsChanged(this.data.campaignId, allTags)),
    );

    this.tags$ = combineLatest([
      this.searchBarControl.valueChanges.pipe(startWith(''), debounceTime(200)),
      allTagsToggledByUser$,
    ]).pipe(map(([searchTerm, allTags]) => this.filterTagsBySearchTerm(searchTerm, allTags)));
  }

  async deleteTag(tag: SenderTag): Promise<void> {
    const sender = await firstValueFrom(this.config.sender$);
    firstValueFrom(this.campaignTaggingService.deleteTag(sender.type, sender.id, tag.tagId))
      .catch(() => {
        this.snackbarService.openErrorSnack('MANAGE_TAGS.TAG_DELETE_FAILURE');
      })
      .then(() => {
        this.senderTagService.deleteTag(tag);
        this.snackbarService.openSuccessSnack('MANAGE_TAGS.TAG_DELETE_SUCCESS');
      });
  }

  loadAllTagsFromServer(): Observable<TagData[]> {
    return combineLatest([
      this.senderTags$,
      this.data.campaignId
        ? this.campaignTaggingService.listTagsForCampaign(this.data.campaignId, '', 10)
        : of({ campaignTags: [] }),
    ]).pipe(
      map(([senderTags, campaign]) => {
        if (campaign) {
          return this.applyCampaignSelectionToTags(senderTags, campaign?.campaignTags);
        }
        return this.applyCampaignSelectionToTags(senderTags, []);
      }),
    );
  }

  /**
   * Returns the set of tags available to the sender with a "selected" boolean
   * set to "true" for each tag that is also present on the campaign.
   */
  private applyCampaignSelectionToTags(tagsAvailableToSender: SenderTag[], tagsOnCampaign: CampaignTag[]): TagData[] {
    return tagsAvailableToSender.map((tag) => {
      const selected = this.checkIfCampaignUsesTag(tag, tagsOnCampaign);
      return {
        tagName: tag.tagName,
        selected: selected,
        tagId: tag.tagId,
        isBackCompatTag: tag.isBackCompatTag,
      };
    });
  }

  checkIfCampaignUsesTag(senderTag: CampaignTagInterface, campaignTags: CampaignTagInterface[]): boolean {
    return campaignTags.some((campaignTag) => campaignTag.tagId === senderTag.tagId);
  }

  filterTagsBySearchTerm(searchTerm: string, tags: TagData[]): TagData[] {
    if (!searchTerm) {
      return tags;
    }
    const fuse = new Fuse(tags, {
      keys: ['tagName'],
      threshold: 0.3,
      ignoreLocation: true,
    });
    return fuse.search(searchTerm).map((result) => result.item);
  }

  async openNewTagDialog(): Promise<void> {
    this.dialog.open(AddNewTagDialogComponent, {
      width: '300px',
    });
  }

  handleTagUpdated(tag: TagData): void {
    this.userModifiedTagEvents$$.next(tag);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}

/**
 * If a tag ID is present in "userToggledTags", then its "selected" value
 * will be used. Otherwise, the original "selected" value will be used.
 */
export function applyUserSelectionToTags(tags: TagData[], userToggledTags: UserToggledTagsMap): TagData[] {
  return tags.map((tag) => ({
    tagId: tag.tagId,
    selected: useIfPresentElse(userToggledTags[tag.tagId], tag.selected),
    tagName: tag.tagName,
    isBackCompatTag: tag.isBackCompatTag,
  }));
}

function useIfPresentElse(useIfPresent: boolean | undefined, elseUse: boolean): boolean {
  if (useIfPresent === undefined) {
    return elseUse;
  }
  return useIfPresent;
}
