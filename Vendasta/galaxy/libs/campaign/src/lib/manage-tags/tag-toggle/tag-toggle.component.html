<ng-container *ngIf="updateWork$ | glxyAsyncStatus | async as result">
  <div class="container">
    <EXP__glxy-wrap>
      <mat-checkbox
        [checked]="tag.selected"
        [disabled]="result.status === 'loading' || tag.isBackCompatTag || senderMode"
        (change)="changeTag(tag.tagId, $event)"
        (mouseover)="openPopover()"
        (mouseleave)="closePopover()"
        [glxyPopover]="popover"
      >
        {{ tag.tagName }}
      </mat-checkbox>
    </EXP__glxy-wrap>

    <ng-container [ngSwitch]="result.status">
      <ng-container *ngSwitchCase="'loading'">
        <glxy-loading-spinner [inline]="true" [fullWidth]="false"></glxy-loading-spinner>
      </ng-container>
    </ng-container>
  </div>
</ng-container>

<glxy-popover #popover [isOpen]="popoverOpen" [padding]="'small'" [positions]="[PopoverPositions.Right]">
  {{ 'CAMPAIGN_DETAILS.ACTIONS.MANAGE_TAGS.BACK_COMPAT_POPOVER_HINT' | translate }}
</glxy-popover>
