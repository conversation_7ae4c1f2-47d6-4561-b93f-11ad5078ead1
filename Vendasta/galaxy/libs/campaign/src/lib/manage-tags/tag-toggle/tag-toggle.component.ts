import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { CampaignTaggingService, Sender } from '@vendasta/campaigns';
import { PopoverPositions } from '@vendasta/galaxy/popover';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Observable, of, throwError } from 'rxjs';
import { catchError, shareReplay, tap } from 'rxjs/operators';

export interface SenderTag {
  tagId: string;
  tagName: string;
  isBackCompatTag?: boolean;
}

export interface TagData extends SenderTag {
  selected: boolean;
}

@Component({
  selector: 'campaign-tag-toggle',
  templateUrl: './tag-toggle.component.html',
  styleUrls: ['./tag-toggle.component.scss'],
  standalone: false,
})
export class TagToggleComponent {
  public PopoverPositions = PopoverPositions;

  @Input() sender: Sender | undefined;
  @Input() campaignId: string | undefined;
  @Input() senderMode = false;
  @Input() tag: TagData | undefined;

  @Output() tagSelectionChanged = new EventEmitter<TagData>();

  public popoverOpen = false;

  updateWork$: Observable<null> = of(null);

  constructor(
    private readonly campaignTaggingService: CampaignTaggingService,
    private snackbarService: SnackbarService,
  ) {}

  openPopover(): void {
    if (this.tag.isBackCompatTag) {
      this.popoverOpen = true;
    }
  }

  closePopover(): void {
    this.popoverOpen = false;
  }

  async changeTag(tagId: string, event: MatCheckboxChange): Promise<void> {
    let updateWork$: Observable<null>;
    if (event.checked === true) {
      updateWork$ = this.campaignTaggingService.addTagToCampaign(
        this.sender.type,
        this.sender.id,
        this.campaignId,
        tagId,
      );
    } else {
      updateWork$ = this.campaignTaggingService.removeTagFromCampaign(this.campaignId, tagId);
    }
    updateWork$ = updateWork$.pipe(
      shareReplay({ bufferSize: 1, refCount: true }),
      tap(() => this.updateSelected(event.checked)),
      tap(() => {
        this.snackbarService.openSuccessSnack('CAMPAIGN_DETAILS.ACTIONS.MANAGE_TAGS.TAG_ADD_SUCCESS');
      }),
      catchError((err) => {
        this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.ACTIONS.MANAGE_TAGS.TAG_ADD_FAILURE');
        return throwError(err);
      }),
    );

    this.updateWork$ = updateWork$;
  }

  private updateSelected(selected: boolean): void {
    this.tagSelectionChanged.emit({
      selected: selected,
      tagId: this.tag.tagId,
      tagName: this.tag.tagName,
    });
  }
}
