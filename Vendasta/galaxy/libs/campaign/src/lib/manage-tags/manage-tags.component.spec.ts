import { applyUserSelectionToTags, UserToggledTagsMap } from './manage-tags.component';
import { TagData } from './tag-toggle/tag-toggle.component';

describe('applyUserSelectionToTags', () => {
  it('should set all tags from false to true if user has selected all tags', () => {
    const allTags: TagData[] = [
      {
        tagId: 'a',
        selected: false,
        tagName: 'A',
      },
      {
        tagId: 'b',
        selected: false,
        tagName: 'B',
      },
    ];
    const userSelection: UserToggledTagsMap = {
      a: true,
      b: true,
    };
    const result = applyUserSelectionToTags(allTags, userSelection);
    const resultSelections = result.map((r) => r.selected);
    expect(resultSelections).toEqual([true, true]);
  });
  it('should set all tags from true to false if user has deselected all tags', () => {
    const allTags: TagData[] = [
      {
        tagId: 'a',
        selected: true,
        tagName: 'A',
      },
      {
        tagId: 'b',
        selected: true,
        tagName: 'B',
      },
    ];
    const userSelection: UserToggledTagsMap = {
      a: false,
      b: false,
    };
    const result = applyUserSelectionToTags(allTags, userSelection);
    const resultSelections = result.map((r) => r.selected);
    expect(resultSelections).toEqual([false, false]);
  });
  it('should preserve original selections if user has not toggled anything', () => {
    const allTags: TagData[] = [
      {
        tagId: 'a',
        selected: true,
        tagName: 'A',
      },
      {
        tagId: 'b',
        selected: false,
        tagName: 'B',
      },
    ];
    const userSelection: UserToggledTagsMap = {};
    const result = applyUserSelectionToTags(allTags, userSelection);
    const resultSelections = result.map((r) => r.selected);
    expect(resultSelections).toEqual([true, false]);
  });
  it('should preserve original selections if user has only toggled some of the tags [false->true]', () => {
    const allTags: TagData[] = [
      {
        tagId: 'a',
        selected: false,
        tagName: 'A',
      },
      {
        tagId: 'b',
        selected: false,
        tagName: 'B',
      },
      {
        tagId: 'c',
        selected: false,
        tagName: 'C',
      },
    ];
    const userSelection: UserToggledTagsMap = {
      a: true,
      // nothing for b
      c: true,
    };
    const result = applyUserSelectionToTags(allTags, userSelection);
    const resultSelections = result.map((r) => r.selected);
    expect(resultSelections).toEqual([true, false, true]);
  });
  it('should preserve original selections if user has only toggled some of the tags [true->false]', () => {
    const allTags: TagData[] = [
      {
        tagId: 'a',
        selected: true,
        tagName: 'A',
      },
      {
        tagId: 'b',
        selected: true,
        tagName: 'B',
      },
      {
        tagId: 'c',
        selected: true,
        tagName: 'C',
      },
    ];
    const userSelection: UserToggledTagsMap = {
      a: false,
      // nothing for b
      c: false,
    };
    const result = applyUserSelectionToTags(allTags, userSelection);
    const resultSelections = result.map((r) => r.selected);
    expect(resultSelections).toEqual([false, true, false]);
  });
});
