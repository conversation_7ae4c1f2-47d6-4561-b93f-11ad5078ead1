import { Injectable, Inject } from '@angular/core';
import { Router } from '@angular/router';
import { CampaignConfig } from '../../../../libs/campaign/src/lib/dependencies';
import { GalaxyFilterInterface } from '@vendasta/galaxy/filter/chips';
import { take } from 'rxjs/operators';
import { CONFIG_TOKEN } from './tokens';

@Injectable({ providedIn: 'root' })
export class AddContactsToCampaignRedirectService {
  constructor(
    private router: Router,
    @Inject(CONFIG_TOKEN) private config: CampaignConfig,
  ) {}

  navigateToSendCampaign(
    ids: string[],
    filters?: GalaxyFilterInterface[],
    search?: string,
    useSelectAll?: boolean,
    totalContacts?: number,
  ): void {
    const contactQueryString = encodeURIComponent('[' + ids.join(',') + ']');
    const filtersQueryString = encodeURIComponent(filters ? JSON.stringify(filters) : '');
    const searchQueryString = encodeURIComponent(search || '');
    const useSelectAllQueryString = encodeURIComponent(useSelectAll || false);
    const totalContactsQueryString = totalContacts || 0;
    const backUrl = encodeURIComponent(this.router.url);
    const queryString = `contactIds=${contactQueryString}&backUrl=${backUrl}&filters=${filtersQueryString}&search=${searchQueryString}&useSelectAll=${useSelectAllQueryString}&totalContacts=${totalContactsQueryString}`;

    this.config.basePath$.pipe(take(1)).subscribe((basePath) => {
      this.router.navigateByUrl(`${basePath}/preview-send?${queryString}`);
    });
  }
}
