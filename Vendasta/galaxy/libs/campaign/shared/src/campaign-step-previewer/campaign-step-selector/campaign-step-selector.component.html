<div class="container">
  <div class="recipient-selector">
    <p class="preview-text">{{ 'CAMPAIGN_PREVIEW_MODAL.PREVIEWING_AS' | translate }}</p>
    <p class="recipient-text">{{ selectedName$$ | async }}</p>
    <button mat-stroked-button (click)="openRecipientSelectDialog()">
      {{ 'CAMPAIGN_PREVIEW_MODAL.SELECT_ACTION' | translate }}
    </button>
  </div>
  <div class="step-selector">
    <button mat-icon-button (click)="goToPreviousStep()" [disabled]="onFirstStep$ | async">
      <mat-icon>chevron_left</mat-icon>
    </button>
    <div class="day-text">{{ 'STEP.DAY' | translate }}</div>
    <div class="day-text">{{ stepDay$ | async }}</div>
    <div class="step-count">({{ stepNumber$ | async }}/{{ totalSteps$ | async }})</div>
    <button mat-icon-button (click)="goToNextStep()" [disabled]="onLastStep$ | async">
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>
</div>
