@use 'design-tokens' as *;

.container {
  display: flex;
  justify-content: space-between;
  background-color: $white;
  border: 1px solid $border-color;
  padding: 2px 32px;
}

.recipient-selector {
  display: flex;
  align-items: center;

  .preview-text {
    padding-right: 8px;
  }

  .recipient-text {
    padding-right: 8px;
    font-weight: 500;
  }
}

.step-selector {
  display: flex;
  align-items: center;

  .day-text {
    padding-right: 8px;
    font-weight: 500;
  }

  .step-count {
    font-weight: 300;
  }
}
