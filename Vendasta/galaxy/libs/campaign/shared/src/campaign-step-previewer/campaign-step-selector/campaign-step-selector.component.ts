import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { CampaignPreviewerService } from '../../campaign-previewer.service';
import { BehaviorSubject, combineLatest, firstValueFrom, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ContactData, ContactSelectorComponent } from '../contact-selector-modal/contact-selector.component';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'campaign-step-selector',
  templateUrl: 'campaign-step-selector.component.html',
  styleUrls: ['campaign-step-selector.component.scss'],
  imports: [TranslateModule, CommonModule, MatButtonModule, MatIconModule],
})
export class CampaignStepSelectorComponent {
  stepNumber$: Observable<number>;
  stepDay$: Observable<number>;
  totalSteps$: Observable<number>;
  onFirstStep$: Observable<boolean>;
  onLastStep$: Observable<boolean>;
  public selectedName$$: BehaviorSubject<string> = new BehaviorSubject<string>('');

  constructor(
    private previewerService: CampaignPreviewerService,
    private readonly dialog: MatDialog,
  ) {
    this.stepNumber$ = this.previewerService.getStepNumber();
    this.stepDay$ = this.previewerService.getStepDay();
    this.totalSteps$ = this.previewerService.getTotalSteps();
    this.onFirstStep$ = this.stepNumber$.pipe(map((sn) => sn === 1));
    this.onLastStep$ = combineLatest([this.stepNumber$, this.totalSteps$]).pipe(
      map(([stepNumber, totalSteps]) => stepNumber === totalSteps),
    );
  }

  async openRecipientSelectDialog(): Promise<void> {
    const dialog = this.dialog.open(ContactSelectorComponent, {
      data: this.previewerService.sender$,
      minWidth: '350px',
    });
    const contact: ContactData = await firstValueFrom(dialog.afterClosed());
    if (contact) {
      this.selectedName$$.next(contact.name);
      this.previewerService.setRecipientId({ Id: contact.contactID, Name: contact.name });
    }
  }

  goToPreviousStep(): void {
    this.previewerService.goToPreviousStep();
  }

  goToNextStep(): void {
    this.previewerService.goToNextStep();
  }
}
