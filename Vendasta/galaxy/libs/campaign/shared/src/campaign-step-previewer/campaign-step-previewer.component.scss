@use 'design-tokens' as *;

.sms-container {
  display: flex;
  justify-content: center;
  margin-top: $spacing-2;
}

.email-container {
  height: 100%;
}

.empty-state {
  align-content: center;
  height: 100%;
}

.glxy-loading-spinner {
  min-height: 400px;
}

.non-email-step {
  text-align: center;
  padding: $spacing-5 0;

  mat-icon {
    font-size: 60px;
    width: 60px;
    height: 60px;
    margin-bottom: $spacing-2;
  }

  .title {
    font-size: 30px;
  }

  hr {
    width: 65%;
    margin: $spacing-3 auto;
  }
}
