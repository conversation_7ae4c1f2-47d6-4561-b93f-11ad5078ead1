<campaign-step-selector></campaign-step-selector>
@let data = displayData$ | async;
@if (!displayDataLoading()) {
  @if (data) {
    @if (data.html) {
      @if (data.stepType === stepTypes.CAMPAIGN_STEP_TYPE_EMAIL) {
        <div class="email-container">
          <glxy-email-viewer [html]="data.html"></glxy-email-viewer>
        </div>
      }
      @if (data.stepType === stepTypes.CAMPAIGN_STEP_TYPE_SMS) {
        <div class="sms-container">
          <glxy-chat-message>{{ data.html }}</glxy-chat-message>
        </div>
      }
    } @else {
      @if (data.stepType === stepTypes.CAMPAIGN_STEP_TYPE_SNAPSHOT_CREATION) {
        <div class="non-email-step">
          <mat-icon [ngStyle]="{ color: snapshotCampaignStepPreviewData.icon.color }">
            {{ snapshotCampaignStepPreviewData.icon.name }}
          </mat-icon>
          <div class="title">
            {{ snapshotCampaignStepPreviewData.title }}
          </div>
          <hr />
          <div [innerHtml]="snapshotCampaignStepPreviewData.description"></div>
        </div>
      } @else {
        <div class="empty-state">
          <glxy-empty-state>
            <p class="empty-state-text">{{ 'CAMPAIGN_PREVIEW.LOOKUP_FAILED' | translate }}</p>
          </glxy-empty-state>
        </div>
      }
    }
  } @else {
    <div class="empty-state">
      <glxy-empty-state>
        <p class="empty-state-text">{{ 'CAMPAIGN_PREVIEW.NONE_SELECTED' | translate }}</p>
      </glxy-empty-state>
    </div>
  }
} @else {
  <glxy-loading-spinner [fullHeight]="true"></glxy-loading-spinner>
}
