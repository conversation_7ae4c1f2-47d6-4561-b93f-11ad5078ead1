<h2 mat-dialog-title>{{ 'CAMPAIGN_PREVIEW_MODAL.CONTACT_MODAl.TITLE' | translate }}</h2>
<mat-dialog-content>
  <div class="search-container">
    <form #search="ngForm">
      <mat-form-field appearance="outline">
        <mat-label>{{ 'CAMPAIGN_PREVIEW_MODAL.CONTACT_MODAl.HINT' | translate }}</mat-label>
        <input
          matInput
          autocomplete="off"
          placeholder="{{ 'CAMPAIGN_PREVIEW_MODAL.CONTACT_MODAl.TITLE' | translate }}"
          [(ngModel)]="searchTerm"
          name="Search Term"
        />
      </mat-form-field>
    </form>
  </div>
  <mat-list *ngIf="contacts$ | async as contacts">
    <mat-list-item *ngFor="let c of contacts" (click)="contactSelected(c)">
      <h4 matListItemTitle>{{ c.name }}</h4>
    </mat-list-item>
  </mat-list>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-raised-button mat-dialog-close>
    {{ 'CAMPAIGN_PREVIEW_MODAL.CONTACT_MODAl.CANCEL' | translate }}
  </button>
</mat-dialog-actions>
