import { AfterViewInit, Component, Inject, ViewChild } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { CrmFieldService, StandardExternalIds } from '@galaxy/crm/static';
import { CRMApiService, CrmObject, CrmObjectSearch, ListCrmObjectsRequest } from '@vendasta/crm';
import { Observable, combineLatest } from 'rxjs';
import { map, startWith, switchMap } from 'rxjs/operators';
import { CommonModule } from '@angular/common';
import { MatListModule } from '@angular/material/list';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { SenderInterface } from '@vendasta/campaigns';

export interface ContactData {
  contactID: string;
  accountGroupID: string;
  name: string;
}

@Component({
  templateUrl: './contact-selector.component.html',
  styleUrls: ['./contact-selector.component.scss'],
  imports: [
    CommonModule,
    MatButtonModule,
    TranslateModule,
    MatDialogModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatListModule,
  ],
})
export class ContactSelectorComponent implements AfterViewInit {
  public contacts$: Observable<ContactData[]>;
  @ViewChild('search') search: NgForm;
  public searchTerm = '';

  constructor(
    private readonly dialogRef: MatDialogRef<ContactSelectorComponent>,
    @Inject(MAT_DIALOG_DATA) readonly data$: Observable<SenderInterface>,
    private readonly crmService: CRMApiService,
    private readonly crmFieldService: CrmFieldService,
  ) {}

  ngAfterViewInit(): void {
    this.contacts$ = combineLatest([this.data$, this.search.form.valueChanges.pipe(startWith(''))]).pipe(
      switchMap(([data]) =>
        this.crmService
          .listContacts({
            namespace: data.id,
            search: { searchTerm: this.searchTerm } as CrmObjectSearch,
            pagingOptions: {
              pageSize: 20,
            },
          } as ListCrmObjectsRequest)
          .pipe(map((response) => ({ contacts: response.crmObjects, accountGroupId: data.id }))),
      ),
      map((response) => {
        let contacts = response?.contacts.map((contact: CrmObject) => ({
          name: this.getNameForContact(contact),
          accountGroupID: response.accountGroupId,
          contactID: contact.crmObjectId,
        })) as ContactData[];
        contacts = contacts.filter((contact) => contact.name !== '');
        return contacts;
      }),
    );
  }

  contactSelected(contact: ContactData): void {
    this.dialogRef.close(contact);
  }

  getNameForContact(contact: CrmObject): string {
    let name = '';
    const firstName = this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.FirstName);
    if (firstName !== null) {
      name = firstName.stringValue;
    }

    const lastName = this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.LastName);
    if (lastName !== null) {
      name += ' ' + lastName.stringValue;
    }

    return name;
  }
}
