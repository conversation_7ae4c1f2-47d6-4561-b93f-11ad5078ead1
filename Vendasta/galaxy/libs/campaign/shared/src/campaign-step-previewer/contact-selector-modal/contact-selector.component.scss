@use 'design-tokens' as *;

mat-list-item {
  &:hover {
    background: $primary-background-selected-color;
  }
}

.search-container {
  mat-form-field {
    width: 100%;
    padding: 0 16px;
    .mat-form-field-wrapper {
      padding-bottom: 0;
      background: white;
      .mat-form-field-flex {
        .mat-form-field-infix {
          padding: 8px;
          .mat-form-field-label-wrapper {
            padding-top: 0;
          }
        }
      }
    }
    &:not(.mat-form-field-should-float) {
      .mat-form-field-wrapper {
        .mat-form-field-flex {
          .mat-form-field-infix {
            .mat-form-field-label-wrapper {
              label {
                margin-top: -12px;
              }
            }
          }
        }
      }
    }
  }
}
