<ng-container *ngIf="settings$ | async as emailSettings">
  <mat-card appearance="outlined">
    <mat-card-header>
      <mat-card-title>{{ 'CAMPAIGN_PREVIEW_MODAL.EMAIL_SETTINGS.EMAIL_SETTINGS' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div>
        {{ 'CAMPAIGN_PREVIEW_MODAL.EMAIL_SETTINGS.SENDER_NAME' | translate }} :
        {{ emailSettings.preferredEmailDisplayName }}
      </div>
      <div>
        {{ 'CAMPAIGN_PREVIEW_MODAL.EMAIL_SETTINGS.SENDER_ADDRESS' | translate }} :
        {{ emailSettings.preferredEmailUsername }}&#64;{{ emailSettings.domain }}
      </div>
      <div>
        {{ 'CAMPAIGN_PREVIEW_MODAL.EMAIL_SETTINGS.REPLY_ADDRESS' | translate }} :
        {{ emailSettings.preferredReplyToEmailAddress }}
      </div>
    </mat-card-content>
  </mat-card>
</ng-container>
