import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CampaignSenderSettingsComponent } from './campaign-sender-settings.component';
import { SendersService } from '@vendasta/email';
import { CampaignPreviewerService } from '../../campaign-previewer.service';

const senderServiceMock = {
  getEmailSendingInfo: () => {
    return;
  },
};
const campaignPreviewerServiceMock = {
  setPreviewUsername: () => {
    return;
  },
};
describe('CampaignSenderSettingsComponent', () => {
  let component: CampaignSenderSettingsComponent;
  let fixture: ComponentFixture<CampaignSenderSettingsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CampaignSenderSettingsComponent],
      providers: [
        { provide: SendersService, useValue: senderServiceMock },
        { provide: CampaignPreviewerService, useValue: campaignPreviewerServiceMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CampaignSenderSettingsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
