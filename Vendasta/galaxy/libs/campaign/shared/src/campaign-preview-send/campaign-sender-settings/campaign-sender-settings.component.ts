import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SenderInterface } from '@vendasta/campaigns';
import { GetSenderInfoResponseInterface, SendersService } from '@vendasta/email';
import { BehaviorSubject, Observable } from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { CampaignPreviewerService } from '../../campaign-previewer.service';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'campaign-sender-settings',
  imports: [CommonModule, GalaxyFormFieldModule, TranslateModule, MatCardModule, MatButtonModule],
  templateUrl: './campaign-sender-settings.component.html',
  styleUrl: './campaign-sender-settings.component.scss',
})
export class CampaignSenderSettingsComponent {
  @Input() set sender(s: SenderInterface) {
    this.sender$$.next(s);
  }

  get sender(): SenderInterface {
    return this.sender$$.getValue();
  }

  private sender$$: BehaviorSubject<SenderInterface> = new BehaviorSubject<SenderInterface>({});
  public settings$: Observable<GetSenderInfoResponseInterface>;

  constructor(
    private readonly sendersService: SendersService,
    private previewService: CampaignPreviewerService,
  ) {
    this.settings$ = this.sender$$.asObservable().pipe(
      switchMap((sender) => {
        return this.sendersService.getEmailSendingInfo(sender.type, sender.id);
      }),
      tap((settings) => this.previewService.setPreviewUsername(settings.preferredEmailDisplayName)),
    );
  }
}
