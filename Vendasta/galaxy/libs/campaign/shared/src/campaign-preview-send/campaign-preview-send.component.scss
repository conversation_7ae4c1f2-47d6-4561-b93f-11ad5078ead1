@use 'design-tokens' as *;

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  button {
    margin-right: $spacing-2;
  }
}

.preview-wrapper {
  display: grid;
  grid-template-columns: 65% 35%;
  height: inherit;
  gap: 0;
  overflow: hidden;
}

.campaign-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.send-schedule {
  margin-bottom: $spacing-4;
  width: 100%;
}

.send-schedule .mat-button-toggle-group {
  display: flex;
  justify-content: space-between;
}

.send-schedule .mat-button-toggle {
  flex: 1;
  text-align: center;
}

.send-options-mobile {
  padding: $spacing-3;
}

.send-options {
  padding: $spacing-4;
  border: solid 1px $border-color;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-x: scroll;
}

.select-recipients-text {
  display: flex;
  color: $secondary-text-color;
  height: $spacing-4;
  padding: 0 $spacing-2;
  margin: auto 0;

  p {
    margin: auto;
  }
}

.recipient-chip-wrapper {
  display: flex;
}

.mat-icon-wrapper {
  margin: auto;
}

.chip-row-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: auto;
}

.recipient-chip-form-field {
  margin-bottom: 0;
}

.contact-icon {
  margin-top: $spacing-2;
  margin-left: $spacing-2;
  color: $icon-color;
  font-size: $font-preset-2-size;
}

.est-recipients-tooltip {
  padding-top: $spacing-2;
  font-size: $font-preset-3-size;
  margin-bottom: $negative-1;
  pointer-events: auto;
}

.chip-container {
  display: flex;
}

.warning-icon {
  color: $warn-icon-color;
  height: $font-preset-3-size;
  width: $font-preset-3-size;
  font-size: $font-preset-3-size;
  margin-right: $spacing-1;
  margin-top: 2px;
}

.error-messages {
  margin-bottom: $spacing-4;
}

ul {
  list-style-position: outside;
}
.error-message {
  margin-left: $negative-3;
}

::ng-deep .mat-mdc-tab.mdc-tab {
  width: 50%;
}
