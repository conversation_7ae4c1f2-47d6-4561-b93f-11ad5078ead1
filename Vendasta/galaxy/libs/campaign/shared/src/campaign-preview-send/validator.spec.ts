import { FormControl } from '@angular/forms';
import { emailUsernameValidator } from './campaign-preview-send.component';

describe('EmailValidator', () => {
  const control: FormControl = new FormControl();

  it('should return null if the email is valid', () => {
    control.setValue('noreply');
    expect(emailUsernameValidator(control)).toBeNull();
  });

  it('should return null if email username contains valid characters', () => {
    control.setValue('no._%+-reply');
    expect(emailUsernameValidator(control)).toBeNull();
  });

  it('should return error if email username contains @', () => {
    control.setValue('no@reply');
    expect(emailUsernameValidator(control)).toBeTruthy();
  });

  it('should return error if email username contains other invalid characters', () => {
    control.setValue('no#!reply');
    expect(emailUsernameValidator(control)).toBeTruthy();
  });

  it('should return error if email username contains spaces', () => {
    control.setValue('no   reply');
    expect(emailUsernameValidator(control)).toBeTruthy();
  });
});
