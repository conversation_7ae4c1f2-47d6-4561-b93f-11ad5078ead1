import { Component, HostB<PERSON>ing, inject, Inject, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatButton } from '@angular/material/button';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { CampaignSelectorComponent } from './campaign-selector/campaign-selector.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';

import { MatOption } from '@angular/material/autocomplete';
import { Contact, dialogData } from '../crm-campaign-dialog/crm-campaign-contacts-dialog.component';
import { MatError, MatSelect } from '@angular/material/select';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormControl,
  ValidationErrors,
  Valida<PERSON>,
} from '@angular/forms';
import { SelectInputOption } from '@vendasta/galaxy/input';
import { MatChipsModule } from '@angular/material/chips';
import { CampaignSenderSettingsComponent } from './campaign-sender-settings/campaign-sender-settings.component';
import {
  CampaignStepType,
  ContactType,
  GetterCampaignDataInterface,
  RecipientCampaignService,
  RecipientType,
  SenderInterface,
  SenderOptionsInterface,
  SenderType,
} from '@vendasta/campaigns';
import { CampaignI18nModule } from '../../../src/lib/assets/i18n/campaign-i18n.module';
import { CampaignStepPreviewerComponent } from '../campaign-step-previewer/campaign-step-previewer.component';
import { CampaignPreviewerService } from '../campaign-previewer.service';
import { BehaviorSubject, firstValueFrom, forkJoin, iif, merge, Observable, of, Subscription } from 'rxjs';
import { catchError, filter, map, switchMap, take } from 'rxjs/operators';
import { GalaxyColumnsSortService, GalaxyTableModule, Row } from '@vendasta/galaxy/table';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import {
  CrmFieldService,
  CrmObjectService,
  ListObjectTypeDialogData,
  ListObjectTypeDialogResult,
  PageAnalyticsInjectionToken,
  SelectObjectTypeModalComponent,
  StandardExternalIds,
  tableFiltersInjectionTokenGenerator,
} from '@galaxy/crm/static';
import { Filter, FilterGroupInterface, FilterGroupOperator, FilterOperator, FilterValue } from '@vendasta/crm';
import { MatInput } from '@angular/material/input';
import { GetSenderInfoResponse, SendersService } from '@vendasta/email';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatIcon } from '@angular/material/icon';
import { MatButtonToggle, MatButtonToggleGroup } from '@angular/material/button-toggle';
import { MatDatepicker } from '@angular/material/datepicker';
import { DateAdapter } from '@angular/material/core';
import { format as formatDate } from 'date-fns-tz';
import { GalaxyDateAdapter } from '@vendasta/galaxy/datepicker/src/date-adapter';
import { MatTooltip } from '@angular/material/tooltip';
import { GalaxyFilterChipInjectionToken, GalaxyFilterInterface } from '@vendasta/galaxy/filter/chips';

import { GalaxyTableSelectionService } from '@vendasta/galaxy/table/src/services/table-selection.service';
import { GalaxyTableSearchService } from '@vendasta/galaxy/table/src/services/table-search.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CampaignConfig } from '@galaxy/campaign/dependencies';
import { CONFIG_TOKEN } from '../tokens';
import { BreakpointObserver } from '@angular/cdk/layout';
import { MatTab, MatTabContent, MatTabGroup } from '@angular/material/tabs';

import { CampaignSelectorService } from './campaign-selector/campaign-selector.service';

const MOBILE_MAX_WIDTH = 900;

export interface Recipient {
  id: string;
  type: RecipientType;
  contact: {
    type: ContactType;
    value: string;
  };
  emailAddress: string;
  phoneNumber: string;
  externalId: string;
}

enum CampaignType {
  EMAIL = 'email',
  SMS = 'sms',
  SMS_AND_EMAIL = 'sms_and_email',
  OTHER = 'other',
}

type SendType = 'send-now' | 'send-later';

export function emailUsernameValidator(control: AbstractControl): ValidationErrors | null {
  const isValid = /^[a-zA-Z0-9._%+-]+$/.test(control.value);
  return isValid ? null : { invalidUsername: true };
}

@Component({
  selector: 'campaign-preview-send',
  providers: [
    { provide: DateAdapter, useClass: GalaxyDateAdapter },
    GalaxyTableSelectionService,
    GalaxyTableSearchService,
    GalaxyColumnsSortService,
  ],
  imports: [
    CommonModule,
    MatChipsModule,
    MatButton,
    GalaxyPageModule,
    CampaignSelectorComponent,
    TranslateModule,
    CampaignI18nModule,
    GalaxyFormFieldModule,
    MatOption,
    MatSelect,
    ReactiveFormsModule,
    CampaignSenderSettingsComponent,
    CampaignStepPreviewerComponent,
    MatInput,
    GalaxyAlertModule,
    GalaxyLoadingSpinnerModule,
    MatIcon,
    MatButtonToggleGroup,
    MatButtonToggle,
    FormsModule,
    MatDatepicker,
    GalaxyTableModule,
    MatTooltip,
    MatTabGroup,
    MatTab,
    MatTabContent,
    MatError,
  ],
  templateUrl: './campaign-preview-send.component.html',
  styleUrl: './campaign-preview-send.component.scss',
})
export class CampaignPreviewSendComponent implements OnInit, OnDestroy {
  @HostBinding('class.campaign-preview-send-is-mobile') previewSendIsMobile = false;
  mobileMaxWidth = MOBILE_MAX_WIDTH;
  private readonly breakpointObserver = inject(BreakpointObserver);

  private readonly injector = inject(Injector);
  formGroup = new FormBuilder().nonNullable.group({
    selectFormControl: '',
    dateFormControl: new FormControl(formatDate(new Date(), "yyyy-MM-dd'T'HH:mm"), [this.maxDateValidator(30)]),
  });
  maxDate = formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), "yyyy-MM-dd'T'HH:mm");

  campaignStartTime: Date;
  nowDate = new Date(Date.now());
  default = 'default';
  custom = 'custom';
  assignedSalespersonValue = 'assigned-salesperson';
  assignedSalespersonLabel = this.translate.instant('CAMPAIGN_PREVIEW_MODAL.SENDER_OPTIONS.SALESPERSON');
  showMoreChips = false;
  sendFromControl: FormControl<string | null> = new FormControl<string>(this.default);
  sendFromOptions: SelectInputOption[] = [
    { label: this.translate.instant('CAMPAIGN_PREVIEW_MODAL.SENDER_OPTIONS.DEFAULT'), value: this.default },
    { label: this.translate.instant('CAMPAIGN_PREVIEW_MODAL.SENDER_OPTIONS.CUSTOM'), value: this.custom },
    { label: this.assignedSalespersonLabel, value: this.assignedSalespersonValue },
  ];

  useSelectAll = false;
  previewModalSendAll = false;
  sendOption: SendType = 'send-now';
  selectedCampaign: GetterCampaignDataInterface;
  missingContactInfo: string[] = [];
  contacts: Contact[] = [];
  contactIDs: Observable<string[]>;
  readonly emailFieldID = this.crmFieldService.getFieldId(StandardExternalIds.Email);
  readonly phoneFieldID = this.crmFieldService.getFieldId(StandardExternalIds.PhoneNumber);
  subscriptions: Subscription[] = [];
  senderInfoDomain$: Observable<string>;

  email = new UntypedFormControl('', [Validators.required, emailUsernameValidator]);
  senderName = new UntypedFormControl('', [Validators.required]);
  replyTo = new UntypedFormControl('', [Validators.email]);

  sendingInProgress$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  sendingInProgress$: Observable<boolean> = this.sendingInProgress$$.asObservable();
  estimatedRecipients = 0;
  showErrorMessage = false;
  errorMessagesToDisplay = new Set<string>();
  senderDisplayName = '';
  totalContacts: number | undefined = 0;
  filtersPreview: GalaxyFilterInterface[];
  filtersApplied: boolean;
  filters;
  backUrl = '';
  search = '';

  private selectedCampaignId$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  private disableCampaignSelection$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  disableCampaignSelection$ = this.disableCampaignSelection$$.asObservable();

  selectedCampaignId$ = this.selectedCampaignId$$.asObservable();
  selectedCampaign$ = this.selectedCampaignId$.pipe(
    filter((campaignId) => !!campaignId),
    switchMap((campaignId) => {
      return this.campaignService.get(campaignId);
    }),
  );

  maxDateValidator(maxDays: number) {
    return (control: FormControl) => {
      const selected = new Date(control.value);
      const maxDate = new Date(new Date().getTime() + maxDays * 24 * 60 * 60 * 1000);
      return selected > maxDate ? { maxDateExceeded: true } : null;
    };
  }

  getFilteredSendFromOptions(senderType: SenderType): SelectInputOption[] {
    if (senderType !== SenderType.SENDER_TYPE_PARTNER) {
      return this.sendFromOptions.filter((option) => option.label !== this.assignedSalespersonLabel);
    }
    return this.sendFromOptions;
  }

  getErrorMessage(control: UntypedFormControl): string {
    if (control.hasError('required')) {
      return this.translate.instant('CAMPAIGN_PREVIEW_MODAL.REQUIRED_FIELD');
    } else if (control.hasError('invalidUsername')) {
      return this.translate.instant('CAMPAIGN_PREVIEW_MODAL.INVALID_USERNAME');
    } else {
      return '';
    }
  }

  constructor(
    private posthogService: ProductAnalyticsService,
    private dialog: MatDialog,
    private snackbarService: SnackbarService,
    private readonly translate: TranslateService,
    private previewerService: CampaignPreviewerService,
    private readonly recipientCampaignService: RecipientCampaignService,
    private crmFieldService: CrmFieldService,
    private readonly sendersService: SendersService,
    private readonly crmContactService: CrmObjectService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    @Inject(MAT_DIALOG_DATA) public data: dialogData,
    @Inject(CONFIG_TOKEN) public config: CampaignConfig,
    private readonly campaignService: CampaignSelectorService,
  ) {
    this.subscriptions.push(
      merge(this.sendFromControl.valueChanges, this.senderName.valueChanges).subscribe(() => {
        const sendFromValue = this.sendFromControl.value;
        const senderNameValue = this.senderName.value;
        if (sendFromValue === this.assignedSalespersonValue) {
          this.previewerService.setPreviewUsername(
            this.translate.instant('CAMPAIGN_PREVIEW_MODAL.YOUR_ASSIGNED_SALESPERSON'),
          );
        } else if (sendFromValue === this.default) {
          this.previewerService.setPreviewUsername(this.senderDisplayName);
        } else {
          this.previewerService.setPreviewUsername(senderNameValue);
        }
      }),
    );
    this.contactIDs = this.activatedRoute.queryParams.pipe(
      map((params) => params['contactIds']),
      map((raw: string) => {
        const contactIdsRaw = decodeURIComponent(raw);
        return contactIdsRaw.substring(1, contactIdsRaw.length - 1).split(',');
      }),
    );
    this.contactIDs.subscribe((contactIds) => {
      this.buildContacts(contactIds);
    });
    this.backUrl = decodeURIComponent(this.activatedRoute.snapshot.queryParams['backUrl'] || '');

    this.filtersApplied = this.activatedRoute.snapshot.queryParams['filtersApplied'] === 'true';
    const filters = decodeURIComponent(this.activatedRoute.snapshot.queryParams['filters'] || '');
    this.filters = filters ? JSON.parse(filters) : [];
    this.useSelectAll = this.activatedRoute.snapshot.queryParams['useSelectAll'] === 'true';
    this.search = this.activatedRoute.snapshot.queryParams['search'] || '';
    this.totalContacts = this.activatedRoute.snapshot.queryParams['totalContacts'] || 0;

    const campaignID = this.activatedRoute.snapshot.queryParams['campaignId'];
    if (campaignID) {
      this.selectedCampaignId$$.next(this.activatedRoute.snapshot.queryParams['campaignId'] || '');
      this.disableCampaignSelection$$.next(true);
    } else {
      this.selectedCampaignId$$.next('');
      this.disableCampaignSelection$$.next(false);
    }
    this.subscriptions.push(
      this.selectedCampaign$.subscribe({
        next: (campaign) => {
          this.campaignSelected(campaign);
        },
      }),
    );
  }

  ngOnInit() {
    this.senderInfoDomain$ = this.config.sender$.pipe(
      switchMap((s: SenderInterface) => this.sendersService.getEmailSendingInfo(s.type, s.id)),
      map((r: GetSenderInfoResponse) => r.domain),
    );
    this.previewerService.setSender(this.config.sender$);

    iif(
      () => this.sendFromControl.value === this.custom,
      of(this.senderName.value),
      this.config.sender$.pipe(
        switchMap((s: SenderInterface) => this.sendersService.getEmailSendingInfo(s.type, s.id)),
      ),
    ).subscribe((resp) => {
      if (typeof resp === 'string') {
        this.senderDisplayName = resp;
        this.previewerService.setPreviewUsername(this.senderDisplayName);
      } else {
        this.senderDisplayName = resp.preferredEmailDisplayName;
        this.previewerService.setPreviewUsername(this.senderDisplayName);
      }
    });

    this.subscriptions.push(
      this.breakpointObserver.observe('(max-width: ' + this.mobileMaxWidth + 'px)').subscribe((resp) => {
        this.previewSendIsMobile = resp.matches;
      }),
    );
  }

  private buildContacts(contactIds: string[]) {
    if (!contactIds) {
      return [] as Contact[];
    }
    return this.crmContactService
      .getMultiObject('Contact', contactIds)
      .pipe(map((resp) => resp.crmObjects))
      .subscribe((contacts) => {
        const c = contacts.map((contact) => {
          let email = '';
          let phone = '';
          let firstName = '';
          let lastName = '';
          for (const field of contact.fields) {
            if (field.externalId === 'standard__email') {
              email = field.stringValue;
            } else if (field.externalId === 'standard__phone_number') {
              phone = field.stringValue;
            } else if (field.externalId === 'standard__first_name') {
              firstName = field.stringValue;
            } else if (field.externalId === 'standard__last_name') {
              lastName = field.stringValue;
            }
          }
          const name = (firstName + ' ' + lastName).trim();
          return {
            id: contact.crmObjectId,
            name: name,
            email: email,
            phone: phone,
          } as Contact;
        });
        this.contacts = c.filter((contact) => contact.email || contact.phone);
        this.missingContactInfo = c
          .filter((contact) => !contact.email && !contact.phone)
          .map((contact) => contact.name);
      });
  }

  onCancel(): void {
    this.navigateToBack();
  }

  campaignSelected(campaign: GetterCampaignDataInterface) {
    this.previewerService.setCampaign(campaign as GetterCampaignDataInterface);
    this.selectedCampaign = campaign;
    this.updateEstimatedRecipients();
    this.hideError();
  }

  private navigateToBack(): void {
    this.router.navigateByUrl(this.backUrl);
  }

  onSend() {
    if (this.selectedCampaign === undefined) {
      this.snackbarService.openErrorSnack('CAMPAIGNS.CONTACTS.MODAL.ERRORS.NO_CAMPAIGN_SELECTED');
      return;
    }

    // Check if campaign is properly configured before proceeding
    this.checkCampaignConfiguration().then((isConfigured) => {
      if (!isConfigured) {
        this.snackbarService.openErrorSnack('CAMPAIGNS.CONTACTS.MODAL.ERRORS.CAMPAIGN_CONFIGURATION_ERROR');
        return;
      }

      if (this.formGroup.invalid) {
        return;
      }

      this.sendingInProgress$$.next(true);
      if (this.useSelectAll) {
        this.bulkAddContactsToCampaign();
      } else {
        this.sendToSelectedContacts();
      }
    });
  }

  private sendToSelectedContacts(): void {
    this.posthogService.trackEvent('user-clicked-crm-publish-now-multi', 'send-campaign-workflow', 'click');

    const recipients = this.prepareRecipients();
    if (recipients.length === 0) {
      this.snackbarService.openErrorSnack('CAMPAIGNS.CONTACTS.MODAL.ERRORS.NO_CONTACTS_SELECTED');
      this.sendingInProgress$$.next(false);
      return;
    }

    this.subscriptions.push(
      this.processBatchedRecipients(recipients).subscribe((results) => {
        const failedContacts = results.filter((contact) => contact.error);

        if (failedContacts.length > 0) {
          failedContacts.forEach((contact) => {
            this.showError(contact.contactID);
            this.addErrorMessage(contact.error);
          });
          if (this.errorMessagesToDisplay.size === 0) {
            this.errorMessagesToDisplay.add('CAMPAIGNS.CONTACTS.MODAL.ERRORS.FAILED_CAMPAIGN_ALERT_MESSAGE');
          }
          this.showErrorMessage = true;
        } else {
          this.snackbarService.openSuccessSnack(
            this.translate.instant('CAMPAIGN_PREVIEW_MODAL.SUCCESS', {
              count: this.contacts?.length,
              campaignTitle: this.selectedCampaign.name,
            }),
          );
          this.sendingInProgress$$.next(false);
          this.navigateToBack();
        }
        this.sendingInProgress$$.next(false);
      }),
    );
  }

  private prepareRecipients(): Recipient[] {
    return this.contacts.map((contact) => {
      return {
        id: contact?.id,
        type: RecipientType.RECIPIENT_TYPE_CRM_CONTACT,
        contact: {
          type: ContactType.CONTACT_TYPE_EMAIL,
          value: contact?.email,
        },
        emailAddress: contact?.email,
        phoneNumber: contact?.phone,
        externalId: contact?.id,
      } as Recipient;
    });
  }

  private processBatchedRecipients(recipients: Recipient[]): Observable<{ error: string; contactID: string }[]> {
    const formValues = this.formGroup.getRawValue();
    this.campaignStartTime = formValues.dateFormControl ? new Date(formValues.dateFormControl) : this.nowDate;
    const senderInfo = this.getSenderOptions();

    return forkJoin(
      recipients.map((recipient) => {
        return this.config.sender$.pipe(
          take(1),
          switchMap((sender: SenderInterface) => {
            return this.recipientCampaignService.addToCampaign(
              this.selectedCampaign?.campaignId ?? '',
              sender,
              recipient,
              this.campaignStartTime,
              this.sendOption === 'send-now',
              senderInfo,
            );
          }),
          map(() => ({ error: '', contactID: '' })),
          catchError((err) => {
            return of({ error: err.error.message, contactID: recipient.id });
          }),
        );
      }),
    );
  }

  /**
   * Checks if the campaign is properly configured.
   * If the configuration service is available, it checks the configuration.
   * If the configuration is not proper, it opens a configuration modal.
   *
   * @returns A promise that resolves to true if the campaign is properly configured or if the service is not available,
   * false otherwise.
   */
  private async checkCampaignConfiguration(): Promise<boolean> {
    if (!this.config.campaignsConfigurationService$) {
      return true;
    }

    try {
      const service = await firstValueFrom(this.config.campaignsConfigurationService$.pipe(take(1)));
      const isConfigured = await service.isProperlyConfigured();

      if (!isConfigured) {
        // Open the configuration modal and get the result
        const addressUpdated = await service.openConfigurationModal();

        // If the address was successfully updated, we can consider the campaign configured
        if (addressUpdated) {
          return true;
        }

        // Check again after modal is closed, in case configuration succeeded
        return await service.isProperlyConfigured();
      }

      return true;
    } catch (error) {
      // If there's an error checking configuration, allow the process to continue
      console.error('Error checking campaign configuration:', error);
      return true;
    }
  }

  showError(contactID: string) {
    this.contacts.forEach((contact) => {
      if (contact.id === contactID) {
        contact.error = true;
      }
    });
  }

  hideError() {
    this.contacts.forEach((contact) => (contact.error = false));
    this.showErrorMessage = false;
    this.errorMessagesToDisplay.clear();
  }

  addErrorMessage(error: string) {
    if (error.startsWith('phoneNumber')) {
      this.errorMessagesToDisplay.add('CAMPAIGNS.CONTACTS.MODAL.ERRORS.FAILED_ADDRESS_ERROR_SMS');
    } else if (error.startsWith('emailAddress')) {
      this.errorMessagesToDisplay.add('CAMPAIGNS.CONTACTS.MODAL.ERRORS.FAILED_ADDRESS_ERROR_EMAIL');
    }
  }

  getSenderOptions(): SenderOptionsInterface {
    const senderOptions = {
      username: '',
      name: '',
      replyTo: '',
      salespersonId: '',
      sendFromAssignedSalesperson: false,
    } as SenderOptionsInterface;
    if (this.sendFromControl.value === this.custom) {
      if (this.senderName.valid) {
        senderOptions.name = this.senderName.value;
      }
      if (this.email.valid) {
        senderOptions.username = this.email.value;
      }
      if (this.replyTo.valid) {
        senderOptions.replyTo = this.replyTo.value;
      }
    }
    if (this.sendFromControl.value === this.assignedSalespersonValue) {
      senderOptions.sendFromAssignedSalesperson = true;
    }
    return senderOptions;
  }

  bulkAddContactsToCampaign(): void {
    this.posthogService.trackEvent('user-clicked-crm-publish-now-bulk', 'send-campaign-workflow', 'click');
    const operator: FilterGroupOperator = FilterGroupOperator.FILTER_GROUP_OPERATOR_AND;

    const senderOptions: SenderOptionsInterface = {
      sendFromAssignedSalesperson: this.sendFromControl.value === this.assignedSalespersonValue,
    };

    let filterGroup: FilterGroupInterface | undefined = undefined;
    let contactFilters: Filter[] = [];

    const filtersToUse = this.filtersApplied ? this.filtersPreview : this.filters;
    if (filtersToUse.length > 0) {
      contactFilters = filtersToUse.map((filter) => {
        const temp = new Filter();
        temp.fieldId = filter.fieldId;
        temp.operator = filter.operator.valueOf() as FilterOperator;
        temp.values = filter.values?.valueOf() as FilterValue[];
        return temp;
      });
      filterGroup = {
        operator: operator,
        filters: contactFilters,
      };
    }

    const formValues = this.formGroup.getRawValue();
    this.campaignStartTime = formValues.dateFormControl ? new Date(formValues.dateFormControl) : this.nowDate;
    this.config.sender$
      .pipe(
        take(1),
        switchMap((sender: SenderInterface) =>
          this.recipientCampaignService.bulkAddToCampaign(
            this.selectedCampaign?.campaignId ?? '',
            sender,
            this.campaignStartTime,
            this.sendOption === 'send-now',
            filterGroup,
            this.search,
            senderOptions,
          ),
        ),
        catchError(() => {
          this.snackbarService.openErrorSnack(this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.ERRORS.GENERIC_ERROR'));
          return of(null);
        }),
      )
      .subscribe(() => {
        this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.ERRORS.GENERIC_ERROR');
        this.snackbarService.openSuccessSnack(
          this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.SELECT_ALL_SUCCESS', {
            totalObjects: this.totalContacts,
            campaignTitle: this.selectedCampaign.name,
          }),
        );
        this.navigateToBack();
      });
  }

  removeRecipient(contact: Contact) {
    this.contacts = this.contacts.filter((c) => c?.id !== contact?.id);
    this.updateEstimatedRecipients();
  }

  getEstimated(campaign: GetterCampaignDataInterface) {
    const campaignType = this.getCampaignType(campaign);
    switch (campaignType) {
      case CampaignType.EMAIL:
        return this.contacts.filter((contact) => contact.email).length;
      case CampaignType.SMS:
        return this.contacts.filter((contact) => contact.phone).length;
      case CampaignType.OTHER:
        return this.contacts.length;
      case CampaignType.SMS_AND_EMAIL:
        return this.contacts.filter((contact) => contact.email && contact.phone).length;
      default:
        return this.contacts.length;
    }
  }

  getCampaignType(campaign: GetterCampaignDataInterface): CampaignType {
    const [hasEmailStep, hasSmsStep] = this.hasSmsOrEmailStep(campaign);
    if (hasEmailStep && hasSmsStep) {
      return CampaignType.SMS_AND_EMAIL;
    } else if (hasEmailStep) {
      return CampaignType.EMAIL;
    } else if (hasSmsStep) {
      return CampaignType.SMS;
    }
    return CampaignType.OTHER;
  }

  hasSmsOrEmailStep(campaign: GetterCampaignDataInterface): [boolean, boolean] {
    const campaignSteps = campaign.campaignSchedule;
    const hasEmailStep = campaignSteps.some((step) => step.stepType === CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL);
    const hasSmsStep = campaignSteps.some((step) => step.stepType === CampaignStepType.CAMPAIGN_STEP_TYPE_SMS);
    return [hasEmailStep, hasSmsStep];
  }

  updateEstimatedRecipients() {
    this.estimatedRecipients = this.getEstimated(this.selectedCampaign);
  }

  openContactModal() {
    const dialogRef = this.dialog.open(SelectObjectTypeModalComponent, {
      data: {
        baseColumnIds: [],
        modalTitle: 'Select Contacts',
        objectType: 'Contact',
        associations: [],
        hideCreateButton: true,
        enableMultiplePreSelectedRows: true,
        newSelectionIDs: this.contacts.map((contact) => contact.id),
        associationType: '',
        newSelection: '',
        showActions: true,
        hideActionButton: true,
        useSelectAll: this.useSelectAll,
        filters: this.filters,
      } as ListObjectTypeDialogData,
      injector: Injector.create({
        providers: [
          {
            provide: PageAnalyticsInjectionToken,
            useFactory: () => {
              return {
                trackEvent(): void {
                  return;
                },
              };
            },
          },
          {
            provide: GalaxyFilterChipInjectionToken,
            useFactory: tableFiltersInjectionTokenGenerator('Contact'),
          },
        ],
        parent: this.injector,
      }),
    });
    dialogRef.afterClosed().subscribe((result: ListObjectTypeDialogResult) => {
      if (result && !result.clickedCreate) {
        this.addRecipients(result.selectedRows);
      }
      if (result?.useSelectAll) {
        this.useSelectAll = this.previewModalSendAll = true;
        this.totalContacts = result.totalObjects;
      }
      if (result?.filters?.length > 0) {
        this.filtersPreview = result.filters;
        this.filtersApplied = true;
      }
      this.search = result?.searchTerm;
    });
  }

  addRecipients(selected: Row[]) {
    const selectedContacts = selected.map((s: Row) => this.rowToContact(s)).filter((c) => !!c);
    this.contacts = this.contacts.concat(selectedContacts);
    if (this.contacts.length === 0) {
      this.snackbarService.openErrorSnack('CAMPAIGNS.CONTACTS.MODAL.ERRORS.NO_VALID_CONTACTS_SELECTED');
    }
    this.updateEstimatedRecipients();
  }

  private rowToContact(recipient: Row) {
    const contact = {
      id: recipient.id,
      name: recipient['data']['fullName']['value'],
    } as Contact;
    if (recipient['data'][this.phoneFieldID] != undefined) {
      contact.phone = recipient['data'][this.phoneFieldID]['value'];
    }
    if (recipient['data'][this.emailFieldID] != undefined) {
      contact.email = recipient['data'][this.emailFieldID]['value'];
    }

    if (!contact.email && !contact.phone) {
      return null;
    }
    return contact;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub: Subscription) => sub.unsubscribe());
  }
}
