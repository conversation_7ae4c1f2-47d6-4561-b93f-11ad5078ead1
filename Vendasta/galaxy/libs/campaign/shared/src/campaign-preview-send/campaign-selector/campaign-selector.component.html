<glxy-form-field prefixIcon="search">
  <glxy-label>{{ 'CAMPAIGN_PREVIEW_MODAL.CAMPAIGN_LABEL' | translate }}</glxy-label>
  <input
    matInput
    type="text"
    placeholder="{{ 'CAMPAIGN_PREVIEW_MODAL.SEARCH_CAMPAIGNS' | translate }}"
    [matAutocomplete]="auto"
    [formControl]="myControl"
    (input)="search($event.target.value)"
    required
  />
  <mat-autocomplete
    #auto="matAutocomplete"
    [displayWith]="displayFn"
    (optionSelected)="selectCampaign($event.option?.value)"
  >
    <mat-option *ngFor="let campaign of campaignOptions$ | async" [value]="campaign?.value">
      {{ campaign.label }}
    </mat-option>
  </mat-autocomplete>
  <glxy-error *ngIf="myControl.touched && myControl?.errors !== null">
    {{ 'CAMPAIGN_PREVIEW_MODAL.REQUIRED_FIELD' | translate }}
  </glxy-error>
</glxy-form-field>
