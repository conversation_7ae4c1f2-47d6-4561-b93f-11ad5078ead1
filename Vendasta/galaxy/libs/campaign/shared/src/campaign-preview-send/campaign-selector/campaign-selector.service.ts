import { Injectable } from '@angular/core';
import {
  CampaignService,
  GetterCampaignDataInterface,
  ListCampaignsV2ResponseInterface,
  SenderInterface,
  SortDirection,
  SortField,
  SortOptions,
  Statuses,
} from '@vendasta/campaigns';
import { filter, map, take } from 'rxjs/operators';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class CampaignSelectorService {
  private results$$ = new BehaviorSubject<GetterCampaignDataInterface[]>([]);
  public readonly results$: Observable<GetterCampaignDataInterface[]> = this.results$$.pipe(filter((r) => !!r));

  constructor(private readonly client: CampaignService) {}

  public get(campaignID: string): Observable<GetterCampaignDataInterface> {
    return this.client.get(campaignID);
  }

  public search(sender: SenderInterface, campaignName: string): void {
    this.client
      .listCampaignsV2(
        sender,
        [Statuses.STATUSES_PUBLISHED, Statuses.STATUSES_ACTIVE],
        campaignName,
        { pageSize: 10 },
        { field: SortField.SORT_FIELDS_UPDATED_AT, direction: SortDirection.SORT_ORDER_DESCENDING } as SortOptions,
      )
      .pipe(
        take(1),
        map((resp: ListCampaignsV2ResponseInterface) => resp.campaigns),
      )
      .subscribe((campaigns) => this.results$$.next(campaigns || []));
  }
}
