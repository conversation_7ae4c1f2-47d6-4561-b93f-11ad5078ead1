import { Component, EventEmitter, Inject, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatAutocomplete, MatAutocompleteTrigger, MatOption } from '@angular/material/autocomplete';
import {
  AsyncValidatorFn,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatInput } from '@angular/material/input';
import { CampaignSelectorService } from './campaign-selector.service';
import { GetterCampaignDataInterface } from '@vendasta/campaigns';
import { Observable } from 'rxjs';
import { GalaxyInputModule, SelectInputOption } from '@vendasta/galaxy/input';
import { map, take } from 'rxjs/operators';

import { TranslateModule } from '@ngx-translate/core';
import { CampaignConfig } from '@galaxy/campaign/dependencies';
import { CONFIG_TOKEN } from '../../../../shared/src/tokens';

@Component({
  selector: 'campaign-selector',
  imports: [
    CommonModule,
    GalaxyFormFieldModule,
    MatOption,
    FormsModule,
    MatInput,
    MatAutocomplete,
    ReactiveFormsModule,
    GalaxyInputModule,
    TranslateModule,
    MatAutocompleteTrigger,
  ],
  templateUrl: './campaign-selector.component.html',
  styleUrl: './campaign-selector.component.scss',
})
export class CampaignSelectorComponent implements OnInit {
  public readonly myControl = new FormControl<GetterCampaignDataInterface | null>(
    {},
    [Validators.required],
    [campaignValidator()],
  );
  public campaignOptions$: Observable<SelectInputOption[]> = this.campaignSelectorService.results$.pipe(
    map((options) =>
      options.map((o) => {
        return { label: o.name, value: o } as SelectInputOption;
      }),
    ),
  );

  @Output() readonly selectedCampaign = new EventEmitter<GetterCampaignDataInterface>();

  constructor(
    private readonly campaignSelectorService: CampaignSelectorService,
    @Inject(CONFIG_TOKEN) private readonly config: CampaignConfig,
  ) {}

  ngOnInit(): void {
    this.search('');
  }

  search(term: string) {
    this.config.sender$.pipe(take(1)).subscribe((sender) => this.campaignSelectorService.search(sender, term));
  }

  selectCampaign(campaign: GetterCampaignDataInterface) {
    this.myControl.setValue(campaign);
    this.selectedCampaign.emit(campaign);
  }

  displayFn(campaign: GetterCampaignDataInterface): string {
    return campaign.name || '';
  }
}

function campaignValidator(): AsyncValidatorFn {
  return async (
    campaignControl: FormControl<GetterCampaignDataInterface | string>,
  ): Promise<ValidationErrors | null> => {
    const campaign = campaignControl.value;
    if (typeof campaign === 'string') {
      return { invalidCampaign: true };
    }
    if (campaign?.campaignId) {
      return null;
    }
    return { invalidCampaign: true };
  };
}
