@use 'design-tokens' as *;

.campaign-next-button {
  display: flex;
  justify-self: right;
}
.campaign-back-button {
  margin-right: 0.5em;
}

.campaign-button-container {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-top: 0;
}

.campaign-next-button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.contact-dialog-footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.search-bar {
  z-index: 1;
  position: sticky;
  top: 0;
  background-color: $card-background-color;
  border-bottom: 1px solid $glxy-grey-300;
  padding-top: $spacing-3;
  padding-bottom: $spacing-3;
  margin-bottom: 0;
}
.mat-mdc-dialog-content {
  padding: 0 $spacing-3 $spacing-3 $spacing-3 !important;
  min-height: 45vh;
  max-height: 45vh;
}
.table-controls-row {
  display: block;
}
.recipients-wrapper {
  display: flex;
}
.info_outline {
  align-self: center;
  font-size: $font-preset-3-size;
  height: $font-preset-3-size;
  width: $font-preset-3-size;
  margin-left: $spacing-1;
}
.glxy-alert {
  margin-top: $spacing-2;
}
