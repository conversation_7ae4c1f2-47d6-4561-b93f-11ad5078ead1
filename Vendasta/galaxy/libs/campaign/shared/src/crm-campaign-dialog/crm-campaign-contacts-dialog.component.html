<h2 mat-dialog-title>
  {{
    'CAMPAIGNS.CONTACTS.MODAL.SELECT_CAMPAIGN'
      | translate: { recipientCount: this.data.useSelectAll ? this.data.totalContacts : this.contacts?.length }
  }}
</h2>

<mat-dialog-content>
  <glxy-alert type="warning" *ngIf="this.missingEmailAddresses.length > 0">
    <strong *ngIf="!sendToAll">
      {{
        'CAMPAIGNS.CONTACTS.MODAL.MISSING_EMAIL_ADDRESS'
          | translate: { contactCount: this.missingEmailAddresses.length }
      }}
    </strong>
    {{ 'CAMPAIGNS.CONTACTS.MODAL.MISSING_EMAIL' | translate }}
  </glxy-alert>
  <!--  <va-filter-container [showToolbar]="true" [placeholder]="placeholder" (searchTermChanged)="searchTerm$$.next($event)">-->
  <ng-container>
    <glxy-form-field prefixIcon="search" [showLabel]="false" class="search-bar">
      <input type="search" placeholder="{{ placeholder }}" matInput [formControl]="searchControl" />
    </glxy-form-field>
    <div content>
      <glxy-form-field class="campaigns-button-group">
        <mat-radio-group>
          <div *ngFor="let campaign of campaigns$ | async">
            <div class="list-row">
              <mat-radio-button [value]="campaign.id" (change)="onItemChange(campaign)">
                {{ campaign.title }}
                <extended>
                  {{ 'CAMPAIGNS.CONTACTS.MODAL.UPDATED' | translate }}
                  {{ campaign.updated | date: 'mediumDate' }}
                </extended>
              </mat-radio-button>
            </div>
          </div>
        </mat-radio-group>
      </glxy-form-field>
      <glxy-empty-state *ngIf="(campaigns$ | async)?.length === 0 && (loading$$ | async) === false" [size]="'small'">
        <glxy-empty-state-hero>
          <mat-icon>account_circle</mat-icon>
        </glxy-empty-state-hero>
        <p>{{ 'CAMPAIGNS.CONTACTS.MODAL.NO_CAMPAIGNS' | translate }}</p>
      </glxy-empty-state>
      <div>
        <ng-container>
          <glxy-infinite-scroll-trigger
            (isVisible)="loadNext()"
            [visiblilityMargin]="0"
            [minHeight]="0"
          ></glxy-infinite-scroll-trigger>
        </ng-container>
      </div>
    </div>
  </ng-container>
  <!--  </va-filter-container>-->
</mat-dialog-content>
<mat-dialog-actions class="mat-dialog-actions-contact-dialog">
  <ng-container class="contact-dialog-footer">
    <div class="contact-dialog-footer">
      <div class="recipients-wrapper">
        <span>
          <p>
            {{
              'CAMPAIGNS.CONTACTS.MODAL.RECIPIENT_COUNT'
                | translate
                  : { recipientCount: this.data.useSelectAll ? this.data.totalContacts : this.contacts?.length }
            }}
          </p>
        </span>
        <mat-icon [glxyTooltip]="tooltipText" class="info_outline">info_outline</mat-icon>
      </div>
      <div class="campaign-next-button-container">
        <button mat-stroked-button class="campaign-back-button" color="primary" (click)="close()">
          {{ 'CAMPAIGNS.CONTACTS.MODAL.CANCEL' | translate }}
        </button>
        <button
          mat-flat-button
          class="campaign-next-button"
          color="primary"
          (click)="sendCampaign()"
          [disabled]="loading$$ | async"
        >
          <glxy-button-loading-indicator [isLoading]="loading$$ | async">
            {{ 'CAMPAIGNS.CONTACTS.MODAL.PUBLISH' | translate }}
          </glxy-button-loading-indicator>
        </button>
      </div>
    </div>
  </ng-container>
</mat-dialog-actions>
