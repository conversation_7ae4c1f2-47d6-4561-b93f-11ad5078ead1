import { CommonModule } from '@angular/common';
import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { CrmFieldService, StandardExternalIds } from '@galaxy/crm/static';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import {
  CampaignService as CS,
  ContactType,
  RecipientCampaignService,
  RecipientType,
  Sender,
  SenderType,
  SortDirection,
  SortField,
  SortOptions,
  Statuses,
} from '@vendasta/campaigns';
import { SenderInterface } from '@vendasta/email';
import { VaFormsModule } from '@vendasta/forms';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Row } from '@vendasta/galaxy/table';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { Scrollable } from '@vendasta/rx-utils';
import { VaFilterModule } from '@vendasta/uikit';
import { BehaviorSubject, forkJoin, of, Subscription } from 'rxjs';
import { catchError, debounceTime, distinctUntilChanged, map, startWith } from 'rxjs/operators';
import { GalaxyFilterInterface } from '@vendasta/galaxy/filter/chips';
import { Filter, FilterGroupInterface, FilterGroupOperator, FilterOperator, FilterValue } from '@vendasta/crm';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

export interface dialogData {
  contacts: Row[];
  senderId: string;
  senderType: SenderType;
  filters?: GalaxyFilterInterface[];
  search?: string;
  useSelectAll?: boolean;
  totalContacts?: number;
}

export interface Contact {
  id: string;
  name: string;
  email: string;
  disabled?: boolean;
  phone?: string;
  error?: boolean;
}

export interface Campaign {
  id: string;
  title: string;
  updated: string;
}

@Component({
  imports: [
    CommonModule,
    MatButtonModule,
    TranslateModule,
    GalaxyLoadingSpinnerModule,
    MatDialogModule,
    GalaxyEmptyStateModule,
    MatIconModule,
    GalaxyFormFieldModule,
    MatRadioModule,
    FormsModule,
    VaFormsModule,
    MatInputModule,
    GalaxyButtonLoadingIndicatorModule,
    MatProgressSpinnerModule,
    VaFilterModule,
    GalaxyInfiniteScrollTriggerModule,
    ReactiveFormsModule,
    GalaxyTooltipModule,
    GalaxyAlertModule,
  ],
  templateUrl: './crm-campaign-contacts-dialog.component.html',
  styleUrls: ['./crm-campaign-contacts-dialog.component.scss'],
  providers: [RecipientCampaignService, CrmFieldService],
})
export class CrmCampaignContactsDialogComponent implements OnInit, OnDestroy {
  constructor(
    private readonly translate: TranslateService,
    @Inject(MAT_DIALOG_DATA) readonly data: dialogData,
    readonly ref: MatDialogRef<CrmCampaignContactsDialogComponent>,
    private snackbarService: SnackbarService,
    private readonly recipientCampaignService: RecipientCampaignService,
    private readonly CampaignService: CS,
    private crmFieldService: CrmFieldService,
    private posthogService: ProductAnalyticsService,
  ) {}

  missingEmailAddresses: string[] = [];
  searchControl = new UntypedFormControl();

  // TODO: these initializations are wrong. campaigns$$  is never written to with the campaing values
  campaigns$$ = new BehaviorSubject<Campaign[]>([] as Campaign[]);
  campaigns$ = this.campaigns$$.asObservable();

  criteria$ = this.searchControl.valueChanges.pipe(startWith(''), distinctUntilChanged(), debounceTime(300));
  contacts: (Contact | null)[] = [];
  selectedCampaign: Campaign;
  Scrollable: Scrollable<Campaign, string>;
  emailFieldID = this.crmFieldService.getFieldId(StandardExternalIds.Email);
  phoneFieldID = this.crmFieldService.getFieldId(StandardExternalIds.PhoneNumber);
  loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  subscriptions: Subscription[] = [];
  placeholder = '';
  tooltipText = '';
  sendToAll = false;

  ngOnInit(): void {
    this.sendToAll = this.data.useSelectAll ?? false;
    this.contacts = this.data.contacts
      .map((row) => {
        if (row['data'][this.emailFieldID] != undefined) {
          const contact = {
            id: row.id,
            name: row['data']['fullName']['value'],
            email: row['data'][this.emailFieldID]['value'],
          } as Contact;
          if (row['data'][this.phoneFieldID] != undefined) {
            contact.phone = row['data'][this.phoneFieldID]['value'];
          }
          return contact;
        } else {
          const missingEmailContact = row['data']['fullName']['value'];
          this.missingEmailAddresses.push(missingEmailContact);
        }
        return null;
      })
      .filter((contact) => contact !== null);

    this.Scrollable = this.getScrollableList(this.data.senderId, this.data.senderType);
    this.campaigns$ = this.Scrollable.items$;
    this.placeholder = this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.SEARCH_PLACEHOLDER');
    this.tooltipText = this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.RECIPIENTS_TOOLTIP');
  }

  close() {
    this.ref.close();
  }

  loadNext() {
    this.Scrollable.loadMore();
  }

  onItemChange(campaign: Campaign) {
    this.selectedCampaign = campaign;
  }

  sendCampaign() {
    if (this.selectedCampaign === undefined) {
      this.snackbarService.openErrorSnack('CAMPAIGNS.CONTACTS.MODAL.ERRORS.NO_CAMPAIGN_SELECTED');
      return;
    }
    if (this.sendToAll) {
      this.bulkAddContactsToCampaign();
    } else {
      this.posthogService.trackEvent('user-clicked-crm-publish-now-multi', 'send-campaign-workflow', 'click');
      const sender = {
        type: this.data.senderType,
        id: this.data.senderId,
      } as Sender;
      const recipients = this.contacts.map((contact) => {
        return {
          id: contact?.id,
          type: RecipientType.RECIPIENT_TYPE_CRM_CONTACT,
          contact: {
            type: ContactType.CONTACT_TYPE_EMAIL,
            value: contact?.email,
          },
          emailAddress: contact?.email,
          phoneNumber: contact?.phone,
        };
      });
      this.loading$$.next(true);
      this.subscriptions.push(
        forkJoin(
          recipients.map((recipient) =>
            this.recipientCampaignService
              .addToCampaign(this.selectedCampaign.id, sender, recipient, new Date(Date.now()), true)
              .pipe(
                map(() => ''),
                catchError(() => {
                  return of(recipient.contact?.value || recipient.emailAddress);
                }),
              ),
          ),
        ).subscribe((resp: string[]) => {
          const failedAddresses = resp.filter((address) => address !== '');
          this.loading$$.next(false);
          if (failedAddresses?.length > 0) {
            this.snackbarService.openErrorSnack(
              this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.ERRORS.FAILED_ADDRESS_ERROR') +
                failedAddresses.join(', ') +
                '.',
            );
          } else {
            this.snackbarService.openSuccessSnack(
              this.contacts?.length +
                ' ' +
                this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.SUCCESS') +
                ' ' +
                this.selectedCampaign.title,
            );
            this.close();
          }
        }),
      );
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub: Subscription) => sub.unsubscribe());
  }

  getScrollableList(senderId: string, senderType: SenderType) {
    return new Scrollable<Campaign, string>(this.criteria$, (criteria, cursor) => {
      return this.loadMore(criteria, cursor, senderId, senderType);
    });
  }

  loadMore(criteria: string, cursor: string, senderId: string, senderType: SenderType) {
    this.loading$$.next(true);
    return this.CampaignService.listCampaignsV2(
      { type: senderType, id: senderId } as SenderInterface,
      [Statuses.STATUSES_ACTIVE, Statuses.STATUSES_PUBLISHED],
      criteria,
      { cursor: cursor, pageSize: 10 },
      { field: SortField.SORT_FIELDS_UPDATED_AT, direction: SortDirection.SORT_ORDER_DESCENDING } as SortOptions,
    ).pipe(
      map((response) => {
        this.loading$$.next(false);
        return {
          items: response.campaigns.map((campaign) => {
            return {
              id: campaign.campaignId,
              title: campaign.name,
              updated: campaign.updated.toISOString(),
            } as Campaign;
          }),
          cursor: response.metadata?.nextCursor ?? null,
          hasMore: response.metadata?.hasMore ?? false,
        };
      }),
      catchError(() => of({ items: [] as Array<Campaign>, cursor: '', hasMore: false })),
    );
  }

  bulkAddContactsToCampaign(): void {
    this.posthogService.trackEvent('user-clicked-crm-publish-now-bulk', 'send-campaign-workflow', 'click');
    const operator: FilterGroupOperator = FilterGroupOperator.FILTER_GROUP_OPERATOR_AND;
    let filterGroup: FilterGroupInterface | undefined = undefined;
    const contactFilters: Filter[] = [];
    if (this.data.filters.length > 0) {
      this.data.filters.forEach((filter) => {
        const temp = new Filter();
        temp.fieldId = filter.fieldId;
        temp.operator = filter.operator.valueOf() as FilterOperator;
        temp.values = filter.values?.valueOf() as FilterValue[];
        contactFilters.push(temp);
      });
      filterGroup = {
        operator: operator,
        filters: contactFilters,
      };
    }
    const sender = {
      type: this.data.senderType,
      id: this.data.senderId,
    };
    this.loading$$.next(true);
    this.recipientCampaignService
      .bulkAddToCampaign(this.selectedCampaign.id, sender, new Date(Date.now()), true, filterGroup, this.data.search)
      .pipe(
        catchError(() => {
          this.loading$$.next(false);
          this.snackbarService.openErrorSnack(this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.ERRORS.GENERIC_ERROR'));
          return of(null);
        }),
      )
      .subscribe(() => {
        this.loading$$.next(false);
        this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.ERRORS.GENERIC_ERROR');
        this.snackbarService.openSuccessSnack(
          this.translate.instant('CAMPAIGNS.CONTACTS.MODAL.SELECT_ALL_SUCCESS', {
            totalObjects: this.data.totalContacts,
            campaignTitle: this.selectedCampaign.title,
          }),
        );
        this.ref.close();
      });
  }
}
