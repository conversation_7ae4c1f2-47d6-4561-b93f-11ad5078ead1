# campaign
This library holds campaign related pages.

## How to use
  To provide the campaign pages in your apps router include this child path.
```
children:[
    {
    path: 'campaigns',
    loadChildren: () => import('@galaxy/campaign').then((m) => m.CampaignModule),
    data: {
      pageId: PageId.campaigns,
      pageOptions: { [OPTS.displayFullScreen]: true, [OPTS.hideNavToolbar]: true },
    },
    providers: [
      {
        provide: CONFIG_TOKEN,
        useFactory: CampaignConfigFactory,
      },
      {
        provide: PREVIEW_DATA_TOKEN,
        useFactory: CampaignPreviewDataFactory,
      },
    ],
  }]
```
To load the campaigns routing module, use this code in your apps routing module. 

## Injecting Campaign Config
The campaign module requires a campaign config provider and a Campaign Preview provider.

A campaign config service satisfies this [interface](https://github.com/vendasta/galaxy/blob/b0962b46d83d3fe3aa7f9f52d1faa38e12de0584/libs/campaign/src/lib/dependencies/src/config.ts#L4)https://github.com/vendasta/galaxy/blob/b0962b46d83d3fe3aa7f9f52d1faa38e12de0584/libs/campaign/src/lib/dependencies/src/config.ts#L4.

A campaign preview provider satfies this interface.
//todo document this

## Running unit tests

Run `nx test campaign` to execute the unit tests.
