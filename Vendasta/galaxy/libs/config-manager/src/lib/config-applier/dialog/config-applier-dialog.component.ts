import { Component, computed, inject, OnInit, signal, Signal } from '@angular/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import {
  AbstractControl,
  FormControl,
  FormControlStatus,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { FormGroup } from '@angular/forms';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { CommonModule } from '@angular/common';
import { debounceTime, filter, map, shareReplay, startWith, take, tap } from 'rxjs/operators';
import { AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { combineLatest, lastValueFrom, Observable, of, switchMap } from 'rxjs';
import { MultiLocationService } from '@vendasta/multi-location';
import { PARTNER_ID_TOKEN } from '../../tokens';
import { MatButtonModule } from '@angular/material/button';
import {
  ApplyConfigurationRequest,
  ConfigurationManagementApiService,
  ConfigurationOwner,
  ConfigurationTargetContainer,
  ListConfigurationsRequest,
  ObjectApplicationSelection,
  ObjectApplicationStrategy,
  OwnerNamespaceType,
  PagedRequestOptions,
  TargetContainerNamespaceType,
} from '@vendasta/configuration-management';
import { ConfigOption } from '../../config-editor/config-options/options.component';
import { ConfigOptionsComponent } from '../../config-editor/config-options/options.component';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { IAMService, SortDirection, UserSortField } from '@vendasta/iam';
import { ExtendedConfiguration } from '../../config-list/config-list.component';
import { typedObjectContainersToConfigOptions } from '../../utils';
import { AlertComponent } from '@vendasta/galaxy/alert/src/alert.component';
import { MatIconModule } from '@angular/material/icon';

interface TargetContainer {
  id: string;
  name: string;
}

interface ServiceAccountUser {
  userId: string;
  name: string;
}

interface InputData {
  targetContainers?: TargetContainer[];
  configurationId?: string;
  configOptions?: ConfigOption[];
}

@Component({
  selector: 'config-applier-dialog',
  templateUrl: './config-applier-dialog.component.html',
  styleUrls: ['./config-applier-dialog.component.scss'],
  standalone: true,
  imports: [
    TranslateModule,
    MatDialogModule,
    MatSelectModule,
    MatInputModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    CommonModule,
    MatButtonModule,
    ConfigOptionsComponent,
    AlertComponent,
    MatIconModule,
  ],
})
export class ConfigApplierDialogComponent implements OnInit {
  private readonly dialogRef = inject(MatDialogRef<ConfigApplierDialogComponent>);
  private readonly accountGroupService = inject(AccountGroupService);
  private readonly multiLocationService = inject(MultiLocationService);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly configService = inject(ConfigurationManagementApiService);
  private readonly snackBarService = inject(SnackbarService);
  private readonly iamService = inject(IAMService);
  private readonly translateService = inject(TranslateService);

  currentConfigOptions = signal<ConfigOption[]>([]);
  private currentConfigId = '';

  serviceAccountSearchResults$: Observable<ServiceAccountUser[]> = of([]);
  private selectedServiceAccount: ServiceAccountUser | undefined;

  targetContainerSearchResults$: Observable<TargetContainer[]> = of([]);
  private selectedTargetContainer: TargetContainer | undefined;

  configSearchResults$: Observable<ExtendedConfiguration[]> = of([]);

  isApplyEnabled = computed(() => {
    const hasObject = this.currentConfigOptions().some((opt) => opt.subOptions?.some((subOpt) => subOpt.enabled));
    return this.formStatusSignal() === 'VALID' && hasObject;
  });

  data: InputData = inject(MAT_DIALOG_DATA);

  hasTarget: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
    const type = control.get('targetContainerType')?.value;
    const term = control.get('targetContainer')?.value;

    return (type && term) || (this.data.targetContainers && this.data.targetContainers.length > 0)
      ? null
      : { noTarget: true };
  };

  form = new FormGroup(
    {
      targetContainerType: new FormControl<string>(''),
      targetContainer: new FormControl<string>({ value: '', disabled: true }),
      configSearchTerm: new FormControl<string>(''),
      serviceAccountSelected: new FormControl<string>('', [Validators.required]),
    },
    { validators: [this.hasTarget] },
  );
  formStatusSignal: Signal<FormControlStatus | undefined>;

  constructor() {
    this.currentConfigOptions.set(this.data.configOptions ?? []);
    this.formStatusSignal = toSignal(this.form.statusChanges);
  }

  ngOnInit(): void {
    this.currentConfigId = this.data.configurationId ?? '';
    if (this.data.targetContainers && this.data.targetContainers.length === 0) {
      this.form.disable();
      return;
    }

    const serviceAccounts$ = this.partnerId$.pipe(
      switchMap((partnerId) =>
        this.iamService.listUsers(
          partnerId,
          '',
          '',
          999,
          ['partner_service_account'],
          [],
          [
            { direction: SortDirection.SORT_DIRECTION_ASCENDING, field: UserSortField.USER_SORT_FIELD_FIRST_NAME },
            { direction: SortDirection.SORT_DIRECTION_ASCENDING, field: UserSortField.USER_SORT_FIELD_LAST_NAME },
          ],
        ),
      ),
      map((resp) => resp?.users || []),
      map((users) => {
        return users.map((user) => {
          const namePart = `${user.firstName || ''} ${user.lastName || ''}`.trim();
          const emailIdPart = user.email.split('@')[0];
          return {
            userId: user.userId,
            name: `${namePart} (${emailIdPart})`,
          };
        });
      }),
    );

    const serviceAccountSearchTerm$ = (this.form.get('serviceAccountSelected')?.valueChanges ?? of('')).pipe(
      startWith(''),
      debounceTime(300),
      map((searchTerm) => searchTerm ?? ''),
    );

    this.serviceAccountSearchResults$ = combineLatest([serviceAccounts$, serviceAccountSearchTerm$]).pipe(
      map(([serviceAccounts, searchTerm]) => {
        return searchTerm
          ? serviceAccounts.filter((serviceAccount) =>
              serviceAccount.name.toLowerCase().includes(searchTerm.toLowerCase()),
            )
          : serviceAccounts;
      }),
    );

    const targetContainerType$ = this.form.get('targetContainerType')!.valueChanges.pipe(
      startWith(''),
      map((value) => (value ? value : '')),
      tap((value) => {
        value ? this.form.get('targetContainer')?.enable() : this.form.get('targetContainer')?.disable();
        this.form.get('targetContainer')?.reset();
      }),
      shareReplay(1),
    );

    const targetContainerSearchTerm$ = this.form.get('targetContainer')!.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      map((value) => value ?? ''),
      shareReplay(1),
    );

    const filteredAccountGroups$: Observable<TargetContainer[]> = combineLatest([
      targetContainerSearchTerm$,
      targetContainerType$,
    ]).pipe(
      filter(([, type]) => type === 'account-group'),
      switchMap(([searchTerm]) =>
        this.accountGroupService.lookup(
          new ProjectionFilter().enableAll(),
          undefined,
          searchTerm,
          undefined,
          10,
          undefined,
        ),
      ),
      filter((accountGroups) => !!accountGroups),
      map((accountGroups) =>
        accountGroups.map((accountGroup) => {
          return {
            id: accountGroup.accountGroupId,
            name: accountGroup.napData.companyName,
          };
        }),
      ),
    );

    const brands$: Observable<TargetContainer[]> = this.partnerId$.pipe(
      switchMap((partnerId) => this.multiLocationService.listBrandsMetadata(partnerId)),
      map((brands) =>
        brands.map((brand) => {
          return {
            id: brand.groupId,
            name: brand.name,
          };
        }),
      ),
    );

    const filteredBrands$: Observable<TargetContainer[]> = combineLatest([
      brands$,
      targetContainerSearchTerm$,
      targetContainerType$,
    ]).pipe(
      filter(([, , type]) => type === 'group') &&
        map(([brands, searchTerm]) =>
          searchTerm ? brands.filter((brand) => brand.name.toLowerCase().includes(searchTerm.toLowerCase())) : brands,
        ),
    );

    this.targetContainerSearchResults$ = combineLatest([
      targetContainerType$,
      filteredAccountGroups$,
      filteredBrands$,
    ]).pipe(map(([type, accounts, brands]) => (type === 'account-group' ? accounts : brands)));

    if (!this.data.configurationId && !this.data.configOptions) {
      const configs$ = this.partnerId$.pipe(
        switchMap((partnerId) =>
          this.configService.listConfigurations(
            new ListConfigurationsRequest({
              configurationOwner: new ConfigurationOwner({
                ownerNamespace: partnerId,
                ownerNamespaceType: OwnerNamespaceType.OWNER_NAMESPACE_PARTNER,
              }),
              pagingOptions: new PagedRequestOptions({
                pageSize: 999,
              }),
            }),
          ),
        ),
        map((resp) => {
          return resp.configuration;
        }),
        switchMap((configs) => {
          const agIds = configs.map((config) => config.configurationVersion.sourceNamespace);
          return this.accountGroupService.getMulti(agIds, new ProjectionFilter({ napData: true })).pipe(
            map((accountGroups) => {
              const extendedConfigs: ExtendedConfiguration[] = [];
              configs.forEach((config, index) => {
                const extendedConfiguration = {
                  ...config,
                  configurationVersion: {
                    ...config.configurationVersion,
                    sourceNamespaceObject: accountGroups[index],
                  },
                } as ExtendedConfiguration;
                extendedConfigs.push(extendedConfiguration);
              });
              return extendedConfigs;
            }),
          );
        }),
      );

      const configurationSearchTerm$ = (this.form.get('configSearchTerm')?.valueChanges ?? of('')).pipe(
        startWith(''),
        debounceTime(300),
      );

      this.configSearchResults$ = combineLatest([configs$, configurationSearchTerm$]).pipe(
        map(([configurations, searchTerm]) => {
          return searchTerm && typeof searchTerm === 'string'
            ? configurations.filter((configuration) =>
                configuration.name.toLowerCase().includes(searchTerm.toLowerCase()),
              )
            : configurations;
        }),
      );
    }
  }

  onOptionsChange(options: ConfigOption[]) {
    this.currentConfigOptions.set(options);
  }

  async apply() {
    const formValue = this.form.value;
    const objectApplicationSelections: ObjectApplicationSelection[] = [];
    this.currentConfigOptions().forEach((opt) => {
      opt.subOptions?.forEach((subOpt) => {
        subOpt.enabled &&
          objectApplicationSelections.push(
            new ObjectApplicationSelection({
              configurationObjectId: subOpt.id,
              strategy: ObjectApplicationStrategy.OBJECT_APPLICATION_STRATEGY_SKIP_EXISTING,
            }),
          );
      });
    });

    const targetContainers: ConfigurationTargetContainer[] = this.data.targetContainers
      ? this.data.targetContainers.map(
          (targetContainer: TargetContainer) =>
            new ConfigurationTargetContainer({
              targetContainerNamespace: targetContainer.id,
              targetContainerNamespaceType: TargetContainerNamespaceType.TARGET_CONTAINER_NAMESPACE_ACCOUNT_GROUP,
            }),
        )
      : [
          new ConfigurationTargetContainer({
            targetContainerNamespace: this.selectedTargetContainer?.id,
            targetContainerNamespaceType: this.targetContainerTypeToEnum(formValue.targetContainerType!),
          }),
        ];

    try {
      await lastValueFrom(
        this.configService
          .applyConfiguration(
            new ApplyConfigurationRequest({
              configurationId: this.currentConfigId,
              configurationTargetContainers: targetContainers,
              objectApplicationSelections: objectApplicationSelections,
              runUsingServiceAccountUserId: this.selectedServiceAccount?.userId,
            }),
          )
          .pipe(take(1)),
      );

      this.snackBarService.openSuccessSnack(this.translateService.instant('CONFIG_APPLIER.SUBMISSION_SUCCESS'));
      this.dialogRef.close();
    } catch (e) {
      this.snackBarService.openErrorSnack('CONFIG_APPLIER.SUBMISSION_ERROR');
    }
  }

  targetContainerTypeToEnum(type?: string): TargetContainerNamespaceType {
    if (!type) {
      return TargetContainerNamespaceType.TARGET_CONTAINER_NAMESPACE_UNSPECIFIED;
    }
    switch (type) {
      case 'account-group':
        return TargetContainerNamespaceType.TARGET_CONTAINER_NAMESPACE_ACCOUNT_GROUP;
      case 'group':
        return TargetContainerNamespaceType.TARGET_CONTAINER_NAMESPACE_GROUP;
      default:
        return TargetContainerNamespaceType.TARGET_CONTAINER_NAMESPACE_UNSPECIFIED;
    }
  }

  onServiceAccountSelect(event: MatAutocompleteSelectedEvent) {
    const value: ServiceAccountUser = event.option.value;
    this.selectedServiceAccount = value;
    this.form.get('serviceAccountSelected')?.setValue(value.name);
  }

  onTargetContainerSelect(event: MatAutocompleteSelectedEvent) {
    const value: TargetContainer = event.option.value;
    this.selectedTargetContainer = value;
    this.form.get('targetContainer')?.setValue(value.name);
  }

  onConfigSelect(event: MatAutocompleteSelectedEvent) {
    const value: ExtendedConfiguration = event.option.value;
    this.currentConfigId = value.configurationId;
    this.currentConfigOptions.set(typedObjectContainersToConfigOptions(value.configurationVersion.objectTypes, true));
    this.form.get('configSearchTerm')?.setValue(value.name);
  }
}
