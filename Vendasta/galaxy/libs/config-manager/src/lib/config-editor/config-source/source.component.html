<div class="source-container">
  <form [formGroup]="form">
    <div class="field-container">
      <glxy-form-field [required]="true">
        <glxy-label>{{ 'CONFIG_EDITOR.NAME' | translate }}</glxy-label>
        <input matInput formControlName="name" placeholder="Enter config name" />
        <glxy-error *ngIf="form.get('name')?.hasError('required') && form.get('name')?.dirty">
          {{ 'CONFIG_EDITOR.ERROR.NAME_REQUIRED' | translate }}
        </glxy-error>
      </glxy-form-field>
    </div>

    <div class="field-container">
      <glxy-form-field [required]="true">
        <glxy-label>{{ 'CONFIG_EDITOR.SOURCE_ACCOUNT_GROUP' | translate }}</glxy-label>
        <input
          type="text"
          matInput
          formControlName="accountGroup"
          [matAutocomplete]="auto"
          placeholder="Start typing to search..."
        />
        <mat-icon class="selector-arrow cursor-pointer" matSuffix>arrow_drop_down</mat-icon>
        <glxy-error *ngIf="form.get('accountGroup')?.hasError('required') && form.get('accountGroup')?.dirty">
          {{ 'CONFIG_EDITOR.ERROR.ACCOUNT_GROUP_REQUIRED' | translate }}
        </glxy-error>
        <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn">
          <mat-option *ngIf="isLoading" class="loading-option">
            <mat-spinner diameter="20"></mat-spinner>
            Loading...
          </mat-option>
          <ng-container *ngIf="!isLoading && (filteredAccountGroups$ | async) as options">
            <mat-option *ngFor="let option of options" [value]="option">
              {{ option.napData.companyName }} ({{ option.accountGroupId }})
            </mat-option>
            <mat-option *ngIf="options.length === 0" disabled>
              {{ 'CONFIG_EDITOR.ERROR.NO_MATCHING_ACCOUNT_GROUPS_FOUND' | translate }}
            </mat-option>
          </ng-container>
        </mat-autocomplete>
      </glxy-form-field>
    </div>
  </form>
</div>
