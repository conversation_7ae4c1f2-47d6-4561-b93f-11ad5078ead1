@use 'design-tokens' as *;

.source-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem; // This is the common gap size used in other forms
  padding: $spacing-3;
}

.field-container {
  width: 100%;
  margin-bottom: $spacing-3;
}

.mat-mdc-form-field {
  width: 100%;
}

.full-width {
  width: 100%;
}

.virtual-scroll-viewport {
  height: 256px;
  width: 100%;
}

.loading-option {
  display: flex;
  align-items: center;
  gap: $spacing-2;
}

::ng-deep .account-groups-autocomplete {
  max-height: 300px;
}

.selector-arrow {
  cursor: pointer;
}
