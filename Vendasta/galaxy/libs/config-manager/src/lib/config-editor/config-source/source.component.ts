import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Observable, of } from 'rxjs';
import { debounceTime, map, startWith, switchMap, tap } from 'rxjs/operators';
import { AccountGroup, AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { ConfigManagerI18nModule } from '../../assets/i18n/config-manager-i18n.module';

@Component({
  selector: 'config-source',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatAutocompleteModule,
    MatIconModule,
    ScrollingModule,
    MatProgressSpinnerModule,
    TranslateModule,
    ConfigManagerI18nModule,
  ],
  templateUrl: './source.component.html',
  styleUrls: ['./source.component.scss'],
})
export class ConfigSourceComponent implements OnInit {
  @Output() dataChange = new EventEmitter<{ data: any; valid: boolean }>();

  form = new FormGroup({
    name: new FormControl('', [Validators.required]),
    accountGroup: new FormControl<string | AccountGroup>('', [Validators.required]),
  });

  searchCtrl = this.form.get('accountGroup') as FormControl;
  filteredAccountGroups$: Observable<AccountGroup[]> = of([]);
  accountGroupService = inject(AccountGroupService);
  isLoading = false;

  private fetchAccountGroups(searchTerm: string): Observable<AccountGroup[]> {
    if (!searchTerm) {
      this.isLoading = false;
      return of([]);
    }

    return this.accountGroupService.lookup(
      new ProjectionFilter().enableAll(),
      undefined,
      searchTerm,
      undefined,
      10,
      undefined,
    );
  }

  ngOnInit() {
    this.filteredAccountGroups$ = this.searchCtrl.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      map((value) => (typeof value === 'string' ? value : value?.accountGroupId || '')),
      tap(() => (this.isLoading = true)),
      switchMap((searchTerm) => {
        return this.fetchAccountGroups(searchTerm).pipe(
          tap(() => {
            this.isLoading = false;
          }),
        );
      }),
    );

    this.form.valueChanges.subscribe(() => {
      this.emitChanges();
    });
  }

  displayFn(accountGroup: AccountGroup): string {
    return accountGroup ? `${accountGroup.napData.companyName} (${accountGroup.accountGroupId})` : '';
  }

  private emitChanges(): void {
    const formValue = this.form.value;
    this.dataChange.emit({
      data: {
        name: formValue.name,
        accountGroup: formValue.accountGroup,
        accountGroupId:
          formValue.accountGroup && typeof formValue.accountGroup !== 'string'
            ? formValue.accountGroup.accountGroupId
            : null,
      },
      valid: this.form.valid,
    });
  }
}
