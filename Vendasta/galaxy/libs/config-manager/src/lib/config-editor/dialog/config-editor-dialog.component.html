<div class="dialog-container">
  <h2 mat-dialog-title>{{ 'CONFIG_EDITOR.CREATE_CONFIG' | translate }}</h2>
  <mat-dialog-content>
    <mat-stepper linear>
      <mat-step label="Source" [completed]="isSourceValid()">
        <config-source (dataChange)="onSourceDataChange($event)"></config-source>
        <div class="step-actions">
          <button mat-button (click)="close()">{{ 'COMMON.CANCEL' | translate }}</button>
          <button mat-button matStepperNext [disabled]="!isSourceValid()" (click)="setAccountGroupId()">
            {{ 'COMMON.NEXT' | translate }}
          </button>
        </div>
      </mat-step>

      <mat-step label="Configure">
        <config-options
          #configOptions
          (dataChange)="onOptionsDataChange($event)"
          [configOptions]="configOptionsData()"
        ></config-options>
        <div class="step-actions">
          <button mat-button matStepperPrevious>{{ 'COMMON.BACK' | translate }}</button>
          <button mat-button (click)="close()">{{ 'COMMON.CANCEL' | translate }}</button>
          <button mat-raised-button color="primary" [disabled]="!isSourceValid() || isLoading()" (click)="finish()">
            <span *ngIf="!isLoading()">{{ 'COMMON.CREATE' | translate }}</span>
            <mat-spinner *ngIf="isLoading()" diameter="20"></mat-spinner>
          </button>
        </div>
      </mat-step>
    </mat-stepper>
  </mat-dialog-content>
</div>
