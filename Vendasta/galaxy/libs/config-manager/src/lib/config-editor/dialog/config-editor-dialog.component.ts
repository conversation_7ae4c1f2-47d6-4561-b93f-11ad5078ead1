import { CommonModule } from '@angular/common';
import { Component, computed, DestroyRef, inject, OnInit, signal, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatStepperModule } from '@angular/material/stepper';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { ConfigSourceComponent } from '../config-source/source.component';
import { ConfigOption, ConfigOptionsComponent } from '../config-options/options.component';
import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import { TranslateModule } from '@ngx-translate/core';
import { ConfigManagerI18nModule } from '../../assets/i18n/config-manager-i18n.module';
import {
  ConfigurationManagementApiService,
  OwnerNamespaceType,
  SourceNamespaceType,
} from '@vendasta/configuration-management';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { PARTNER_ID_TOKEN } from '../../tokens';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Observable, of, switchMap, take } from 'rxjs';
import { typedObjectContainersToConfigOptions } from '../../utils';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
  selector: 'config-editor-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatCardModule,
    MatStepperModule,
    ConfigSourceComponent,
    ConfigOptionsComponent,
    TranslateModule,
    ConfigManagerI18nModule,
    MatProgressSpinner,
  ],
  providers: [
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { displayDefaultIndicatorType: false },
    },
  ],
  templateUrl: './config-editor-dialog.component.html',
  styleUrls: ['./config-editor-dialog.component.scss'],
})
export class ConfigEditorDialogComponent implements OnInit {
  private readonly DIALOG_WIDTH = '660px';
  private readonly bpObserver = inject(BreakpointObserver);
  private readonly configService = inject(ConfigurationManagementApiService);
  private readonly snackbar = inject(SnackbarService);
  private readonly partnerId = toSignal(inject(PARTNER_ID_TOKEN));

  private readonly sourceData = signal<any>(null);
  private readonly sourceValid = signal<boolean>(false);
  private readonly optionsData = signal<any[]>([]);
  private readonly accountGroupId = signal<string | undefined>(undefined);
  protected readonly isLoading = signal<boolean>(false);

  protected readonly isSourceValid = computed(() => this.sourceValid());

  configOptionsData = signal<ConfigOption[]>([]);
  @ViewChild('configOptions') configOptionsComponent!: ConfigOptionsComponent;

  constructor(
    public dialogRef: MatDialogRef<ConfigEditorDialogComponent>,
    public readonly destroyRef: DestroyRef,
  ) {}

  ngOnInit(): void {
    this.dialogRef.disableClose = true;

    const isSmallScreen = this.bpObserver.isMatched(Breakpoints.XSmall);
    if (isSmallScreen) {
      this.dialogRef.updateSize('100vw', '100vh');
    } else {
      this.dialogRef.updateSize(this.DIALOG_WIDTH);
    }
  }

  onSourceDataChange(event: { data: any; valid: boolean }): void {
    this.sourceData.set(event.data);
    this.sourceValid.set(event.valid);
    this.accountGroupId.set(event.data.accountGroupId);
  }

  onOptionsDataChange(data: any): void {
    this.optionsData.set(data);
  }

  close(): void {
    this.dialogRef.close();
  }

  finish(): void {
    if (this.isLoading()) {
      return;
    }

    this.isLoading.set(true);

    const typedObjectIds = this.optionsData()
      .map((option: any) => ({
        typeId: option.id,
        typedObjectIds:
          option.subOptions?.filter((subOption: any) => subOption.enabled).map((subOption: any) => subOption.id) || [],
      }))
      .filter((option: any) => option.typedObjectIds.length > 0);

    const partnerId = this.partnerId();
    if (!partnerId) {
      this.snackbar.openErrorSnack('Partner ID is not available');
      this.isLoading.set(false);
      return;
    }

    const request = {
      name: this.sourceData()?.name,
      configurationSource: {
        sourceNamespace: this.accountGroupId(),
        sourceNamespaceType: SourceNamespaceType.SOURCE_NAMESPACE_ACCOUNT_GROUP,
      },
      configurationOwner: {
        ownerNamespace: partnerId,
        ownerNamespaceType: OwnerNamespaceType.OWNER_NAMESPACE_PARTNER,
      },
      selectedTypedObjectIds: typedObjectIds,
    };

    this.configService
      .createConfigurationFromSource(request)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => {
          this.snackbar.openSuccessSnack('Configuration created successfully');
          this.dialogRef.close({
            success: true,
            source: this.sourceData(),
            options: this.optionsData(),
          });
        },
        error: (error) => {
          const errorMessage = error?.error?.message || 'Failed to create configuration';
          this.snackbar.openErrorSnack(errorMessage);
          this.isLoading.set(false);
        },
        complete: () => {
          this.isLoading.set(false);
        },
      });
  }

  setAccountGroupId() {
    const id = this.accountGroupId();
    if (id) {
      this.getConfigOptions()
        .pipe(take(1))
        .subscribe((options) => {
          this.configOptionsData.set(options);
        });
    }
  }

  getConfigOptions(): Observable<ConfigOption[]> {
    return this.configService
      .getConfigurationPreviewDataForSource({
        configurationSource: {
          sourceNamespace: this.accountGroupId(),
          sourceNamespaceType: SourceNamespaceType.SOURCE_NAMESPACE_ACCOUNT_GROUP,
        },
      })
      .pipe(
        switchMap((response) => {
          return of(typedObjectContainersToConfigOptions(response.objectTypes));
        }),
      );
  }
}
