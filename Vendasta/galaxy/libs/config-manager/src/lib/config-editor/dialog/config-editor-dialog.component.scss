@use 'design-tokens' as *;

::ng-deep .mat-dialog-container {
  padding: 0;
}

.dialog-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 600px;
  width: 660px;
}

mat-dialog-content {
  flex-grow: 1;
  overflow-y: auto;
  padding-bottom: 60px;
}

mat-step {
  padding: $spacing-3;
}

.step-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: white;
  z-index: 10;
  padding: $spacing-2;
  display: flex;
  justify-content: flex-end;
  gap: $spacing-2;
  border-top: 1px solid #ddd;
}

::ng-deep {
  .mat-horizontal-stepper-header-container {
    padding: 0 $spacing-3;
  }

  .mat-horizontal-content-container {
    padding: 0 !important;
  }
}

[mat-dialog-title] {
  margin: 0;
  padding: $spacing-3;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  font-size: $font-preset-2-size;
  font-weight: 500;
}
