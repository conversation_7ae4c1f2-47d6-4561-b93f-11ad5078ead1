@use 'design-tokens' as *;
.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 8px;
}

.option-title {
  font-weight: 500;
}

.sub-options {
  margin-top: $spacing-2;
}

.sub-option {
  padding: $spacing-2 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);

  &:last-child {
    border-bottom: none;
  }
}

.sub-option-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.sub-option-info {
  flex: 1;
  margin-right: $spacing-3;
}

.sub-option-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.sub-option-link {
  display: flex;
  align-items: center;
}

.sub-option-description {
  font-size: $font-preset-5-size;
  color: rgba(0, 0, 0, 0.6);
}

::ng-deep {
  .mat-expansion-panel {
    box-shadow: none !important;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px !important;
    margin-bottom: 8px !important;
  }

  .mat-expansion-panel-header {
    padding: 0 16px;
  }

  .mat-expansion-panel-body {
    padding: 0 16px 16px !important;
  }

  .mat-checkbox {
    margin: 0;

    .mat-checkbox-frame {
      border-width: 2px;
    }
  }
}
