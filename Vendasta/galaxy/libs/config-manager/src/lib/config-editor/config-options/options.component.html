<div class="options-container" *ngIf="configOptions() as options">
  <mat-accordion>
    <mat-expansion-panel *ngFor="let option of options" [expanded]="option.subOptions && option.subOptions.length > 0">
      <mat-expansion-panel-header>
        <div class="option-header">
          <div class="option-title">{{ option.name }}</div>
          <mat-checkbox
            [checked]="option.enabled"
            [indeterminate]="option.indeterminate"
            (change)="onOptionChange(option)"
            (click)="$event.stopPropagation()"
            color="primary"
          ></mat-checkbox>
        </div>
      </mat-expansion-panel-header>

      <div class="sub-options" *ngIf="option.subOptions && option.subOptions.length > 0">
        <div *ngFor="let subOption of option.subOptions" class="sub-option">
          <div class="sub-option-content">
            <div class="sub-option-info">
              <div class="sub-option-name">
                @if (subOption.link) {
                  <a [href]="subOption.link" target="_blank" rel="noopener noreferrer" class="sub-option-link">
                    <span>{{ subOption.name }}</span>
                    <mat-icon style="font-size: 20px; margin-left: 6px">open_in_new</mat-icon>
                  </a>
                } @else {
                  <span>{{ subOption.name }}</span>
                }
              </div>
              <div class="sub-option-description" *ngIf="subOption.description">
                {{ subOption.description }}
              </div>
            </div>
            <mat-checkbox
              [checked]="subOption.enabled"
              (change)="onSubOptionChange(option, subOption, $event.checked)"
              color="primary"
            ></mat-checkbox>
          </div>
        </div>
      </div>
    </mat-expansion-panel>
  </mat-accordion>
</div>
