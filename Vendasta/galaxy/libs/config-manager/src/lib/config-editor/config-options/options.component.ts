import { Component, Output, EventEmitter, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';

export interface ConfigOption {
  id: string;
  name: string;
  enabled: boolean;
  indeterminate?: boolean;
  description?: string;
  subOptions?: ConfigOption[];
  link?: string;
}

@Component({
  selector: 'config-options',
  standalone: true,
  imports: [CommonModule, MatExpansionModule, MatSlideToggleModule, MatIconModule, MatCheckboxModule, FormsModule],
  templateUrl: './options.component.html',
  styleUrls: ['./options.component.scss'],
})
export class ConfigOptionsComponent {
  @Output() dataChange = new EventEmitter<any>();

  configOptions = input.required<ConfigOption[]>();

  onOptionChange(option: ConfigOption) {
    option.enabled = !option.enabled;
    option.indeterminate = false;

    if (option.subOptions) {
      option.subOptions.forEach((subOption) => {
        subOption.enabled = option.enabled;
      });
    }
    this.dataChange.emit([...this.configOptions()]);
  }

  onSubOptionChange(parentOption: ConfigOption, subOption: ConfigOption, event: boolean) {
    subOption.enabled = event;

    if (parentOption.subOptions) {
      const enabledCount = parentOption.subOptions.filter((so) => so.enabled).length;
      const totalSubOptions = parentOption.subOptions.length;

      if (enabledCount === 0) {
        parentOption.enabled = false;
        parentOption.indeterminate = false;
      } else if (enabledCount === totalSubOptions) {
        parentOption.enabled = true;
        parentOption.indeterminate = false;
      } else {
        parentOption.enabled = false;
        parentOption.indeterminate = true;
      }
    }
    this.dataChange.emit([...this.configOptions()]);
  }
}
