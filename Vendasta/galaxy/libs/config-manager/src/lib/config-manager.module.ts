import { InjectionToken, ModuleWithProviders, NgModule } from '@angular/core';
import { ConfigManagerService } from './config-manager.service';
import { CONFIG_MANAGER_CONFIG_TOKEN, ConfigManagerConfig, PARTNER_ID_TOKEN } from './tokens';
import { isObservable, Observable, of } from 'rxjs';

@NgModule()
export class ConfigManagerModule {
  static forRoot(c: { config: InjectionToken<ConfigManagerConfig> }): ModuleWithProviders<ConfigManagerModule> {
    const result = {
      ngModule: ConfigManagerModule,
      providers: [
        ConfigManagerService,
        { provide: CONFIG_MANAGER_CONFIG_TOKEN, useExisting: c.config },
        { provide: PARTNER_ID_TOKEN, deps: [CONFIG_MANAGER_CONFIG_TOKEN], useFactory: partnerIdFactory },
      ],
    };
    return result;
  }
}

function partnerIdFactory(config: ConfigManagerConfig): Observable<string | undefined | null> {
  return isObservable(config.partnerId$) ? config.partnerId$ : of(config.partnerId$);
}
