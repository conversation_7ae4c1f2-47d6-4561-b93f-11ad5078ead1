import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { TranslateModule } from '@ngx-translate/core';
import { ConfigManagerI18nModule } from '../../assets/i18n/config-manager-i18n.module';
import { ExtendedConfiguration } from '../config-list.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfigApplierDialogComponent } from '../../config-applier/dialog/config-applier-dialog.component';
import { typedObjectContainersToConfigOptions } from '../../utils';
import { ConfigViewDialogComponent } from '../../config-view/dialog/config-view-dialog.component';
import { MatMenuModule } from '@angular/material/menu';
import { ConfigurationManagementApiService } from '@vendasta/configuration-management';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ConfigDeleteDialogComponent } from '../../config-delete/dialog/config-delete-dialog.component';
import { take } from 'rxjs/operators';

@Component({
  selector: 'config-card',
  templateUrl: './config-card.component.html',
  styleUrls: ['./config-card.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatChipsModule,
    ConfigManagerI18nModule,
    MatMenuModule,
  ],
})
export class ConfigCardComponent {
  @Input({ required: true }) config!: ExtendedConfiguration;
  @Output() refreshList = new EventEmitter<void>();

  dialog = inject(MatDialog);
  private configService = inject(ConfigurationManagementApiService);
  private snackbarService = inject(SnackbarService);

  applyConfiguration(): void {
    this.dialog.open(ConfigApplierDialogComponent, {
      data: {
        configurationId: this.config.configurationId,
        configOptions: typedObjectContainersToConfigOptions(this.config.configurationVersion.objectTypes, true),
      },
    });
  }

  openConfigViewDialog(): void {
    this.dialog.open(ConfigViewDialogComponent, {
      data: {
        name: this.config.name,
        source: this.config.configurationVersion.sourceNamespaceObject.napData.companyName,
        configOptions: typedObjectContainersToConfigOptions(this.config.configurationVersion.objectTypes, true),
      },
    });
  }

  deleteConfiguration(): void {
    const dialogRef = this.dialog.open(ConfigDeleteDialogComponent, {
      data: {
        configName: this.config.name,
      },
    });

    dialogRef
      .afterClosed()
      .pipe(take(1))
      .subscribe((result) => {
        if (result) {
          this.configService
            .deleteConfiguration({ configurationId: this.config.configurationId })
            .pipe(take(1))
            .subscribe({
              next: () => {
                this.snackbarService.openSuccessSnack('CONFIG_CARD.DELETE_SUCCESS');
                this.refreshList.emit();
              },
              error: () => {
                this.snackbarService.openErrorSnack('CONFIG_CARD.DELETE_ERROR');
              },
            });
        }
      });
  }
}
