<div class="config-card">
  <mat-card>
    <mat-card-header>
      <div class="card-title-container">
      <mat-card-title (click)="openConfigViewDialog()">{{ config.name }}</mat-card-title>
    </div>
      <button mat-icon-button [matMenuTriggerFor]="menu" class="kebab-menu">
        <mat-icon>more_vert</mat-icon>
      </button>
      <mat-menu #menu="matMenu">
        <button mat-menu-item (click)="deleteConfiguration()">
          <mat-icon>delete</mat-icon>
          <span>{{ 'CONFIG_CARD.DELETE' | translate }}</span>
        </button>
      </mat-menu>
    </mat-card-header>

    <mat-card-content>
      <div class="source-info">
        <span class="label">{{ 'CONFIG_CARD.SOURCE' | translate }}:</span>
        <span class="source-name">{{ config.configurationVersion.sourceNamespaceObject.napData.companyName }}</span>
      </div>

      <div class="content-section">
        <div class="contains-section">
          <span class="label">{{ 'CONFIG_CARD.CONTAINS' | translate }}:</span>
          <div class="items-container">
            <ng-container *ngFor="let item of config.configurationVersion.objectTypes">
              <span class="item-count">{{ item.objects.length }} {{ item.typeName }}</span>
            </ng-container>
          </div>
        </div>
      </div>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-raised-button color="primary" (click)="applyConfiguration()">
        {{ 'CONFIG_CARD.APPLY_TO' | translate }}
      </button>
    </mat-card-actions>
  </mat-card>
</div>
