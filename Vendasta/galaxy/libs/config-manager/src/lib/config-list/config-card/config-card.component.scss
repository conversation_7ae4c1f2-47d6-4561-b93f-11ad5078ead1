@use 'design-tokens' as *;

.config-card {
  width: 100%;

  mat-card {
    padding: $spacing-3;
    background-color: var(--card-background-color);
    border-radius: var(--border-radius);
  }

  mat-card-header {
    margin-bottom: $spacing-4;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-title-container {
    margin-bottom: $spacing-2;
  }

  mat-card-title {
    margin: 0;
    font-size: $font-preset-2-size;
    font-weight: 500;
    color: var(--primary-color);
    cursor: pointer;
  }

  .kebab-menu {
    margin-left: auto;
  }

  .label {
    color: var(--secondary-text-color);
    font-size: $font-preset-3-size;
    font-weight: bold;
  }

  .source-info {
    margin-bottom: $spacing-2;

    .source-name {
      color: var(--primary-color);
      font-size: $font-preset-3-size;
      margin-left: $spacing-2;
    }
  }

  .content-section {
    display: flex;
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
  }

  .contains-section {
    display: flex;

    .items-container {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-2;
      margin-left: $spacing-2;

      .item-count {
        font-size: $font-preset-3-size;
        color: var(--primary-color);

        &:not(:last-child)::after {
          content: ',';
          margin-right: $spacing-1;
          color: var(--secondary-text-color);
        }
      }
    }
  }

  .empty-state {
    color: var(--secondary-text-color);
    font-style: italic;
    font-size: $font-preset-4-size;
    background-color: var(--surface-color);
    padding: $spacing-3;
    border-radius: var(--border-radius);
  }

  mat-card-actions {
    padding: 0;
    margin: $spacing-5 0 0;
    border-top: 1px solid var(--border-color);
    padding-top: $spacing-3;
  }
}
