import { Component, effect, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { ConfigManagerI18nModule } from '../assets/i18n/config-manager-i18n.module';
import {
  Configuration,
  ConfigurationManagementApiService,
  ConfigurationOwner,
  ConfigurationVersion,
  ListConfigurationsRequest,
  OwnerNamespaceType,
  PagedRequestOptions,
} from '@vendasta/configuration-management';
import { ConfigCardComponent } from './config-card/config-card.component';
import { switchMap, map, take, of } from 'rxjs';
import { AccountGroupService, ProjectionFilter, AccountGroup } from '@galaxy/account-group';
import { PARTNER_ID_TOKEN } from '../tokens';
import { toSignal } from '@angular/core/rxjs-interop';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatIconModule } from '@angular/material/icon';

export interface ExtendedConfigurationVersion extends ConfigurationVersion {
  sourceNamespaceObject: AccountGroup;
}

export interface ExtendedConfiguration extends Configuration {
  configurationVersion: ExtendedConfigurationVersion;
}

@Component({
  selector: 'config-list',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    ConfigCardComponent,
    TranslateModule,
    ConfigManagerI18nModule,
    GalaxyEmptyStateModule,
    GalaxyLoadingSpinnerModule,
  ],
  templateUrl: './config-list.component.html',
  styleUrls: ['./config-list.component.scss'],
})
export class ConfigListComponent {
  configs = signal<ExtendedConfiguration[] | null>(null);
  private accountGroupService = inject(AccountGroupService);
  private configService = inject(ConfigurationManagementApiService);
  private readonly partnerId = toSignal(inject(PARTNER_ID_TOKEN));

  constructor() {
    effect(() => {
      this.loadListData();
    });
  }

  loadListData(): void {
    this.configs.set(null);
    this.configService
      .listConfigurations(
        new ListConfigurationsRequest({
          configurationOwner: new ConfigurationOwner({
            ownerNamespace: this.partnerId(),
            ownerNamespaceType: OwnerNamespaceType.OWNER_NAMESPACE_PARTNER,
          }),
          pagingOptions: new PagedRequestOptions({
            pageSize: 10,
          }),
        }),
      )
      .pipe(
        take(1),
        switchMap((configs) => {
          if (!configs.configuration || !configs.configuration.length) {
            return of([]);
          }
          const agIDs = configs.configuration.map((config) => config.configurationVersion.sourceNamespace);
          return this.accountGroupService.getMulti(agIDs, new ProjectionFilter({ napData: true })).pipe(
            map((response: AccountGroup[]) => {
              const extendedConfigs: ExtendedConfiguration[] = [];
              configs.configuration.forEach((config, i) => {
                const extendedVersion = {
                  ...config.configurationVersion,
                  sourceNamespaceObject: response[i],
                } as ExtendedConfigurationVersion;

                extendedConfigs.push(
                  new Configuration({
                    ...config,
                    configurationVersion: extendedVersion,
                  }) as ExtendedConfiguration,
                );
              });
              return extendedConfigs;
            }),
          );
        }),
      )
      .subscribe((configs) => {
        this.configs.set(configs);
      });
  }
}
