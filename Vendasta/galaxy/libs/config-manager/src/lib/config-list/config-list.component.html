<div class="config-list-container">
  <div class="config-list">
    @if (configs() === null) {
      <glxy-loading-spinner></glxy-loading-spinner>
    } @else {
      @if (configs()?.length > 0) {
        <config-card *ngFor="let config of configs()" [config]="config" (refreshList)="loadListData()"></config-card>
      } @else {
        <glxy-empty-state>
          <glxy-empty-state-hero>
            <mat-icon>deployed_code_update</mat-icon>
          </glxy-empty-state-hero>
          <glxy-empty-state-title>{{ 'CONFIG_LIST.EMPTY_STATE' | translate }}</glxy-empty-state-title>
          <glxy-empty-state-actions>{{ 'CONFIG_LIST.EMPTY_STATE_ACTION' | translate }}</glxy-empty-state-actions>
        </glxy-empty-state>
      }
    }
  </div>
</div>
