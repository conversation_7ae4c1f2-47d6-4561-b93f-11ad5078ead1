import { InjectionToken } from '@angular/core';
import { Observable } from 'rxjs';

export interface ConfigManagerConfig {
  partnerId$: Observable<string>;
  // Add other configuration options as needed
}

export const CONFIG_MANAGER_CONFIG_TOKEN = new InjectionToken<ConfigManagerConfig>(
  '[ConfigManager]: token for configuration',
);

export const PARTNER_ID_TOKEN = new InjectionToken<Observable<string>>('[ConfigManager]: token for partnerId');
