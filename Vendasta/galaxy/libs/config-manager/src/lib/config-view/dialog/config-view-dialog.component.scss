@use 'design-tokens' as *;

.dialog-container {
  width: 660px;
}

mat-dialog-content {
  max-height: 500px;
  height: 500px;
  overflow-y: auto;
}

.config-name {
  font-size: $font-preset-2-size;
  color: var(--primary-color);
}

.source-info {
  .label {
    color: var(--secondary-text-color);
    font-size: $font-preset-3-size;
    font-weight: bold;
  }

  .source-name {
    color: var(--primary-color);
    font-size: $font-preset-3-size;
    margin-left: $spacing-2;
  }

  margin-bottom: $spacing-3;
}

.option-title {
  font-weight: bold;
}

.sub-option {
  padding: $spacing-2;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);

  &:last-child {
    border-bottom: none;
  }
}
