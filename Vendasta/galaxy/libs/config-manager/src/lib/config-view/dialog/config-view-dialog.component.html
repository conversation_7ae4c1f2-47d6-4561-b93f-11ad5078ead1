<div class="dialog-container">
  <h2 mat-dialog-title class="config-name">{{ data.name }}</h2>
  <mat-dialog-content>
    <div class="source-info">
      <span class="label">{{ 'CONFIG_CARD.SOURCE' | translate }}:</span>
      <span class="source-name">{{ data.source }}</span>
    </div>
    <mat-accordion>
      <mat-expansion-panel
        *ngFor="let option of data.configOptions"
        [expanded]="option.subOptions && option.subOptions.length > 0"
      >
        <mat-expansion-panel-header>
          <div class="option-title">{{ option.name }}</div>
        </mat-expansion-panel-header>

        <div *ngIf="option.subOptions && option.subOptions.length > 0">
          <div *ngFor="let subOption of option.subOptions" class="sub-option">
            {{ subOption.name }}
          </div>
        </div>
      </mat-expansion-panel>
    </mat-accordion>
  </mat-dialog-content>
  <mat-dialog-actions>
    <button mat-raised-button color="primary" mat-dialog-close>{{ 'COMMON.CLOSE' | translate }}</button>
  </mat-dialog-actions>
</div>
