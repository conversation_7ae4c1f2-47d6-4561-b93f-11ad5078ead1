import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogClose,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { MatButton } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { ConfigOption } from '../../config-editor/config-options/options.component';

@Component({
  selector: 'config-manager-config-view-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogContent,
    MatDialogTitle,
    TranslateModule,
    MatDialogActions,
    MatButton,
    MatDialogClose,
    MatExpansionModule,
    MatIconModule,
  ],
  templateUrl: './config-view-dialog.component.html',
  styleUrls: ['./config-view-dialog.component.scss'],
})
export class ConfigViewDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ConfigViewDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { configOptions: ConfigOption[]; name: string; source: string },
  ) {}
}
