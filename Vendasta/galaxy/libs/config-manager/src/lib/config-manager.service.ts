import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CONFIG_MANAGER_CONFIG_TOKEN, ConfigManagerConfig, PARTNER_ID_TOKEN } from './tokens';

@Injectable({
  providedIn: 'root',
})
export class ConfigManagerService {
  constructor(
    @Inject(CONFIG_MANAGER_CONFIG_TOKEN) private config: ConfigManagerConfig,
    @Inject(PARTNER_ID_TOKEN) private partnerId$: Observable<string>,
  ) {}

  getPartnerId(): Observable<string> {
    return this.partnerId$;
  }
}
