import { TypedObjectContainer } from '@vendasta/configuration-management';
import { ConfigOption } from './config-editor/config-options/options.component';

export function typedObjectContainersToConfigOptions(
  typedObjectContainers: TypedObjectContainer[],
  defaultEnabled = false,
): ConfigOption[] {
  return typedObjectContainers.map((t) => ({
    id: t.typeId,
    name: t.typeName,
    enabled: defaultEnabled,
    indeterminate: false,
    subOptions: t.objects.map((o) => ({
      id: o.id,
      name: o.name,
      description: o.description,
      link: o.link,
      enabled: defaultEnabled,
    })),
  }));
}
