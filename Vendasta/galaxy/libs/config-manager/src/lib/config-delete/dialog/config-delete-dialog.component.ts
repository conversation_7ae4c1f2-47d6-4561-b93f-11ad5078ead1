import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { ConfigManagerI18nModule } from '../../assets/i18n/config-manager-i18n.module';

@Component({
  selector: 'config-delete-dialog',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatButtonModule, TranslateModule, ConfigManagerI18nModule],
  templateUrl: './config-delete-dialog.component.html',
  styleUrls: ['./config-delete-dialog.component.scss'],
})
export class ConfigDeleteDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ConfigDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { configName: string },
  ) {}

  onConfirm(): void {
    this.dialogRef.close(true);
  }
}
