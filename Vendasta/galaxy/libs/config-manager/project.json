{"name": "config-manager", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/config-manager/src", "prefix": "config-manager", "projectType": "library", "tags": ["scope:shared"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/config-manager/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}, "weblate-upload": {"executor": "./tools/builders/weblate-upload:upload", "options": {"filePath": "libs/config-manager/src/lib/assets/i18n/en_devel.json", "weblateProject": "common", "weblateComponent": "config-manager"}}, "weblate-commit": {"executor": "./tools/builders/weblate-commit:commit", "options": {"weblateProject": "common", "weblateComponent": "config-manager"}}}}