{"name": "business-nav", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/business-nav/src", "prefix": "bc", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/business-nav"], "options": {"jestConfig": "libs/business-nav/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "weblate-upload": {"executor": "./tools/builders/weblate-upload:upload", "options": {"filePath": "libs/business-nav/src/lib/assets/i18n/en_devel.json", "weblateProject": "common", "weblateComponent": "business-nav"}}, "weblate-commit": {"executor": "./tools/builders/weblate-commit:commit", "options": {"weblateProject": "common", "weblateComponent": "business-nav"}}}, "tags": ["scope:shared"]}