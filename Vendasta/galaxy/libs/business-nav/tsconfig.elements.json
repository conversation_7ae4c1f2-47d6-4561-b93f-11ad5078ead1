{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../out-tsc/lib", "declaration": true, "sourceMap": true, "inlineSources": true, "experimentalDecorators": true, "importHelpers": true, "types": ["node"]}, "angularCompilerOptions": {"annotateForClosureCompiler": true, "skipTemplateCodegen": true, "strictMetadataEmit": true, "fullTemplateTypeCheck": true, "strictInjectionParameters": true, "enableResourceInlining": true}, "files": ["src/elements/main.ts", "src/elements/polyfills.ts"]}