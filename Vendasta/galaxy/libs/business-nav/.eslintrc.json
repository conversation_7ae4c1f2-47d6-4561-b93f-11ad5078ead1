{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["libs/business-nav/tsconfig.*?.json"]}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "bc", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "bc", "style": "kebab-case"}], "no-restricted-imports": ["error", {"paths": ["@angular/material"]}], "@angular-eslint/prefer-standalone": "off"}, "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates"]}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template"], "rules": {}}]}