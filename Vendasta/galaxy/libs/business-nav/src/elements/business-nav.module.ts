import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Injector, NgModule } from '@angular/core';
import { DEFINE_CUSTOM_ELEMENTS } from '../lib/module-common';
import { BrowserModule } from '@angular/platform-browser';
import { BusinessNavModule } from '../lib';

@NgModule({
  imports: [BrowserModule, BusinessNavModule],
  exports: [BusinessNavModule],
})
export class BusinessNavElementsModule implements DoBootstrap {
  constructor(private injector: Injector) {
    DEFINE_CUSTOM_ELEMENTS(this.injector);
  }

  // eslint-disable-next-line @angular-eslint/no-empty-lifecycle-method
  ngDoBootstrap(): void {
    // pass
  }
}
