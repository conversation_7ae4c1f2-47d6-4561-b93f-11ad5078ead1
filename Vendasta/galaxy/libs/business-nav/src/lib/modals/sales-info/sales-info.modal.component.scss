@use 'design-tokens' as *;
@use '../../common' as *;

.content {
  padding: $spacing-4;
}

.glxy-form-field.bottom-spacing--default {
  margin: $spacing-4 0 0;
  width: 600px;

  @include respond-to(mobile) {
    width: 100%;
  }
}

.business-navbar__sales-info__message-composer {
  width: 100%;
  padding: 16px 24px;
}

.business-navbar__sales-info {
  max-width: 550px;
  min-width: 550px;
  max-height: 90vh;
}

.business-navbar__sales-info__sales-card {
  display: flex;
}

.business-navbar__sales-info__sales-card__picture {
  border-radius: 50%;
  margin-right: 16px;
}

.business-navbar__sales-info__sales-card__contact-details__name {
  font-size: 16px;
  font-weight: 500;
}

.business-navbar__sales-info__sales-card__contact-details__title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.54);
}

.business-navbar__sales-info__sales-card__contact-details__start-chat {
  margin-top: 8px;
}
