import { Component, Optional, inject } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { InboxService, ParticipantService, SubjectParticipant } from '@galaxy/conversation/core';
import { FeatureFlagService } from '@galaxy/partner';
import { TranslateService } from '@ngx-translate/core';
import { AtlasApiService, SalesContact, UserViewType } from '@vendasta/atlas';
import { ConversationChannel, GlobalParticipantType, Participant } from '@vendasta/conversation';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { CountryCode, format } from 'libphonenumber-js';
import { Observable, combineLatest, firstValueFrom, of } from 'rxjs';
import { catchError, map, shareReplay, switchMap } from 'rxjs/operators';
import { BusinessNavConfigService } from '../../config.service';
import { BusinessNavDataService } from '../../data.service';

const SMB_MESSAGE_PARTNER_FEATURE = 'inbox_smb_message_partner';

@Component({
  selector: 'bc-sales-info-modal',
  templateUrl: './sales-info.modal.component.html',
  styleUrls: ['./sales-info.modal.component.scss'],
  standalone: false,
})
export class SalesInfoModalComponent {
  dialogRef = inject(MatDialogRef<SalesInfoModalComponent>);

  public headerText$: Observable<string>;
  public isAdminView$: Observable<boolean>;
  public enableMessagePartnerFeature$: Observable<boolean>;
  public messageTxt: string;
  public sendingMessage: boolean;
  public partnerId$: Observable<string>;
  public accountGroupId$: Observable<string>;
  public canAccessInbox$: Observable<boolean> = of(false);
  public accountHasInboxEnabled$: Observable<boolean>;
  public readonly messageSoftLimit = 500;

  subjectParticipants$ = combineLatest([this.configService.partnerId$, this.configService.accountGroupId$]).pipe(
    map(([partnerId, accountGroupId]) => [
      new SubjectParticipant({
        internalParticipantId: accountGroupId,
        participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
      }),
      new SubjectParticipant({
        internalParticipantId: partnerId,
        participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
      }),
    ]),
  );

  currentIAMParticipant$: Observable<Participant>;
  readonly conversationChannel = ConversationChannel.CONVERSATION_CHANNEL_INTERNAL;

  salespersonContact$: Observable<SalesContact> = this.dataService.salespersonContact$;
  salespersonPicture$: Observable<string> = this.dataService.salespersonPictureUrl$;

  constructor(
    private dataService: BusinessNavDataService,
    private t: TranslateService,
    private apiService: AtlasApiService,
    private configService: BusinessNavConfigService,
    private snackbarService: SnackbarService,
    private featureFlagService: FeatureFlagService,
    @Optional() private inboxService: InboxService,
    @Optional() private participantService: ParticipantService,
  ) {
    this.headerText$ = this.dataService.partnerName$.pipe(
      switchMap((partnerName) => {
        return this.t.stream('COMMON.CONTACT_SALESPERSON', { salesPersonName: partnerName });
      }),
    );
    this.isAdminView$ = this.dataService.userViewType$.pipe(map((res) => res === UserViewType.USER_VIEW_TYPE_ADMIN));
    this.initSmbToPartnerMessageFeatureFlag();
    this.initInboxAccess();
  }

  initSmbToPartnerMessageFeatureFlag(): void {
    this.enableMessagePartnerFeature$ = this.configService.partnerId$.pipe(
      switchMap((partnerId) => this.featureFlagService.batchGetStatus(partnerId, '', [SMB_MESSAGE_PARTNER_FEATURE])),
      map((feature) => feature[SMB_MESSAGE_PARTNER_FEATURE]),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  /**
   * Initiate access rules specifically for Inbox Chat Modal Button in Business App.
   */
  initInboxAccess(): void {
    if (!this.inboxService || !this.participantService) {
      return;
    }

    this.currentIAMParticipant$ = this.participantService?.buildIAMUserParticipant() as Observable<Participant>;

    this.accountHasInboxEnabled$ = this.dataService.navigationItems$.pipe(
      map((navItems) => !!navItems.find((navItem) => navItem?.sideNavigationLink?.navigationId === 'nav-inbox')),
    );

    this.canAccessInbox$ = combineLatest([
      this.inboxService.allowSmbChatWithPartner$,
      this.enableMessagePartnerFeature$,
      this.accountHasInboxEnabled$,
    ]).pipe(
      map(
        ([allowSmbChat, enableMessagePartner, accountHasInboxEnabled]) =>
          allowSmbChat && enableMessagePartner && accountHasInboxEnabled,
      ),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  formatPhoneNumber(phoneNumber: string | number, countryCode?: string): string {
    if (!phoneNumber || phoneNumber === undefined) {
      return '';
    }

    const numberString = phoneNumber.toString();
    if (numberString === '') {
      return '';
    }
    const formatting = 'NATIONAL';
    if (countryCode as CountryCode) {
      const newCountryCode = countryCode as CountryCode;
      return format(numberString, newCountryCode, formatting);
    }
    return numberString;
  }

  closeModal(): void {
    this.dialogRef.close();
  }

  async sendMessage(): Promise<void> {
    if (this.sendingMessage) {
      return;
    }
    this.sendingMessage = true;
    const resp = await firstValueFrom(
      this.configService.accountGroupId$.pipe(
        switchMap((accountGroupId: string) =>
          this.apiService
            .contactUs({
              accountGroupId: accountGroupId,
              message: this.messageTxt,
            })
            .pipe(
              catchError(() => {
                this.snackbarService.openErrorSnack('COMMON.RESULT.FAILURE', {
                  duration: 4000,
                });
                this.sendingMessage = false;
                return of(null);
              }),
            ),
        ),
      ),
    );
    if (resp.ok) {
      this.snackbarService.openSuccessSnack('COMMON.RESULT.SUCCESS');
      this.sendingMessage = false;
      this.closeModal();
    }
  }
}
