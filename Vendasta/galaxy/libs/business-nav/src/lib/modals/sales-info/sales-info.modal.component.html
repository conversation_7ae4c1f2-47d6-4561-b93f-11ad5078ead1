<h2 mat-dialog-title>{{ headerText$ | async }}</h2>
<mat-dialog-content class="content">
  @if (salespersonContact$ | async; as contact) {
    <div class="business-navbar__sales-info__sales-card">
      <div class="business-navbar__sales-info__sales-card__picture">
        <glxy-avatar [src]="salespersonPicture$ | async" [width]="70"></glxy-avatar>
      </div>
      <div class="business-navbar__sales-info__sales-card__contact-details">
        <div class="business-navbar__sales-info__sales-card__contact-details__name">
          {{ contact.firstName }} {{ contact.lastName }}
        </div>
        <div class="business-navbar__sales-info__sales-card__contact-details__title">
          {{ contact.jobTitle }}
        </div>
        <div class="business-navbar__sales-info__sales-card__contact-details__phone-number">
          <a [href]="'tel:' + contact?.phoneNumber">
            {{ formatPhoneNumber(contact?.phoneNumber, contact.country) }}
          </a>
        </div>
        <div class="business-navbar__sales-info__sales-card__contact-details__email">
          <a [href]="'mailto:' + contact.email">{{ contact.email }}</a>
        </div>
        @if (!!contact?.meetingBookingUrl) {
          <div class="business-navbar__sales-info__sales-card__contact-details__booking-url">
            <bc-meeting-booking-url [bookingUrl]="contact?.meetingBookingUrl"></bc-meeting-booking-url>
          </div>
        }
      </div>
    </div>
  }
  @if ((canAccessInbox$ | async) === false) {
    <glxy-form-field class="contact-message">
      <textarea
        matInput
        #msgCounterSoftLimit
        placeholder="{{ 'REQUEST_ASSISTANCE_MODAL.COMPOSE_MESSAGE' | translate }}"
        [(ngModel)]="messageTxt"
        matTextareaAutosize
        matAutosizeMinRows="2"
        matAutosizeMaxRows="10"
      ></textarea>
      @if (msgCounterSoftLimit.value?.length <= messageSoftLimit) {
        <glxy-hint align="end"> {{ msgCounterSoftLimit.value?.length || 0 }}/{{ messageSoftLimit }} </glxy-hint>
      }
      @if (msgCounterSoftLimit.value?.length > messageSoftLimit) {
        <glxy-error align="end">
          Too Many characters &nbsp; &nbsp;
          {{ msgCounterSoftLimit.value?.length || 0 }}/{{ messageSoftLimit }}
        </glxy-error>
      }
    </glxy-form-field>
  }
</mat-dialog-content>
<mat-dialog-actions>
  @if ((canAccessInbox$ | async) === false) {
    <button (click)="closeModal()" mat-stroked-button>{{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}</button>
    <button
      mat-flat-button
      color="primary"
      glxyTooltip="{{ 'COMMON.RESULT.CANNOT_BE_IMPERSONATED' | translate }}"
      [glxyTooltipDisabled]="(isAdminView$ | async) === false"
      [disabled]="sendingMessage || messageTxt?.length > 500 || (isAdminView$ | async)"
      (click)="sendMessage()"
    >
      <glxy-button-loading-indicator [isLoading]="sendingMessage">
        {{ 'COMMON.ACTION_LABELS.SEND' | translate }}
      </glxy-button-loading-indicator>
    </button>
  }
  @if ((canAccessInbox$ | async) === true) {
    <inbox-button-send-message
      [config]="{
        currentParticipant: currentIAMParticipant$ | async,
        subjectParticipants: subjectParticipants$ | async,
        channel: conversationChannel
      }"
      (closeModal)="closeModal()"
    ></inbox-button-send-message>
  }
</mat-dialog-actions>
