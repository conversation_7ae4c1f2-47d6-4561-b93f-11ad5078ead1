import { CommonModule } from '@angular/common';
import { Component, Input, NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'bc-meeting-booking-url',
  templateUrl: './meeting-booking-url.component.html',
  styleUrls: ['./meeting-booking-url.component.scss'],
  standalone: false,
})
export class MeetingBookingUrlComponent {
  @Input() bookingUrl: string;

  bookMeeting(): void {
    window.open(this.bookingUrl, '_blank');
  }
}

@NgModule({
  imports: [CommonModule, MatButtonModule, MatIconModule, TranslateModule],
  exports: [MeetingBookingUrlComponent],
  declarations: [MeetingBookingUrlComponent],
})
export class MeetingBookingUrlModule {}
