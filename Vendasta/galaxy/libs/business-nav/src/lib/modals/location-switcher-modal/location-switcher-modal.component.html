<atlas-modal>
  <atlas-modal-header>{{ 'SWITCH_LOCATION_MODAL.SELECT_LOCATION' | translate }}</atlas-modal-header>
  <atlas-modal-content>
    @if (infoText$ | async; as infoText) {
      <glxy-alert [borderRadius]="false">{{ infoText | translate }}</glxy-alert>
    }
    <glxy-form-field bottomSpacing="none" class="search-bar">
      <glxy-label>{{ 'SWITCH_LOCATION_MODAL.SEARCH' | translate }}</glxy-label>
      <input #search type="text" matInput [formControl]="searchControl" />
    </glxy-form-field>
    @if (!isNativeApp) {
      <bc-tab-header>
        <bc-location-tab mode="both" [label]="allTabText$ | async" [isDefaultSelected]="true"></bc-location-tab>
        <bc-location-tab mode="single" [label]="accountGroupTabText$ | async"></bc-location-tab>
        <bc-location-tab mode="brands" [label]="brandTabText$ | async"></bc-location-tab>
      </bc-tab-header>
    }
    @if ((locations$ | async)?.length > 0 || (loading$ | async)) {
      <bc-location-list [locations]="locations$ | async" (locationSelected)="close()"></bc-location-list>
      @if ((locations$ | async)?.length > 0) {
        <glxy-infinite-scroll-trigger (isVisible)="listScroll()"></glxy-infinite-scroll-trigger>
      }
      @if (loading$ | async) {
        <glxy-loading-spinner size="large" [fullWidth]="true"></glxy-loading-spinner>
      }
    }

    @if (isEmptyResult$ | async) {
      <div class="no-access-to-any">
        @if (hasQueryText$ | async) {
          <glxy-empty-state>
            <glxy-empty-state-title class="no-access-to-any-title">
              {{ 'SWITCH_LOCATION_MODAL.NO_SEARCH_RESULT.TITLE' | translate }}
            </glxy-empty-state-title>
            <div class="no-access-to-any-addition">
              {{ 'SWITCH_LOCATION_MODAL.NO_SEARCH_RESULT.MESSAGE' | translate }}
            </div>
          </glxy-empty-state>
        } @else {
          <glxy-empty-state>
            <glxy-empty-state-title class="no-access-to-any-title">
              {{ 'SWITCH_LOCATION_MODAL.EMPTY_STATES.TITLE' | translate }}
              <span class="no-access-to-any-tab-name">{{ currentTabText$ | async | translate }}.</span>
            </glxy-empty-state-title>
            <div class="no-access-to-any-addition">
              {{ 'SWITCH_LOCATION_MODAL.EMPTY_STATES.MESSAGE' | translate }}
              <b>{{ 'SWITCH_LOCATION_MODAL.EMPTY_STATES.CONTACT' | translate }}</b>
            </div>
          </glxy-empty-state>
        }
      </div>
    }
  </atlas-modal-content>
</atlas-modal>
