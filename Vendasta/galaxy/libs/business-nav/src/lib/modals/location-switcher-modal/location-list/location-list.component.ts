import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { Location } from '@vendasta/atlas';
import { LocationListItemComponent } from './location-list-item/location-list-item.component';
import { catchError, switchMap } from 'rxjs/operators';
import { map, Observable, of, shareReplay } from 'rxjs';
import { AppPartnerService, AppSettings } from '@galaxy/marketplace-apps';
import { BusinessNavConfigService } from '../../../config.service';
import { MarketplaceAppService } from '@galaxy/marketplace-apps/v1';

@Component({
  selector: 'bc-location-list',
  templateUrl: './location-list.component.html',
  styleUrls: ['./location-list.component.scss'],
  standalone: false,
})
export class LocationListComponent {
  @Input() locations: Location[] = [];
  @Output() locationSelected = new EventEmitter();
  private readonly navConfigService = inject(BusinessNavConfigService);
  private partnerAppService = inject(AppPartnerService);
  private marketPlaceAppService = inject(MarketplaceAppService);

  protected brandingAppIconUrl$ = this.navConfigService.config$.pipe(
    switchMap((config) =>
      this.fetchAppSettings(config.serviceProviderId, config.partnerId, config.marketId).pipe(
        switchMap((appSettings) => {
          if (appSettings?.branding?.iconUrl && appSettings?.branding?.enabled) {
            return of(appSettings.branding.iconUrl);
          }
          return this.fetchAppSettings(config.serviceProviderId, config.partnerId, 'all-markets-default-fallback').pipe(
            map((defaultMarketSettings: AppSettings) => defaultMarketSettings?.branding.iconUrl || null),
          );
        }),
        switchMap((iconUrl) => {
          if (iconUrl) return of(iconUrl);
          return this.marketPlaceAppService.getApp(config.serviceProviderId).pipe(map((app) => app?.icon || null));
        }),
      ),
    ),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  private fetchAppSettings(appId: string, partnerId: string, marketId: string): Observable<AppSettings> {
    return this.partnerAppService.getAppSettings(appId, partnerId, marketId).pipe(catchError(() => of(null)));
  }

  switchLocation(location: LocationListItemComponent): void {
    location.goToLocation();
    this.onLocationSelected();
  }

  onLocationSelected(): void {
    this.locationSelected.emit();
  }
}
