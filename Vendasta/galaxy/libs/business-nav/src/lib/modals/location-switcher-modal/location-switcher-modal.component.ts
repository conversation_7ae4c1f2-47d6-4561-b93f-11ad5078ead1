import { AfterViewInit, Component, ElementRef, inject, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { Capacitor } from '@capacitor/core';
import { TranslateService } from '@ngx-translate/core';
import { UserViewType } from '@vendasta/atlas';
import { combineLatest, Observable } from 'rxjs';
import { map, startWith, switchMap } from 'rxjs/operators';
import { BusinessNavDataService } from '../../data.service';
import { BusinessNavLocationService } from './location.service';
import { BusinessNavLocationTabService } from './tab/tab.service';

@Component({
  selector: 'bc-location-switcher-modal',
  templateUrl: './location-switcher-modal.component.html',
  styleUrls: ['./location-switcher-modal.component.scss'],
  standalone: false,
})
export class LocationSwitcherModalComponent implements OnInit, AfterViewInit {
  dialogRef = inject(MatDialogRef<LocationSwitcherModalComponent>);

  @ViewChild('search') private readonly searchElement: ElementRef;

  tabElemSelected$: Observable<ElementRef>;

  allTabText$: Observable<string> = this.t.stream('NAVIGATION.TABS.ALL', 'All');
  accountGroupTabText$: Observable<string> = this.t.stream('NAVIGATION.TABS.LOCATIONS', 'Locations');
  brandTabText$: Observable<string> = this.t.stream('NAVIGATION.TABS.GROUPS', 'Groups');
  userViewType$: Observable<UserViewType>;
  infoText$: Observable<string>;
  currentTabText$: Observable<string>;
  viewType = UserViewType;

  readonly isNativeApp = Capacitor.isNativePlatform();

  public locations$ = this.locationsService.scrollableLocations.items$;
  protected readonly isEmptyResult$: Observable<boolean> = this.locationsService.scrollableLocations.state$.pipe(
    map((value) => value.empty),
  );
  protected readonly hasQueryText$: Observable<boolean> = this.locationsService.scrollableLocations.state$.pipe(
    map((value) => !value.criteria.query),
  );
  public loading$ = this.locationsService.scrollableLocations.loading$;
  protected readonly searchControl = new FormControl<string>('');

  constructor(
    private tabService: BusinessNavLocationTabService,
    private t: TranslateService,
    private locationsService: BusinessNavLocationService,
    private dataService: BusinessNavDataService,
  ) {
    this.tabService.clear();
    this.searchControl.valueChanges.pipe(takeUntilDestroyed()).subscribe((text) => this.textChanged(text));
  }

  ngOnInit(): void {
    this.userViewType$ = this.dataService.userViewType$.pipe(startWith(UserViewType.USER_VIEW_TYPE_SMB));

    this.tabElemSelected$ = this.tabService.selectedTabElement$;

    this.infoText$ = combineLatest([this.userViewType$, this.dataService.defaultLocation$]).pipe(
      map(([userViewType, defaultLocation]) => {
        if (!(defaultLocation?.accountGroupId || defaultLocation?.groupId)) {
          return 'SWITCH_LOCATION_MODAL.SELECT_DEFAULT';
        }
        if (userViewType === this.viewType.USER_VIEW_TYPE_ADMIN) {
          return 'SWITCH_LOCATION_MODAL.ADMIN_VIEW';
        }
        return;
      }),
    );

    this.currentTabText$ = this.tabService.selectedTabMode$.pipe(
      switchMap((tabMode) => {
        switch (tabMode) {
          case 'both':
            return this.t.stream('SWITCH_LOCATION_MODAL.EMPTY_STATES.ALL_TAB_NAME');
          case 'single':
            return this.accountGroupTabText$;
          case 'brands':
            return this.brandTabText$;
        }
      }),
    );
  }

  ngAfterViewInit(): void {
    this.searchElement.nativeElement.focus();
  }

  listScroll(): void {
    this.locationsService.loadMore();
  }

  textChanged(text: string): void {
    this.locationsService.setQuery(text);
  }

  close(): void {
    this.dialogRef.close();
  }
}
