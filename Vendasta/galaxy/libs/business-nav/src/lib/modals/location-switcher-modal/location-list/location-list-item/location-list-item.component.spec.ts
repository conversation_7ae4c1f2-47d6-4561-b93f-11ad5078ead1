import { provideHttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { NavigationEnd, provideRouter, Router } from '@angular/router';
import { AccountGroupInterface, BrandInterface, Location } from '@vendasta/atlas';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { filter, firstValueFrom, map, of, skip } from 'rxjs';
import { BusinessNavModule, LOCATION_SWITCH_CONFIG_TOKEN } from '../../../../business-nav.module';
import { BusinessNavConfigService } from '../../../../config.service';
import { BusinessNavDataService } from '../../../../data.service';
import { LocationListItemComponent } from './location-list-item.component';

// Mock window.location
delete global.window.location;
global.window.location = {
  href: '',
  toString: () => {
    return global.window.location.href;
  },
};

const mockConfig = {
  config$: of({
    serviceProviderId: 'VBC',
  }),
};

const mockDataSvc = {
  defaultLocation$: of({}),
};

const SUBSET_OF_BUSINESS_APP_LOCATION_SWITCH_CONFIG = {
  locationSwitchConfig: {
    supportedLocationContextConfig: [
      {
        context: 'brand' as const,
        locationParamKey: 'brandname',
      },
      {
        context: 'location' as const,
        locationParamKey: 'agid',
      },
    ],
    defaultPath: 'account/unset/id_placeholder/home',
    loadingPath: 'account/unset/id_placeholder/switching-location',
    blockListedPaths: [
      // these paths have domain requirements that make incompatible with direct location switching
      /settings\/email/i,
      /settings\/notifications/i,
      /settings\/inbox/i,
      /settings\/connections/i,
      { blockListedPath: /settings\/widgets/i, destUrl: 'account/location/id_placeholder/settings/inbox' },
      /settings\/ai-knowledge/i, // This libraries design does not support location switching

      /automations/i, // https://vendasta.jira.com/browse/BREW-716
    ],
  },
};

const APP_WITH_ONLY_SINGLE_LOCATION_SUPPORT_SWITCH_CONFIG = {
  supportedLocationContextConfig: [
    {
      context: 'location' as const,
      locationParamKey: 'agid',
    },
  ],
  defaultPath: 'account/unset/id_placeholder/home',
  blockListedPaths: [],
};

@Component({
  standalone: true,
  template: `<div>nothing to see here</div>`,
})
class DummyComponent {}

describe('LocationListItemComponent', () => {
  let component: LocationListItemComponent;
  let fixture: ComponentFixture<LocationListItemComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        MatIconModule,
        MatMenuModule,
        MatListModule,
        TranslateTestingModule.withTranslations({}),
        GalaxyBadgeModule,
        BusinessNavModule.forRoot(SUBSET_OF_BUSINESS_APP_LOCATION_SWITCH_CONFIG),
      ],
      declarations: [LocationListItemComponent],
      providers: [
        { provide: BusinessNavConfigService, useValue: mockConfig },
        provideRouter([
          {
            path: 'account/location/:agid',
            children: [
              { path: 'home', component: DummyComponent },
              { path: 'somewhere', component: DummyComponent },
              {
                path: 'executive-report',
                children: [{ path: ':frequency', component: DummyComponent }],
              },
              {
                path: 'report',
                redirectTo: 'executive-report',
                pathMatch: 'prefix',
              },
              {
                path: 'settings',
                children: [
                  { path: 'inbox', component: DummyComponent },
                  {
                    path: 'widgets',
                    children: [{ path: ':widgetId/edit', component: DummyComponent }],
                  },
                ],
              },
              { path: '**', redirectTo: 'home' },
            ],
          },
          {
            path: 'account/brands/:brandname',
            children: [
              { path: 'home', component: DummyComponent },
              { path: 'report', component: DummyComponent },
              { path: '**', redirectTo: 'home' },
            ],
          },
        ]),
        provideHttpClient(),
        { provide: BusinessNavDataService, useValue: mockDataSvc },
        { provide: SnackbarService, useValue: null },
      ],
    }).compileComponents();
  }));

  describe('single location', () => {
    // location of the location list item (where you are going)
    let location: Location;
    let firstHref: string;

    beforeEach(() => {
      global.window.location.href = 'https://vendasta-training.smblogin.com/account/location/AG-123/home';
      const mockAccountGroup: AccountGroupInterface = {
        accountGroupId: 'AG-321',
      };
      location = new Location({ accountGroup: mockAccountGroup });
      firstHref = global.window.location.toString();
      TestBed.overrideProvider(BusinessNavConfigService, {
        useValue: {
          config$: of({ accountId: 'AG-123', serviceProviderId: 'VBC' }),
        },
      });
    });

    it('should exit early if current location is the same as location list item', async () => {
      global.window.location.href = 'https://vendasta-training.smblogin.com/account/location/AG-321/home';
      firstHref = global.window.location.toString();
      TestBed.overrideProvider(BusinessNavConfigService, {
        useValue: {
          config$: of({ accountId: 'AG-321', serviceProviderId: 'VBC' }),
        },
      });
      const router = TestBed.inject(Router);
      await router.navigateByUrl('/account/location/AG-321/home');
      fixture = TestBed.createComponent(LocationListItemComponent);
      component = fixture.componentInstance;
      fixture.componentRef.setInput('location', location);

      await component.goToLocation();

      // expect(router.navigated).toBe(false);
      expect(router.url).toBe('/account/location/AG-321/home');
      expect(window.location.href).toBe(firstHref);
    });

    it('should navigate from current location to location list item location', async () => {
      const router = TestBed.inject(Router);
      fixture = TestBed.createComponent(LocationListItemComponent);
      component = fixture.componentInstance;
      await router.navigateByUrl('/account/location/AG-123/home');
      fixture.componentRef.setInput('location', location);

      const wentTo = firstValueFrom(
        router.events.pipe(
          filter((event) => event instanceof NavigationEnd),
          map((event) => event.urlAfterRedirects),
        ),
      );

      await component.goToLocation();

      expect(router.navigated).toBe(true);
      expect(router.lastSuccessfulNavigation).not.toBeNull();
      expect(await wentTo).toBe('/account/location/AG-321/home');
      expect(router.url).toBe('/account/location/AG-321/home');
    });

    it('should replace the current location id with the new location id', async () => {
      const router = TestBed.inject(Router);
      await router.navigateByUrl('/account/location/AG-123/settings/inbox');
      fixture = TestBed.createComponent(LocationListItemComponent);
      component = fixture.componentInstance;
      fixture.componentRef.setInput('location', location);

      const wentTo = firstValueFrom(
        router.events.pipe(
          filter((event) => event instanceof NavigationEnd),
          skip(1), // skip first navigation to intermediate loading path route
          map((event) => event.urlAfterRedirects),
        ),
      );

      await component.goToLocation();

      expect(router.navigated).toBe(true);
      expect(router.lastSuccessfulNavigation).not.toBeNull();
      expect(router.url).toBe('/account/location/AG-321/settings/inbox');
      expect(await wentTo).toBe('/account/location/AG-321/settings/inbox');
    });

    // regression test (haven't pinned what caused the regression)
    it('should navigate to the config destination Url when provided', async () => {
      const router = TestBed.inject(Router);
      await router.navigateByUrl('/account/location/AG-123/settings/widgets/789/edit');
      fixture = TestBed.createComponent(LocationListItemComponent);
      component = fixture.componentInstance;
      fixture.componentRef.setInput('location', location);

      const wentTo = firstValueFrom(
        router.events.pipe(
          filter((event) => event instanceof NavigationEnd),
          skip(1), // skip first navigation to intermediate loading path route
          map((event) => event.urlAfterRedirects),
        ),
      );

      await component.goToLocation();

      expect(router.navigated).toBe(true);
      expect(router.lastSuccessfulNavigation).not.toBeNull();
      expect(await wentTo).toBe('/account/location/AG-321/settings/inbox');
      expect(router.url).toBe('/account/location/AG-321/settings/inbox');
    });

    it('returns to business app home if switching to an account where the current service provider is inaccessible', async () => {
      global.window.location.href = 'http://vendasta-university.steprep.com/account/AG-123/overview/';
      firstHref = global.window.location.toString();
      TestBed.overrideProvider(BusinessNavConfigService, {
        useValue: {
          config$: of({
            accountId: 'AG-123',
            serviceProviderId: 'SM',
          }),
        },
      });
      fixture = TestBed.createComponent(LocationListItemComponent);
      component = fixture.componentInstance;
      location = new Location({
        accountGroup: {
          accountGroupId: 'AG-321',
          url: 'https://vendasta-training.smblogin.com/account/location/AG-321',
        },
      });
      fixture.componentRef.setInput('location', location);

      await component.goToLocation();

      expect(global.window.location.href).toBe('https://vendasta-training.smblogin.com/account/location/AG-321/home');
    });

    it('should go to a brand from a single location on blocklisted path', async () => {
      global.window.location.href = 'https://vendasta-training.smblogin.com/account/location/AG-123/settings/inbox';
      const router = TestBed.inject(Router);
      fixture = TestBed.createComponent(LocationListItemComponent);
      component = fixture.componentInstance;
      await router.navigateByUrl('/account/location/AG-123/settings/inbox');

      const mockBrand: BrandInterface = {
        pathNodes: ['G-12'],
        url: 'https://vendasta-training.smblogin.com/account/brands/G-12',
      };
      location = new Location({ brand: mockBrand });
      fixture.componentRef.setInput('location', location);

      const wentTo = firstValueFrom(
        router.events.pipe(
          filter((event) => event instanceof NavigationEnd),
          map((event) => event.urlAfterRedirects),
        ),
      );

      await component.goToLocation();

      expect(router.navigated).toBe(true);
      expect(router.lastSuccessfulNavigation).not.toBeNull();
      expect(await wentTo).toBe('/account/brands/G-12/home');
      expect(router.url).toBe('/account/brands/G-12/home');
    });
  });

  describe('multi location', () => {
    // location of the location list item (where you are going)
    let location: Location;
    let firstHref: string;

    beforeEach(() => {
      global.window.location.href = 'https://vendasta-training.smblogin.com/account/brands/G-6/home';
      const mockBrand: BrandInterface = {
        pathNodes: ['G-12'],
        url: 'https://vendasta-training.smblogin.com/account/brands/G-12',
      };
      location = new Location({ brand: mockBrand });
      firstHref = global.window.location.toString();
      TestBed.overrideProvider(BusinessNavConfigService, {
        useValue: {
          config$: of({ groupPath: 'G-6', serviceProviderId: 'VBC' }),
        },
      });
    });

    it('should exit early if current location is the same as location list item', async () => {
      TestBed.overrideProvider(BusinessNavConfigService, {
        useValue: {
          config$: of({ groupPath: 'G-12', serviceProviderId: 'VBC' }),
        },
      });
      const router = TestBed.inject(Router);
      fixture = TestBed.createComponent(LocationListItemComponent);
      component = fixture.componentInstance;
      router.navigateByUrl('/account/brands/G-12/home');
      fixture.componentRef.setInput('location', location);

      await component.goToLocation();

      // expect(router.navigated).toBe(false);
      expect(router.url).toBe('/account/brands/G-12/home');
      expect(window.location.href).toBe(firstHref);
    });

    it('should go home if switch config does not support the context of the location list item', async () => {
      global.window.location.href = 'https://some-app.com/account/location/AG-123/somewhere';
      firstHref = global.window.location.toString();
      const mockBrand: BrandInterface = {
        pathNodes: ['G-12'],
        url: 'https://some-app.com/account/brands/G-12',
      };
      location = new Location({ brand: mockBrand });
      TestBed.overrideProvider(LOCATION_SWITCH_CONFIG_TOKEN, {
        useValue: APP_WITH_ONLY_SINGLE_LOCATION_SUPPORT_SWITCH_CONFIG,
      });
      const router = TestBed.inject(Router);
      fixture = TestBed.createComponent(LocationListItemComponent);
      component = fixture.componentInstance;
      router.navigateByUrl('/account/location/AG-123/somewhere');
      fixture.componentRef.setInput('location', location);

      await component.goToLocation();

      expect(global.window.location.href).toBe('https://some-app.com/account/brands/G-12/home');
    });

    it('should go from a brand report to single location home when the routes do not match', async () => {
      global.window.location.href = 'https://vendasta-training.smblogin.com/account/brands/G-6/report';
      firstHref = global.window.location.toString();
      const mockAccountGroup: AccountGroupInterface = {
        accountGroupId: 'AG-321',
      };
      location = new Location({ accountGroup: mockAccountGroup });
      const router = TestBed.inject(Router);
      fixture = TestBed.createComponent(LocationListItemComponent);
      component = fixture.componentInstance;
      router.navigateByUrl('/account/brands/G-6/report');
      fixture.componentRef.setInput('location', location);

      await component.goToLocation();

      expect(router.navigated).toBe(true);
      expect(router.lastSuccessfulNavigation).not.toBeNull();
      expect(router.url).toBe('/account/location/AG-321/home');
    });
  });
});
