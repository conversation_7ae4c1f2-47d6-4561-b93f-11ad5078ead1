import { ElementRef, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { delay, filter, map, shareReplay } from 'rxjs/operators';
import { LocationMode } from '../location-mode';

export interface Tab {
  id: string;
  elem: ElementRef;
  mode: LocationMode;
}

@Injectable({ providedIn: 'root' })
export class BusinessNavLocationTabService {
  private selectedTab$$: BehaviorSubject<Tab> = new BehaviorSubject<Tab>(null);
  private selectedTab$: Observable<Tab> = this.selectedTab$$.asObservable().pipe(delay(0)); // delay 0 for the ink bar to be the right width
  public selectedTabElement$: Observable<ElementRef>;
  public selectedTabId$: Observable<string>;
  public selectedTabMode$: Observable<LocationMode>;

  private tabs: Tab[] = [];

  constructor() {
    this.selectedTabId$ = this.selectedTab$.pipe(
      map((tab: Tab) => (tab ? tab.id : '')),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
    this.selectedTabElement$ = this.selectedTab$.pipe(
      filter((tab: Tab) => tab !== null),
      map((tab: Tab) => tab.elem),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
    this.selectedTabMode$ = this.selectedTab$.pipe(
      map((tab: Tab) => (tab ? tab.mode : 'both')),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  private getTabById(id: string): Tab {
    return this.tabs.find((tab: Tab) => tab.id === id);
  }

  public selectTab(tabId: string): void {
    const tab = this.getTabById(tabId);
    if (!tab) {
      console.log('Could not select tab, was it registered?');
      return;
    }
    this.selectedTab$$.next(tab);
  }

  public registerTabIndex(tab: Tab): void {
    this.tabs.push(tab);
  }

  public clear(): void {
    this.tabs = [];
    this.selectedTab$$.next(null);
  }
}
