@use 'design-tokens' as *;
@use '../../../../common' as *;

.business-navbar__location-list-item__container {
  display: flex;
  align-items: center;
  height: 72px;
  padding: 0 16px;
  color: rgba(0, 0, 0, 0.87);

  &:hover {
    background-color: $li-background;
  }
}

.business-navbar__location-list-item__container.business-navbar__location-list-item__container--active {
  background-color: $li-background;

  &:hover {
    background-color: $li-background;
  }
}

.business-navbar__location-list-item__container.business-navbar__location-list-item__container--current-location {
  background-color: $primary-background-selected-color;
}

.business-navbar__location-list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100%;
  color: inherit;
  flex-grow: 1;
  overflow: hidden;
}

.business-navbar__location-list-item__icon {
  user-select: none;
  white-space: nowrap;
  text-align: center;
  min-width: 24px;
  max-width: 24px;
}

.product-icon {
  width: $spacing-4;
  height: $spacing-4;
}
