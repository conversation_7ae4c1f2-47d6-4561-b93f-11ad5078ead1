$ink-bar-color: #1e88e5;

:host {
  display: flex;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

:host ::ng-deep .business-navbar__tab-header__tab {
  height: 48px;
  padding: 0 24px;
  cursor: pointer;
  box-sizing: border-box;
  opacity: 0.6;
  min-width: 160px;
  text-align: center;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  position: relative;
  flex-grow: 1;
}

:host ::ng-deep .business-navbar__tab-header__tab--selected {
  color: $ink-bar-color;
  opacity: 1;
}

.business-navbar__tab-header__container {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  z-index: 1;
}

.business-navbar__tab-header__container__tab-list {
  flex-grow: 1;
  position: relative;
  transition: transform 0.5s cubic-bezier(0.35, 0, 0.25, 1);
  transform: translateX(0px);
}

.business-navbar__tab-header__container__labels {
  display: flex;
}

.business-navbar__tab-header__paginator {
  user-select: none;
  position: relative;
  display: none;
  justify-content: center;
  align-items: center;
  min-width: 32px;
  z-index: 2;
  touch-action: none;
  box-shadow: none;
  cursor: default;

  .business-navbar__pagination-chevron {
    border-color: rgba(0, 0, 0, 0.38);
  }
}

.business-navbar__tab-header__paginator--visible {
  display: flex;
}

.business-navbar__tab-header__paginator--enabled {
  cursor: pointer;
  box-shadow:
    0 2px 4px -1px rgba(0, 0, 0, 0.2),
    0 4px 5px 0 rgba(0, 0, 0, 0.14),
    0 1px 10px 0 rgba(0, 0, 0, 0.12);
  .business-navbar__pagination-chevron {
    border-color: rgba(0, 0, 0, 0.87);
  }
}

.business-navbar__tab-header__paginator--before .business-navbar__pagination-chevron {
  transform: rotate(-135deg);
}

.business-navbar__tab-header__paginator--after .business-navbar__pagination-chevron {
  transform: rotate(45deg);
}

.business-navbar__pagination-chevron {
  border-style: solid;
  border-width: 2px 2px 0 0;
  content: '';
  height: 8px;
  width: 8px;
}

.business-navbar__tab-header__ink-bar {
  position: relative;
  bottom: 0;
  height: 2px;
  background-color: $ink-bar-color;
  user-select: none;
  transition: left 0.5s cubic-bezier(0.35, 0, 0.25, 1);
}
