<div
  class="
    business-navbar__tab-header__paginator
    business-navbar__tab-header__paginator--before
  "
  [class.business-navbar__tab-header__paginator--visible]="
    chevronsVisible$ | async
  "
  [class.business-navbar__tab-header__paginator--enabled]="
    leftChevronEnabled$ | async
  "
  (click)="scrollLeft(container)"
>
  <div class="business-navbar__pagination-chevron"></div>
</div>
<div
  class="business-navbar__tab-header__container"
  #container
  (scroll)="containerScrolled(container)"
  (resize)="calcChevronVisible(container)"
>
  <div class="business-navbar__tab-header__container__tab-list">
    <div class="business-navbar__tab-header__container__labels">
      <ng-content></ng-content>
    </div>
    <div
      class="business-navbar__tab-header__ink-bar"
      [style.width.px]="inkBarWidth$ | async"
      [style.left.px]="inkBarOffset$ | async"
    ></div>
  </div>
</div>
<div
  class="
    business-navbar__tab-header__paginator
    business-navbar__tab-header__paginator--after
  "
  [class.business-navbar__tab-header__paginator--visible]="
    chevronsVisible$ | async
  "
  [class.business-navbar__tab-header__paginator--enabled]="
    rightChevronEnabled$ | async
  "
  (click)="scrollRight(container)"
>
  <div class="business-navbar__pagination-chevron"></div>
</div>
