import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { BusinessNavLocationTabService } from './tab.service';
import { LocationMode } from '../location-mode';

@Component({
  selector: 'bc-location-tab',
  templateUrl: './tab.component.html',
  styleUrls: ['./tab.component.scss'],
  standalone: false,
})
export class LocationTabComponent implements OnInit {
  @ViewChild('tabElem', { read: ElementRef, static: true }) tabElem: ElementRef;
  @Input() label: string;
  @Input() isDefaultSelected: boolean;
  @Input() mode: LocationMode;
  tabId: string;

  constructor(
    public tabService: BusinessNavLocationTabService,
    public elRef: ElementRef,
  ) {}

  ngOnInit(): void {
    // For readability in html, each tab should have different text. It also shouldn't ever change
    this.tabId = 'tab-' + this.makeId();

    this.tabService.registerTabIndex({ id: this.tabId, elem: this.tabElem, mode: this.mode });
    if (this.isDefaultSelected) {
      this.tabService.selectTab(this.tabId);
    }
  }

  private makeId(): string {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 10; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  }
}
