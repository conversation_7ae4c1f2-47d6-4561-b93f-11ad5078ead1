import { Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { AtlasApiService, ListLocationsRequestInterface } from '@vendasta/atlas';
import { Scrollable } from '@vendasta/rx-utils';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, shareReplay, startWith } from 'rxjs/operators';
import { BusinessNavConfigService } from '../../config.service';
import { BusinessNavLocationTabService } from './tab/tab.service';

export const LOCATIONS_PAGE_SIZE = 25;

@Injectable({ providedIn: 'root' })
export class BusinessNavLocationService {
  private query$$: BehaviorSubject<string> = new BehaviorSubject<string>(null);

  public readonly scrollableLocations = new Scrollable(
    combineLatest([
      this.query$$.pipe(distinctUntilChanged(), debounceTime(500)),
      this.configService.partnerId$,
      this.tabService.selectedTabMode$,
    ]).pipe(map(([query, partnerId, mode]) => ({ query, partnerId, mode }))),
    ({ query, partnerId, mode }, cursor) => {
      const req: ListLocationsRequestInterface = {
        search: query,
        cursor: cursor as string,
        pageSize: LOCATIONS_PAGE_SIZE,
        partnerId: partnerId,
        includeAccountGroups: true,
        includeBrands: true,
      };
      switch (mode) {
        case 'single': {
          req.includeBrands = false;
          break;
        }
        case 'brands': {
          req.includeAccountGroups = false;
          break;
        }
      }
      return this.apiService.listLocations(req).pipe(
        map((resp) => {
          return {
            cursor: resp.cursor ?? null,
            hasMore: resp.hasMore ?? false,
            items: resp.locations,
          };
        }),
      );
    },
  );

  readonly activePath$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(this.router.url),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  constructor(
    private tabService: BusinessNavLocationTabService,
    private router: Router,
    private apiService: AtlasApiService,
    private configService: BusinessNavConfigService,
  ) {}

  public loadMore(): void {
    this.scrollableLocations.loadMore();
  }

  public setQuery(input: string): void {
    this.query$$.next(input);
  }
}
