<div
  class="business-navbar__location-list-item__container"
  [class.business-navbar__location-list-item__container--active]="isActive()"
  [class.business-navbar__location-list-item__container--current-location]="isCurrentLocation()"
>
  @if (!isCurrentLocation()) {
    <a (click)="goToLocation($event)" class="business-navbar__location-list-item">
      <ng-content *ngTemplateOutlet="item"></ng-content>
    </a>
  }

  @if (isCurrentLocation()) {
    <div class="business-navbar__location-list-item">
      <ng-content *ngTemplateOutlet="item"></ng-content>
    </div>
  }

  <button mat-icon-button color="primary" [matMenuTriggerFor]="menu">
    <mat-icon>more_vert</mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    @if (location().accountGroup) {
      <button mat-menu-item (click)="goToLocation($event)" [disabled]="isCurrentLocation()">
        <mat-icon>swap_horiz</mat-icon>
        {{ 'SWITCH_LOCATION_MODAL.SELECT_LOCATION' | translate }}
      </button>
      @if ((dataService.defaultLocation$ | async)?.accountGroupId !== location().accountGroup.accountGroupId) {
        <button mat-menu-item (click)="setDefaultLocation(location().accountGroup.accountGroupId)">
          <mat-icon>bookmark</mat-icon>
          {{ 'SWITCH_LOCATION_MODAL.MAKE_DEFAULT' | translate }}
        </button>
      }
    }
    @if (location()?.brand?.pathNodes?.join('|'); as groupPath) {
      <button mat-menu-item (click)="goToLocation($event)" [disabled]="isCurrentLocation()">
        <mat-icon>swap_horiz</mat-icon>
        {{ 'SWITCH_LOCATION_MODAL.SELECT_LOCATION' | translate }}
      </button>
      @if ((dataService.defaultLocation$ | async)?.groupId !== groupPath) {
        <button mat-menu-item (click)="setDefaultLocation(null, groupPath)">
          <mat-icon>bookmark</mat-icon>
          {{ 'SWITCH_LOCATION_MODAL.MAKE_DEFAULT' | translate }}
        </button>
      }
    }
  </mat-menu>
</div>

<ng-template #item>
  <mat-list-item [activated]="isCurrentLocation()">
    @if (!isCurrentLocation() && location().brand) {
      <svg
        matListItemIcon
        class="material-icons business-navbar__location-list-item__icon"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        x="0px"
        y="0px"
        viewBox="0 0 24 24"
        style="enable-background: new 0 0 24 24"
        xml:space="preserve"
      >
        <g>
          <path
            d="M22.5,6.3h-16v2h16V6.3z M23.5,16.3v-2l-1-5h-16l-1,5v2h1v6h10v-6h4v6h2v-6H23.5z M14.5,20.3h-6v-4h6V20.3z"
          />
          <path
            d="M17.6,1.3h-16v2h16V1.3z M5.7,5.3l12.3,0.1l-0.3-1.1h-16l-1,5v2h1v6h3v-2l-1,0v-4l1.6,0l0.5-2.2L5.7,5.3z"
          />
        </g>
      </svg>
    }
    @if (isCurrentLocation() && location().brand) {
      <mat-icon matListItemIcon>check</mat-icon>
    }
    @if (location().accountGroup) {
      <mat-icon matListItemIcon>{{ isCurrentLocation() ? 'check' : 'store' }}</mat-icon>
    }
    <div matListItemTitle>{{ location().accountGroup ? location().accountGroup.name : location().brand.name }}</div>
    <div matListItemLine>{{ location().accountGroup ? location().accountGroup.address : 'Group' }}</div>
  </mat-list-item>
  @if ((hasProduct$ | async) && iconUrl()) {
    <img src="{{ iconUrl() }}" class="product-icon" />
  }
  @if ((isDefault$ | async) === true) {
    <glxy-badge>{{ 'SWITCH_LOCATION_MODAL.DEFAULT' | translate }}</glxy-badge>
  }
</ng-template>
