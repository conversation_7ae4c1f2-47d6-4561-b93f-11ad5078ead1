import {
  AfterContentInit,
  Component,
  ContentChildren,
  ElementRef,
  OnDestroy,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { BehaviorSubject, Observable, Subject, combineLatest, fromEvent } from 'rxjs';
import { filter, startWith, takeUntil } from 'rxjs/operators';
import { LocationTabComponent } from '../tab/tab.component';
import { BusinessNavLocationTabService } from '../tab/tab.service';

@Component({
  selector: 'bc-tab-header',
  templateUrl: './tab-header.component.html',
  styleUrls: ['./tab-header.component.scss'],
  standalone: false,
})
export class TabHeaderComponent implements OnInit, AfterContentInit, OnDestroy {
  @ContentChildren(LocationTabComponent) tabsContent: LocationTabComponent[];
  @ViewChild('container', { static: true }) container: ElementRef;
  private _destroyed$$: Subject<void> = new Subject<void>();
  private _selectedTab$$: BehaviorSubject<LocationTabComponent> = new BehaviorSubject<LocationTabComponent>(null);
  private _tabs$$: BehaviorSubject<LocationTabComponent[]> = new BehaviorSubject<LocationTabComponent[]>([]);
  private _inkBarWidth$$: Subject<number> = new Subject<number>();
  private _inkBarOffset$$: Subject<number> = new Subject<number>();
  private _leftChevronEnabled$$: Subject<boolean> = new Subject<boolean>();
  private _rightChevronEnabled$$: Subject<boolean> = new Subject<boolean>();
  private _chevronsVisible$$: Subject<boolean> = new Subject<boolean>();

  public inkBarWidth$: Observable<number> = this._inkBarWidth$$.asObservable();
  public inkBarOffset$: Observable<number> = this._inkBarOffset$$.asObservable();
  public leftChevronEnabled$: Observable<boolean> = this._leftChevronEnabled$$.asObservable();
  public chevronsVisible$: Observable<boolean> = this._chevronsVisible$$.asObservable();
  public rightChevronEnabled$: Observable<boolean> = this._rightChevronEnabled$$.asObservable().pipe(startWith(true));

  constructor(
    private tabService: BusinessNavLocationTabService,
    private renderer: Renderer2,
  ) {}

  ngOnInit(): void {
    combineLatest(this.tabService.selectedTabId$, this._tabs$$)
      .pipe(
        takeUntil(this._destroyed$$),
        filter(([, tabs]: [string, LocationTabComponent[]]) => tabs.length > 0),
      )
      .subscribe(([selectedTab, tabs]: [string, LocationTabComponent[]]) => {
        for (const tab of tabs) {
          if (!tab) {
            continue;
          }
          if (tab.tabId === selectedTab) {
            this.clearSelected();
            this.setSelected(tab);
            break;
          }
        }
      });

    fromEvent(window, 'resize')
      .pipe(takeUntil(this._destroyed$$))
      .subscribe(() => {
        this.calcChevronVisible(this.container);
        this.calcInkBar(this._selectedTab$$.getValue());
      });
  }

  ngAfterContentInit(): void {
    this.tabsContent.map((tab: LocationTabComponent) => {
      this.renderer.addClass(tab.elRef.nativeElement, 'business-navbar__tab-header__tab');
      tab.elRef.nativeElement.addEventListener('click', () => this.tabService.selectTab(tab.tabId));
    });
    this._tabs$$.next(this.tabsContent.map((t) => t));
    window.setTimeout(() => this.calcChevronVisible(this.container), 0);
  }

  calcChevronVisible(container: HTMLElement | ElementRef): void {
    let c: HTMLElement;
    if (container instanceof ElementRef) {
      c = container.nativeElement;
    } else {
      c = container as HTMLElement;
    }
    this._chevronsVisible$$.next(c.scrollWidth > c.clientWidth);
  }

  containerScrolled(container: HTMLElement): void {
    this._leftChevronEnabled$$.next(container.scrollLeft !== 0);
    this._rightChevronEnabled$$.next(container.scrollLeft + container.clientWidth < container.scrollWidth);
  }

  scrollRight(container: HTMLElement): void {
    this.scroll(container, container.scrollWidth * 0.35);
  }

  scrollLeft(container: HTMLElement): void {
    this.scroll(container, -container.scrollWidth * 0.35);
  }

  private scroll(container: HTMLElement, scrollAmount: number): void {
    // Handle IE
    if (typeof container.scrollBy !== 'undefined') {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      return;
    }
    container.scrollLeft += scrollAmount;
  }

  private clearSelected(): void {
    if (this._selectedTab$$.getValue() !== null) {
      this.renderer.removeClass(
        this._selectedTab$$.getValue().elRef.nativeElement,
        'business-navbar__tab-header__tab--selected',
      );
    }
  }

  private setSelected(tab: LocationTabComponent): void {
    if (tab === null) {
      return;
    }
    this.clearSelected();
    this._selectedTab$$.next(tab);
    this.renderer.addClass(tab.elRef.nativeElement, 'business-navbar__tab-header__tab--selected');
    tab.elRef.nativeElement.scrollIntoView({ behavior: 'smooth', inline: 'center' });
    this.calcInkBar(tab);
  }

  private calcInkBar(tab: LocationTabComponent): void {
    this._inkBarWidth$$.next(tab.elRef.nativeElement.clientWidth);
    this._inkBarOffset$$.next(tab.elRef.nativeElement.offsetLeft);
  }

  ngOnDestroy(): void {
    this._destroyed$$.next();
    this._destroyed$$.complete();
  }
}
