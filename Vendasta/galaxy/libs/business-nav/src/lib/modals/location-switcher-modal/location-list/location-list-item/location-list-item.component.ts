import { ListKeyManagerOption } from '@angular/cdk/a11y';
import { Component, computed, ElementRef, inject, input, output } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRouteSnapshot, Router, UrlTree } from '@angular/router';
import { Location } from '@vendasta/atlas';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { combineLatest, firstValueFrom, map, shareReplay, take } from 'rxjs';
import { isPathBlockListed, LOCATION_SWITCH_CONFIG_TOKEN } from '../../../../business-nav.module';
import { BusinessNavConfigService, Config } from '../../../../config.service';
import { BusinessNavDataService } from '../../../../data.service';
import { BusinessNavLocationService } from '../../location.service';

enum AccountType {
  brand = 'account/brands',
  location = 'account/location',
  unset = 'account/unset',
}
const AccountTypes = Object.values(AccountType);

const ID_PLACEHOLDER = 'id_placeholder';

@Component({
  selector: 'bc-location-list-item',
  templateUrl: './location-list-item.component.html',
  styleUrls: ['./location-list-item.component.scss'],
  standalone: false,
})
export class LocationListItemComponent implements ListKeyManagerOption {
  location = input.required<Location>();
  isActive = input(false);
  iconUrl = input<string | null>(null);
  selected = output();
  /** Never disabled for now, this is here for the interface implementation. */
  disabled = false;

  readonly dataService = inject(BusinessNavDataService);
  private readonly config = inject(BusinessNavConfigService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly router = inject(Router);
  private readonly elRef = inject(ElementRef);

  // Only use configFirstEmission for synchronous access to config where the values don't change for the life cycle of the app
  // If the values can change, use the config$ observable directly
  private readonly configFirstEmission = toSignal(
    this.config.config$.pipe(
      map((config: Config) => config),
      take(1),
    ),
    { requireSync: true },
  );
  private readonly serviceProviderId = computed(() => this.configFirstEmission().serviceProviderId);
  protected readonly isCurrentLocation = computed(() => {
    const sameAccountGroup =
      !!this.location().accountGroup &&
      this.configFirstEmission().accountId === this.location().accountGroup.accountGroupId;
    const sameGroup =
      !!this.location().brand && this.configFirstEmission().groupPath === this.location().brand.pathNodes.join('|');
    return sameAccountGroup || sameGroup;
  });

  private readonly businessNavLocationService = inject(BusinessNavLocationService);

  protected readonly hasProduct$ = combineLatest([
    this.businessNavLocationService.activePath$,
    this.config.config$,
  ]).pipe(
    map(([, config]) => {
      if (!this.location().accountGroup || !this.location().accountGroup.activatedProductIds) {
        return false;
      }

      return this.location().accountGroup.activatedProductIds.some(
        (activatedProductId) => activatedProductId === config.serviceProviderId,
      );
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  protected readonly defaultLocation$ = this.dataService.defaultLocation$;
  protected readonly isDefault$ = this.defaultLocation$.pipe(
    map(
      (defaultLocation) =>
        (this.location().accountGroup &&
          defaultLocation?.accountGroupId === this.location().accountGroup.accountGroupId) ||
        (this.location().brand && defaultLocation?.groupId === this.location().brand.pathNodes.join('|')),
    ),
  );

  private readonly locationSwitchConfig = inject(LOCATION_SWITCH_CONFIG_TOKEN);

  setDefaultLocation(accountGroupId?: string, groupId?: string): void {
    this.dataService.setDefaultLocation(accountGroupId, groupId).subscribe({
      error: () => this.snackbarService.openErrorSnack('COMMON.RESULT.FAILURE'),
    });
  }

  /** Gets the label for this option. */
  getLabel(): string {
    if (this.location().accountGroup) {
      return this.location().accountGroup.name;
    } else if (this.location().brand) {
      return this.location().brand.name;
    }
    // Location not provided yet, should probably not get here
    return '';
  }

  async goToLocation(event?: Event) {
    if (event) {
      event.stopPropagation();
    }
    this.selected.emit();
    if (this.isCurrentLocation()) {
      return;
    }

    const params = collectRouteParams(this.router);

    const configForContext = this.locationSwitchConfig.supportedLocationContextConfig.find(
      (config) =>
        config.context ===
        (this.router.url.includes('location') || this.location().accountGroup ? 'location' : 'brand'),
    );

    const canSwitchWithinServiceProvider =
      this.serviceProviderId().toUpperCase() === 'VBC' || (await firstValueFrom(this.hasProduct$));

    if (!configForContext || !canSwitchWithinServiceProvider) {
      const url = this.location().accountGroup?.url || this.location().brand?.url;
      window.location.href = url + '/home';
      return;
    }

    const replaceId = params[configForContext.locationParamKey] || ID_PLACEHOLDER;
    const [isBlockListed, destUrl] = isPathBlockListed(this.locationSwitchConfig, this.router.url);
    if (isBlockListed) {
      const loadingPath = this.interpolateUrl(
        this.locationSwitchConfig.loadingPath || this.locationSwitchConfig.defaultPath,
        ID_PLACEHOLDER,
      );
      const loadingUrl = this.router.createUrlTree([loadingPath]);
      let interpolatedUrl = '';
      if (destUrl.includes(replaceId)) {
        interpolatedUrl = this.interpolateUrl(destUrl, replaceId);
      } else {
        interpolatedUrl = this.interpolateUrl(destUrl, ID_PLACEHOLDER);
      }
      return this.navigateToUrl(interpolatedUrl, { loadPath: loadingUrl });
    }

    if (!this.router.url.includes(replaceId)) {
      const url = this.router.createUrlTree([this.locationSwitchConfig.defaultPath]).toString();
      return this.navigateToUrl(this.interpolateUrl(url, ID_PLACEHOLDER), {});
    }

    const url = this.interpolateUrl(this.router.url, replaceId);
    return this.navigateToUrl(url, {});
  }

  onScroll(): void {
    this.elRef.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }

  private interpolateUrl(url: string, replaceId: string): string {
    const useCategory = this.location().accountGroup ? AccountType.location : AccountType.brand;
    const interpolated = AccountTypes.reduce((acc, category) => acc.replace(category, useCategory), url);

    const useId = this.location().accountGroup
      ? this.location().accountGroup.accountGroupId
      : this.location().brand.pathNodes.join('|');

    return interpolated.replace(replaceId, useId);
  }

  private async navigateToUrl(url: string, { loadPath }: { loadPath?: UrlTree }) {
    if (this.serviceProviderId().toUpperCase() === 'VBC' && !loadPath) {
      return this.router.navigateByUrl(url);
    } else if (this.serviceProviderId().toUpperCase() === 'VBC' && loadPath) {
      return this.router
        .navigateByUrl(loadPath, {
          skipLocationChange: true,
          state: { target: url },
        })
        .then(() => this.router.navigateByUrl(url));
    } else {
      window.location.href = url;
    }
  }
}

function collectRouteParams(router: Router) {
  let params = {};
  const stack: ActivatedRouteSnapshot[] = [router.routerState.snapshot.root];
  while (stack.length > 0) {
    const route = stack.pop();
    params = { ...params, ...route.params };
    stack.push(...route.children);
  }
  return params;
}
