// add a smoke test for BusinessNavModule

import { TestBed } from '@angular/core/testing';
import { BusinessNavModule, LOCATION_SWITCH_CONFIG_TOKEN } from './business-nav.module';

describe('BusinessNavModule', () => {
  let mod: BusinessNavModule;
  let config: object;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        BusinessNavModule.forRoot({
          locationSwitchConfig: {
            defaultPath: 'account/unset/id_placeholder/dashboard',
            blockListedPaths: [/\/settings\//gi],
            supportedLocationContextConfig: [
              {
                context: 'brand',
                locationParamKey: 'brandname',
              },
              {
                context: 'location',
                locationParamKey: 'agid',
              },
            ],
          },
        }),
      ],
    });

    mod = TestBed.inject(BusinessNavModule);
    config = TestBed.inject(LOCATION_SWITCH_CONFIG_TOKEN);
  });

  it('should create', () => {
    expect(mod).toBeDefined();
  });

  it('strips the global flag from blocklistedPaths', () => {
    config['blockListedPaths'].forEach((path) => {
      expect(path.flags).not.toContain('g');
    });
  });
});
