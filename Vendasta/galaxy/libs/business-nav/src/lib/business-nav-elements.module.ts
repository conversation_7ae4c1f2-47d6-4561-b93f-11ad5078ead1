import { <PERSON><PERSON><PERSON><PERSON>p, Injector, NgModule } from '@angular/core';
import { BusinessNavModule } from '.';
import { DEFINE_CUSTOM_ELEMENTS } from './module-common';

@NgModule({
  imports: [BusinessNavModule],
})
export class LocalBusinessNavElementsModule implements DoBootstrap {
  constructor(private injector: Injector) {
    DEFINE_CUSTOM_ELEMENTS(this.injector);
  }

  // eslint-disable-next-line @angular-eslint/no-empty-lifecycle-method
  ngDoBootstrap(): void {
    // pass
  }
}
