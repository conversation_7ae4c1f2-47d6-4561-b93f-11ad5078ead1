import { Injectable } from '@angular/core';
import { Observable, ReplaySubject } from 'rxjs';
import { filter, map, shareReplay } from 'rxjs/operators';

export interface Config {
  partnerId: string;
  marketId: string | null;
  accountId: string | null;
  groupPath: string | null;
  serviceProviderId: string;
  defaultURL: string;
}

export enum SidenavType {
  NOT_SET = 0,
  ACCOUNT_GROUP = 1,
  BRAND = 2,
}

@Injectable({ providedIn: 'root' })
export class BusinessNavConfigService {
  private readonly config$$ = new ReplaySubject<Config>(1);
  public readonly config$ = this.config$$.asObservable();
  public readonly navType$: Observable<SidenavType>;
  public readonly isBrandType$: Observable<boolean>;
  public readonly isAccountGroupType$: Observable<boolean>;
  public readonly partnerId$: Observable<string>;
  public readonly marketId$: Observable<string>;
  public readonly accountGroupId$: Observable<string>;
  public readonly groupPath$: Observable<string>;
  public readonly serviceProviderId$: Observable<string>;
  public readonly locationId$: Observable<string>;

  constructor() {
    this.navType$ = this.config$.pipe(
      filter((config) => !!config && (!!config.accountId || !!config.groupPath)),
      map((config: Config) => {
        if (config.accountId) {
          return SidenavType.ACCOUNT_GROUP;
        } else if (config.groupPath) {
          return SidenavType.BRAND;
        }
        return SidenavType.NOT_SET;
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.isBrandType$ = this.navType$.pipe(
      map((type: SidenavType) => type === SidenavType.BRAND),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.isAccountGroupType$ = this.navType$.pipe(
      map((type: SidenavType) => type === SidenavType.ACCOUNT_GROUP),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.partnerId$ = this.config$.pipe(
      map((config: Config) => config?.partnerId),
      filter((pid) => pid !== null && pid !== undefined),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.marketId$ = this.config$.pipe(
      map((config: Config) => config.marketId),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.accountGroupId$ = this.config$.pipe(
      filter((config) => !!config && (!!config.accountId || !!config.groupPath)),
      map((config: Config) => config.accountId),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.groupPath$ = this.config$.pipe(
      filter((config) => !!config && (!!config.accountId || !!config.groupPath)),
      map((config: Config) => config.groupPath),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.serviceProviderId$ = this.config$.pipe(
      map((config: Config) => config.serviceProviderId),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.locationId$ = this.config$.pipe(
      filter((config) => !!config && (!!config.accountId || !!config.groupPath)),
      map((config: Config) => (config.accountId ? config.accountId : config.groupPath)),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  public initialize(
    partnerId: string,
    marketId: string,
    accountId: string,
    groupPath: string,
    serviceProviderId: string,
    defaultURL?: string,
  ): void {
    this.config$$.next({
      partnerId: partnerId,
      marketId: marketId || null,
      accountId: accountId || null,
      groupPath: groupPath || null,
      serviceProviderId: serviceProviderId,
      defaultURL: defaultURL,
    });
  }
}
