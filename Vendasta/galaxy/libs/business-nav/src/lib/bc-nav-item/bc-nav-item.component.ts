import { Component, Input } from '@angular/core';
import { SideNavigationItem } from '@vendasta/atlas';
import { Item } from './bc-nav-item.interface';

@Component({
  selector: 'bc-nav-item',
  templateUrl: './bc-nav-item.component.html',
  styleUrls: ['./bc-nav-item.component.scss'],
  standalone: false,
})
export class BcNavItemComponent {
  @Input({ required: true }) set item(item: SideNavigationItem) {
    if (item.sideNavigationSection) {
      this._item = {
        type: 'section',
        section: item.sideNavigationSection,
      };
    } else if (item.sideNavigationLink) {
      this._item = {
        type: 'link',
        link: item.sideNavigationLink,
      };
    } else if (item.sideNavigationContainer) {
      this._item = {
        type: 'container',
        container: item.sideNavigationContainer,
      };
    } else {
      throw new Error(`Unknown item type: ${item}`);
    }
  }

  protected _item: Item;
}
