import { HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Capacitor } from '@capacitor/core';
import {
  AtlasApiService,
  DropdownItem,
  ExitLinkConfiguration,
  GetNavigationDataRequest,
  GetNavigationDataResponse,
  GetSalesInfoRequest,
  GetSalesInfoResponse,
  PinnedItem,
  PlatformMode,
  SalesContact,
  SideNavigationItem,
  SideNavigationLink,
  UserViewType,
} from '@vendasta/atlas';
import {
  Observable,
  Subject,
  combineLatest,
  distinctUntilChanged,
  filter,
  map,
  merge,
  shareReplay,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { BusinessNavConfigService, Config } from './config.service';
import { signOutLabel } from './globals';

interface CachedGetNavigationDataResponse {
  locationId: string;
  response: GetNavigationDataResponse;
}

const DEFAULT_IMAGE_URL =
  'https://lh3.googleusercontent.com/70DGcv8fyAzy1n_cWh8GyVbsCIWzPGZMdBFiwJa8Ot-Ss5Rf_oZY8hKOG2wAttY4_iuU5TlO9NLlDrBGuEJBj6DHwRnGd2CJ32J2Im3c1g';

@Injectable({ providedIn: 'root' })
export class BusinessNavDataService {
  private readonly platformMode = Capacitor.isNativePlatform() ? PlatformMode.MOBILE : PlatformMode.WEB;
  private navigationData$ = this.cfgService.config$.pipe(
    filter((config) => !!config && (!!config.accountId || !!config.groupPath)),
    distinctUntilChanged((a, b) => {
      return (
        a.accountId === b.accountId &&
        a.groupPath === b.groupPath &&
        a.partnerId === b.partnerId &&
        a.marketId === b.marketId
      );
    }),
    switchMap((config) => {
      const locationId = BusinessNavDataService.locationIdFromConfig(config);
      const lastResponse = BusinessNavDataService.getLastResponse();
      return this.apiService
        .getNavigationData(
          new GetNavigationDataRequest({
            accountGroupId: config.accountId,
            groupPath: config.groupPath,
            partnerId: config.partnerId,
            marketId: config.marketId,
            platformMode: this.platformMode,
          }),
        )
        .pipe(
          tap((response) => BusinessNavDataService.rememberLastResponse({ locationId, response })),
          startWith(lastResponse?.locationId === locationId ? lastResponse.response : ('LOADING' as const)),
        );
    }),
    shareReplay({ refCount: false, bufferSize: 1 }),
    filter((res): res is GetNavigationDataResponse => res !== 'LOADING'),
  );

  readonly navigationLinks$ = this.navigationData$.pipe(
    map((res) => res?.navigationItems || []),
    map((allItems: SideNavigationItem[]) => {
      const pushlinks = (items: SideNavigationItem[]): SideNavigationLink[] => {
        return items.reduce((acc, item) => {
          if (item.sideNavigationContainer) {
            acc.push(...pushlinks(item.sideNavigationContainer.sideNavigationItems));
          }
          if (item.sideNavigationLink) {
            acc.push(item.sideNavigationLink);
            acc.push(...(item.sideNavigationLink.subLinks || []));
          }
          return acc;
        }, []);
      };

      return pushlinks(allItems);
    }),
  );
  readonly navigationIds$ = this.navigationLinks$.pipe(map((links) => links.map((link) => link.navigationId)));

  readonly linksWithSublinks$ = this.navigationData$.pipe(
    map((res) => res?.navigationItems || []),
    map((allItems: SideNavigationItem[]) => {
      return allItems
        .reduce(
          (accumulator, item) =>
            item.sideNavigationLink
              ? [...accumulator, item]
              : item.sideNavigationContainer
                ? [...accumulator, ...item.sideNavigationContainer.sideNavigationItems]
                : accumulator,
          [] as SideNavigationItem[],
        )
        .filter((item) => item.sideNavigationLink && item.sideNavigationLink.subLinks?.length > 0)
        .map((item) => item.sideNavigationLink);
    }),
  );

  private salesInfo$: Observable<GetSalesInfoResponse>;
  public disableBusinessNav$: Observable<boolean>;
  public disableProductSwitcher$: Observable<boolean>;
  public businessCenterName$: Observable<string>;
  public partnerName$: Observable<string>;
  public cobrandingLogoURL$: Observable<string>;
  public marketName$: Observable<string>;
  public marketLogoUrl$: Observable<string>;
  public darkMarketLogoUrl$: Observable<string>;
  public salespersonName$: Observable<string>;
  public salespersonPictureUrl$: Observable<string>;
  public currentBrandName$: Observable<string>;
  public salespersonContact$: Observable<SalesContact>;
  public navigationItems$: Observable<SideNavigationItem[]>;
  public dropdownItems$: Observable<DropdownItem[]>;
  public pinnedItems$: Observable<PinnedItem[]>;
  public language$: Observable<string>;
  public userViewType$: Observable<UserViewType>;
  public locationSwitchable$: Observable<boolean>;
  public businessAppBranding$: Observable<boolean>;
  public exitLinkConfiguration$: Observable<ExitLinkConfiguration>;

  public userId$: Observable<string>;
  public readonly defaultLocation$: Observable<{
    accountGroupId?: string;
    groupId?: string;
  }>;
  private defaultLocationUpdate$$ = new Subject<{ accountGroupId: string; groupId: string }>();

  private static rememberLastResponse(res: CachedGetNavigationDataResponse): void {
    localStorage.setItem(
      'business-nav-last-response',
      JSON.stringify({
        locationId: res.locationId,
        response: res.response.toApiJson(),
      }),
    );
  }

  private static getLastResponse(): CachedGetNavigationDataResponse {
    // eslint-disable-next-line @typescript-eslint/ban-types
    const o: { locationId: string; response: object } = JSON.parse(localStorage.getItem('business-nav-last-response'));
    if (!o || !o.response) {
      return null;
    }
    return {
      locationId: o.locationId,
      response: GetNavigationDataResponse.fromProto(o.response),
    };
  }

  private static locationIdFromConfig(cfg: Config): string {
    return cfg.accountId ? cfg.accountId : cfg.groupPath;
  }

  constructor(
    private apiService: AtlasApiService,
    private cfgService: BusinessNavConfigService,
  ) {
    this.salesInfo$ = this.cfgService.config$.pipe(
      filter((config: Config) => !!config && (!!config.accountId || !!config.groupPath)),
      distinctUntilChanged(),
      switchMap((config: Config) =>
        this.apiService
          .getSalesInfo(
            new GetSalesInfoRequest({
              accountGroupId: config.accountId,
              groupPath: config.groupPath,
            }),
          )
          .pipe(filter((res: GetSalesInfoResponse) => JSON.stringify(res.toApiJson()) !== '{}')),
      ),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.dropdownItems$ = combineLatest([this.cfgService.serviceProviderId$, this.navigationData$]).pipe(
      map(([serviceProviderId, res]: [string, GetNavigationDataResponse]) => {
        if (!res.dropdownItems) {
          return [];
        }
        // Disable all dropdown items except for logout when business nav is disabled
        if (serviceProviderId !== 'VBC' && res.disableBusinessNav) {
          return res.dropdownItems.filter(
            (dropdownItem: DropdownItem) => dropdownItem.translationId === 'NAVIGATION.MENU.SIGN_OUT',
          );
        }
        return res.dropdownItems;
      }),
      startWith([
        {
          label: signOutLabel,
          translationId: 'NAVIGATION.MENU.SIGN_OUT',
          url: '/logout/',
        } as DropdownItem,
      ]),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.disableBusinessNav$ = this.navigationData$.pipe(
      filter((res: GetNavigationDataResponse) => !!res.disableBusinessNav),
      map((res: GetNavigationDataResponse) => res.disableBusinessNav),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.disableProductSwitcher$ = this.navigationData$.pipe(
      filter((res: GetNavigationDataResponse) => !!res.disableProductSwitcher),
      map((res: GetNavigationDataResponse) => res.disableProductSwitcher),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.exitLinkConfiguration$ = this.navigationData$.pipe(
      filter((res: GetNavigationDataResponse) => !!res.branding),
      map((res: GetNavigationDataResponse) => res.branding.exitLinkConfiguration),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.businessCenterName$ = this.navigationData$.pipe(
      filter((res) => !!res.branding),
      map((res) => res.branding.centerName),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.marketLogoUrl$ = this.navigationData$.pipe(
      filter((res) => !!res.branding),
      map((res) => res.branding.logoUrl),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.darkMarketLogoUrl$ = this.navigationData$.pipe(
      filter((res) => !!res.branding),
      map((res) => res.branding.darkModeLogoUrl),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.partnerName$ = this.navigationData$.pipe(
      filter((res) => !!res.branding),
      map((res) => res.branding.partnerName),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.cobrandingLogoURL$ = this.navigationData$.pipe(
      filter((res) => !!res.branding),
      map((res) => res.branding.cobrandingLogoUrl),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.marketName$ = this.navigationData$.pipe(
      filter((res) => !!res.branding),
      map((res) => res.branding.marketName),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.salespersonName$ = this.salesInfo$.pipe(
      filter((res) => !!res.salesInfo && !!res.salesInfo.salesContact),
      map((res) => {
        const { firstName, lastName } = res.salesInfo.salesContact;
        if (!firstName) {
          return '';
        } else {
          return firstName + (lastName ? ' ' + lastName : '');
        }
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.salespersonPictureUrl$ = this.salesInfo$.pipe(
      filter((res) => !!res.salesInfo?.salesContact?.photoUrlSecure),
      map((res) => res.salesInfo.salesContact.photoUrlSecure || DEFAULT_IMAGE_URL),
      startWith(DEFAULT_IMAGE_URL),
    );

    this.salespersonContact$ = this.salesInfo$.pipe(
      filter((res) => !!res.salesInfo && !!res.salesInfo.salesContact),
      map((res) => res.salesInfo.salesContact),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.navigationItems$ = this.navigationData$.pipe(
      map((res) => res.navigationItems),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.pinnedItems$ = this.navigationData$.pipe(
      map((res) => res.pinnedItems),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.language$ = this.navigationData$.pipe(
      map((res) => res.language),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.currentBrandName$ = this.navigationData$.pipe(
      map((res) => res.currentBrandName),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.userViewType$ = this.navigationData$.pipe(
      map((res) => {
        if (!res.userView) {
          return UserViewType.USER_VIEW_TYPE_SMB;
        }
        return res.userView;
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.locationSwitchable$ = this.navigationData$.pipe(
      map((res) => {
        return (
          res.userView === UserViewType.USER_VIEW_TYPE_ADMIN ||
          (res.associatedLocationIds.accountGroupIds?.length ?? 0) +
            (res.associatedLocationIds.groupPaths?.length ?? 0) >
            1
        );
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.userId$ = this.navigationData$.pipe(
      map((resp: GetNavigationDataResponse) => resp.userId),
      distinctUntilChanged(),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.defaultLocation$ = merge(
      this.defaultLocationUpdate$$,
      this.cfgService.partnerId$.pipe(switchMap((partnerId) => this.apiService.getDefaultLocation({ partnerId }))),
    ).pipe(shareReplay({ refCount: true, bufferSize: 1 }));

    this.businessAppBranding$ = this.navigationData$.pipe(
      map((data) => data.businessAppBranding ?? false),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  setDefaultLocation(accountGroupId: string, groupId: string): Observable<HttpResponse<null>> {
    return this.cfgService.partnerId$.pipe(
      switchMap((partnerId) => this.apiService.setDefaultLocation({ partnerId, accountGroupId, groupId })),
      tap(() => this.defaultLocationUpdate$$.next({ accountGroupId, groupId })),
      take(1),
    );
  }
}
