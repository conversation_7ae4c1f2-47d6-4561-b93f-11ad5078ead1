import { DefaultUrlSerializer, UrlSerializer, UrlTree } from '@angular/router';
import { Injectable } from '@angular/core';

// Handle brand paths that contain a url encoded `/`
@Injectable({ providedIn: 'root' })
export class CustomUrlSerializer implements UrlSerializer {
  private _defaultUrlSerializer: DefaultUrlSerializer = new DefaultUrlSerializer();

  parse(url: any): UrlTree {
    url = url.replace('%252F', '%2F');
    return this._defaultUrlSerializer.parse(url);
  }

  serialize(tree: UrlTree): any {
    return this._defaultUrlSerializer.serialize(tree).replace('%252F', '%2F');
  }
}
