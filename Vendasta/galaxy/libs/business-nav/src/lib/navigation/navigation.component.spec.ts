import { PinnedItem, SideNavigationItem } from '@vendasta/atlas';
import { removeUnpinnedItems } from './navigation.component';

describe('removeUnpinnedItems', () => {
  it('should remove unpinned items', () => {
    const items = [
      { sideNavigationLink: { navigationId: '1', pinnable: true } },
      { sideNavigationLink: { navigationId: '2', pinnable: true } },
      { sideNavigationLink: { navigationId: '3', pinnable: true } },
      { sideNavigationContainer: { navigationId: '4', pinnable: true } },
      { sideNavigationContainer: { navigationId: '5', pinnable: true } },
    ] as SideNavigationItem[];
    const pins = [{ navigationId: '1' }, { navigationId: '3' }, { navigationId: '4' }] as PinnedItem[];
    expect(removeUnpinnedItems(items, pins)).toEqual([
      { sideNavigationLink: { navigationId: '1', pinnable: true } },
      { sideNavigationLink: { navigationId: '3', pinnable: true } },
      { sideNavigationContainer: { navigationId: '4', pinnable: true } },
    ]);
  });

  it('should not remove unpinnable items', () => {
    const items = [
      { sideNavigationLink: { navigationId: '1', pinnable: false } },
      { sideNavigationLink: { navigationId: '2', pinnable: false } },
      { sideNavigationLink: { navigationId: '3', pinnable: false } },
      { sideNavigationContainer: { navigationId: '4', pinnable: false } },
      { sideNavigationContainer: { navigationId: '5', pinnable: false } },
    ] as SideNavigationItem[];
    const pins = [{ navigationId: '1' }, { navigationId: '3' }] as PinnedItem[];
    expect(removeUnpinnedItems(items, pins)).toEqual([
      { sideNavigationLink: { navigationId: '1', pinnable: false } },
      { sideNavigationLink: { navigationId: '2', pinnable: false } },
      { sideNavigationLink: { navigationId: '3', pinnable: false } },
      { sideNavigationContainer: { navigationId: '4', pinnable: false } },
      { sideNavigationContainer: { navigationId: '5', pinnable: false } },
    ]);
  });

  it('should remove all pinnable items when pins are null', () => {
    const items = [
      { sideNavigationLink: { navigationId: '1', pinnable: false } },
      { sideNavigationLink: { navigationId: '2', pinnable: true } },
      { sideNavigationLink: { navigationId: '3', pinnable: false } },
      { sideNavigationContainer: { navigationId: '4', pinnable: true } },
      { sideNavigationContainer: { navigationId: '5', pinnable: false } },
    ] as SideNavigationItem[];
    const pins: PinnedItem[] = null;
    expect(removeUnpinnedItems(items, pins)).toEqual([
      { sideNavigationLink: { navigationId: '1', pinnable: false } },
      { sideNavigationLink: { navigationId: '3', pinnable: false } },
      { sideNavigationContainer: { navigationId: '5', pinnable: false } },
    ]);
  });

  it('should remove all pinnable items when no pins are provided', () => {
    const items = [
      { sideNavigationLink: { navigationId: '1', pinnable: false } },
      { sideNavigationLink: { navigationId: '2', pinnable: true } },
      { sideNavigationLink: { navigationId: '3', pinnable: false } },
      { sideNavigationContainer: { navigationId: '4', pinnable: true } },
      { sideNavigationContainer: { navigationId: '5', pinnable: false } },
    ] as SideNavigationItem[];
    const pins: PinnedItem[] = [];
    expect(removeUnpinnedItems(items, pins)).toEqual([
      { sideNavigationLink: { navigationId: '1', pinnable: false } },
      { sideNavigationLink: { navigationId: '3', pinnable: false } },
      { sideNavigationContainer: { navigationId: '5', pinnable: false } },
    ]);
  });
});
