@use 'design-tokens' as *;
@use '@angular/material' as mat;

$panel-width: 276px;
$new-panel-width: 310px;

.atlas-spacer {
  flex: 1 1 auto;
}

.back-to-dashboard {
  padding: $spacing-1 $spacing-2 $spacing-1 $spacing-2;
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100%;
  text-align: left;

  a {
    color: $primary-text-color;
    display: flex;
    align-items: center;
    text-decoration: none;
  }

  mat-icon {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);

    margin-right: $spacing-2;
  }
}

.toolbar-location-selector {
  display: flex;
  flex: 1 1 auto;
}

.mobile-nav-open {
  opacity: 0;
  transition: opacity 0.25s ease;
}

.mobile-nav-close {
  opacity: 1;
  transition: opacity 0.25s ease;
}

.toolbar-left {
  overflow: hidden;
}

.page-content {
  height: calc(100dvh - $atlas-bar-height);
  overflow: hidden;

  &.mobile {
    height: unset;
    position: relative;
  }
}

@media print {
  .page-content {
    height: auto;
    overflow: visible;
  }
}

glxy-nav-panel {
  width: $new-panel-width;
  overflow-y: auto;
  background-color: $primary-background-color;
}

:not(:has(.new-nav)) {
  :host glxy-nav-panel {
    width: $panel-width;
    background-color: var(--themingPrimaryColor);
  }

  :host .back-to-dashboard a {
    color: var(--themingFontColor);
  }
}

.portal-container {
  display: flex;
  flex-direction: column;
  width: $panel-width;

  .menu-button {
    height: 40px;
    width: 48px;
  }

  .nav-container {
    height: 100%;
  }

  &.new-nav {
    width: $new-panel-width;
  }
}

@media print {
  bc-sidebar {
    display: none !important;
  }
  glxy-navigation-toolbar {
    display: none !important;
  }
}

:has(.new-nav) {
  .page-content {
    box-sizing: border-box;
    background-color: $card-background-color;
    border: 1px solid $border-color;
    border-bottom: none;
    border-radius: $spacing-3 $spacing-3 0 0;
    margin-inline: 1px $spacing-4;
    height: calc(100dvh - $atlas-bar-height - env(safe-area-inset-top));

    &:has(.business-center-client__navigation__drawer-open) {
      margin-inline: 1px 0;

      ::ng-deep .glxy-navigation-toolbar {
        margin-right: -$new-panel-width;
      }
    }

    &.mobile {
      margin-inline: 0;
    }

    &.nav-closed {
      @media screen and (min-width: $media--tablet-minimum) {
        margin-inline: $spacing-4;
      }
    }

    @media print {
      margin-inline: 0;
      outline: none;
    }
  }

  .header {
    background-color: unset;
    color: $primary-text-color;
  }

  .footer-container {
    background-color: $primary-background-color;
    color: $primary-text-color;

    .contact-message-text {
      color: $secondary-text-color;
    }
  }

  .primary-item {
    background: $primary-background-color;

    &:hover {
      background: $nav-list-item-hover-color;
    }

    &:active {
      background: $nav-list-item-active-color;
    }
  }

  .powered-by-text {
    color: $secondary-text-color;
  }
}

.no-content-overlay {
  ::ng-deep .mat-sidenav-container.glxy-nav--mat-sidenav-container > .mat-drawer-backdrop {
    background-color: unset;
  }

  ::ng-deep .glxy-nav--mat-sidenav {
    box-shadow: none;
  }
}

.header {
  background-color: $white;
  color: $grey;

  padding: $spacing-3;
  text-align: center;

  .logo {
    max-width: 100%;
    height: 120px;
    transition: max-height 0.2s cubic-bezier(0.4, 0, 1, 1);
    overflow: hidden;
  }

  .logo-img {
    max-width: 242px;
    max-height: 120px;
  }

  .center {
    font-size: $font-preset-5-size;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    margin-top: $spacing-3;
    min-height: 14px;
  }

  .center-no-logo {
    margin-top: 0 !important;
  }

  .center-name {
    font-size: $font-preset-5-size;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    margin-top: $spacing-3;
    height: 14px;
  }
}

.nav-panel-close-button {
  margin-left: $spacing-2;
}

.primary-item {
  background: var(--themingPrimaryColor);

  &:hover {
    background: var(--themingPrimaryHoverColor);
  }

  &:active {
    background: var(--themingPrimaryActiveColor);
  }
}

.sidebar-powered-by {
  padding-bottom: $spacing-2;
}

.powered-by-logo {
  height: 14px;
  vertical-align: middle;
}

.powered-by-text {
  font-size: $font-preset-5-size;
  font-style: italic;
  color: $grey;
  padding-right: $spacing-1;
}

.footer-container {
  background-color: $white;
  color: $glxy-grey-700;
  padding-bottom: $spacing-3;

  position: sticky;
  bottom: 0;
  z-index: 10;

  .footer {
    display: flex;
    padding: $spacing-2 0 $spacing-2 $spacing-3;
    cursor: pointer;
    align-items: center;
  }

  .footer-picture {
    margin-right: $spacing-3;
  }

  .footer-contact {
    display: inline-block;
    overflow: hidden;
    line-height: 20px;

    .contact-name {
      font-weight: 700;
      overflow: hidden;
      word-break: break-word;
    }

    .contact-message {
      overflow: hidden;
      text-overflow: ellipsis;
      color: $tertiary-text-color;
      height: 20px;

      .contact-message-icon {
        cursor: pointer;
        vertical-align: top;
        font-size: inherit;
        height: inherit;
        line-height: inherit;
        width: inherit;
        font-weight: 700;
      }

      .contact-message-text {
        cursor: pointer;
        vertical-align: top;
        text-overflow: ellipsis;
        overflow: hidden;
        font-weight: 700;
      }
    }
  }

  .footer-logo-img-wide {
    max-height: 88px;
    max-width: 70%;
  }
}

.fade-effect {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -$spacing-3;
    left: 0;
    right: 0;
    height: $spacing-3;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), $primary-background-color);
    pointer-events: none;
  }
}

.nav-panel-main-content-container {
  display: flex;
  flex-direction: column;
  padding-bottom: $spacing-3;
}

.sidenav-location-selector {
  padding: $spacing-3 $spacing-4 $spacing-3 $spacing-4;
}

.nav-list {
  margin: 0 $spacing-2;
}
