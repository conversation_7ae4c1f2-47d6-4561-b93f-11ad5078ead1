@let branding = branding$ | async;
<!-- TODO: remove service providerId check when O&O apps are ready to use new nav -->
@if (serviceProviderId() !== 'VBC') {
  <atlas-navbar
    [dropdownItems]="dropdownItems$ | async"
    [hideCenters]="(disableProductSwitcher$ | async) === true || hideCenters"
    [hideNotifications]="hideNotifications"
    [hideBottomBorder]="branding.isDarkMode"
    [hideLanguageSelector]="showLanguageSelector === false"
  >
    @if (showLegacyButton() === true) {
      <atlas-item
        interactable
        icon="store"
        iconText="{{ 'MENU_TOGGLE' | translate }}"
        customClass="atlas-navbar__menu-toggle"
        (click)="toggleMenu()"
      ></atlas-item>
    }
    <bc-location-selector></bc-location-selector>
    <div class="atlas-spacer"></div>
    @if (exitLinkConfiguration$ | async; as exitLink) {
      <atlas-item interactable>
        <div class="back-to-dashboard">
          <a [href]="exitLink.exitLinkUrl">
            <mat-icon>exit_to_app</mat-icon>
            @if (!isMobile()) {
              {{ exitLink.exitLinkText }}
            }
          </a>
        </div>
      </atlas-item>
    }
    <ng-container *ngTemplateOutlet="topbarContent" />
  </atlas-navbar>
}
@if (isPrimaryNav() === true) {
  <!-- remove navAutoSize once only using the new nav -->
  <glxy-nav
    class="no-content-overlay"
    [fixedTopGap]="0"
    [navAutoSize]="true"
    [usePushOnMobile]="true"
    [disabled]="disableNavigation()"
  >
    <glxy-nav-panel class="new-nav">
      <glxy-nav-header>
        @if (isMobile() === true) {
          <button mat-icon-button (click)="toggleMenu()" class="nav-panel-close-button">
            <mat-icon>close</mat-icon>
          </button>
        }
        <ng-container *ngTemplateOutlet="header" />
      </glxy-nav-header>
      <div class="nav-panel-main-content-container">
        <bc-location-selector
          [isRounded]="true"
          [icon]="'sync_alt'"
          [className]="'sidenav-location-selector'"
        ></bc-location-selector>
        <ng-container *ngTemplateOutlet="navItems" />
      </div>
      <glxy-nav-footer *ngTemplateOutlet="footer"></glxy-nav-footer>
    </glxy-nav-panel>
    @if (serviceProviderId() === 'VBC') {
      <glxy-navigation-toolbar
        content
        (menuToggleClicked)="toggleMenu()"
        [hideMenuToggle]="showNavToggle() === false"
        [class.mobile-nav-open]="navIsOpen() && isMobile()"
        [class.mobile-nav-close]="!navIsOpen() && isMobile()"
      >
        @if (isMobile() || disableNavigation() === true) {
          <glxy-navigation-toolbar-left class="toolbar-left">
            <bc-location-selector class="toolbar-location-selector"></bc-location-selector>
          </glxy-navigation-toolbar-left>
        }
        <glxy-navigation-toolbar-right>
          @if (exitLinkConfiguration$ | async; as exitLink) {
            <atlas-item interactable>
              <div class="back-to-dashboard">
                <a [href]="exitLink.exitLinkUrl">
                  <mat-icon>exit_to_app</mat-icon>
                  @if (!isMobile()) {
                    {{ exitLink.exitLinkText }}
                  }
                </a>
              </div>
            </atlas-item>
          }
          <ng-container *ngTemplateOutlet="topbarContent" />
          <atlas-top-bar-items
            useNewTopBar
            [overrideSignOutText]="overrideSignOutText()"
            [dropdownItems]="dropdownItems$ | async"
            [hideCenters]="(disableProductSwitcher$ | async) === true || hideCenters"
            [hideNotifications]="hideNotifications"
          >
          </atlas-top-bar-items>
        </glxy-navigation-toolbar-right>
      </glxy-navigation-toolbar>
    }
    <div class="page-content" [class.mobile]="isMobile()" [class.nav-closed]="navIsOpen() === false">
      <ng-content select="[content]"></ng-content>
    </div>
  </glxy-nav>
} @else {
  <ng-template cdkPortal>
    <div @animateNav class="portal-container">
      <div class="nav-container">
        <glxy-nav-panel>
          <glxy-nav-header>
            <ng-container *ngTemplateOutlet="header" />
          </glxy-nav-header>
          <ng-container *ngTemplateOutlet="navItems" />
          <glxy-nav-footer *ngTemplateOutlet="footer"></glxy-nav-footer>
        </glxy-nav-panel>
      </div>
    </div>
  </ng-template>
}

<ng-template #topbarContent>
  <ng-content select="[topbar]"></ng-content>
</ng-template>

<ng-template #header>
  <div nav-header class="header">
    @if (coBrandingUrl$ | async; as coBrandingUrl) {
      <div class="sidebar-powered-by">
        <span class="powered-by-text">{{ 'COMMON.POWERED_BY' | translate }}</span>
        <img class="powered-by-logo" [src]="coBrandingUrl" />
      </div>
    }
    <div class="logo" [ngStyle]="{ 'max-height.px': logoHeight$ | async }">
      @if (branding.logoUrl) {
        <img #logo [src]="branding.logoUrl" (load)="logoLoaded(logo)" class="logo-img" />
      }
    </div>
    <div
      class="center"
      [ngClass]="{
        'center-no-logo': businessAppBranding$ | async,
      }"
    >
      <div class="center-name">
        {{ businessCenterName$ | async }}
      </div>
    </div>
  </div>
</ng-template>

<ng-template #footer>
  <div nav-footer class="footer-container">
    <div class="fade-effect"></div>
    <div data-action="clicked-contact-us" class="footer" (click)="openAssistanceModal()">
      <div class="footer-picture">
        <glxy-avatar [src]="salespersonPictureUrl$ | async" [width]="40"></glxy-avatar>
      </div>
      <div class="footer-contact">
        <div class="contact-name">{{ partnerName$ | async }}</div>
        <div class="contact-message">
          <span>
            <i class="material-icons contact-message-icon">chat_bubble</i>
          </span>
          <span class="contact-message-text">
            @if (salespersonName$ | async; as name) {
              {{ 'COMMON.CONTACT_SALESPERSON' | translate: { salesPersonName: name } }}
            } @else {
              {{ 'COMMON.ACTION_LABELS.CONTACT_US' | translate }}
            }
          </span>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #navItems>
  <glxy-list-container class="nav-list">
    <!-- sections are not currently supported -->
    @for (navItem of navigationItems$ | async; track trackBy(navItem)) {
      @if (navItem.sideNavigationContainer; as container) {
        <glxy-sublist>
          <glxy-list-item>
            @if (container.showIcon) {
              <bc-icon
                glxyListItemIcon
                [matIcon]="container.icon"
                [logoUrl]="container.logoUrl"
                [productName]="container.translationId || container.label | translate"
              />
            }
            {{ container.translationId || container.label | translate }}
            @if (container.chipContent) {
              <bc-chip
                glxyListItemAccessory
                [value]="{ label: container.chipContent | translate, type: 'nav' }"
              ></bc-chip>
            }
          </glxy-list-item>
          <glxy-sublist-content>
            @for (subItem of container.sideNavigationItems; track trackBy(subItem)) {
              <bc-nav-link [item]="subItem.sideNavigationLink" />
            }
          </glxy-sublist-content>
        </glxy-sublist>
      }

      @if (navItem.sideNavigationLink; as link) {
        <bc-nav-link [item]="link" />
      }
    }
  </glxy-list-container>
</ng-template>
