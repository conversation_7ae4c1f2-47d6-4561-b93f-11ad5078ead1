@use 'design-tokens' as *;

:host {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.locations-list {
  flex: 1;
  overflow: hidden;
}

.search-box {
  margin-top: $spacing-2;
  width: 100%;
}

mat-spinner {
  margin: auto;
}

.no-access-to-any {
  align-self: center;
  flex: 1;

  .no-access-to-any-title {
    margin: 56px 0 16px 0;

    .no-access-to-any-tab-name {
      text-transform: lowercase;
    }
  }

  .no-access-to-any-addition {
    margin: 16px 48px 0 48px;
    color: $secondary-text-color;
  }
}
