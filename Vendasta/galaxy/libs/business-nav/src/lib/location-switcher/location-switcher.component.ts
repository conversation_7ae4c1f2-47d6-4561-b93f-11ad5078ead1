import { CommonModule } from '@angular/common';
import { Component, ElementRef } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { Location, UserViewType } from '@vendasta/atlas';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { Observable, combineLatest, map, shareReplay, startWith } from 'rxjs';
import { BusinessNavModule } from '../business-nav.module';
import { BusinessNavDataService } from '../data.service';
import { BusinessNavLocationService } from '../modals/location-switcher-modal/location.service';
import { BusinessNavLocationTabService } from '../modals/location-switcher-modal/tab/tab.service';
import { Capacitor } from '@capacitor/core';

@Component({
  selector: 'bc-location-switcher',
  imports: [
    CommonModule,
    BusinessNavModule,
    MatProgressSpinnerModule,
    TranslateModule,
    GalaxyInfiniteScrollTriggerModule,
    GalaxyEmptyStateModule,
    MatTabsModule,
    GalaxyAlertModule,
    MatInputModule,
    MatFormFieldModule,
    ReactiveFormsModule,
  ],
  exportAs: 'locationSwitcher',
  templateUrl: './location-switcher.component.html',
  styleUrls: ['./location-switcher.component.scss'],
})
export class LocationSwitcherComponent {
  readonly locations$: Observable<Location[]>;
  readonly infoText$: Observable<string>;
  readonly userViewType$: Observable<UserViewType>;
  readonly loading$ = this.locationsService.scrollableLocations.loading$;
  readonly isEmptyResult$: Observable<boolean>;
  readonly hasQueryText$: Observable<boolean>;
  readonly currentTabText$: Observable<string>;
  readonly tabElemSelected$: Observable<ElementRef>;
  readonly searchControl = new FormControl<string>('');
  readonly isNativeApp = Capacitor.isNativePlatform();

  viewType = UserViewType;

  constructor(
    private readonly locationsService: BusinessNavLocationService,
    private readonly tabService: BusinessNavLocationTabService,
    private readonly dataService: BusinessNavDataService,
  ) {
    this.isEmptyResult$ = this.locationsService.scrollableLocations.state$.pipe(map((value) => value.empty));
    this.hasQueryText$ = this.locationsService.scrollableLocations.state$.pipe(map((value) => !!value.criteria.query));
    this.locations$ = this.locationsService.scrollableLocations.items$.pipe(
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    if (this.isNativeApp) {
      this.tabService.registerTabIndex({ id: '1', elem: null, mode: 'single' });
      this.tabService.selectTab('1');
    } else {
      this.tabService.registerTabIndex({ id: '0', elem: null, mode: 'both' });
      this.tabService.registerTabIndex({ id: '1', elem: null, mode: 'single' });
      this.tabService.registerTabIndex({ id: '2', elem: null, mode: 'brands' });
    }

    this.searchControl.valueChanges.subscribe((value) => {
      this.textChanged(value);
    });
    this.userViewType$ = this.dataService.userViewType$.pipe(startWith(UserViewType.USER_VIEW_TYPE_SMB));
    this.tabElemSelected$ = this.tabService.selectedTabElement$;
    this.currentTabText$ = this.tabService.selectedTabMode$.pipe(
      map((tabMode) => {
        switch (tabMode) {
          case 'both':
            return 'SWITCH_LOCATION_MODAL.EMPTY_STATES.ALL_TAB_NAME';
          case 'single':
            return 'NAVIGATION.TABS.LOCATIONS';
          case 'brands':
            return 'NAVIGATION.TABS.GROUPS';
        }
      }),
    );
    this.infoText$ = combineLatest([this.userViewType$, this.dataService.defaultLocation$]).pipe(
      map(([userViewType, defaultLocation]) => {
        if (!(defaultLocation?.accountGroupId || defaultLocation?.groupId)) {
          return 'SWITCH_LOCATION_MODAL.SELECT_DEFAULT';
        }
        if (userViewType === this.viewType.USER_VIEW_TYPE_ADMIN) {
          return 'SWITCH_LOCATION_MODAL.ADMIN_VIEW';
        }
        return;
      }),
    );
  }

  textChanged(text: string): void {
    this.locationsService.setQuery(text);
  }

  tabSelected(tabId: number): void {
    this.tabService.selectTab(tabId.toString());
  }

  listScroll(): void {
    this.locationsService.loadMore();
  }
}
