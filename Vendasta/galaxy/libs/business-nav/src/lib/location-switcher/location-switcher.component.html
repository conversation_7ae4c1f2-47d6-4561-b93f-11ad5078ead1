@if (infoText$ | async; as infoText) {
  <glxy-alert type="info">{{ infoText | translate }}</glxy-alert>
}
<mat-form-field class="search-box">
  <mat-label>{{ 'SWITCH_LOCATION_MODAL.SEARCH' | translate }}</mat-label>
  <input matInput type="text" [formControl]="searchControl" />
</mat-form-field>

@if (!isNativeApp) {
  <mat-tab-group stretchTabs="true" (selectedIndexChange)="tabSelected($event)" class="locations-list">
    <mat-tab [label]="'NAVIGATION.TABS.ALL' | translate">
      <ng-template [ngTemplateOutlet]="tabContent"></ng-template>
    </mat-tab>
    <mat-tab [label]="'NAVIGATION.TABS.LOCATIONS' | translate">
      <ng-template [ngTemplateOutlet]="tabContent"></ng-template>
    </mat-tab>
    <mat-tab [label]="'NAVIGATION.TABS.GROUPS' | translate">
      <ng-template [ngTemplateOutlet]="tabContent"></ng-template>
    </mat-tab>
  </mat-tab-group>
} @else {
  <ng-template [ngTemplateOutlet]="tabContent"></ng-template>
}

@if (isEmptyResult$ | async) {
  <div class="no-access-to-any">
    @if (hasQueryText$ | async) {
      <glxy-empty-state>
        <glxy-empty-state-title class="no-access-to-any-title">
          {{ 'SWITCH_LOCATION_MODAL.NO_SEARCH_RESULT.TITLE' | translate }}
        </glxy-empty-state-title>
        <div class="no-access-to-any-addition">
          {{ 'SWITCH_LOCATION_MODAL.NO_SEARCH_RESULT.MESSAGE' | translate }}
        </div>
      </glxy-empty-state>
    } @else {
      <glxy-empty-state>
        <glxy-empty-state-title class="no-access-to-any-title">
          {{ 'SWITCH_LOCATION_MODAL.EMPTY_STATES.TITLE' | translate }}
          <span class="no-access-to-any-tab-name">{{ currentTabText$ | async | translate | lowercase }}.</span>
        </glxy-empty-state-title>
        <div class="no-access-to-any-addition">
          {{ 'SWITCH_LOCATION_MODAL.EMPTY_STATES.MESSAGE' | translate }}
          <b>{{ 'SWITCH_LOCATION_MODAL.EMPTY_STATES.CONTACT' | translate }}</b>
        </div>
      </glxy-empty-state>
    }
  </div>
}

<ng-template #tabContent>
  <bc-location-list [locations]="locations$ | async"></bc-location-list>
  @if ((locations$ | async)?.length > 0) {
    <glxy-infinite-scroll-trigger (isVisible)="listScroll()"></glxy-infinite-scroll-trigger>
  }
  @if (loading$ | async) {
    <mat-spinner [diameter]="50"></mat-spinner>
  }
</ng-template>
