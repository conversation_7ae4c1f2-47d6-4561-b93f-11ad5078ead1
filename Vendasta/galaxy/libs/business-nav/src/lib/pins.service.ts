import { Injectable, OnDestroy } from '@angular/core';
import { PinnedItem, PinnedItemInterface, PinsApiService, SetPinsRequest } from '@vendasta/atlas';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { filter, map, shareReplay, switchMap, take } from 'rxjs/operators';
import { BusinessNavConfigService, Config } from './config.service';
import { BusinessNavDataService } from './data.service';

export class PinnedProductItem extends PinnedItem {
  constructor(kwargs?: PinnedItemInterface) {
    if (!kwargs || !kwargs.navigationId) {
      return;
    }
    let navigationId = kwargs.navigationId;

    if (!navigationId.startsWith('product-')) {
      navigationId = `product-${navigationId}`;
    }
    super({ navigationId: navigationId });
  }
}

@Injectable({ providedIn: 'root' })
export class PinsService implements OnDestroy {
  private pins$$: BehaviorSubject<PinnedItem[]> = new BehaviorSubject<PinnedItem[]>(null);
  public pins$: Observable<PinnedItem[]> = this.pins$$.asObservable();

  public navPins$: Observable<PinnedItem[]>;
  public productPins$: Observable<PinnedItem[]>;

  public canPin$: Observable<boolean>;

  private subs: Subscription[] = [];

  constructor(
    private apiService: PinsApiService,
    private dataService: BusinessNavDataService,
    private cfgService: BusinessNavConfigService,
  ) {
    this.subs.push(
      this.dataService.pinnedItems$
        .pipe(filter((pins: PinnedItem[]) => typeof pins !== 'undefined'))
        .subscribe((pins: PinnedItem[]) =>
          this.initializePins(pins.map((x) => new PinnedItem({ navigationId: x.navigationId }))),
        ),
    );

    this.navPins$ = this.pins$.pipe(
      map((pins: PinnedItem[]) => {
        if (!pins) {
          return [];
        }
        return pins.filter((pin: PinnedItem) => !pin.navigationId.startsWith('product-'));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.productPins$ = this.pins$.pipe(
      map((pins: PinnedItem[]) => {
        if (!pins) {
          return [];
        }
        const p = pins.filter((pin: PinnedItem) => pin.navigationId.startsWith('product-'));
        return p.map((pin: PinnedItem) => new PinnedItem({ navigationId: pin.navigationId.split('product-')[1] }));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.canPin$ = this.dataService.userId$.pipe(map((userID: string) => !!userID));
  }

  ngOnDestroy(): void {
    for (const sub of this.subs) {
      sub.unsubscribe();
    }
  }

  public addPin(pinId: string): void {
    let curPins = this.pins$$.getValue();
    if (!curPins) {
      curPins = [];
    } else if (curPins.find((p: PinnedItem) => p.navigationId === pinId)) {
      return;
    }

    curPins.push(new PinnedItem({ navigationId: pinId }));
    this.pins$$.next(curPins);
    this.savePins(curPins);
  }

  public removePin(pinId: string): void {
    const curPins = this.pins$$.getValue().filter((p: PinnedItem) => p.navigationId !== pinId);
    this.pins$$.next(curPins);
    this.savePins(curPins);
  }

  public setPins(pins: PinnedItem[]): void {
    this.pins$$.next(pins);
    this.savePins(pins);
  }

  public initializePins(pins: PinnedItem[]): void {
    this.pins$$.next(pins);
  }

  private savePins(pins: PinnedItem[]): void {
    this.cfgService.config$
      .pipe(
        filter((config) => !!config && (!!config.accountId || !!config.groupPath)),
        map((config: Config) => (config.accountId ? config.accountId : config.groupPath)),
        switchMap((identifier: string) =>
          this.apiService.setPins(
            new SetPinsRequest({
              identifier: identifier,
              items: pins,
            }),
          ),
        ),
        take(1),
      )
      .subscribe();
  }
}
