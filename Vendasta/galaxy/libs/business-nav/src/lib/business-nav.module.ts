import { DragDropModule } from '@angular/cdk/drag-drop';
import { OverlayModule } from '@angular/cdk/overlay';
import { PortalModule } from '@angular/cdk/portal';
import { CommonModule } from '@angular/common';
import { InjectionToken, ModuleWithProviders, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { RouterModule, UrlSerializer } from '@angular/router';
import { ATLAS_CONFIG_TOKEN, AtlasConfig, AtlasModule } from '@galaxy/atlas';
import { InboxButtonModule } from '@galaxy/conversation/button';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { GalaxyListModule } from '@vendasta/galaxy/list';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyNavModule } from '@vendasta/galaxy/nav';
import { GalaxyNavigationToolbarModule } from '@vendasta/galaxy/navigation-toolbar';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { map, Observable, shareReplay } from 'rxjs';
import * as baseTranslation from './assets/i18n/en_devel.json';
import { BcNavItemComponent } from './bc-nav-item/bc-nav-item.component';
import { BusinessNavConfigService } from './config.service';
import { BusinessNavChipComponent } from './core-components/chip/chip.component';
import { EncodeBrandNamePipe } from './core-components/encode-brand.pipe';
import { SearchBoxComponent } from './core-components/form-controls/search-box/search-box.component';
import { IconComponent } from './core-components/icon/icon.component';
import { NavItemContainerComponent } from './core-components/nav-item-container/nav-item-container.component';
import { NavItemSectionComponent } from './core-components/nav-item-section/nav-item-section.component';
import { LegacyNavLinkComponent } from './core-components/nav-link/legacy-nav-link.component';
import { NavLinkComponent } from './core-components/nav-link/nav-link.component';
import { AtlasTooltipModule } from './core-components/tooltip/tooltip-module';
import { CustomUrlSerializer } from './custom-url-serializer';
import { LocationSelectorComponent } from './location-selector/location-selector.component';
import { LocationListItemComponent } from './modals/location-switcher-modal/location-list/location-list-item/location-list-item.component';
import { LocationListComponent } from './modals/location-switcher-modal/location-list/location-list.component';
import { LocationSwitcherModalComponent } from './modals/location-switcher-modal/location-switcher-modal.component';
import { TabHeaderComponent } from './modals/location-switcher-modal/tab-header/tab-header.component';
import { LocationTabComponent } from './modals/location-switcher-modal/tab/tab.component';
import { MeetingBookingUrlModule } from './modals/sales-info/meeting-booking-url/meeting-booking-url.component';
import { SalesInfoModalComponent } from './modals/sales-info/sales-info.modal.component';
import { UserRequiredModalComponent } from './modals/user-required/user-required.modal.component';
import { NavigationComponent } from './navigation/navigation.component';

type BlockListPath = {
  /** Disabled path regexes */
  blockListedPath: RegExp;
  /** Destination url to redirect to */
  destUrl?: string;
};

// Checks if blockListPath is of type BlockListPath which has more options when blocking a path on location switch
function isBlockListPath(blockListPath: RegExp | BlockListPath): blockListPath is BlockListPath {
  return 'blockListedPath' in blockListPath;
}

// Checks if blockListPath is of type RegExp which is simple block list option
function isSimpleBlockListPath(blockListPath: RegExp | BlockListPath): blockListPath is RegExp {
  return !isBlockListPath(blockListPath);
}

function atlasConfigFactory(configService: BusinessNavConfigService): Observable<AtlasConfig> {
  return configService.config$.pipe(
    map((config) => ({
      partnerId: config.partnerId,
      marketId: config.marketId,
      accountId: config.accountId,
      groupPath: config.groupPath,
      serviceProviderId: config.serviceProviderId,
    })),
    shareReplay({ bufferSize: 1, refCount: true }),
  );
}

export function isPathBlockListed(
  config: BusinessNavModuleConfig['locationSwitchConfig'],
  path: string,
): [boolean, string] {
  // Check if the path is blocklisted for BlockListPath type and returns destUrl if it exists
  const isBlockListed = config.blockListedPaths.filter(isBlockListPath).some((p) => p.blockListedPath.test(path));
  if (isBlockListed) {
    const blockedPath = config.blockListedPaths.filter(isBlockListPath).find((p) => p.blockListedPath.test(path));
    const destUrl = blockedPath?.destUrl || path;
    return [true, destUrl];
  }

  // Check if the path is blocklisted for RegExp type and returns original path as destionation url
  const isBlockListedFallback = config.blockListedPaths.filter(isSimpleBlockListPath).some((p) => p.test(path));
  if (isBlockListedFallback) return [true, path];

  // Path is not blocklisted and destionation url is to be ignored
  return [false, ''];
}

type BusinessNavModuleConfig = {
  locationSwitchConfig: {
    /** The keys to use for the location params */
    supportedLocationContextConfig: {
      /** The location context this config is applicable to */
      context: 'brand' | 'location';
      /**The url param with the associated context key*/
      locationParamKey: string;
    }[];

    /** Where to go if no location is set. Not a replacement for auth guards. */
    defaultPath: string;

    /** Path to the location switch loading component. If not provided, the location switcher will use the default path */
    loadingPath?: string;

    /**
     * Disabled path regexes with optional destination url. Path will attempted to be matched against blockListedPath regex.
     * If url is matched, the user will be redirected to destUrl if it exists; otherwise reload the component.
     * For backwards compatibility and simpler option, RegExp will continue to be supported.
     **/
    blockListedPaths: (RegExp | BlockListPath)[];
  };
};

// Do not provide this directly, instead call BusinessNavModule.forRoot({ locationSwitchConfig: ... })
export const LOCATION_SWITCH_CONFIG_TOKEN = new InjectionToken<BusinessNavModuleConfig['locationSwitchConfig']>(
  'LOCATION_SWITCH_CONFIG',
);

@NgModule({
  declarations: [
    NavigationComponent,
    NavItemContainerComponent,
    NavItemSectionComponent,
    BcNavItemComponent,
    SalesInfoModalComponent,
    UserRequiredModalComponent,
    LocationSelectorComponent,
    LocationSwitcherModalComponent,
    SearchBoxComponent,
    LocationTabComponent,
    LocationListComponent,
    LocationListItemComponent,
    EncodeBrandNamePipe,
    TabHeaderComponent,
    LegacyNavLinkComponent,
  ],
  providers: [
    {
      provide: UrlSerializer,
      useClass: CustomUrlSerializer,
    },
    {
      provide: ATLAS_CONFIG_TOKEN,
      useFactory: atlasConfigFactory,
      deps: [BusinessNavConfigService],
    },
  ],
  imports: [
    CommonModule,
    OverlayModule,
    PortalModule,
    RouterModule,
    FormsModule,
    AtlasModule,
    DragDropModule,
    AtlasTooltipModule,
    GalaxyEmptyStateModule,
    GalaxyInfiniteScrollTriggerModule,
    GalaxyLoadingSpinnerModule,
    InboxButtonModule,
    MeetingBookingUrlModule,
    MatIconModule,
    GalaxyAvatarModule,
    MatButtonModule,
    MatMenuModule,
    MatListModule,
    GalaxyBadgeModule,
    TranslateModule,
    MatDialogModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatInputModule,
    GalaxyButtonLoadingIndicatorModule,
    GalaxyTooltipModule,
    GalaxyNavModule,
    GalaxyNavigationToolbarModule,
    GalaxyListModule,
    IconComponent,
    BusinessNavChipComponent,
    NavLinkComponent,
    LexiconModule.forChild({
      componentName: 'common/business-nav',
      baseTranslation,
    }),
    GalaxyAlertModule,
  ],
  exports: [
    NavigationComponent,
    LegacyNavLinkComponent,
    NavLinkComponent,
    NavItemContainerComponent,
    AtlasModule,
    LocationListComponent,
    SalesInfoModalComponent,
  ],
})
export class BusinessNavModule {
  static forRoot({ locationSwitchConfig }: BusinessNavModuleConfig): ModuleWithProviders<BusinessNavModule> {
    const simpleBlockListedPaths = locationSwitchConfig.blockListedPaths.filter(isSimpleBlockListPath);
    const blockListedPaths = locationSwitchConfig.blockListedPaths.filter(isBlockListPath).map((path) => {
      path.blockListedPath = cleanRegex([path.blockListedPath])[0];
      return path;
    });
    locationSwitchConfig.blockListedPaths = cleanRegex([...simpleBlockListedPaths, ...GlobalBlockListedPaths]);
    locationSwitchConfig.blockListedPaths.push(...blockListedPaths);
    return {
      ngModule: BusinessNavModule,
      providers: [
        {
          provide: LOCATION_SWITCH_CONFIG_TOKEN,
          useValue: locationSwitchConfig,
        },
      ],
    };
  }
}

function cleanRegex(regexes: RegExp[]): RegExp[] {
  // remove the global flag from the regexes because its presence causes the regex `test` method
  // to mutate the regex's `lastIndex` property, which causes the regex to fail on subsequent calls
  // see:
  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp/lastIndex#description
  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp/test
  return regexes.map((r) => new RegExp(r.source, r.flags.replace('g', '')));
}

/**
 * TODO (BREW-230): List of paths that should not be replaced with the current location due to data leakage or initialization logic that breaks
 * when the path is changed. These are bugs that should be fixed, but for now we need to block them so users don't suffer.
 */
const GlobalBlockListedPaths: RegExp[] = [/\/crm\//i];
