import { Injectable, inject, signal } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { SideNavigationLink } from '@vendasta/atlas';
import { filter, firstValueFrom } from 'rxjs';
import { BusinessNavDataService } from './data.service';

@Injectable({
  providedIn: 'root',
})
export class ActiveLinkService {
  private readonly links$ = inject(BusinessNavDataService).navigationLinks$;
  private _activeItem = signal<Pick<SideNavigationLink, 'navigationId' | 'path'>>({ navigationId: 'unset', path: '' });

  constructor(private readonly router: Router) {
    this.refreshActiveItem();

    this.router.events.pipe(filter((e) => e instanceof NavigationEnd)).subscribe((e: NavigationEnd) => {
      const activePath = this.getActivePath(e.url);
      if (this._activeItem().navigationId === 'unset' || !activePath.includes(this._activeItem().path)) {
        this.refreshActiveItem(activePath);
      }
    });
  }

  get activeItem() {
    return this._activeItem.asReadonly();
  }

  public readonly setActiveItem = (item: SideNavigationLink) => {
    this._activeItem.set(item);
  };

  public async refreshActiveItem(activePath?: string) {
    activePath = activePath || this.getActivePath(this.router.url);
    const links = await firstValueFrom(this.links$);
    const activeItem = links.find((l) => activePath.includes(l.path));
    if (activeItem) this.setActiveItem(activeItem);
  }

  private getActivePath(url: string) {
    return decodeURI(`/${this.router.parseUrl(url).root.children.primary.toString()}`);
  }
}
