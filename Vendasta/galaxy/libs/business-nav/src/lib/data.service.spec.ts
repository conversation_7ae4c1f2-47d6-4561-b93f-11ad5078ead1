import { TestBed } from '@angular/core/testing';
import {
  AtlasApiService,
  GetNavigationDataResponse,
  GetSalesInfoResponse,
  SideNavigationItem,
  SideNavigationLink,
} from '@vendasta/atlas';
import { TestScheduler } from 'rxjs/testing';
import { BusinessNavConfigService, Config } from './config.service';
import { BusinessNavDataService } from './data.service';

const sched = new TestScheduler((a, b) => expect(a).toEqual(b));

const VALID_CONFIG: Config = {
  partnerId: 'partnerId',
  marketId: 'marketId',
  accountId: 'accountId',
  groupPath: 'groupPath',
  serviceProviderId: 'serviceProviderId',
  defaultURL: 'defaultURL',
};

type pickedApiFields = 'getNavigationData' | 'getSalesInfo';
class MockAtlasApiService implements Pick<AtlasApiService, pickedApiFields> {
  getNavigationData = jest.fn(() => sched.createColdObservable('a', { a: new GetNavigationDataResponse() }));
  getSalesInfo = jest.fn(() => sched.createColdObservable('a', { a: new GetSalesInfoResponse() }));
}

type pickedConfigFields = 'config$' | 'partnerId$';
class MockBusinessNavConfigService implements Pick<BusinessNavConfigService, pickedConfigFields> {
  get config$() {
    return sched.createHotObservable('a', { a: VALID_CONFIG });
  }
  get partnerId$() {
    return sched.createHotObservable('a', { a: VALID_CONFIG.partnerId });
  }
}

describe('BusinessNavDataService', () => {
  let service: BusinessNavDataService;
  const mockApi: MockAtlasApiService = new MockAtlasApiService();
  const mockConfig: MockBusinessNavConfigService = new MockBusinessNavConfigService();

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        { provide: AtlasApiService, useValue: mockApi },
        { provide: BusinessNavConfigService, useValue: mockConfig },
      ],
    });
    service = TestBed.inject(BusinessNavDataService);
  });

  afterEach(() => {
    sched.flush();
    jest.clearAllMocks();
    localStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('navigationLinks$', () => {
    it('emits empty list if navigation items source emits empty list', () => {
      mockApi.getNavigationData.mockReturnValueOnce(
        sched.createColdObservable('a', { a: new GetNavigationDataResponse({ navigationItems: [] }) }),
      );
      sched.expectObservable(service.navigationLinks$).toBe('a', { a: [] });
    });

    it('emits all navigation items in a flat list, and in the same order', () => {
      const item1 = new SideNavigationLink({ navigationId: 'ID1' });
      const item2 = new SideNavigationLink({ navigationId: 'ID2' });
      const item3 = new SideNavigationLink({ navigationId: 'ID3' });
      const res = new GetNavigationDataResponse({
        navigationItems: [
          new SideNavigationItem({ sideNavigationLink: item1 }),
          new SideNavigationItem({ sideNavigationLink: item2 }),
          new SideNavigationItem({ sideNavigationLink: item3 }),
        ],
      });
      mockApi.getNavigationData.mockReturnValueOnce(sched.createColdObservable('a', { a: res }));
      sched.expectObservable(service.navigationLinks$).toBe('a', { a: [item1, item2, item3] });
    });

    it('emits one navigation item for a container, and one for each child link', () => {
      const item1 = new SideNavigationLink({ navigationId: 'ID1' });
      const item2 = new SideNavigationLink({ navigationId: 'ID2' });
      const item3 = new SideNavigationLink({ navigationId: 'ID3' });
      const res = new GetNavigationDataResponse({
        navigationItems: [
          new SideNavigationItem({
            sideNavigationContainer: {
              label: 'Container',
              sideNavigationItems: [
                new SideNavigationItem({ sideNavigationLink: item1 }),
                new SideNavigationItem({ sideNavigationLink: item2 }),
                new SideNavigationItem({ sideNavigationLink: item3 }),
              ],
            },
          }),
        ],
      });
      mockApi.getNavigationData.mockReturnValueOnce(sched.createColdObservable('a', { a: res }));
      sched.expectObservable(service.navigationLinks$).toBe('a', { a: [item1, item2, item3] });
    });
  });
});
