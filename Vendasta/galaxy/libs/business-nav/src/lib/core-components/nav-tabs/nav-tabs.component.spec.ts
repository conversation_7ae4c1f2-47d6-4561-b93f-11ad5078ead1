import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { NavTabsComponent } from './nav-tabs.component';
import { NAV_TABS_CONTAINER_CONFIG_TOKEN } from '../nav-tabs-container';

describe('NavTabsContainerComponent', () => {
  let component: NavTabsComponent;
  let fixture: ComponentFixture<NavTabsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NavTabsComponent, RouterTestingModule],
      providers: [
        {
          provide: NAV_TABS_CONTAINER_CONFIG_TOKEN,
          useValue: of({
            link: {},
            tabs: [
              { navigationId: 'nav-dashboard', path: '/dashboard' },
              { navigationId: 'nav-get-started', path: '/get-started' },
              { navigationId: 'nav-recent-activity', path: '/recent-activity' },
            ],
          }),
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(NavTabsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('contains the correct number of tabs', () => {
    expect(fixture.nativeElement.querySelectorAll('a').length).toEqual(3);
  });
});
