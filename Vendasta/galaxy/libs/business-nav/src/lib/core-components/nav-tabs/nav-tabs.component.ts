import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgForOf, NgIf } from '@angular/common';
import { Component, Inject, Optional } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SideNavigationLink } from '@vendasta/atlas';
import { Observable, filter, map, startWith, switchMap } from 'rxjs';
import { NAV_TABS_CONTAINER_CONFIG_TOKEN, NavTabsContainerConfig } from '../nav-tabs-container';

@Component({
  selector: 'bc-nav-tabs',
  imports: [AsyncPipe, NgIf, NgForOf, MatTabsModule, RouterModule, TranslateModule],
  templateUrl: './nav-tabs.component.html',
  styleUrls: ['./nav-tabs.component.scss'],
})
export class NavTabsComponent {
  protected readonly tabs$: Observable<SideNavigationLink[]>;
  protected readonly activeTab$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    map((e: NavigationEnd) => e.url),
    startWith(this.router.url),
    switchMap((url) => this.tabs$.pipe(map((tabs) => tabs.find((t) => url.includes(t.path))?.navigationId ?? ''))),
  );
  protected readonly showTabs$: Observable<boolean>;

  constructor(
    @Optional() @Inject(NAV_TABS_CONTAINER_CONFIG_TOKEN) config: Observable<NavTabsContainerConfig> | undefined,
    private readonly router: Router,
  ) {
    this.tabs$ = config?.pipe(map((c) => c.tabs));
    this.showTabs$ = config?.pipe(map((c) => c.tabs.length > 1));
  }
}
