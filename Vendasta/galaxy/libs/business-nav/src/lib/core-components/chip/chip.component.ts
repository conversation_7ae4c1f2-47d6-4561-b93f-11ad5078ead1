import { Component, input } from '@angular/core';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';

interface ChipValue {
  label: string;
  type: 'default' | 'nav' | 'beta';
}

@Component({
  selector: 'bc-chip',
  templateUrl: './chip.component.html',
  styleUrls: ['./chip.component.scss'],
  imports: [GalaxyBadgeModule],
})
export class BusinessNavChipComponent {
  readonly value = input.required<ChipValue>();
}
