/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

/**
 * Forked code, allows for usage within our Angular elements without
 * dependency on @angular/material
 */

import { OverlayModule } from '@angular/cdk/overlay';
import { A11yModule } from '@angular/cdk/a11y';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {
  MatTooltipDirective as AtlasTooltipDirective,
  TooltipComponent as AtlasTooltipComponent,
  MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER as ATLAS_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER,
} from './tooltip';

@NgModule({
  imports: [A11yModule, CommonModule, OverlayModule],
  exports: [AtlasTooltipDirective, AtlasTooltipComponent],
  declarations: [AtlasTooltipDirective, AtlasTooltipComponent],
  providers: [ATLAS_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],
})
export class AtlasTooltipModule {}
