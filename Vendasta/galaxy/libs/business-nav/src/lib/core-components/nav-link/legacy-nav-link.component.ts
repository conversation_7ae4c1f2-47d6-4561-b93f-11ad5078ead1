import { Component, computed, effect, inject, input } from '@angular/core';
import { ModalService } from '@galaxy/atlas';
import { SideNavigationLink } from '@vendasta/atlas';
import { ActiveLinkService } from '../../active-link.service';
import { BusinessNavConfigService } from '../../config.service';
import { UserRequiredModalComponent } from '../../modals/user-required/user-required.modal.component';

@Component({
  selector: 'bc-legacy-nav-link',
  templateUrl: './legacy-nav-link.component.html',
  styleUrls: ['./legacy-nav-link.component.scss'],
  standalone: false,
})
export class LegacyNavLinkComponent {
  protected item = input<SideNavigationLink>();
  protected serviceProviderId$ = inject(BusinessNavConfigService).serviceProviderId$;
  protected isActiveLink = computed(
    () => this.activeLinkService.activeItem().navigationId === this.item().navigationId,
  );
  protected indentation = 0;
  protected readonly setActiveLink = this.activeLinkService.setActiveItem;

  constructor(
    private modalService: ModalService,
    protected readonly activeLinkService: ActiveLinkService,
  ) {
    effect(async () => {
      if (!this.item().showIcon) {
        this.indentation = 24; // 24px is the offset for the icon
      }
    });
  }

  showUserRequiredModal(): void {
    this.modalService.openInformational(UserRequiredModalComponent);
  }
}
