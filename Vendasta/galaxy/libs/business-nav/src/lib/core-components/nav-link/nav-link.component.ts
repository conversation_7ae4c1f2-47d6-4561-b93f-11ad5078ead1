import { AsyncPipe, NgTemplateOutlet } from '@angular/common';
import { Component, computed, inject, input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { ModalService } from '@galaxy/atlas';
import { TranslateModule } from '@ngx-translate/core';
import { SideNavigationLink } from '@vendasta/atlas';
import { GalaxyListModule } from '@vendasta/galaxy/list';
import { ActiveLinkService } from '../../active-link.service';
import { BusinessNavConfigService } from '../../config.service';
import { UserRequiredModalComponent } from '../../modals/user-required/user-required.modal.component';
import { BusinessNavChipComponent } from '../chip/chip.component';
import { IconComponent } from '../icon/icon.component';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'bc-nav-link',
  templateUrl: './nav-link.component.html',
  styleUrls: ['./nav-link.component.scss'],
  imports: [
    AsyncPipe,
    NgTemplateOutlet,
    GalaxyListModule,
    RouterLink,
    BusinessNavChipComponent,
    IconComponent,
    TranslateModule,
    MatIconModule,
  ],
})
export class NavLinkComponent {
  private modalService = inject(ModalService);
  protected readonly activeLinkService = inject(ActiveLinkService);

  protected serviceProviderId$ = inject(BusinessNavConfigService).serviceProviderId$;
  protected item = input<SideNavigationLink>();
  protected isActiveLink = computed(
    () => this.activeLinkService.activeItem().navigationId === this.item().navigationId,
  );

  protected showUserRequiredModal() {
    this.activeLinkService.setActiveItem(this.item());
    this.modalService.openInformational(UserRequiredModalComponent);
  }
}
