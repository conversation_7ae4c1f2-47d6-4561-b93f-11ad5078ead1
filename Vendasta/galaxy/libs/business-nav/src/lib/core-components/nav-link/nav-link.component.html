<ng-template #itemContent>
  <glxy-list-nav-item [active]="isActiveLink()">
    @if (item().showIcon) {
      <bc-icon
        glxyListItemIcon
        [matIcon]="item().icon"
        [logoUrl]="item().logoUrl"
        [productName]="item().translationId || item().label | translate"
      />
    }
    {{ item().translationId || item().label | translate }}
    @if (item().chipContent) {
      <bc-chip glxyListItemAccessory [value]="{ label: item().chipContent | translate, type: 'nav' }"></bc-chip>
    }
    @if (item().external) {
      <mat-icon class="external-link-icon" glxyListItemAccessory>open_in_new</mat-icon>
    }
  </glxy-list-nav-item>
</ng-template>

@if (item().userRequired) {
  <a (click)="showUserRequiredModal()">
    <ng-container *ngTemplateOutlet="itemContent" />
  </a>
} @else if (item().serviceProviderId === (serviceProviderId$ | async)) {
  <a [routerLink]="item().path" (click)="activeLinkService.setActiveItem(item())">
    <ng-container *ngTemplateOutlet="itemContent" />
  </a>
} @else {
  <a
    [href]="item().url"
    [attr.target]="!item().openInNewTab ? null : '_blank'"
    [attr.noopener]="!item().openInNewTab ? null : ''"
    (click)="activeLinkService.setActiveItem(item())"
  >
    <ng-container *ngTemplateOutlet="itemContent" />
  </a>
}
