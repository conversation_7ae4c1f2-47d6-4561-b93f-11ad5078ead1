@use '../../theming' as *;

:host {
  background: var(--themingPrimaryColor);
}

.business-navbar__items {
  height: 36px;
  display: flex;
  flex-direction: row;
  align-items: center;
  user-select: none;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  font-family: Roboto, 'Helvetica Neue', sans-serif;
  font-size: 14px;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  transition:
    background-color 0.2s cubic-bezier(0.4, 0, 1, 1),
    color 0.15s ease-in-out;
  background: inherit;
  color: var(--themingFontColor);

  &.business-navbar__items--active {
    background: var(--themingFocusColor);
  }
}

.business-navbar__item-wrapper {
  display: flex;
  min-width: 0;
  flex: 1;
}

.business-navbar__item {
  display: flex;
  width: calc(100% - 0px);
  align-items: center;
  padding: 0 0 0 24px;
}

.business-navbar__item__text {
  overflow: hidden;
  width: 100%;
  text-overflow: ellipsis;
  margin: 0 8px 0 16px;
  white-space: nowrap;
}
