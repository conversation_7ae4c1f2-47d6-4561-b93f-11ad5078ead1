@if (item().userRequired) {
  <a
    (click)="showUserRequiredModal()"
    class="business-navbar__items"
    [class.business-navbar__items--active]="isActiveLink()"
    [style.padding-left.px]="indentation"
  >
    <ng-content *ngTemplateOutlet="navItem"></ng-content>
  </a>
} @else {
  @if (item().serviceProviderId === (serviceProviderId$ | async)) {
    <a
      [routerLink]="item().path"
      class="business-navbar__items"
      [class.business-navbar__items--active]="isActiveLink()"
      [style.padding-left.px]="indentation"
      (click)="activeLinkService.setActiveItem(item())"
    >
      <ng-content *ngTemplateOutlet="navItem"></ng-content>
    </a>
  } @else {
    <a
      [href]="item().url"
      [attr.target]="!item().openInNewTab ? null : '_blank'"
      [attr.noopener]="!item().openInNewTab ? null : ''"
      class="business-navbar__items"
      [class.business-navbar__items--active]="isActiveLink()"
      [style.padding-left.px]="indentation"
      (click)="activeLinkService.setActiveItem(item())"
    >
      <ng-content *ngTemplateOutlet="navItem"></ng-content>
    </a>
  }
}

<ng-template #navItem>
  <div class="business-navbar__item-wrapper">
    <span class="business-navbar__item" [title]="item().translationId || item().label | translate">
      @if (item().showIcon) {
        <bc-icon
          [matIcon]="item().icon"
          [logoUrl]="item().logoUrl"
          [productName]="item().translationId || item().label | translate"
        />
      }
      <span class="business-navbar__item__text">{{ item().translationId || item().label | translate }}</span>
      <ng-content></ng-content>
    </span>
  </div>
  @if (item().chipContent) {
    <bc-chip [value]="{ label: item().chipContent | translate, type: 'nav' }"></bc-chip>
  }
</ng-template>
