import { InjectionToken } from '@angular/core';
import { Route } from '@angular/router';
import { SideNavigationLink } from '@vendasta/atlas';
import { Observable, map, combineLatest } from 'rxjs';
import { BusinessNavDataService } from '../../data.service';
import { FeatureFlagService } from '@galaxy/partner';
import { BusinessNavConfigService } from '../../config.service';

export const PAGE_SUBLINKS_ID_TOKEN = new InjectionToken<string>('PAGE_SUBLINKS_ID');
export const PAGE_SUBLINKS_CONFIG_TOKEN = new InjectionToken<Observable<PageSublinksConfig>>('PAGE_SUBLINKS_CONFIG');

export type PageSublinksConfig = {
  sublinks: SideNavigationLink[];
};

/*
  Add providers to a route to provide the sublinks for the page.
*/
export function pageSublinksRoute(navigationId: string, Overrides: Route): Route {
  const routeDefaults: Partial<Route> = {
    providers: [
      { provide: PAGE_SUBLINKS_ID_TOKEN, useValue: navigationId },
      {
        provide: PAGE_SUBLINKS_CONFIG_TOKEN,
        useFactory: configFactory,
        deps: [BusinessNavDataService, PAGE_SUBLINKS_ID_TOKEN, FeatureFlagService, BusinessNavConfigService],
      },
    ],
  };

  const route: Route = {
    ...routeDefaults,
    ...Overrides,
  };

  for (const key in Overrides) {
    if (Object.prototype.hasOwnProperty.call(Overrides, key) && Array.isArray(Overrides[key])) {
      route[key] = [...(routeDefaults[key] || []), ...(Overrides[key] || [])];
    }
  }

  return route;
}

function configFactory(
  navigationService: BusinessNavDataService,
  navigationId: string,
): Observable<PageSublinksConfig> {
  return combineLatest([navigationService.linksWithSublinks$]).pipe(
    map(([links]) => {
      const link = links.find((c) => c.navigationId === navigationId);
      const sublinks = link?.subLinks ?? [];
      return sublinks.filter((link) => !link.userRequired && !(link.navigationId === 'nav-social-connections'));
    }),
    map((sublinks) => {
      return { sublinks };
    }),
  );
}
