@use '../../theming' as *;

:host {
  background: inherit;
}

.business-navbar__text {
  margin: 0 8px 0 0;
}

.business-navbar__section {
  height: 36px;
  display: flex;
  flex-direction: row;
  align-items: center;
  user-select: none;
  cursor: pointer;
  text-decoration: none;
  font-family: Roboto, 'Helvetica Neue', sans-serif;
  font-size: 14px;
  transition:
    background-color 0.2s cubic-bezier(0.4, 0, 1, 1),
    color 0.15s ease-in-out;
  color: var(--themingFontColor);
  background: inherit;
  border-top: 1px solid var(--themingBorderColor);
  border-bottom: 1px solid var(--themingBorderColor);
}

.business-navbar__section.business-navbar__section--opened {
  background: var(--themingSecondaryColor);
  &:hover {
    background: var(--themingSecondaryHoverColor);
  }

  &:active {
    background: var(--themingSecondaryActiveColor);
  }
}

.business-navbar__section > i {
  transition: transform 0.15s;
}

.business-navbar__section__right-arrow {
  width: 24px;
}

.business-navbar__section__right-arrow--opened {
  transform: rotate(90deg);
}

.business-navbar__section-items-wrapper {
  overflow: hidden;
}

.business-navbar__section-items {
  margin-top: -10000px; // Switched at runtime by the component.
  transition: margin-top 0.3s ease;
  border-bottom: 1px solid var(--themingBorderColor);
}
