<div class="business-navbar__section" (click)="toggle()" [class.business-navbar__section--opened]="opened$ | async">
  <i
    class="material-icons business-navbar__section__right-arrow"
    [ngClass]="{
      'business-navbar__section__right-arrow--opened': (opened$ | async),
    }"
  >
    arrow_right
  </i>
  <div class="business-navbar__text">{{ text$ | async }}</div>
  @if (_section.chipContent) {
    <bc-chip [value]="{ label: _section.chipContent | translate, type: 'nav' }"></bc-chip>
  }
</div>
<div class="business-navbar__section-items-wrapper">
  <div #section class="business-navbar__section-items">
    <ng-content></ng-content>
  </div>
</div>
