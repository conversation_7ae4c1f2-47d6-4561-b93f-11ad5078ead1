import {
  AfterContentInit,
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnDestroy,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SideNavigationItem, SideNavigationLink, SideNavigationSection } from '@vendasta/atlas';
import { BehaviorSubject, Observable, Subscription, firstValueFrom } from 'rxjs';
import { BusinessNavConfigService, Config } from '../../config.service';

@Component({
  selector: 'bc-nav-item-section',
  templateUrl: './nav-item-section.component.html',
  styleUrls: ['./nav-item-section.component.scss'],
  standalone: false,
})
export class NavItemSectionComponent implements OnInit, AfterViewInit, AfterContentInit, OnD<PERSON>roy {
  @Input() startOpen: boolean;
  @Input({ required: true }) set item(item: SideNavigationSection) {
    this.children = item.sideNavigationItems;
    this._section = item;
  }
  protected _section: SideNavigationSection;
  protected children: SideNavigationItem[];
  @ViewChild('section', { static: true }) section: ElementRef;
  text$: Observable<string>;

  private opened$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public opened$: Observable<boolean> = this.opened$$.asObservable();
  private subs: Subscription[] = [];

  constructor(
    private renderer: Renderer2,
    private t: TranslateService,
    private configService: BusinessNavConfigService,
    private router: Router,
  ) {}

  get sectionHeight(): number {
    return this.section.nativeElement.offsetHeight;
  }

  ngOnInit(): void {
    this.text$ = this.t.stream(this._section.translationId || this._section.label);
    if (this.startOpen) {
      this.open();
    }
  }

  ngAfterContentInit(): void {
    this.setOpenState();
  }

  private async setOpenState() {
    const config = await firstValueFrom(this.configService.config$);
    if (this.checkChildrenForActiveRoute(this.children, config)) {
      this.open();
    }
  }

  ngOnDestroy(): void {
    this.subs.map((sub: Subscription) => sub.unsubscribe());
  }

  private open(): void {
    if (this.opened$$.getValue()) {
      return;
    }
    this.updateRendererStyle(true);
    this.opened$$.next(true);
  }

  public toggle(): void {
    const toggledValue = !this.opened$$.getValue();
    this.updateRendererStyle(toggledValue);
    this.opened$$.next(toggledValue);
  }

  updateRendererStyle(open: boolean): void {
    if (open) {
      this.renderer.setStyle(this.section.nativeElement, 'margin-top', '0');
    } else {
      this.renderer.setStyle(this.section.nativeElement, 'margin-top', -this.sectionHeight + 'px');
    }
  }

  ngAfterViewInit(): void {
    window.setTimeout(() => this.updateRendererStyle(this.opened$$.getValue()), 0);
  }

  private checkChildrenForActiveRoute(children: SideNavigationItem[], config: Config): boolean {
    let active = false;
    if (!children || children.length === 0) {
      return active;
    }

    const curPath = `/${this.router.parseUrl(this.router.url).root.children.primary.toString()}`;

    for (const child of children) {
      if (child.sideNavigationSection) {
        active = this.checkChildrenForActiveRoute(child.sideNavigationSection.sideNavigationItems, config);
      } else if (child.sideNavigationContainer) {
        active = this.checkChildrenForActiveRoute(child.sideNavigationContainer.sideNavigationItems, config);
      } else if (child.sideNavigationLink.serviceProviderId === config.serviceProviderId) {
        active =
          curPath === child.sideNavigationLink.path ||
          this.checkSublinksForActiveRoute(child.sideNavigationLink.subLinks, config);
      }

      if (active) {
        break;
      }
    }

    return active;
  }

  private checkSublinksForActiveRoute(sublinks: SideNavigationLink[], config: Config): boolean {
    if (!sublinks || sublinks.length === 0) {
      return false;
    }

    const curPath = `/${this.router.parseUrl(this.router.url).root.children.primary.toString()}`;

    return sublinks.some(
      (sublink) => sublink.serviceProviderId === config.serviceProviderId && curPath === sublink.path,
    );
  }
}
