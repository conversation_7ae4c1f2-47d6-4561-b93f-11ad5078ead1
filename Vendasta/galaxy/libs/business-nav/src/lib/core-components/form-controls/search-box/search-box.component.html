<div
  class="business-navbar__search-box__wrapper business-navbar__search-box__outline-mode"
  [ngClass]="{
    'business-navbar__search-box__wrapper--focused': isFocused,
    'business-navbar__search-box__wrapper--not-empty': !!text
  }"
>
  <div class="business-navbar__search-box__outline-wrapper"></div>
  <div class="business-navbar__search-box__field__flex">
    <div class="business-navbar__search-box__field-infix">
      <input
        class="business-navbar__search-box__text"
        (focus)="focused(true)"
        (focusout)="focused(false)"
        [(ngModel)]="text"
        (ngModelChange)="textChange.emit(text)"
        autofocus
      />
      <span class="business-navbar__search-box__label">
        <label class="business-navbar__search-box__label__text">
          {{ 'SWITCH_LOCATION_MODAL.SEARCH' | translate }}
        </label>
      </span>
    </div>
  </div>
  <div class="business-navbar__search-box__field-underline">
    <span class="business-navbar__search-box__field-underline__field-ripple"></span>
  </div>
</div>
