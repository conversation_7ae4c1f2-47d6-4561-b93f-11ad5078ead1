import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'bc-search-box',
  templateUrl: './search-box.component.html',
  styleUrls: ['./search-box.component.scss'],
  standalone: false,
})
export class SearchBoxComponent {
  @Output() textChange = new EventEmitter<string>();
  text: string;
  public isFocused = true;

  focused(focused: boolean): void {
    this.isFocused = focused;
  }
}
