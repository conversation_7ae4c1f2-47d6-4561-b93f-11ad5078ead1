@use '../colors' as *;

.business-navbar__search-box__wrapper {
  width: 100%;
  max-width: 100%;
  padding-bottom: 1.25em;
  position: relative;
}

.business-navbar__search-box__field__flex {
  display: inline-flex;
  align-items: baseline;
  box-sizing: border-box;
  width: 100%;
}

.business-navbar__search-box__field-infix {
  display: block;
  position: relative;
  flex: auto;
  min-width: 0;
  width: 180px;
  padding: 0.4375em 0;
  border-top: 0.84375em solid transparent;
  text-align: left;
}

.business-navbar__search-box__text {
  min-height: 20px;
  font-size: 16px;
  width: 100%;
  max-width: 100%;
  min-width: 100%;
  resize: vertical;
  border: none;
  outline: none;
}

.business-navbar__search-box__label {
  position: absolute;
  left: 0;
  box-sizing: content-box;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  top: -0.84375em;
  padding-top: 0.84375em;
}

.business-navbar__search-box__label__text {
  font-size: 16px;
  transform: perspective(100px);
  color: rgba(0, 0, 0, 0.54);
  top: 1.28125em;
  position: absolute;
  left: 0;
  pointer-events: none;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  transform-origin: 0 0;
  transition:
    transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),
    color 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),
    width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.business-navbar__search-box__wrapper--focused .business-navbar__search-box__label__text {
  color: $focus-highlight;
  transform: translateY(-1.28125em) scale(0.75) perspective(100px) translateZ(0.001px);
  width: 133.33333333%;
}

.business-navbar__search-box__wrapper--not-empty .business-navbar__search-box__label__text {
  transform: translateY(-1.28125em) scale(0.75) perspective(100px) translateZ(0.001px);
  width: 133.33333333%;
}

.business-navbar__search-box__field-underline {
  height: 1px;
  bottom: 1.25em;
  background-color: rgba(0, 0, 0, 0.42);
  position: absolute;
  width: 100%;
  pointer-events: none;
  transform: scaleY(1.0001);
}

.business-navbar__search-box__field-underline__field-ripple {
  top: 0;
  height: 2px;
  overflow: hidden;
  background-color: $focus-highlight;
  position: absolute;
  left: 0;
  width: 100%;
  transform-origin: 50%;
  transform: scaleX(0.5);
  opacity: 0;
  transition: background-color 0.3s cubic-bezier(0.55, 0, 0.55, 0.2);
}

.business-navbar__search-box__wrapper--focused .business-navbar__search-box__field-underline__field-ripple {
  opacity: 1;
  transform: scaleX(1);
  transition:
    transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
    opacity 0.1s cubic-bezier(0.25, 0.8, 0.25, 1),
    background-color 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.business-navbar__search-box__outline-wrapper {
  display: none;
}

.business-navbar__search-box__outline-mode {
  padding: 0;
  border-radius: 4px;
  background: $outline-background;
  margin: 8px 0;

  .business-navbar__search-box__outline-wrapper {
    border: 1px solid $outline-border-color;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: block;
    border-radius: 4px;
  }
  .business-navbar__search-box__field-infix {
    .business-navbar__search-box__text {
      padding: 0 12px;
      margin: 0 4px;
      width: calc(100% - 24px);
      min-width: calc(100% - 24px);
    }
  }
  .business-navbar__search-box__label {
    overflow: visible;
    .business-navbar__search-box__label__text {
      top: 0.9em;
      background: $outline-background;
      width: auto;
      padding: 0 8px;
      margin-left: 6px;
    }
  }
  // Hide the line
  .business-navbar__search-box__field-underline {
    display: none;
  }

  &:hover {
    .business-navbar__search-box__outline-wrapper {
      border-color: $outline-border-highlight;
      border-width: 2px;
    }
  }
  &.business-navbar__search-box__wrapper--focused {
    .business-navbar__search-box__outline-wrapper {
      border-color: $focus-highlight;
      border-width: 2px;
    }
  }
}
