import { AsyncPipe, NgClass } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import { NavigationEnd, Route, Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SideNavigationLink } from '@vendasta/atlas';
import { Observable, filter, map, startWith, switchMap } from 'rxjs';
import { BusinessNavDataService } from '../../data.service';
import {
  AppUrlResolver,
  NAV_TABS_CONTAINER_CONFIG_TOKEN,
  NAV_TABS_CONTAINER_ID_TOKEN,
  NavTabsContainerConfig,
  configFactory,
  defaultRouteGuard,
} from './nav-tabs-container.interface';

@Component({
  selector: 'bc-nav-tabs-container',
  imports: [AsyncPipe, NgClass, MatTabsModule, RouterModule, TranslateModule],
  templateUrl: './nav-tabs-container.component.html',
  styleUrls: ['./nav-tabs-container.component.scss'],
})
export class NavTabsContainerComponent {
  protected readonly tabs$: Observable<SideNavigationLink[]>;
  protected readonly activeTab$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    map((e: NavigationEnd) => e.url),
    startWith(this.router.url),
    switchMap((url) => this.tabs$.pipe(map((tabs) => tabs.find((t) => url.includes(t.path))?.navigationId ?? ''))),
  );
  protected readonly showTabs$: Observable<boolean>;

  constructor(
    @Inject(NAV_TABS_CONTAINER_CONFIG_TOKEN) config: Observable<NavTabsContainerConfig>,
    private readonly router: Router,
  ) {
    this.tabs$ = config.pipe(map((c) => c.tabs));
    this.showTabs$ = config.pipe(map((c) => c.tabs.length > 1));
  }
}

type TabbedRouteOverrides = Omit<Route, 'loadComponent' | 'loadChildren' | 'component' | 'canLoad' | 'redirectTo'> &
  Required<Pick<Route, 'path' | 'children'>>;

/**
 * Creates a route for a set of pages that use tabs for navigation.
 * @param navigationId the navigationId of the parent route to the tabbed pages.
 * @param overrides are a subset of Route params, additive where applicable
 */
export function tabbedRoute(navigationId: string, overrides: TabbedRouteOverrides): Route {
  return tabbedContainerRoute(navigationId, overrides, false);
}

/**
 * Creates a route for a tabbed container.
 * Overrides are additive where applicable.
 */
export function tabbedContainerRoute(
  navigationId: string,
  overrides: TabbedRouteOverrides,
  withNavTabsContainer: false,
): Route;
/**
 * @Deprecated: Use `tabbedRoute` instead. Requires use of `nav-tabs.component` on page.
 */
export function tabbedContainerRoute(navigationId: string, overrides: TabbedRouteOverrides): Route;
/**
 * @Deprecated: Use `tabbedRoute` instead. Requires use of `nav-tabs.component` on page.
 */
export function tabbedContainerRoute(
  navigationId: string,
  overrides: TabbedRouteOverrides,
  withNavTabsContainer: true,
): Route;
export function tabbedContainerRoute(
  navigationId: string,
  overrides: TabbedRouteOverrides,
  withNavTabsContainer = true,
): Route {
  const routeDefaults: Partial<Route> = {
    providers: [
      { provide: NAV_TABS_CONTAINER_ID_TOKEN, useValue: navigationId },
      {
        provide: NAV_TABS_CONTAINER_CONFIG_TOKEN,
        useFactory: configFactory,
        deps: [BusinessNavDataService, NAV_TABS_CONTAINER_ID_TOKEN],
      },
    ],
    loadComponent: withNavTabsContainer
      ? () => import('./nav-tabs-container.component').then((m) => m.NavTabsContainerComponent)
      : undefined,
    runGuardsAndResolvers: 'always',
    canActivate: [defaultRouteGuard],
    resolve: {
      appUrl: AppUrlResolver,
    },
  };

  const route: Route = {
    ...routeDefaults,
    ...overrides,
  };

  for (const key in overrides) {
    if (Object.prototype.hasOwnProperty.call(overrides, key) && Array.isArray(overrides[key])) {
      route[key] = [...(routeDefaults[key] || []), ...(overrides[key] || [])];
    }
  }

  return route;
}
