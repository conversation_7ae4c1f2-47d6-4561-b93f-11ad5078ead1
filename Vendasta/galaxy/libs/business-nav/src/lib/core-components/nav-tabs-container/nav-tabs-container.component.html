@if ((showTabs$ | async) === true) {
  <nav mat-tab-nav-bar [mat-stretch-tabs]="false" [tabPanel]="panel">
    @for (tab of tabs$ | async; track tab) {
      <a mat-tab-link [routerLink]="tab.path" [active]="tab.navigationId === (activeTab$ | async)">
        {{ tab.label }}
      </a>
    }
  </nav>
}

<mat-tab-nav-panel #panel>
  <div class="scroll" [ngClass]="{ 'has-tabs': (showTabs$ | async) === true }">
    <router-outlet></router-outlet>
  </div>
</mat-tab-nav-panel>
