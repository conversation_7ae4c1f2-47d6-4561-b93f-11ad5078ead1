import { InjectionToken, inject } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, createUrlTreeFromSnapshot } from '@angular/router';
import { SideNavigationLink } from '@vendasta/atlas';
import { filterNullAndUndefined } from '@vendasta/rx-utils';
import { Observable, defaultIfEmpty, map, take } from 'rxjs';
import { BusinessNavDataService } from '../../data.service';

export const NAV_TABS_CONTAINER_CONFIG_TOKEN = new InjectionToken<Observable<NavTabsContainerConfig>>(
  'NAV_TABS_CONTAINER_CONFIG',
);
export type NavTabsContainerConfig = {
  link: SideNavigationLink;
  tabs: SideNavigationLink[];
};

export const NAV_TABS_CONTAINER_ID_TOKEN = new InjectionToken<string>('NAV_TABS_CONTAINER_ID');
export const configFactory = (
  navigationService: BusinessNavDataService,
  containerId: string,
): Observable<NavTabsContainerConfig> => {
  return navigationService.linksWithSublinks$.pipe(
    map((container) => container.find((c) => c.navigationId === containerId)),
    filterNullAndUndefined(),
    map((link) => {
      return {
        link: link,
        tabs: link.subLinks,
      };
    }),
  );
};

// route guard that will always route to the first tab
export const defaultRouteGuard = (r: ActivatedRouteSnapshot, s: RouterStateSnapshot) => {
  const c = inject(NAV_TABS_CONTAINER_CONFIG_TOKEN);

  return c.pipe(
    take(1),
    map((config) => {
      if (!config || !config.tabs) return false;
      if (!config.tabs?.find((t) => s.url.includes(t.path))) {
        return createUrlTreeFromSnapshot(r, [config.tabs?.[0].path]);
      }

      return true;
    }),
  );
};

// route resolver that adds the app url to the route data pageOptions field
export const AppUrlResolver = () => {
  const c = inject(NAV_TABS_CONTAINER_CONFIG_TOKEN);

  return c.pipe(
    take(1),
    filterNullAndUndefined(),
    map((config) => config.link.launchUrl),
    defaultIfEmpty(''),
  );
};
