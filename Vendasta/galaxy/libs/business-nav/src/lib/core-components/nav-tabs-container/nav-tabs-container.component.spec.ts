import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { NavTabsContainerComponent } from './nav-tabs-container.component';
import { NAV_TABS_CONTAINER_CONFIG_TOKEN } from './nav-tabs-container.interface';

describe('NavTabsContainerComponent', () => {
  let component: NavTabsContainerComponent;
  let fixture: ComponentFixture<NavTabsContainerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NavTabsContainerComponent, RouterTestingModule],
      providers: [
        {
          provide: NAV_TABS_CONTAINER_CONFIG_TOKEN,
          useValue: of({
            link: {},
            tabs: [
              { navigationId: 'nav-dashboard', path: '/dashboard' },
              { navigationId: 'nav-get-started', path: '/get-started' },
              { navigationId: 'nav-recent-activity', path: '/recent-activity' },
            ],
          }),
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(NavTabsContainerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('contains the correct number of tabs', () => {
    expect(fixture.nativeElement.querySelectorAll('a').length).toEqual(3);
  });
});
