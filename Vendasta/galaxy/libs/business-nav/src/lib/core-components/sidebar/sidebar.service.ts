import { Injectable } from '@angular/core';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { distinctUntilChanged, map, shareReplay } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class SidebarService {
  private readonly disabled$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  readonly disabled$: Observable<boolean> = this.disabled$$.asObservable().pipe(map((disabled) => disabled));

  private readonly forceShowMenuToggle$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  readonly forceShowMenuToggle$: Observable<boolean> = this.forceShowMenuToggle$$.asObservable();

  readonly showMenuToggle$: Observable<boolean> = combineLatest([this.disabled$, this.forceShowMenuToggle$]).pipe(
    map(([isDisabled, forceShowToggle]) => {
      if (forceShowToggle || !isDisabled) {
        return true;
      }
      return false;
    }),
    distinctUntilChanged(),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  setDisabled(isDisabled: boolean): void {
    this.disabled$$.next(isDisabled);
  }

  setShowMenuToggle(isVisible: boolean): void {
    this.forceShowMenuToggle$$.next(isVisible);
  }
}
