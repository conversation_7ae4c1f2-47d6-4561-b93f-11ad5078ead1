@use 'design-tokens' as *;
@use '../shimmer' as shim;

:host {
  height: 36px;
  width: $spacing-4;
  min-width: $spacing-4;
  display: inline-flex;
  align-items: center;
}

.business_navbar__icon__size {
  border-radius: 50%;
  width: $spacing-4;
  height: $spacing-4;
  border: 1px solid $white;
  background: $white;
}

.business-navbar__icon__stencil {
  border-radius: 50% !important;
  width: 24px;
  height: 24px;

  @include shim.stencil();
}

.business-navbar__icon-container {
  width: 1em;
  height: 1em;
  font-size: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

span {
  font-size: 0.4em;
  font-style: normal;
  font-weight: normal;
  color: $white;
  text-transform: uppercase;
}
