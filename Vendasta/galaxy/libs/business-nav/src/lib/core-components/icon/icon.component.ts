import { IMAGE_LOADER, ImageLoaderConfig, NgOptimizedImage } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'bc-icon',
  templateUrl: './icon.component.html',
  styleUrls: ['./icon.component.scss'],
  imports: [MatIconModule, NgOptimizedImage],
  providers: [
    {
      provide: IMAGE_LOADER,
      useValue: (config: ImageLoaderConfig) => {
        if (config.src.startsWith('https://lh3.googleusercontent.com/')) {
          return `${config.src}=w${config.width}-c`; // https://developers.google.com/people/image-sizing
        }
        return config.src;
      },
    },
  ],
})
export class IconComponent implements OnInit {
  @Input() matIcon: string;
  @Input() logoUrl: string;
  @Input() productName: string;
  iconType: string;

  ngOnInit(): void {
    if (this.matIcon) {
      if (this.matIcon.endsWith('_outlined')) {
        this.iconType = 'matIcon_outlined';
      } else {
        this.iconType = 'matIcon';
      }
    } else if (this.logoUrl) {
      this.iconType = 'logoUrl';
    } else if (this.productName && !this.matIcon && !this.logoUrl) {
      this.iconType = 'generated';
    }
  }

  public getAbbreviationForName(): string {
    const defaultAbbreviation = 'U';
    if (this.productName) {
      const names = this.productName.split(' ');
      return names.length > 1 ? names[0][0] + names[1][0] : names[0][0];
    }
    return defaultAbbreviation;
  }

  getIconColorForName(): string {
    const COLOR_CODES = [
      '#EF5350',
      '#42A5F5',
      '#66BB6A',
      '#FFA726',
      '#AB47BC',
      '#FFCA28',
      '#EC407A',
      '#26C6DA',
      '#FF7B57',
    ];
    let nameSum = 0;
    const defaultColor = '#808080';
    if (!this.productName) {
      return defaultColor;
    }
    for (let i = 0; i < this.productName.length; i++) {
      nameSum += this.productName[i].charCodeAt(0);
    }
    const index = nameSum % COLOR_CODES.length;

    return COLOR_CODES[index];
  }
}
