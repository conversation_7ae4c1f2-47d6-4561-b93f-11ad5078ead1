@switch (iconType) {
  @case ('matIcon_outlined') {
    <mat-icon [svgIcon]="matIcon"></mat-icon>
  }
  @case ('matIcon') {
    <i class="material-icons">{{ matIcon }}</i>
  }
  @case ('logoUrl') {
    <div class="business-navbar__icon-container">
      <img class="business_navbar__icon__size" [ngSrc]="logoUrl" width="24" height="24" alt="productIcon" />
    </div>
  }
  @case ('generated') {
    <div class="business-navbar__icon-container" [style.background-color]="getIconColorForName()">
      @if (!!productName && !logoUrl) {
        <span>{{ getAbbreviationForName() }}</span>
      }
    </div>
  }
  @default {
    <div class="business-navbar__icon__stencil"></div>
  }
}
