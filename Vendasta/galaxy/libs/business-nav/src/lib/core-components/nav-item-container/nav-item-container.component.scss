@use '../../theming' as *;

.bc-nav__container {
  display: flex;
  align-items: center;
  user-select: none;
  cursor: pointer;
  color: var(--themingFontColor);
  background-color: var(--themingPrimaryColor);

  &.open {
    background: var(--themingPrimaryHoverColor);
  }

  .bc-nav__primary {
    display: flex;
    flex: 1 1 100%;
    align-items: center;
    background: inherit;
    color: var(--themingFontColor);

    .dropdown-arrow {
      width: 24px;
      flex-shrink: 0;
      transition: 0.3s all ease-in-out;

      &.open {
        transform: rotate(90deg);
      }
    }

    .bc-nav__text {
      overflow: hidden;
      width: 100%;
      text-overflow: ellipsis;
      margin: 0 8px 0 16px;
      white-space: nowrap;
    }

    &:hover {
      transition:
        background-color 0.2s cubic-bezier(0.4, 0, 1, 1),
        color 0.15s ease-in-out;
      background: var(--themingSecondaryHoverColor);
    }
  }
}
