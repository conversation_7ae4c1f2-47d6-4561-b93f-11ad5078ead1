<div class="bc-nav__container" [class.open]="open()">
  <span class="bc-nav__primary" (click)="toggle()">
    <mat-icon class="dropdown-arrow" [class.open]="open()">arrow_right</mat-icon>
    @if (_container.showIcon) {
      <bc-icon [matIcon]="_container.icon" [logoUrl]="_container.logoUrl" [productName]="_container.label"></bc-icon>
    }
    <span class="bc-nav__text">{{ text | translate }}</span>
    @if (_container.chipContent) {
      <bc-chip [value]="{ label: _container.chipContent | translate, type: 'nav' }"></bc-chip>
    }
  </span>
</div>
<div [@openClose]="open() ? 'open' : 'closed'">
  <ng-content></ng-content>
</div>
