import { animate, state, style, transition, trigger } from '@angular/animations';
import { Component, Input, inject, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { SideNavigationContainer, SideNavigationLink, SideNavigationSection } from '@vendasta/atlas';
import { map } from 'rxjs';
import { BusinessNavConfigService } from '../../config.service';

type HasChildLinks =
  | Pick<SideNavigationLink, 'subLinks'>
  | Pick<SideNavigationContainer, 'sideNavigationItems'>
  | Pick<SideNavigationSection, 'sideNavigationItems'>;

function hasChildLinks(item: object): item is HasChildLinks {
  return 'sideNavigationItems' in item || 'subLinks' in item;
}

@Component({
  selector: 'bc-nav-item-container',
  templateUrl: './nav-item-container.component.html',
  styleUrls: ['./nav-item-container.component.scss'],
  animations: [
    trigger('openClose', [
      state(
        'closed',
        style({
          height: '0px',
          opacity: 0,
          visibility: 'hidden',
          overflow: 'clip',
        }),
      ),
      state(
        'open',
        style({
          height: '*',
          opacity: 1,
          visibility: 'visible',
          overflow: 'visible',
        }),
      ),
      transition('closed <=> open', [animate('0.2s')]),
    ]),
  ],
  standalone: false,
})
export class NavItemContainerComponent {
  private readonly router = inject(Router);
  private readonly serviceProviderId$ = inject(BusinessNavConfigService).config$.pipe(
    map((config) => config.serviceProviderId),
  );

  @Input({ required: true }) set item(item: SideNavigationContainer) {
    this._container = item;
    this.text = this._container.translationId || this._container.label;
    if (this.checkLinksForActiveRoute(this.mapToLinks(item))) {
      this.open.set(true);
    }
  }
  protected _container: SideNavigationContainer;
  protected text: string;
  protected readonly serviceProviderId = toSignal(this.serviceProviderId$);
  protected open = signal<boolean>(false);

  public toggle(): void {
    this.open.set(!this.open());
  }

  private mapToLinks(item: HasChildLinks): SideNavigationLink[] {
    if ('sideNavigationItems' in item) {
      return item.sideNavigationItems.flatMap((child) => {
        if (hasChildLinks(child)) {
          return this.mapToLinks(child);
        }

        return child.sideNavigationLink;
      });
    }

    return item.subLinks;
  }

  private checkLinksForActiveRoute(links: SideNavigationLink[]): boolean {
    const curPath = `/${this.router.parseUrl(this.router.url).root.children.primary.toString()}`;
    return links.some((link) => link.serviceProviderId === this.serviceProviderId() && curPath.includes(link.path));
  }
}
