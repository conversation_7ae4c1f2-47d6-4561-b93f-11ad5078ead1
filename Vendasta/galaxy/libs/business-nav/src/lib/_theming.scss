@use 'sass:map';

// pass variables into a sass map
$variables: (
  --themingPrimaryColor: var(--themingPrimaryColor),
  --themingPrimaryHoverColor: var(--themingPrimaryHoverColor),
  --themingPrimaryActiveColor: var(--themingPrimaryActiveColor),
  --themingFontColor: var(--themingFontColor),
  --themingFontDisabledColor: var(--themingFontDisabledColor),
  --themingSecondaryColor: var(--themingSecondaryColor),
  --themingSecondaryHoverColor: var(--themingSecondaryHoverColor),
  --themingSecondaryActiveColor: var(--themingSecondaryActiveColor),
  --themingAccentsColor: var(--themingAccentsColor),
  --themingAccentsActiveColor: var(--themingAccentsActiveColor),
  --themingFocusColor: var(--themingFocusColor),
  --themingBorderColor: var(--themingBorderColor),
);

@function var($variable) {
  @return map.get($variables, $variable);
}
