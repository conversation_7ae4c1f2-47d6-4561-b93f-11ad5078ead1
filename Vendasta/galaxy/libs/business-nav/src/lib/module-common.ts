import { Injector } from '@angular/core';
import { createCustomElement } from '@angular/elements';
import { NavItemContainerComponent } from './core-components/nav-item-container/nav-item-container.component';
import { LegacyNavLinkComponent } from './core-components/nav-link/legacy-nav-link.component';
import { NavigationComponent } from './navigation/navigation.component';

// ************ Elements-specific ************ //

export function DEFINE_CUSTOM_ELEMENTS(injector: Injector): void {
  try {
    const navbar = createCustomElement(NavigationComponent, {
      injector: injector,
    });
    customElements.define('bc-business-navbar-element', navbar);

    const item = createCustomElement(LegacyNavLinkComponent, { injector: injector });
    customElements.define('bc-nav-item-element', item);

    const itemContainer = createCustomElement(NavItemContainerComponent, {
      injector: injector,
    });
    customElements.define('bc-nav-item-container-element', itemContainer);
  } catch (e) {
    console.log(
      `failed to register web elements, you may need the @webcomponents/custom-elements polyfill: ${e.message}`,
    );
  }
}
