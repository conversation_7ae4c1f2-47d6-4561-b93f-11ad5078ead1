@if (interactable$ | async) {
  <atlas-item
    [customClass]="isRounded() ? 'atlas-navbar__item--rounded' : ''"
    interactable
    (click)="showSwitchLocationDialog()"
    class="atlas-item"
  >
    <ng-content *ngTemplateOutlet="locationSelector"></ng-content>
  </atlas-item>
} @else {
  <atlas-item [customClass]="isRounded() ? 'atlas-navbar__item--rounded' : ''">
    <ng-content *ngTemplateOutlet="locationSelector"></ng-content>
  </atlas-item>
}

<ng-template #locationSelector>
  @let interactable = interactable$ | async;
  @if (currentLocationName$ | async; as locationName) {
    <div class="location-info">
      <div class="name-and-address" [ngClass]="{ 'name-and-address--with-icon': interactable }">
        <span class="name" [ngClass]="{ 'name--with-icon': interactable }">{{ locationName }}</span>
        @if (currentLocationAddress$ | async; as locationAddress) {
          <span class="address" [ngClass]="{ 'address--with-icon': interactable }">{{ locationAddress }}</span>
        }
      </div>
      @if (interactable) {
        <i class="material-icons">{{ icon() }}</i>
      }
    </div>
  }
</ng-template>
