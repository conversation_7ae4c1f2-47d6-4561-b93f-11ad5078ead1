import { Component, input } from '@angular/core';
import { ModalService } from '@galaxy/atlas';
import {
  AtlasApiService,
  GetLocationsRequest,
  GetLocationsRequestAccountGroups,
  GetLocationsRequestGroups,
  Location,
} from '@vendasta/atlas';
import { Observable } from 'rxjs';
import { distinctUntilChanged, filter, map, shareReplay, startWith, switchMap, withLatestFrom } from 'rxjs/operators';
import { BusinessNavConfigService, Config } from '../config.service';
import { SidebarService } from '../core-components/sidebar/sidebar.service';
import { BusinessNavDataService } from '../data.service';
import { LocationSwitcherModalComponent } from '../modals/location-switcher-modal/location-switcher-modal.component';
import { BusinessNavLocationService } from '../modals/location-switcher-modal/location.service';

interface CachedLocationResponse {
  locationId: string;
  response: Location;
}

@Component({
  selector: 'bc-location-selector',
  templateUrl: './location-selector.component.html',
  styleUrls: ['./location-selector.component.scss'],
  standalone: false,
})
export class LocationSelectorComponent {
  icon = input<'sync_alt' | 'arrow_drop_down'>('arrow_drop_down');
  isRounded = input<boolean>(false);
  interactable$: Observable<boolean> = this.dataService.locationSwitchable$;
  currentLocation$: Observable<Location>;
  currentLocationName$: Observable<string>;
  currentLocationAddress$: Observable<string>;
  showIcon$: Observable<boolean>;

  constructor(
    private configService: BusinessNavConfigService,
    private locationService: BusinessNavLocationService,
    private dataService: BusinessNavDataService,
    private sidebarService: SidebarService,
    private atlasApiService: AtlasApiService,
    private modalService: ModalService,
  ) {
    this.currentLocation$ = this.configService.config$.pipe(
      filter((config) => !!config.accountId || !!config.groupPath),
      switchMap((config) => {
        let req: GetLocationsRequest;
        if (config.groupPath) {
          req = new GetLocationsRequest({
            groups: new GetLocationsRequestGroups({ groupPaths: [config.groupPath] }),
          });
        } else {
          req = new GetLocationsRequest({
            accountGroups: new GetLocationsRequestAccountGroups({ accountGroupIds: [config.accountId] }),
          });
        }
        return this.atlasApiService.getLocations(req);
      }),
      map((locations) => {
        let cache: CachedLocationResponse;
        const location = locations.locations[0];
        if (location && location.brand) {
          cache = {
            locationId: location.brand.pathNodes.join('|'),
            response: location,
          };
        } else if (location && location.accountGroup) {
          cache = {
            locationId: location.accountGroup.accountGroupId,
            response: location,
          };
        }
        this.setLocationCache(cache);
        return cache;
      }),
      startWith(LocationSelectorComponent.getLastLocation()),
      withLatestFrom(this.configService.config$),
      filter(
        ([res, config]: [CachedLocationResponse, Config]) =>
          !!res && res.locationId === LocationSelectorComponent.locationIdFromConfig(config),
      ),
      map(([res]: [CachedLocationResponse, Config]) => res.response),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.currentLocationName$ = this.currentLocation$.pipe(
      distinctUntilChanged(),
      map((location: Location) => this.getLocationName(location)),
      filter((name: string) => !!name),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    this.currentLocationAddress$ = this.currentLocation$.pipe(
      distinctUntilChanged(),
      map((location: Location) => this.getLocationAddress(location)),
      filter((address: string) => !!address),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.showIcon$ = this.sidebarService.showMenuToggle$.pipe(
      map((isVisible: boolean) => !isVisible),
      distinctUntilChanged(),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private static getLastLocation(): CachedLocationResponse {
    // eslint-disable-next-line @typescript-eslint/ban-types
    const o: { locationId: string; response: object } = JSON.parse(
      localStorage.getItem('business-nav-current-location'),
    );
    if (!o || !o.response) {
      return null;
    }
    return {
      locationId: o.locationId,
      response: Location.fromProto(o.response),
    };
  }

  private static locationIdFromConfig(cfg: Config): string {
    return cfg.accountId ? cfg.accountId : cfg.groupPath;
  }

  setLocationCache(cache: CachedLocationResponse): void {
    localStorage.setItem(
      'business-nav-current-location',
      JSON.stringify({
        locationId: cache.locationId,
        response: cache.response.toApiJson(),
      }),
    );
  }

  getLocationName(location: Location): string {
    if (!location) {
      return '';
    }
    if (location.accountGroup) {
      return location.accountGroup.name;
    }
    if (location.brand) {
      return location.brand.name;
    }
    return '';
  }

  getLocationAddress(location: Location): string {
    if (!location) {
      return '';
    }
    if (location.accountGroup) {
      return location.accountGroup.address;
    }
    if (location.brand) {
      return 'Group';
    }
    return '';
  }

  showSwitchLocationDialog(): void {
    this.locationService.setQuery('');
    this.modalService.openList(LocationSwitcherModalComponent);
  }
}
