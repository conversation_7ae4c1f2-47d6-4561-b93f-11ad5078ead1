@use 'design-tokens' as *;

:host {
  overflow: hidden;
}

.location-info {
  padding: $spacing-1 $spacing-2 $spacing-1 $spacing-2;
  // right padding situation is complicated by the optional dropdown arrow,
  // which includes its own padding. if dropwdown arrow is rendered, we don't
  // need as much padding on the right. if drowpdown arrow is not rendered, the
  // above padding is added to right padding on name-and-address (below), which
  // also serves as a gap when the dropdown arrow IS rendered. phew.
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100%;
  text-align: left;
}

.name-and-address {
  overflow: hidden;
  flex-grow: 1;
  @include text-preset-5;
  &--with-icon {
    padding-right: $spacing-2;
  }
}

.name {
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  white-space: nowrap;
  @include text-preset-5--bold;
  &--with-icon {
    max-width: 10.65rem;
  }
}

.address {
  text-overflow: ellipsis;
  overflow: hidden;
  display: block;
  line-height: 14px;
  white-space: nowrap;
  &--with-icon {
    max-width: 10.65rem;
  }
}

.atlas-item {
  overflow: hidden;
}
