# Changelog
# 7.0.0
- Upgrade to Angular 20

# 6.10.3
- Add `standalone: false` to all standalone components to prepare for Angular 19 update

# 6.10.2
- Fix height for `NavigationComponent` content container for pages that still depend on it when using the old navigation

# 6.10.1
- Add translation for automation tab

# 6.10.0
- switch `BusinessNavDataService.navigationLinks$` over to using the new `navigation_items` field on the rpc.

# 6.9.5
- Add scroll function to settings button in business nav

# 6.9.4
- Remove extra if statement.
- Handle the no logo case for `business-sidebar` footer.

# 6.9.3
- Remove `business app` in location switcher banner

# 6.9.2
- Replace salesperson img with `glxy-avatar`

# 6.9.1
- Fix full navigation item URL being used for Stop Impersonation in addition to Sign Out when in VBC

## 6.9.0
- Remove retention config and change subscription modal

# 6.8.3
- Fix the sales person image

# 6.8.2
- Reduce `bc-sidebar` z-index from 999 to 997 to stop it from going over the `atlas-navbar`.

# 6.8.1
- Fix language loader not using PID for white label

# 6.8.0
- Use the rx-utils Scrollable to back the location switcher pagination

## 6.7.1
- Fix showing wrong empty state on loading

## 6.7.0
- Support empty state in the location switcher

## 6.6.3
- Fix the top of partner logo display.

## 6.6.2
- Sort the navigation items from business nav according to the list NAV_IDS_ABOVE_PRODUCTS_LIST

## 6.6.1
- Move the Customers tab to below of the Inbox Messages tab.

## 6.6.0
- Support the ability to disable the left navigation bar from a partner setting

## 6.5.0
- Replace en.json with en_devel.json

## 6.4.1
- Update weblate component name to 'common/business-nav'

## 6.4.0
- Load translations from lexicon

## 6.3.5
- Fix some UI issues in location switcher.

## 6.3.4
- Using `GROUPS` in location switcher.

## 6.3.3
- Change brands to groups

## 6.3.2
- Fix bugs from paging rework

## 6.3.1
- Bug fix. Update `activePath$` in location service to make the location switcher working in products.

## 6.3.0
- Rework paging to be more reliable.

## 6.2.1
- Bug fix. Passing null value to `coBrandingUrl$` if url is undefined.

## 6.2.0
- Angular 12

## 6.1.1
- @vendasta/atlas patch version 6.3.2
  - Make translations URL injection token Optional so it isn't a breaking change

## 6.1.0
- Upgrade @vendasta/atlas install to 6.3.1, language selector stores choice locally in storage and reloads app

## 6.0.1
- Provide the `ModalService` in the common module rather than root to prevent it from being imported before the tooltip module

## 6.0.0
- Depend on Atlas 6.0.0
- Remove `BrowserModule` import from `BusinessNavModule`
- Provide all services in root
- Upgraded to NG 11 and RXJS 6.6.6

## 5.8.0
- Pull `GetSalesInfo` out of `GetNavigationData`

## 5.7.0
- Add German back to language options

## 5.6.4
- When switching locations only replace url segments that start with "AG-" or "G-" not any that contain them.

## 5.6.3
- Update Business Nav footer to handle the over length market name.

## 5.6.2
- Making less space between user icon and the market name to display whole name for the long named market.

## 5.6.1
- Use the loaded location variables to get counts for location switcher

## 5.6.0
- Remove the Heimdall `UserEventsService` duplicated code and its usages. This was
performed as part of deprecating the `UserEventsService` in favour of Snowplow

## 5.5.0
- Angular 10

## 5.4.2
- Move the `Get Started` to the top of the side nav

## 5.4.1
- Destroy components in business nav only if it exists
  
## 5.4.0
- Update Atlas dependency to 5.2.0
  - Impersonator and Impersonatee usernames now displayed in user dropdown menu

## 5.3.0
- Expose `showLanguageSelector` as Input for NavigationComponent

## 5.2.3
- check if more locations are needed on tab switch

## 5.2.2
- fix smb loading preventing elevated loading

## 5.2.1
- load smb locations before loading elevated locations

## 5.2.0
- Check marketName from the branding data instead of from the salesInfo as its more consistent

## 5.1.1
- Remove beta chip from location swicther

## 5.1.0

- Fix the `SalesInfoModalComponent` when the contact hasn't phone number;
- Upgrade `@vendasta/atlas` to 5.1.0;

## 5.0.0

- Bump to atlas 5.0.1; see atlas-navbar changelog for breaking changes
- Clients now MUST include `"resolveJsonModule": true` in their tsconfig's `compilerOptions`

## 4.0.1

- Add nav-inbox to the list of side nav items

## 4.0.0

- Breaking change in dependency of Atlas, one of partner ID or account ID is required now

## 3.4.7

- Change default text in location switcher search bar
- Auto focus on search bar

## 3.4.6

- Fix bug where 2 blue dots showed up
- Swapped info_circle material-icon for circle material-icon

## 3.4.5

- Update atlas sdk
- Update the format of the sales phone number

## 3.4.4

- Fix long business center names when they wrap to two lines

## 3.4.3

- Add Tooltip and Disable request assistance if in admin mode
- Add clearer error messages

## 3.4.2

- Update the height of the cobranding logo

## 3.4.1

- Update the BC sidebar
- If partner not set up their logo then don't show the `powered by` part in footer

## 3.4.0

- Keep O&O products in current tab

## 3.3.3

- Update the `Contact Us` text and icon to `ThemingFontColor`
- Update bottom space for `bottom logo`
- Set the BC sidebar footer always be light color in dark theme

## 3.3.2

- Update styling

## 3.3.1

- Add cobranding to businessAppBranding

## 3.3.0

- Use `cobrandingLogoUrl` of navigation data parameter to show cobranding logo

## 3.2.2

- Fix another place where partner shows where market should

## 3.2.1

- Fix partner showing where market should
- Fix businessAppBranding layout

## 3.2.0

- Add `businessAppBranding` flag and associated change in layout

## 3.1.0

- Add optional `defaultURL` parameter to use in cases where URLs don't have and AGID

## 3.0.0

- Update to Angular 9, atlas dep-> 3.0.0

## 2.0.0

- Update to Angular 8, atlas dep-> 2.0.0

## 1.26.4

- Fix the user required modal not showing in pinned items

## 1.26.3

- Fix the user required modal showing up inverse of when it should

## 1.26.2

- Fix user required modal navigating instead of showing

## 1.26.1

- Pass switch-location-modal labels through translate pipe

## 1.26.0

- Update product icon in switcher for SM and RM

## 1.25.0

- Pinning/Reordering no longer enabled for sessions without user ids
- Add canPin observable to PinService

## 1.24.0

- Add user required modal

## 1.23.7

- Fix Left nav icon issue

## 1.23.6

- Fix undefined in addition causing NaN

## 1.23.5

- Fix switching locations in business centre
- Fix location switcher buttons to contain href (can be opened in new tab)

## 1.23.4

- Fix salesperson access to business centre

## 1.23.3

- Increase the reliability of switching locations in Business Center.

## 1.23.2

- Fix bugs with loading more locations.

## 1.23.1

- Fix providers.

## 1.23.0

- Add heimdall tracking.

## 1.22.3

- Make brands nav items show in the correct location.

## 1.22.2

- Fix injection bug from previous fix

## 1.22.1

- Prevent infinite loop when injecting items into nav-bar

## 1.22.0

- Add french (france) and dutch to language loader

## 1.21.0

- Add ability to inject items into the Atlas topbar

## 1.20.8

- Remove Nav Headers from ML

## 1.20.7

- Fix redirection logic in VBC

## 1.20.6

- Observable logic fixes

## 1.20.5

- Business Nav side menu tweaks
  - Improve hover left-padding on switcher button.
  - Add drag handle to draggable (product) items.
  - "Contact Us" link is now blue (link coloured).

## 1.20.4

- Bug Fixes:
  - Prevent the location switcher from sometimes appearing as intractable but not being able to be clicked.
  - Ensure that the current location is loaded to prevent a state where no location was shown
  - Improve observable logic to to prevent loading from failing

## 1.20.3

- Do not import HttpClientModule

## 1.20.2

- fix pinned item hover + active styles

## 1.20.1

- Fix memory leak in translate pipe.

## 1.20.0

- Bring drag-n-drop/sorting back for pinned products
- Don't call SetPins on initialization

## 1.19.0

- Move nav items around:
  - Executive report moved below dashboard
  - Moved Store and my products into products section
  - Put all other tabs after products section

## 1.18.0

- Add admin view.

## 1.17.0

- Business nav no longer handles the blacklist of pinned items. This has been moved to the backend.

## 1.16.1

- Fix an issue with trying to `find` on a null value

## 1.16.0

- Pinned items are now represented as a blacklist.
- Pin sorting is no longer supported.

## 1.15.1

- Fix an issue with an initial state check causing a null error in Business Sidebar

## 1.15.0

- Sidebar ordering tweaks

## 1.14.1

- Fix overflow issue when admin bar is visible

## 1.14.0

- Update how products work
  - Only show Pinned Products in a new section

## 1.13.0

- Support an unauthenticated view for business nav

## 1.12.0

- Add retention config and change subscription modal

## 1.11.0

- Add a state for Admin View
- Update styling of the location switcher modal

## 1.10.0

- Update Hamburger menu
- Update styling of location switcher

## 1.9.3

- Fall back to the current url when swapping locations if no active path was found.

## 1.9.2

- Bump atlas version, fix notification badge size

## 1.9.1

- pipe hideNotifications through to atlas

## 1.9.0

- Up atlas version, includes the notification bell + settings
- Fix theming on the "contact us" footer

## 1.8.0

- Pass the brand route using group ids instead of brand name

## 1.7.11

- Make location switcher dynamically load locations on scroll

## 1.7.10

- Fix account switcher when there are no associated brands

## 1.7.9

- Fix weird inherit styles on containers

## 1.7.8

- bump atlas version

## 1.7.7

- Fix secondary and primary theming on side navigation items

## 1.7.6

- Handle unset theming properly

## 1.7.5

- Handle unset theming properly

## 1.7.4

- Rename ThemingService to AtlasThemingService for less collision

## 1.7.3

- Bump atlas dependency

## 1.7.2

- Use new sdk with Theming in it, depend on Atlas with correct versions

## 1.7.1

- Depend on new Atlas version

## 1.7.0

- Use css variables and implement custom theming

## 1.6.16

- Handle undefined brand name

## 1.6.15

- Handle brand names with slashes in side navigation

## 1.6.14

- Fix brand name with slash
- Fix path getting removed from brand navigation
- Fix Sales Person image

## 1.6.13

- Fix right border in light theme

## 1.6.12

- Fix Tabs in the location modal on mobile.

## 1.6.11

- Fix snackbar from overflowing on mobile

## 1.6.10

- Don't use cached location if the current location no longer matches

## 1.6.9

- Don't open the dialog if the keybind happened in an input/textarea element

## 1.6.8

- Only prevent default on enter if modal is open

## 1.6.7

- Fix an internationalization issue in the location switcher

## 1.6.6

- Add beta chip to brands tab in location switcher

## 1.6.5

- Default to the light theme

## 1.6.4

- Don't show the business navigation if we're viewing the page as a print view

## 1.6.3

- Close the sidenav when the overlay is clicked again
- The sidenav will now automatically be closed upon swapping routes

## 1.6.2

- Add auto scrolling when using the keyboard to navigation the location selector
- Fix issue where the menu wasn't closing in the location selector upon interacting with it

## 1.6.1

- Guard against undefined values before iterating

## 1.6.0

- Split elements module stuff out into its own module

## 1.5.0

- Add the option to always show the menu toggle

## 1.4.1

- The "Select Location" menu item for the location list items was showing a localhost link. Make this the proper link.

## 1.4.0

- Expose the known locations in a service
- Fix how we build the brand paths
- Correctly select the active brand in the location switcher
- Add Current Brand Name to the sdk

## 1.3.3

- Fix multiple brands being selected in the location selector.

## 1.3.2

- Actually make the brands navigation work

## 1.3.1

- Fix location list item brand paths. They were being doubly URL encoded

## 1.3.0

- Up required Atlas version. Check Atlas' changelog for more information

## 1.2.0

- Add the option to hide the user dropdown

## 1.1.3

- Bug fixed: Router navigation would previously not properly navigate you

## 1.1.2

- When clicking a route in the location switcher in VBC you will now be navigated there using the angular router.

## 1.1.1

- Fix subscription leaks from modals closing

## 1.1.0

- Expose a `PinnedProductItem` for externals to properly set product pins.

## 1.0.4

- Remove `product-` from pinned product ids

## 1.0.3

- Filter out empty pin lists a better way

## 1.0.2

- Filter out empty pin lists

## 1.0.1

- Support brand locations

## 1.0.0

- General Release

## 0.10.1

- fix border styles

## 0.10.0

- Provide an interface for disabling the navigation entirely.
- You can now specify [disableNavigation] on the `bc-business-navbar` to disable the navigation

## 0.9.0

- Show icons next to location to indicate which type of location it is
- You can no longer navigate to the location you are currently on
- Added a little indicator on if the location you are attempting to visit has the current product.

## 0.8.2

- Fix sidebar theming

## 0.8.1

- Fix sidebar font colours

## 0.8.0

- Add caching of the current location, remove flickering of the location selector

## 0.7.1

- Active route will now properly be detected when the current route has params or segments

## 0.7.0

- The location switcher will now navigate within a product if the location we are attempting to go to has the current product.

## 0.6.1

- Fix issue where the errored state is being cached.

## 0.6.0

- Add active states to all interactable business-nav items

## 0.5.2

- Actually translate the chip content

## 0.5.1

- Up the required atlas version

## 0.5.0

- Allow chips to be passed through via api. Display them on the sidenav if passed.

## 0.4.5

- Fix expression changed error if any angular directives are placed on the business navbar.

## 0.4.4

- Filter out empty batch requests so that we don't make useless calls

## 0.4.3

- Don't add nulls to the location batcher

## 0.4.2

- Fix mobile toggle not being placed into atlas correctly.

## 0.4.1

- Always show the current location's information in the top left.

## 0.4.0

- Update `DEFAULT` chip position when picking a new default location

## 0.3.1

- Handle errors from api service

## 0.3.0

- Add a chip component

## 0.2.1

- Fixes for IE

## 0.2.0

- Fix Routing
- Add numbers to account selector tabs
- Hide account selector tabs if the user doesn't have brands.

## 0.1.0

- Initial release
