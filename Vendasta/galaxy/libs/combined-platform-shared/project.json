{"name": "combined-platform-shared", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/combined-platform-shared/src", "prefix": "cps", "tags": ["scope:shared"], "projectType": "library", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/combined-platform-shared/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}