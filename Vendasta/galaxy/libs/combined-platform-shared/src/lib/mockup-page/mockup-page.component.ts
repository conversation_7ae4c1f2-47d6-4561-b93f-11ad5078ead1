import { CommonModule } from '@angular/common';
import { Component, ElementRef, inject, ViewChild, type OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { GalaxyPageModule } from '@vendasta/galaxy/page';

@Component({
  selector: 'cps-mockup-page',
  imports: [GalaxyPageModule, CommonModule],
  templateUrl: './mockup-page.component.html',
  styleUrl: './mockup-page.component.scss',
})
export class MockupPageComponent implements OnInit {
  router = inject(Router);

  pageTitle = '';

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  pageContent: any[] = [];

  @ViewChild('scrollTarget') scrollTarget!: ElementRef;

  clamp(num: number, min: number, max: number) {
    return Math.min(Math.max(num, min), max);
  }

  ngOnInit() {
    this.generateTitleAndContent();
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.generateTitleAndContent();
        this.scrollToTop();
      }
    });
  }

  scrollToTop() {
    this.scrollTarget.nativeElement.scrollIntoView({ behavior: 'instant', block: 'end' });
  }

  generateTitleAndContent() {
    // get the last part of the url
    let tempTitle = String(this.router.url.split('/').at(-1));
    // then place all dashes with spaces
    tempTitle = tempTitle.replace(/-/g, ' ');
    // and remove anything in brackets from the title
    tempTitle = tempTitle.replace(/\(.*\)/, '');
    // then remove anything after a question mark from the title
    tempTitle = tempTitle.replace(/\?.*/, '');
    this.pageTitle = tempTitle;

    function createChildren(num: number): { id: number }[] {
      return Array.from({ length: num }, (_, i) => ({
        id: i,
      }));
    }

    // generate a number from the page title
    const num = this.pageTitle.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);

    // generate an array of objects between 1 and 8 based on the number
    this.pageContent = Array.from({ length: this.clamp(num % 12, 3, 13) }, (_, i) => ({
      id: i,
      count: createChildren(this.clamp((num + i) % 4, 1, 4)),
      height: this.clamp((num + i) % 3, 1, 3),
    }));
  }
}
