import { Component, ElementRef, HostBinding, HostListener, Input, inject } from '@angular/core';
import { LINK_STYLE } from './sidenav-link.provider';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'cps-nav-link, [cps-nav-link]',
  imports: [CommonModule, RouterModule, MatIconModule],
  templateUrl: './global-nav-child-link.component.html',
  styleUrl: './global-nav-child-link.component.scss',
})
export class GlobalNavChildLinkComponent {
  linkStyle = inject(LINK_STYLE, { optional: true });
  nativeElement = inject(ElementRef).nativeElement;

  @Input() icon?: string;

  @Input() hasMore = false;

  @HostBinding('class') class = 'cps-nav-link';

  @HostBinding('class.desktop') get isDesktopClass() {
    return this.linkStyle !== 'mobile';
  }
  @HostBinding('class.mobile') get isMobileClass() {
    return this.linkStyle === 'mobile';
  }

  // open links on spacebar press just like buttons
  @HostListener('keydown.space', ['$event.target', '$event']) emulateClick(
    linkItem: EventTarget | null,
    event: KeyboardEvent,
  ) {
    if (linkItem instanceof HTMLAnchorElement) {
      event.preventDefault();
      event.stopPropagation();
      linkItem.click();
    }
  }
}
