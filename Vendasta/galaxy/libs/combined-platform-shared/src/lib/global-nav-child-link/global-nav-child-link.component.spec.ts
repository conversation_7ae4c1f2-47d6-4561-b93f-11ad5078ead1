import { ComponentFixture, TestBed } from '@angular/core/testing';

import { GlobalNavChildLinkComponent } from './global-nav-child-link.component';

describe('GlobalNavChildLinkComponent', () => {
  let component: GlobalNavChildLinkComponent;
  let fixture: ComponentFixture<GlobalNavChildLinkComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [GlobalNavChildLinkComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(GlobalNavChildLinkComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
