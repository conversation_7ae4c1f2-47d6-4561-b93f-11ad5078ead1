:host {
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
  border: none;
  user-select: none;
  background-color: transparent;
  -webkit-font-smoothing: antialiased;
  font-family: inherit;
  color: #444746;
  border-radius: 100px;
  padding: 2px 16px;
  border-radius: 100px;

  &:hover,
  &:focus-visible {
    background-color: rgba(0, 0, 0, 0.04);
    color: black;
  }

  &.active {
    color: black;
    background-color: rgba(0, 0, 0, 0.08);
  }
  &:hover.active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

// desktop styles
:host.desktop {
  font-size: 14px;
  height: 44px;
}

// mobile styles
:host.mobile {
  font-size: 16px;
  height: 48px;
}

.main-icon {
  color: #444746;
  margin-right: 16px;
  -webkit-font-smoothing: antialiased;
}

:host.active .nav-has-more,
:host:hover .nav-has-more,
:host:focus-visible .nav-has-more {
  opacity: 1;
}

.nav-has-more {
  margin-left: auto;
  opacity: 0.25;
  transition: opacity 0.2s cubic-bezier(0.2, 0, 0, 1);
  font-size: 20px;
  line-height: 24px;
}
