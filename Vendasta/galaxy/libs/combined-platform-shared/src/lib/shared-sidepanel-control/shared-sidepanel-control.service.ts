import { inject, Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class SharedSidepanelControlService {
  private router = inject(Router);

  public isOpen = false;
  public currentSidepanelRouteBase: string | null = null;

  public currentRouteSignal = signal('');

  open(routeToActivate: string): void {
    console.log('open ' + routeToActivate);
    this.router.navigate(['', { outlets: { sidepanel: routeToActivate } }]);
  }

  close(): void {
    console.log('close');
    this.router.navigate(['', { outlets: { sidepanel: null } }]);
  }

  toggle(routeToActivate: string): void {
    console.log('toggle ' + routeToActivate);
    const currentRoute = this.getCurrentSidepanelRoute();

    if (currentRoute === routeToActivate) {
      this.close();
    } else {
      this.open(routeToActivate);
    }
  }

  isCurrentRoute(route: string): boolean {
    return this.getCurrentSidepanelRoute() === route;
  }

  onRouterOutletActivate() {
    this.isOpen = true;
    this.currentSidepanelRouteBase = this.getCurrentSidepanelRoute();
  }

  onRouterOutletDeactivate() {
    const currentRoute = this.getCurrentSidepanelRoute();
    if (!currentRoute) this.isOpen = false;
    this.currentSidepanelRouteBase = currentRoute;
  }

  getCurrentSidepanelRoute(): string {
    const currentUrlTree = this.router.parseUrl(this.router.url);
    const currentSidepanelRoute = currentUrlTree.root.children?.['sidepanel']?.segments[0]?.path;
    return currentSidepanelRoute;
  }
}
