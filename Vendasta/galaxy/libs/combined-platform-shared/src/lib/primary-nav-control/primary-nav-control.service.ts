import { BreakpointObserver } from '@angular/cdk/layout';
import { EventEmitter, inject, Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter, skip } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PrimaryNavControlService {
  private readonly breakpointObserver = inject(BreakpointObserver);
  private readonly router = inject(Router);

  public isOpen = true;

  private localStorageKey = 'combined-app-nav-state';
  private storedPanelState = '';

  public navMode: 'over' | 'side' = 'side';
  public navIsMobile = false;
  public closeNavOnRouterNavigation = true;
  public currentUrlBase = '';

  navAutoSize = false;
  autoHideNavMaxWidth = 1023;
  autoHideNav = false;
  startingNavIsOpen = true;
  navIsClosedForThisPage = false;

  mobileMaxWidth = 768;

  navModeChangeTimeout: any = undefined;

  mobileNavShowChildren = false;

  toggleEmitter$: EventEmitter<null> = new EventEmitter<null>();
  openEmitter$: EventEmitter<null> = new EventEmitter<null>();
  closeEmitter$: EventEmitter<null> = new EventEmitter<null>();

  toggle(): void {
    this.toggleEmitter$.emit();
    this.isOpen = !this.isOpen;
  }

  open(): void {
    this.openEmitter$.emit();
    this.isOpen = true;
  }

  close(): void {
    this.closeEmitter$.emit();
    this.isOpen = false;
  }

  updateBodyClass() {
    if (this.navIsMobile) {
      document.body.classList.add('nav-is-mobile');
    } else {
      document.body.classList.remove('nav-is-mobile');
    }
  }

  updateBodyClassIfOpenOnMobile() {
    if (this.navIsMobile && this.isOpen) {
      document.body.style.overflowY = 'hidden';
    } else {
      document.body.style.overflowY = 'unset';
    }
  }

  initNavState() {
    console.log('initNavState()');
    // this.storedPanelState = localStorage.getItem(this.localStorageKey);

    const shouldHideNav =
      this.autoHideNav || this.storedPanelState === 'false' || !this.startingNavIsOpen || this.navIsClosedForThisPage;

    if (shouldHideNav) {
      // collapse the nav if mobile, or hidden by user
      this.isOpen = false;
    } else {
      this.isOpen = true;
    }

    clearTimeout(this.navModeChangeTimeout);

    if (this.autoHideNav || this.navIsClosedForThisPage) {
      // overlay the menu when on mobile, or if navIsClosedForThisPage is set
      // Set the mode to "over" after any sort of animation is done
      this.navModeChangeTimeout = setTimeout(() => {
        this.navMode = 'over';
      }, 500);
    } else {
      this.navMode = 'side';
    }
  }

  closeOnMobileNavClick() {
    if (this.navIsMobile && this.isOpen && this.closeNavOnRouterNavigation) {
      this.close();
    }
    // reset to true once we have checked it
    this.closeNavOnRouterNavigation = true;
  }

  navigationHasHappened(event: NavigationEnd) {
    this.closeOnMobileNavClick();
    this.currentUrlBase = event.urlAfterRedirects.split('/')[1];
    console.log('navigationHasHappened - curentUrlBase:', this.currentUrlBase);
  }

  constructor() {
    if (!this.isOpen) {
      this.startingNavIsOpen = false;
    }

    // Watch if viewport is tablet sized or smaller
    this.breakpointObserver.observe('(max-width: ' + this.autoHideNavMaxWidth + 'px)').subscribe((resp) => {
      this.autoHideNav = resp.matches;

      // initNavState() fires both on load and on viewport change
      // if it changes the `autoHideNavMaxWidth` px value
      this.initNavState();
    });

    // Watch if viewport is mobile sized or smaller
    this.breakpointObserver.observe('(max-width: ' + this.mobileMaxWidth + 'px)').subscribe((resp) => {
      this.navIsMobile = resp.matches;
      this.updateBodyClass();
    });

    // Listen to route changes, so we can close the menu on mobile
    this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((navEndEvent) => {
      this.navigationHasHappened(navEndEvent as NavigationEnd);
    });

    // experiment: show nav children on mobile when a parent is clicked and nav action happens
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        skip(1), // the first instance is the initial page load, which we don't care about
      )
      .subscribe(() => {
        this.mobileNavShowChildren = true;
      });
  }
}
