import { Component, ElementRef, HostListener, Input, ViewChild, type AfterViewInit, type OnInit } from '@angular/core';
import { MatRippleModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { localstorageKey, sessionstorageKey } from '../constants';

@Component({
  selector: 'app-link',
  imports: [MatIconModule, MatRippleModule],
  templateUrl: './link.component.html',
  styleUrl: './link.component.scss',
})
export class LinkComponent implements OnInit, AfterViewInit {
  @Input({ required: true }) centerId?: string;
  @Input() version = '';

  @ViewChild('linkElement', { static: true }) linkElement!: ElementRef;

  isCurrentLink = false;
  currentPath = window.location.pathname;

  @HostListener('click') onClick() {
    sessionStorage.setItem(sessionstorageKey, 'true');
  }

  ngOnInit(): void {
    const currentLocalStorageValue = localStorage.getItem(localstorageKey);
    if (currentLocalStorageValue === this.centerId) {
      this.isCurrentLink = true;
    }
  }

  ngAfterViewInit(): void {
    if (this.isCurrentLink) {
      this.linkElement.nativeElement.focus();
    }
  }
}
