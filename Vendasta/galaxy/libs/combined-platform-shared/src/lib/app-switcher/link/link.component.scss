@use 'design-tokens' as *;

:host {
  display: block;
}

a.link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 10px;
  min-height: 40px;
  background-color: $primary-background-color;
  border: 1px solid $border-color;
  border-radius: $default-border-radius;
  line-height: 1;
  margin-bottom: 8px;
  color: $primary-text-color;

  &:hover,
  &:focus {
    // background-color: color-mix(in srgb, $info-background-color 50%, transparent);
    // border: 1px solid $primary-color;
    // color: $primary-color;
    outline-width: 2px;
    outline-style: solid;
    outline-offset: -1px;
    outline-color: $primary-color;
    mat-icon {
      opacity: 1;
      color: $primary-color;
    }
  }

  mat-icon {
    opacity: 0.5;
  }
}

.content {
  @include text-preset-4;
}

.version {
  margin-left: auto;
  opacity: 0.3;
  font-size: 12px;
}

i {
  color: $primary-color;
}
