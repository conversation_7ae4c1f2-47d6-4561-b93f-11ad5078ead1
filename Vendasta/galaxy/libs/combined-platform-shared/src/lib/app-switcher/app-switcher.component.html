<div class="title">App Selector</div>

<div class="content">
  @for (app of appSwitcherList; track app.appName) {
    <div class="section-title">{{ app.appName }}:</div>

    @for (version of app.versions; track version.id; let i = $index) {
      <app-link #link [centerId]="version.id" [version]="i + 1 + ''">{{ version.name }}</app-link>
    }
  }
</div>

<div class="footer">
  Tip: You can also open this modal by presing

  @if (platformIsMac) {
    <code>Command</code>
  } @else {
    <code>Ctrl</code>
  }
  + <code>K</code>
  @if (isOnChooserPage) {
    in an app
  }
</div>
