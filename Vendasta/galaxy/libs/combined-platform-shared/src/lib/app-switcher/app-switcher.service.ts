import { Clipboard } from '@angular/cdk/clipboard';
import { inject, Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AppSwitcherComponent } from './app-switcher.component';
import { localstorageKey, sessionstorageKey } from './constants';

export function appSwitcherCheck(): string | null {
  // check url params to see if there is a string stored for 'currentCenter'
  // if so, set the center to that value and return it
  const urlParams = new URLSearchParams(window.location.search);
  const currentCenterParam = urlParams.get('currentCenter');
  if (currentCenterParam) {
    localStorage.setItem(localstorageKey, currentCenterParam);
    // if we are setting the center from the url, we don't want to show the app switcher modal
    sessionStorage.setItem(sessionstorageKey, 'true');
    return currentCenterParam;
  }

  // if not, check local storage to see if there is a stored value
  const currentLocalStorageValue = localStorage.getItem(localstorageKey);
  if (currentLocalStorageValue) {
    return currentLocalStorageValue;
  }

  // if nothing found, then return null so the app can default to the app switcher
  return null;
}

@Injectable({
  providedIn: 'root',
})
export class AppSwitcherService {
  // in order to use this service, you MUST import the MatDialogModule in your base app.module
  // otherwise you will get an error like this:
  //
  //    ERROR Error: Uncaught (in promise): NullInjectorError:
  //    R3InjectorError(ConfirmationModalModule)[OpenConfirmationModalService -> OpenConfirmationModalService
  //        -> MatDialog -> MatDialog]: NullInjectorError: No provider for MatDialog!
  //

  private readonly dialog = inject(MatDialog);
  platformIsMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  clipboard = inject(Clipboard);

  constructor() {
    window.addEventListener('keydown', (event: KeyboardEvent) => {
      if (this.platformIsMac) {
        // Mac
        if (event.metaKey && event.code == 'KeyK') {
          this.openAppSwitcherModal();
          event.preventDefault();
        }
      } else {
        // Windows
        if (event.ctrlKey && event.code == 'KeyK') {
          this.openAppSwitcherModal();
          event.preventDefault();
        }
      }
    });

    const hasSeenAppSwitcherThisSession = sessionStorage.getItem(sessionstorageKey);

    if (hasSeenAppSwitcherThisSession !== 'true') {
      this.openAppSwitcherModal();
    }
  }

  openAppSwitcherModal() {
    const ModalWidth = '540px';
    const ModalMaxWidth = 'calc( 100vw - 8px )';

    this.dialog
      .open(AppSwitcherComponent, {
        width: ModalWidth,
        maxWidth: ModalMaxWidth,
        autoFocus: false,
      })
      .afterClosed()
      .subscribe(() => {
        sessionStorage.setItem(sessionstorageKey, 'true');
      });
    // this observable autocloses after the first value is emitted
    // so no need to unsubscribe or use take(1)
  }

  generateLinkWithCenter() {
    const currentLocalStorageValue = localStorage.getItem(localstorageKey) ?? '';
    const url = new URL(window.location.href);
    url.searchParams.set('currentCenter', currentLocalStorageValue);
    this.clipboard.copy(url.toString());
  }
}
