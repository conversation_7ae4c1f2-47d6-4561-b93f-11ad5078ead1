import { Component, HostBinding, Input } from '@angular/core';
import { LinkComponent } from './link/link.component';
import { appSwitcherList } from './app-list';

@Component({
  selector: 'app-switcher',
  imports: [LinkComponent],
  templateUrl: './app-switcher.component.html',
  styleUrl: './app-switcher.component.scss',
})
export class AppSwitcherComponent {
  @Input() @HostBinding('class.is-on-chooser-page') isOnChooserPage = false;
  platformIsMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  appSwitcherList = appSwitcherList;
}
