@use 'design-tokens' as *;

:host {
  display: block;
  background-color: $card-background-color;
  overflow: auto;
}

:host.is-on-chooser-page {
  border-radius: $default-border-radius;
  border: 1px solid $border-color;
  width: 100%;
  max-width: 540px;
}

.title {
  @include text-preset-4;
  border-bottom: 1px solid $border-color;
  padding: $spacing-3 $spacing-4;
  background-color: $primary-background-color;
  color: $secondary-text-color;
}

.content {
  padding: $spacing-4;
}

.section-title {
  @include text-preset-5;
  padding-bottom: $spacing-2;
  color: $secondary-text-color;

  &:not(:first-child) {
    padding-top: $spacing-4;
  }
}

.footer {
  padding: $spacing-3 $spacing-4;
  line-height: 1;
  // border-top: 1px solid $border-color;
  background-color: $primary-background-color;
  justify-content: space-between;
  color: $tertiary-text-color;
  font-size: 13px;
}

code {
  padding: $spacing-1;
  background-color: $primary-background-color;
  border-radius: 2px;
  border: 1px solid $border-color;
  font-size: 0.9em;
}
