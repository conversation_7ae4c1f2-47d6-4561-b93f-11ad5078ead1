/* eslint-disable */
export default {
  displayName: 'business-category',
  globals: {},
  transform: {
    '^.+\\.(ts|mjs|js|html)$': [
      'jest-preset-angular',
      {
        tsconfig: '<rootDir>/tsconfig.spec.json',
        stringifyContentPathRegex: '\\.(html|svg)$',
        coverageDirectory: '../../coverage/libs/business-category',
        coverageReporters: ['html'],
      },
    ],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  setupFilesAfterEnv: ['<rootDir>/src/test-setup.ts'],
  resolver: '@nx/jest/plugins/resolver',
  preset: '../../jest.preset.js',
};
