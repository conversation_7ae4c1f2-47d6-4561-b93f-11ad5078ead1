{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["libs/business-category/tsconfig.*?.json"]}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "category", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "category", "style": "kebab-case"}], "@angular-eslint/prefer-standalone": "off"}, "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates"]}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template"], "rules": {}}]}