# Business Category library

## Usage

- `import { BusinessCategoryModule } from '@galaxy/business-category';` to module where you need to use a component;
- add `BusinessCategorySelectorModule` to Module imports;
- add the component to the template
- if the app is multi-language, add the `LANGUAGE_TOKEN` to the providers of the module (see below)

### category-select
The `category-select` component is a multi-select that works as a form control and can be used with a mat-form-field.
- `categoryType` is the category type that will be used to filter the categories, the default is `CategoryType.CATEGORY_TYPE_V_CATEGORY`
- the component is a form control, so it can be used with a mat-form-field and the values will be available in the form control using `formControl` or `formControlName`

``` html
<mat-form-field>
  <mat-label>Categories</mat-label>
  <category-select
    [categoryType]="CategoryType.CATEGORY_TYPE_V_CATEGORY"
    [formControl]="businessCategoriesCtrl"
  ></category-select>
  <mat-error *ngIf="businessCategoriesCtrl.getError('required')">At least 1 category is required</mat-error>
</mat-form-field>
```

### category-overview
The `category-overview` component is a read-only component that shows the selected categories.
- `categoryIds` is an array of category ids
- `categoryType` is the type of the categories, default is vCategory (see `CategoryType` enum)

```html
<category-overview
  [categoryIds]="vCategoryIds"
  [categoryType]="CategoryType.CATEGORY_TYPE_V_CATEGORY"
></category-overview>
```
### Multi-language support
Add the LANGUAGE_TOKEN to the providers of the module where you use the component.
If the LANGUAGE_TOKEN is not provided, the library will try to use the AtlasLanguageService to verify the current language.

```typescript
import { LANGUAGE_TOKEN } from '@galaxy/business-category';

providers: [
  {
    provide: LANGUAGE_TOKEN,
    useValue: of('en'), // Observable<string>
  },
],
```