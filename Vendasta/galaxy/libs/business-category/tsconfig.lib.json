{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "declaration": true, "types": [], "lib": ["dom", "es2018"]}, "angularCompilerOptions": {"skipTemplateCodegen": true, "strictMetadataEmit": true, "fullTemplateTypeCheck": true, "strictInjectionParameters": true, "flatModuleId": "AUTOGENERATED", "flatModuleOutFile": "AUTOGENERATED", "enableResourceInlining": true}, "include": ["**/*.ts"], "exclude": ["**/*.spec.ts", "jest.config.ts"]}