import { Pipe, PipeTransform } from '@angular/core';
import { CategoryInterface } from '@vendasta/category';

@Pipe({
  name: 'chipColor',
  standalone: false,
})
export class ChipColorPipe implements PipeTransform {
  transform(category: CategoryInterface): string {
    return this.getColor(category);
  }

  private isInvalid(category: CategoryInterface): boolean {
    return category.isHidden || !category.isActive;
  }

  private getColor(category: CategoryInterface): string {
    if (this.isInvalid(category)) {
      return 'red';
    }
    return '';
  }
}
