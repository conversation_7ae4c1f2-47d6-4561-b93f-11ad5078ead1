import { Pipe, PipeTransform } from '@angular/core';
import { CategoryInterface } from '@vendasta/category';

@Pipe({
  name: 'excludeCategories',
  standalone: false,
})
export class ExcludeCategoriesPipe implements PipeTransform {
  transform(value: CategoryInterface[], excludeIds?: string[]): CategoryInterface[] {
    if (excludeIds?.length > 0) {
      return value.filter((category) => !excludeIds.includes(category.externalId));
    }
    return value;
  }
}
