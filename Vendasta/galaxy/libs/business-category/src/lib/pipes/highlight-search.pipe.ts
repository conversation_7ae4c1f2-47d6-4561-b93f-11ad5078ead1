import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

@Pipe({
  name: 'highlight',
  standalone: false,
})
export class HighlightSearchPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  transform(value: string, searchTerm?: string): any {
    if (!searchTerm) {
      return value;
    }
    const options = searchTerm.split(' ').filter(Boolean).join('|');
    const re = new RegExp(`(${options})`, 'gi');
    const replacedValue = value.replace(re, '<strong>$1</strong>');
    return this.sanitizer.bypassSecurityTrustHtml(replacedValue);
  }
}
