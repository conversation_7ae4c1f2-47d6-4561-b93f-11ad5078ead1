import { CategoryOverviewComponent } from './category-overview.component';
import { of } from 'rxjs';
import { CategoryType, CategoryRequest, CategoriesApiService } from '@vendasta/category';
import { take } from 'rxjs/operators';

const VCAT_IDS = ['aadhar_center'];
const VCAT_RESPONSE: string[] = ['vcat_response'];
const TAX_IDS = ['publicservicesgovt'];
const TAX_RESPONSE: string[] = ['tax_response'];

const buildCategoryResponse = (request: CategoryRequest[]) => {
  if (request[0]?.categoryType === CategoryType.CATEGORY_TYPE_V_CATEGORY) {
    return { category: VCAT_RESPONSE };
  } else {
    return { category: TAX_RESPONSE };
  }
};

describe('CategoryOverviewComponent', () => {
  let fixture: CategoryOverviewComponent;
  let categoryServiceMock;

  beforeEach(() => {
    categoryServiceMock = {
      getCategoryByExternalIDsAndType: jest.fn((request: { categories: CategoryRequest[] }) =>
        of(buildCategoryResponse(request?.categories)),
      ),
    } as unknown as CategoriesApiService;

    fixture = new CategoryOverviewComponent(categoryServiceMock, null, null);
    fixture.ngOnInit();
  });

  describe('Setup component', () => {
    it('should create the app', () => {
      expect(fixture).toBeTruthy();
    });
  });

  describe('Get categories', () => {
    it('should get vCategories when the categoryType is CATEGORY_TYPE_V_CATEGORY', async () => {
      fixture.categoryType = CategoryType.CATEGORY_TYPE_V_CATEGORY;
      fixture.categoryIds = VCAT_IDS;
      await expect(fixture.categories$.pipe(take(1)).toPromise()).resolves.toEqual(VCAT_RESPONSE);
    });

    it('should get taxonomies when the categoryType is CATEGORY_TYPE_V_TAX', async () => {
      fixture.categoryType = CategoryType.CATEGORY_TYPE_V_TAX;
      fixture.categoryIds = TAX_IDS;
      await expect(fixture.categories$.pipe(take(1)).toPromise()).resolves.toEqual(TAX_RESPONSE);
    });
  });
});
