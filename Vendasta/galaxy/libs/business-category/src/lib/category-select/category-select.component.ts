import { BooleanInput, NumberInput, coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';
import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  Inject,
  Input,
  OnDestroy,
  Optional,
  Output,
  Self,
  ViewChild,
} from '@angular/core';
import { ControlValueAccessor, FormControl, NgControl } from '@angular/forms';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatFormFieldControl } from '@angular/material/form-field';
import { AtlasLanguageService } from '@galaxy/atlas/core';
import { Category, CategoryInterface, CategoryRequest, CategoryType } from '@vendasta/category';
import { SubscriptionList } from '@vendasta/rx-utils';
import { BehaviorSubject, EMPTY, Observable, Subject, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, switchMap, tap } from 'rxjs/operators';
import { LANGUAGE_TOKEN } from '../constants';
import { SearchResult } from '../interfaces';
import { CategorySelectService } from './category-select.service';

interface SetCategoriesRequest {
  type: CategoryType;
  externalIds: string[];
}

type OnChangeFn = (value: string[]) => void;
type OnTouchedFn = () => void;

@Component({
  selector: 'category-select',
  templateUrl: './category-select.component.html',
  styleUrls: ['./category-select.component.scss'],
  providers: [CategorySelectService, { provide: MatFormFieldControl, useExisting: CategorySelectComponent }],
  standalone: false,
})
export class CategorySelectComponent
  implements ControlValueAccessor, MatFormFieldControl<string[]>, OnDestroy, AfterViewInit
{
  private static nextId = 0;
  //#region Input/Output parameters
  @Input()
  get maxCategories(): number {
    return this._maxCategories;
  }
  set maxCategories(value: NumberInput) {
    this.setMaxCategories(coerceNumberProperty(value, 0));
  }
  private _maxCategories = 0;

  @Input()
  get categoryType(): CategoryType {
    return this.categorySelectService.categoryType;
  }
  set categoryType(value: CategoryType) {
    this.setCategoryType(value);
  }

  @Input()
  get disabled(): boolean {
    return this._disabled;
  }
  set disabled(value: BooleanInput) {
    this._disabled = coerceBooleanProperty(value);
    this.stateChanges$$.next();
    this._disabled ? this.addCategoryControl.disable() : this.addCategoryControl.enable();
  }
  private _disabled = false;

  @Input()
  get required(): boolean {
    return this._required;
  }
  set required(value: BooleanInput) {
    this._required = coerceBooleanProperty(value);
    this.stateChanges$$.next();
  }
  private _required = false;

  @Input()
  get value(): string[] {
    return this._value;
  }
  set value(externalIds: string[]) {
    this._value = externalIds;
    this.stateChanges$$.next();
    this.refreshCategoryChipList();
  }
  private _value: string[] = [];

  @Input()
  get placeholder(): string {
    return this._placeholder;
  }
  set placeholder(plh: string) {
    this._placeholder = plh;
    this.stateChanges$$.next();
  }
  private _placeholder = 'Add category...';

  // eslint-disable-next-line @angular-eslint/no-output-on-prefix
  @Output() readonly onCategoriesChange = new EventEmitter<CategoryInterface[]>();
  //#endregion

  //#region Template bindings
  @ViewChild('addCategoryCtrl') private readonly addCategoryCtrl: ElementRef<HTMLInputElement>;
  @ViewChild('categoryOptionsContainer') private readonly categoryOptionsContainer: ElementRef;

  @HostBinding('[id]') id = `category-select-${CategorySelectComponent.nextId++}`;
  @HostBinding('[class.floating]') get shouldLabelFloat(): boolean {
    return this.focused || !this.empty;
  }
  //#endregion

  private readonly subscriptions = SubscriptionList.new();
  private readonly stateChanges$$ = new Subject<void>();
  private _categories: CategoryInterface[] = [];

  onChange: OnChangeFn;
  onTouched: OnTouchedFn;
  focused = false;

  readonly addCategoryControl = new FormControl<string>({ value: '', disabled: true });
  readonly refreshCategories$$ = new Subject<SetCategoriesRequest>();
  readonly searchResult$: Observable<SearchResult> = this.categorySelectService.categories$;
  readonly loading$$ = new BehaviorSubject<boolean>(false);
  readonly stateChanges = this.stateChanges$$;
  readonly controlType = 'category-select';

  constructor(
    @Optional() @Self() public ngControl: NgControl,
    private readonly categorySelectService: CategorySelectService,
    @Optional() @Inject(LANGUAGE_TOKEN) private readonly languageToken$: Observable<string>,
    @Optional() private readonly atlasLanguageService: AtlasLanguageService,
  ) {
    if (this.ngControl) {
      // required by MatFormFieldControl
      this.ngControl.valueAccessor = this;
    }
    this.setupSubscriptions();
  }

  //#region Angular lifecycle event
  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  ngAfterViewInit(): void {
    this.addCategoryCtrl.nativeElement.addEventListener('focusin', () => this.onFocusIn());
    this.addCategoryCtrl.nativeElement.addEventListener('focusout', () => this.onFocusOut());
  }
  //#endregion

  get empty(): boolean {
    return !(this._value?.length > 0);
  }

  get errorState(): boolean {
    return !this.isNull(this.ngControl.errors) && !!this.ngControl.touched;
  }

  get categories(): CategoryInterface[] {
    return this._categories;
  }
  set categories(value: CategoryInterface[]) {
    this._categories = value;
    this.onCategoriesChange.emit(value);
  }

  get addDisabled(): boolean {
    return (
      this.disabled ||
      (this._maxCategories > 0 && this.value?.length >= this._maxCategories) ||
      this.isNull(this.categorySelectService.categoryType)
    );
  }

  private setMaxCategories(value: number): void {
    if (value !== this._maxCategories) {
      this._maxCategories = value;
    }
  }

  private setCategoryType(value: CategoryType): void {
    if (value !== this.categorySelectService.categoryType) {
      this.categorySelectService.categoryType = value;
      this.refreshCategoryChipList();
    }
  }

  private isNull(value?: unknown): boolean {
    return value === null || value === undefined;
  }

  private setupSubscriptions(): void {
    this.subscriptions.add(this.categorySelectService.afterInitialFetch, () =>
      this.categoryOptionsContainer?.nativeElement?.scrollIntoView?.(),
    );

    const language$ = this.languageToken$ ?? this.atlasLanguageService?.language$ ?? EMPTY;
    this.subscriptions.add(language$, (value) => this.categorySelectService.language$$.next(value));

    const refreshCategories$ = this.refreshCategories$$.pipe(
      debounceTime(100),
      filter((r) => this.validCategoryRequest(r)),
      distinctUntilChanged((prev, curr: SetCategoriesRequest) => this.sameCategoryRequest(prev, curr)),
      tap(() => this.loading$$.next(true)),
      map((r) => r.externalIds?.map((externalId) => <CategoryRequest>{ externalId, categoryType: r.type }) ?? []),
      switchMap((r) => (r.length > 0 ? this.categorySelectService.getCategoryByExternalIDsAndType(r) : of([]))),
      map((categories) => this.validateCategories(categories)),
      tap(() => this.loading$$.next(false)),
    );
    this.subscriptions.add(refreshCategories$, (categories) => (this.categories = categories));
  }

  private validateCategories(categories: CategoryInterface[]): CategoryInterface[] {
    // accepts only the specified quantity of categories
    if (this._maxCategories > 0 && categories?.length > this._maxCategories) {
      categories = categories.slice(0, this._maxCategories);
      this.value = categories.map((c) => c.externalId);
      this.onChange?.(this.value);
      this.markAsTouched();
    }
    return categories;
  }

  private refreshCategoryChipList(): void {
    this.refreshCategories$$.next({
      type: this.categorySelectService.categoryType,
      externalIds: this._value,
    });
  }

  private validCategoryRequest(req: SetCategoriesRequest): boolean {
    return !this.isNull(req.type) && req.externalIds?.join() !== this.categories.map((c) => c.externalId).join();
  }

  private sameCategoryRequest(prev: SetCategoriesRequest, curr: SetCategoriesRequest): boolean {
    return prev.externalIds?.join() === curr.externalIds?.join() && prev.type === curr.type;
  }

  addCategory(event: MatAutocompleteSelectedEvent): void {
    this.markAsTouched();
    this.categories = [...this.categories, event.option.value];
    this.value = this.categories.map((c) => c.externalId);
    this.onChange?.(this.value);
    event.option.deselect();
    this.addCategoryCtrl.nativeElement.value = '';
    this.addCategoryCtrl.nativeElement.focus();
    this.categorySelectService.fetch('');
  }

  removeCategory(category: Category): void {
    this.markAsTouched();
    this.categories = this.categories.filter((c) => c.externalId !== category.externalId);
    this.value = this.categories.map((c) => c.externalId);
    this.onChange?.(this.value);
  }

  updateCategorySearchTerm(value: string): void {
    this.categorySelectService.fetch(value);
  }

  fetchMoreCategories(): void {
    this.categorySelectService.fetchNext();
  }

  setDescribedByIds(ids: string[]): void {
    this.addCategoryCtrl?.nativeElement?.setAttribute('aria-describedby', ids.join(' '));
  }

  onContainerClick(event?: MouseEvent): void {
    if (event) {
      // no-op
    }
    this.addCategoryCtrl?.nativeElement?.focus();
  }

  onFocusIn(): void {
    this.focused = true;
    this.stateChanges$$.next();
  }

  onFocusOut(): void {
    this.focused = false;
    this.onTouched?.();
    this.stateChanges$$.next();
  }

  //#region ControlValueAccessor implementation
  writeValue(externalIds: string[]) {
    this.value = externalIds;
  }

  registerOnChange(onChange: OnChangeFn) {
    this.onChange = onChange;
  }

  registerOnTouched(onTouched: OnTouchedFn) {
    this.onTouched = onTouched;
  }

  markAsTouched() {
    if (!this.ngControl.touched) {
      this.ngControl.control?.markAsTouched();
    }
    this.onTouched?.();
  }

  setDisabledState(disabled: boolean) {
    this.disabled = disabled;
  }
  //#endregion
}
