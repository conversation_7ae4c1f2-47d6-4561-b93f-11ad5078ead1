<!-- Loading state -->
<glxy-loading-spinner *ngIf="loading$$ | async; else component" [size]="'small'" [inline]="true"></glxy-loading-spinner>
<!-- Control -->
<ng-template #component>
  <mat-chip-grid #chipList>
    <mat-chip-row
      *ngFor="let category of categories"
      (removed)="removeCategory(category)"
      [disabled]="disabled"
      [class]="category | chipColor"
    >
      {{ category.fullName }}
      <mat-icon *ngIf="!disabled" [class]="category | chipColor" matChipRemove>cancel</mat-icon>
    </mat-chip-row>
    <input
      matInput
      #addCategoryCtrl
      class="mat-chip-input"
      [hidden]="addDisabled"
      [placeholder]="placeholder"
      [formControl]="addCategoryControl"
      [matAutocomplete]="categoryAutocomplete"
      [matChipInputFor]="chipList"
      (input)="updateCategorySearchTerm($event.target.value)"
      [required]="required"
    />
  </mat-chip-grid>
  <mat-autocomplete
    autoActiveFirstOption
    #categoryAutocomplete="matAutocomplete"
    (optionSelected)="addCategory($event)"
  >
    <ng-container *ngIf="searchResult$ | async as searchResult">
      <section #categoryOptionsContainer *ngIf="searchResult.categories | excludeCategories: value as filteredResult">
        <ng-container *ngIf="filteredResult.length > 0; else noResults">
          <ng-container *ngFor="let option of filteredResult; last as isLast">
            <mat-option [value]="option">
              <glxy-infinite-scroll-trigger
                *ngIf="isLast"
                (isVisible)="fetchMoreCategories()"
              ></glxy-infinite-scroll-trigger>
              <span [innerHTML]="option.fullName | highlight: searchResult.searchTerm"></span>
            </mat-option>
          </ng-container>
        </ng-container>
        <ng-template #noResults>
          <mat-option disabled>No results found</mat-option>
        </ng-template>
      </section>
    </ng-container>
  </mat-autocomplete>
</ng-template>
