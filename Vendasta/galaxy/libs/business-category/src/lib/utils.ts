import { CategoryInterface, CategoryType, Locale } from '@vendasta/category';

/** External IDs included in Health category which are not covered by the root name */
const healthCategoryExceptions = ['addiction_treatment_center'];
const healthCategoryRootName = 'health';

/**
 * Health category determined by:
 * vTax externalId starting with `health`
 * vCategory under the `Health & Medical` root category
 * vCategory externalId equal to `addiction_treatment_center` (mapped from vTax health:physicians:addiction_specialist)
 */
export function isHealthCategory(category: CategoryInterface): boolean {
  const type = category?.type ?? CategoryType.CATEGORY_TYPE_V_CATEGORY;
  const englishTranslation = category?.translations?.find((t) => t.locale === Locale.LOCALE_ENGLISH);
  const fullName = (englishTranslation?.fullName || category?.fullName)?.toLowerCase();
  const externalId = category?.externalId?.toLocaleLowerCase();
  return (
    (type === CategoryType.CATEGORY_TYPE_V_TAX && externalId.startsWith(healthCategoryRootName)) ||
    (type === CategoryType.CATEGORY_TYPE_V_CATEGORY && fullName.startsWith(healthCategoryRootName)) ||
    healthCategoryExceptions.includes[externalId]
  );
}

export function isInvalidForSelection(category: CategoryInterface): boolean {
  // when category is not active or hidden, it is invalid for selection
  return !category.isActive || category.isHidden;
}
