CHANGE LOG
==========

### 4.0.0
- Upgrade to Angular 20

### 3.1.1
- Add `standalone: false` to all standalone components to prepare for Angular 19 update

### 3.1.0
- Uses `CategoryType.CATEGORY_TYPE_V_CATEGORY` as default value for `categoryType` in `CategorySelectComponent`

### 3.0.0
- Remove `taxIds`, `vCatIds` and `vCategoryFeatureFlag` from `CategoryOverviewComponent`

### 2.4.0
- Add `categoryType` and `categoryIds` to `CategoryOverviewComponent` to allow the component to show categories of a specific type

### 2.3.0
- If `LANGUAGE_TOKEN` is not provided, the library will try to use the `AtlasLanguageService` to verify the current language

### 2.2.0
- Add `LANGUAGE_TOKEN` Injection Token to the library to provide translated names for the categories
- Add the locale to `CategoryOverviewComponent`

### 2.1.0
- Add spinner to `CategorySelectComponent` while loading the inputted categories

### 2.0.0
- Remove the `CategorySelectorComponent` component

### 1.9.1
- Fix `CategorySelectComponent` to limit the number of infered categories as parametrized

### 1.9.0
- Remove the logic to fallback to vTax names if no vCategory is available to show in `CategoryOverviewComponent`

### 1.8.0
- Search only visible categories in the category selector

### 1.7.0
- Colorize the category selector chips depending on the category visibility

### 1.6.0
- Add `CategorySelectComponent` to the library
- Add `ExcludeCategoriesPipe` to the library (internal use only)
- Deprecate `CategorySelectorComponent` component
- Add more context to README file

### 1.5.1
- Remove `business_category_library` feature flag constant

### 1.5.0
- Export method `isHealthCategory` to detect health categories

### 1.4.4
- Properly handles the `dirty` and `touched` properties on `CategorySelectorComponent`

### 1.4.3
- Fix `CategorySelectorComponent` to emit an updated value after the initial load and mark the control as pristine

### 1.4.2
- Fix comparer function for distinctUntilChanged() on `CategorySelectorComponent`

### 1.4.1
- Fix import source of `CategoryRequest`

### 1.4.0
- Add `placeholder` property to `CategoryOverviewComponent`
- Refactor `CategoryOverviewComponent` to avoid multiple equal requests
- Fallback to taxonomy names if no vCategory is available to show in `CategoryOverviewComponent`

### 1.3.1
- Add tests for `CategoryOverviewComponent` and `CategorySelectorComponent`

### 1.3.0
- Add categoryUiErrorMatcher

### 1.2.0
- Add constants for feature flags
- Change library input to the list of ids
- Change library output to the list of categories with vtax or vcat type

### 1.1.0
- Create the `CategoryOverviewComponent` and exported it.

### 1.0.0
- Create the `CategorySelectorComponent` and exported it.
