{"name": "bit-chart", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/bit-chart/src", "prefix": "bit-chart", "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/bit-chart"], "options": {"jestConfig": "libs/bit-chart/jest.config.ts"}}}, "tags": ["scope:shared"]}