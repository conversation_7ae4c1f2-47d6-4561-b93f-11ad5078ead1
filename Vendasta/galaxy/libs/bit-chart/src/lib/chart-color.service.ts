import { Injectable } from '@angular/core';
import { GalaxyGradientFill, GalaxyChartColor } from './interface';

@Injectable()
export class ChartColorService {
  /**
   * Parses the GalaxyChartColor into either the string representation of the color or a
   * gradient fill compatable with Canvas
   * @param ctx - Canvas 2D context
   * @param value - GalaxyChartColor config
   */
  public parseColorInformation(ctx: CanvasRenderingContext2D, value: GalaxyChartColor | GalaxyChartColor[]) {
    // If the value is a string, that means they are just passing in a Hex, RGB, etc.
    if (typeof value === 'string') {
      return value;
    }

    // If it is an array of colors, change the check a bit
    if (Array.isArray(value)) {
      return value.map((val) => {
        return this.parseColorInformation(ctx, val);
      });
    }

    const gradientFill = ctx.createLinearGradient(ctx.canvas.width / 2, 0, ctx.canvas.width / 2, ctx.canvas.height);
    (value as GalaxyGradientFill).colors.forEach((color, index) => {
      gradientFill.addColorStop(index / value.colors.length, color);
    });
    return gradientFill;
  }
}
