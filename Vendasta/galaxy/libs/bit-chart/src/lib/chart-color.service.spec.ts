import { TestBed } from '@angular/core/testing';
import 'jest-canvas-mock';
import { ChartColorService } from './chart-color.service';
import { GalaxyChartColor } from './interface';

describe.skip('ChartColorService', () => {
  let ctx;
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [ChartColorService],
    });
    const canvas = document.createElement('canvas');
    ctx = canvas.getContext('2d');
  });

  it('should be created', () => {
    const service: ChartColorService = TestBed.inject(ChartColorService);
    expect(service).toBeTruthy();
  });

  it('should return string without context being accessed', () => {
    const service: ChartColorService = TestBed.inject(ChartColorService);
    const color = '#123123';
    const createSpy = jest.spyOn(ctx, 'createLinearGradient');
    const parseSpy = jest.spyOn(service, 'parseColorInformation');
    const response = service.parseColorInformation(ctx, color);
    expect(response).toEqual(color);
    expect(createSpy).toHaveBeenCalledTimes(0);
    expect(parseSpy).toHaveBeenCalledTimes(1);
  });

  it('should recursively call itself if it finds an array', () => {
    const service: ChartColorService = TestBed.inject(ChartColorService);
    const parseSpy = jest.spyOn(service, 'parseColorInformation');
    const payload: GalaxyChartColor[] = ['test'];

    const response = service.parseColorInformation(ctx, payload);

    expect(response).toEqual(payload);
    expect(parseSpy).toHaveBeenCalledTimes(2);
  });

  it('should create a gradient linear gradient if GalaxyGradientFill is found', () => {
    const service: ChartColorService = TestBed.inject(ChartColorService);
    const payload: GalaxyChartColor = {
      colors: ['red', 'green'],
    };

    const response = service.parseColorInformation(ctx, payload);

    expect(response).toBeInstanceOf(CanvasGradient);
  });

  it('should create a multiple gradients', () => {
    const service: ChartColorService = TestBed.inject(ChartColorService);
    const payload: GalaxyChartColor[] = [
      {
        colors: ['red', 'green'],
      },
      {
        colors: ['orange', 'purple'],
      },
    ];

    const response = service.parseColorInformation(ctx, payload);

    expect(response[0]).toBeInstanceOf(CanvasGradient);
    expect(response[1]).toBeInstanceOf(CanvasGradient);
  });

  it('should handle strings and GalaxyGradientFills in the same array', () => {
    const service: ChartColorService = TestBed.inject(ChartColorService);
    const payload: GalaxyChartColor[] = [
      {
        colors: ['red', 'green'],
      },
      'yellow',
      {
        colors: ['orange', 'purple'],
      },
      'brown',
    ];

    const response = service.parseColorInformation(ctx, payload);

    expect(response[0]).toBeInstanceOf(CanvasGradient);
    expect(response[1]).toEqual(payload[1]);
    expect(response[2]).toBeInstanceOf(CanvasGradient);
    expect(response[3]).toEqual(payload[3]);
  });
});
