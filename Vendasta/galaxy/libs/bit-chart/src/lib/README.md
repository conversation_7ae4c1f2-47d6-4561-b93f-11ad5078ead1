# Galaxy Charting Library

The galaxy charting library is powered by Chart.js. This is more of a wrapper for Chart.JS to be able to be used
throughout the Vendasta platform. For more information: https://www.chartjs.org/docs/latest/

You can also view the interface file, as some of the options were removed the from interface -> chart.js interface to help
simplify the details.

## HTML Legends

Chart.js provides for HTML legends by specifying the legendCallback config property and calling a chart's generateLegend() method.  Details here: https://www.chartjs.org/docs/latest/configuration/legend.html#html-legends
  
If require an HTML legend for your Galaxy Chart you can pass in `[htmlLegend]="true" `and Galaxy Charts will call `generateLegend()` for you after the chart is setup and when there are changes made to the chart data/config.  It will also emit the string produced by `generateLegend()` as an Output named `htmlLegendContent` which you can provide a callback for and style and render wherever you like.

For example, in your template:
```
<glxy-chart [config]="config" [data]="data" [htmlLegend]="true" (htmlLegendContent)="htmlLegendCallback($event)" style="display: block;"></glxy-chart>
<div #legendContentHolder></div>
```

And then in your component:

```
export class MyComponent {
    @ViewChild('legendContentHolder', {static: false})
    legendContentHolder: ElementRef;

    ...
    stuff
    ...

    htmlLegendCallback($event: string) {
        if (this.legendContentHolder) {
          (this.legendContentHolder.nativeElement as HTMLDivElement).innerHTML = $event;
        }
    }
}
```

You also need to provide a legendCallback in your config.  For example, this one pulls the background color and label out of the charts datasets, and you can style them however you like or add additional values, icons, whatever:
```
    config: GalaxyChartConfig = {
      options: {
            ...
        legendCallback: (chart: Chart) => {
          const text = [];
          text.push('<div class="legend-container">');
          for (const dataset of chart.data.datasets) {
            text.push('<div class="legend-item">');
            text.push('<div class="legend-item-color-box" style="background-color:' + dataset.backgroundColor + '"></div>');
            text.push('<div class="legend-item-label">' + dataset.label + '. Custom stuff here!</div>');
            text.push('</div>');
          }
          text.push('</div>');
          return text.join('');
        },
            ...
      }
    }
```

## Chart Support

Currently the charting library is only supporting Line and Bar charts with more to come. If you require a different chart type of be available, please reach out to #wisakejak on Slack.
