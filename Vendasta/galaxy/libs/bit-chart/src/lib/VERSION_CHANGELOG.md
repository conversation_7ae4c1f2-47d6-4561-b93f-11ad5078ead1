2.0.1
- Add `standalone: false` to all standalone components to prepare for Angular 19 update

2.0.0
- BREAKING CHANGE: HTML Legend Functionality has been changed.  Component now emits the generated HTML legend as an Output which the user can place and style independently of the Chart. 

1.2.0
- Expose Colors and GalaxyChartData

1.1.2
- Moving HTML legend population to onChanges to regenerate legend on data updates

1.1.1
- Move legendContainer population to ngAfterViewInit so @ViewChildren are available

1.1.0
- Expose legendCallback,hover mode, and maintainAspectRatio in options

1.0.4
- Update readme with more information
- Fix: Chart responds to changes on its data or config

1.0.3
1.0.2
1.0.1
- Release testing

1.0.0
- Inital release of the Galaxy Charting Library
