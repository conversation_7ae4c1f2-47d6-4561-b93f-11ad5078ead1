{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "target": "es2015", "declaration": true, "declarationMap": true, "inlineSources": true, "types": [], "lib": ["dom", "es2018"]}, "angularCompilerOptions": {"skipTemplateCodegen": true, "strictMetadataEmit": true, "enableResourceInlining": true}, "exclude": ["src/test-setup.ts", "**/*.spec.ts", "jest.config.ts"], "include": ["**/*.ts"]}