{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["libs/bit-chart/tsconfig.*?.json"]}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": ["glxy", "bitChart"], "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": ["glxy", "bit-chart"], "style": "kebab-case"}], "@angular-eslint/prefer-standalone": "off"}, "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates"]}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template"], "rules": {}}]}