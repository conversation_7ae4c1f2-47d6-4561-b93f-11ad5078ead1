package seoworkflow

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	dataforseo "github.com/vendasta/listing-products/internal/dataforseo"
)

func TestSEOWorkflowService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()

	type testCase struct {
		name           string
		method         string
		args           interface{}
		expectedResult interface{}
		expectedError  error
		mockSetup      func()
	}

	date := time.Now()
	testCases := []*testCase{
		{
			name:   "StartKeywordInfoWorkflows - success",
			method: "StartKeywordInfoWorkflows",
			args: struct {
				date       time.Time
				businesses []*KeywordSearchParams
			}{
				date: date,
				businesses: []*KeywordSearchParams{
					{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
				},
			},
			expectedResult: []string{"workflow1"},
			expectedError:  nil,
			mockSetup: func() {
				mockService.EXPECT().
					StartKeywordInfoWorkflows(ctx, date, []*KeywordSearchParams{
						{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
					}).
					Return([]string{"workflow1"}, nil).Times(1)
			},
		},
		{
			name:   "StartSERPWorkflows - missing tags error",
			method: "StartSERPWorkflows",
			args: struct {
				force                 bool
				ignoreDataLakeResults bool
				businesses            []*KeywordSearchParams
				date                  time.Time
				tags                  []string
			}{
				force:                 true,
				ignoreDataLakeResults: false,
				businesses: []*KeywordSearchParams{
					{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
				},
				date: date,
				tags: nil,
			},
			expectedResult: []string{},
			expectedError:  nil,
			mockSetup: func() {

				mockService.EXPECT().
					StartSERPWorkflows(ctx, true, false, []*KeywordSearchParams{
						{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
					}, date).
					Return([]string{}, nil).Times(1)
			},
		},
		// Existing test cases...
		{
			name:   "StartSuggestedKeywordsWorkflows - success",
			method: "StartSuggestedKeywordsWorkflows",
			args: struct {
				date       time.Time
				businesses []*KeywordSearchParams
			}{
				date: date,
				businesses: []*KeywordSearchParams{
					{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
				},
			},
			expectedResult: []string{"workflow1"},
			expectedError:  nil,
			mockSetup: func() {
				mockService.EXPECT().
					StartSuggestedKeywordsWorkflows(ctx, date, []*KeywordSearchParams{
						{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
					}).
					Return([]string{"workflow1"}, nil).Times(1)
			},
		},
		{
			name:   "StartSuggestedKeywordsWorkflows - error",
			method: "StartSuggestedKeywordsWorkflows",
			args: struct {
				date       time.Time
				businesses []*KeywordSearchParams
			}{
				date: date,
				businesses: []*KeywordSearchParams{
					{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
				},
			},
			expectedResult: []string{},
			expectedError:  errors.New("workflow failed"),
			mockSetup: func() {
				mockService.EXPECT().
					StartSuggestedKeywordsWorkflows(ctx, date, []*KeywordSearchParams{
						{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
					}).
					Return([]string{}, errors.New("workflow failed")).Times(1)
			},
		},
		// New test cases for GetDataForSEOLocationForBusiness
		{
			name:   "GetDataForSEOLocationForBusiness - success",
			method: "GetDataForSEOLocationForBusiness",
			args: struct {
				businessID string
			}{
				businessID: "AG-H7TQNDHQ5C",
			},
			expectedResult: &dataforseo.Location{
				Code:           0,
				Name:           "",
				CodeParent:     0,
				CountryISOCode: "",
				Type:           "",
			},
			expectedError: nil,
			mockSetup: func() {
				mockService.EXPECT().
					GetDataForSEOLocationForBusiness(ctx, "AG-H7TQNDHQ5C").
					Return(&dataforseo.Location{
						Code:           0,
						Name:           "",
						CodeParent:     0,
						CountryISOCode: "",
						Type:           "",
					}, nil).
					Times(1)
			},
		},
		{
			name:   "GetDataForSEOLocationForBusiness - error",
			method: "GetDataForSEOLocationForBusiness",
			args: struct {
				businessID string
			}{
				businessID: "AG-H7TQNDHQ5C",
			},
			expectedResult: &dataforseo.Location{
				Code:           0,
				Name:           "",
				CodeParent:     0,
				CountryISOCode: "",
				Type:           "",
			},
			expectedError: errors.New("business not found"),
			mockSetup: func() {
				mockService.EXPECT().
					GetDataForSEOLocationForBusiness(ctx, "AG-H7TQNDHQ5C").
					Return(&dataforseo.Location{
						Code:           0,
						Name:           "",
						CodeParent:     0,
						CountryISOCode: "",
						Type:           "",
					}, errors.New("business not found")).
					Times(1)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mocks
			tc.mockSetup()

			// Execute the appropriate method
			var result interface{}
			var err error

			switch tc.method {
			case "StartCategoryWorkflow":
				args := tc.args.(struct {
					date       time.Time
					businesses []string
				})
				result, err = mockService.StartCategoryWorkflow(ctx, args.date, args.businesses)

			case "StartKeywordInfoWorkflows":
				args := tc.args.(struct {
					date       time.Time
					businesses []*KeywordSearchParams
				})
				result, err = mockService.StartKeywordInfoWorkflows(ctx, args.date, args.businesses)

			case "StartSERPWorkflows":
				args := tc.args.(struct {
					force                 bool
					ignoreDataLakeResults bool
					businesses            []*KeywordSearchParams
					date                  time.Time
					tags                  []string
				})
				result, err = mockService.StartSERPWorkflows(ctx, args.force, args.ignoreDataLakeResults, args.businesses, args.date, args.tags...)

			case "StartSuggestedKeywordsWorkflows":
				args := tc.args.(struct {
					date       time.Time
					businesses []*KeywordSearchParams
				})
				result, err = mockService.StartSuggestedKeywordsWorkflows(ctx, args.date, args.businesses)

			case "GetDataForSEOLocationForBusiness":
				args := tc.args.(struct {
					businessID string
				})
				result, err = mockService.GetDataForSEOLocationForBusiness(ctx, args.businessID)

			default:
				t.Fatalf("Unsupported method: %s", tc.method)
			}

			// Validate the result
			assert.Equal(t, tc.expectedError, err)
			assert.Equal(t, tc.expectedResult, result)
		})
	}
}
func TestStartSERPWorkflows(t *testing.T) {
	// Create a new gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a mock of SEOWorkflowServiceInterface
	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	// Set up test data
	ctx := context.Background()
	force := true
	ignoreDataLakeResults := false
	businesses := []*KeywordSearchParams{
		{BusinessID: "biz1", Keywords: []string{"seo", "marketing"}},
		{BusinessID: "biz2", Keywords: []string{"advertising", "digital"}},
	}
	date := time.Now()
	expectedTags := []string{"tag1", "tag2"}
	expectedResult := []string{"workflow1", "workflow2"}

	// Define the expected call to StartSERPWorkflows
	mockService.EXPECT().
		StartSERPWorkflows(ctx, force, ignoreDataLakeResults, businesses, date, expectedTags).
		Return(expectedResult, nil)

	// Call the method under test
	actualResult, err := mockService.StartSERPWorkflows(ctx, force, ignoreDataLakeResults, businesses, date, expectedTags...)

	// Assert the result
	assert.NoError(t, err)
	assert.Equal(t, expectedResult, actualResult)
}
func TestStartCategoryWorkflow(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()
	date := time.Now()
	businesses := []string{"AG-H7TQNDHQ5C", "AG-BG2VTP7RD3"}
	expectedResult := []string{"workflow1", "workflow2"}

	mockService.EXPECT().
		StartCategoryWorkflow(ctx, date, businesses).
		Return(expectedResult, nil)

	actualResult, err := mockService.StartCategoryWorkflow(ctx, date, businesses)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, actualResult)
}
func TestStartKeywordInfoWorkflows(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()
	date := time.Now()
	businesses := []*KeywordSearchParams{
		{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid", "keyword2"}},
		{BusinessID: "AG-BG2VTP7RD3", Keywords: []string{"keyword3", "keyword4"}},
	}
	expectedResult := []string{"workflow1", "workflow2"}

	mockService.EXPECT().
		StartKeywordInfoWorkflows(ctx, date, businesses).
		Return(expectedResult, nil)

	actualResult, err := mockService.StartKeywordInfoWorkflows(ctx, date, businesses)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, actualResult)
}
func TestStartSuggestedKeywordsWorkflows(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()
	date := time.Now()
	businesses := []*KeywordSearchParams{
		{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"suggestion1", "suggestion2"}},
		{BusinessID: "AG-BG2VTP7RD3", Keywords: []string{"suggestion3", "suggestion4"}},
	}
	expectedResult := []string{"workflow1", "workflow2"}

	mockService.EXPECT().
		StartSuggestedKeywordsWorkflows(ctx, date, businesses).
		Return(expectedResult, nil)

	actualResult, err := mockService.StartSuggestedKeywordsWorkflows(ctx, date, businesses)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, actualResult)
}
func TestGetDataForSEOLocationForBusiness(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()
	businessID := "business123"
	expectedResult := &dataforseo.Location{
		Code:           0,
		Name:           "",
		CodeParent:     0,
		CountryISOCode: "",
		Type:           "",
	}

	mockService.EXPECT().
		GetDataForSEOLocationForBusiness(ctx, businessID).
		Return(expectedResult, nil)

	actualResult, err := mockService.GetDataForSEOLocationForBusiness(ctx, businessID)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, actualResult)
}
func TestStartCategoryWorkflow_Errors(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()
	date := time.Now()
	businesses := []string{"AG-H7TQNDHQ5C", "AG-BG2VTP7RD3"}
	expectedError := errors.New("failed to start category workflow")

	// Error scenario
	mockService.EXPECT().
		StartCategoryWorkflow(ctx, date, businesses).
		Return(nil, expectedError)

	actualResult, err := mockService.StartCategoryWorkflow(ctx, date, businesses)

	assert.Nil(t, actualResult)
	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
}
func TestStartKeywordInfoWorkflows_Errors(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()
	date := time.Now()
	businesses := []*KeywordSearchParams{
		{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid", "keyword2"}},
	}
	expectedError := errors.New("workflow initialization failed")

	// Error scenario
	mockService.EXPECT().
		StartKeywordInfoWorkflows(ctx, date, businesses).
		Return(nil, expectedError)

	actualResult, err := mockService.StartKeywordInfoWorkflows(ctx, date, businesses)

	assert.Nil(t, actualResult)
	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
}
func TestStartSuggestedKeywordsWorkflows_Errors(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()
	date := time.Now()
	businesses := []*KeywordSearchParams{
		{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"suggestion1"}},
	}
	expectedError := errors.New("no suggestions available for the given keywords")

	// Error scenario
	mockService.EXPECT().
		StartSuggestedKeywordsWorkflows(ctx, date, businesses).
		Return(nil, expectedError)

	actualResult, err := mockService.StartSuggestedKeywordsWorkflows(ctx, date, businesses)

	assert.Nil(t, actualResult)
	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
}
func TestStartSERPWorkflows_Errors(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()
	date := time.Now()
	force := true
	ignoreDataLakeResults := false
	businesses := []*KeywordSearchParams{
		{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid"}},
	}
	tags := []string{"tag1", "tag2"}
	expectedError := errors.New("SERP workflow failed due to data inconsistency")

	// Error scenario
	mockService.EXPECT().
		StartSERPWorkflows(ctx, force, ignoreDataLakeResults, businesses, date, tags).
		Return(nil, expectedError)

	actualResult, err := mockService.StartSERPWorkflows(ctx, force, ignoreDataLakeResults, businesses, date, tags...)

	assert.Nil(t, actualResult)
	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
}
func TestGetDataForSEOLocationForBusiness_Errors(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockSEOWorkflowServiceInterface(ctrl)

	ctx := context.Background()
	businessID := "AG-1"
	expectedError := errors.New("business ID not found")

	// Error scenario
	mockService.EXPECT().
		GetDataForSEOLocationForBusiness(ctx, businessID).
		Return(nil, expectedError)

	actualResult, err := mockService.GetDataForSEOLocationForBusiness(ctx, businessID)

	assert.Nil(t, actualResult)
	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
}

// import (
// 	"context"
// 	"errors"
// 	"strings"
// 	"testing"
// 	"time"

// 	"github.com/stretchr/testify/assert"
// 	"github.com/stretchr/testify/mock"
// )

// // MockService is a mock implementation of the Service type.
// type MockService struct {
// 	mock.Mock
// }

// func (m *MockService) getQueryResults(ctx context.Context, date time.Time) ([]*KeywordSearchParams, error) {
// 	args := m.Called(ctx, date)
// 	return args.Get(0).([]*KeywordSearchParams{
// 				{BusinessID: "AG-H7TQNDHQ5C", Keywords: []string{"dr mcdavid", "keyword2"}},
// 			}), args.Error(1)
// }

// func (m *MockService) getQueryStdResults(ctx context.Context) ([]string, error) {
// 	args := m.Called(ctx)
// 	return args.Get(0).([]string), args.Error(1)
// }

// func (m *MockService) StartSERPWorkflows(ctx context.Context, force bool, ignoreData bool, results []string, date time.Time) ([]string, error) {
// 	args := m.Called(ctx, force, ignoreData, results, date)
// 	return args.Get(0).([]string), args.Error(1)
// }

// func (m *MockService) StartSuggestedKeywordsWorkflows(ctx context.Context, date time.Time, results []string) ([]string, error) {
// 	args := m.Called(ctx, date, results)
// 	return args.Get(0).([]string), args.Error(1)
// }

// func (m *MockService) StartKeywordInfoWorkflows(ctx context.Context, date time.Time, results []string) ([]string, error) {
// 	args := m.Called(ctx, date, results)
// 	return args.Get(0).([]string), args.Error(1)
// }

// func (m *MockService) StartSEOWorkflows(ctx context.Context, forceSERPWorkflows bool, forceKeywordInfoWorkflows bool, forceSuggestedKeywordWorkflows bool, ignoreDataLakeResults bool, date time.Time) error {
// 	args := m.Called(ctx, forceSERPWorkflows, forceKeywordInfoWorkflows, forceSuggestedKeywordWorkflows, ignoreDataLakeResults, date)
// 	return args.Error(1)
// }

// func TestStartSEOWorkflows(t *testing.T) {
// 	ctx := context.Background()
// 	date := time.Date(2024, 11, 30, 0, 0, 0, 0, time.UTC) // Last day of the month
// 	results := []string{"result1", "result2"}

// 	mockService := new(MockService)
// 	mockService.On("getQueryResults", ctx, date).Return(results, nil)
// 	mockService.On("getQueryStdResults", ctx).Return([]string{"stdResult1", "stdResult2"}, nil)
// 	mockService.On("StartSERPWorkflows", ctx, true, false, mock.Anything, date).Return(nil, nil)
// 	mockService.On("StartSuggestedKeywordsWorkflows", ctx, date, results).Return(nil, nil)
// 	mockService.On("StartKeywordInfoWorkflows", ctx, date, results).Return(nil, nil)

// 	err := mockService.StartSEOWorkflows(ctx, true, true, true, false, date)
// 	assert.NoError(t, err)

// 	mockService.AssertExpectations(t)
// }

// func TestStartSEOWorkflows_ErrorOnGetQueryResults(t *testing.T) {
// 	ctx := context.Background()
// 	date := time.Now()

// 	mockService := new(MockService)
// 	mockService.On("getQueryResults", ctx, date).Return(nil, errors.New("query results error"))

// 	err := mockService.StartSEOWorkflows(ctx, false, false, false, false, date)
// 	assert.Error(t, err)
// 	assert.True(t, strings.Contains(err.Error(), "query results error"))

// 	mockService.AssertExpectations(t)
// }



func TestStartSnapshotSEOWorkflow(t *testing.T) {
	ctrl := gomock.NewController(t)
	ctx := context.Background()

	service, _, _, _, _, _, _ := setupMocks(t)
	const businessID = "AG-M8GZ83PL"
	const snapshotID = "test-snapshot-id"
	const dateFMT = "2006-01-02"
	formattedDate := time.Now().Format(dateFMT)
	workflowID := fmt.Sprintf("%s-SnapshotLocalSEOWorkflow-%s", businessID, formattedDate)
	taskQueue := constants.SEOTaskList

	workflowParams := &SnapshotWorkflowParams{
		Date:       formattedDate,
		BusinessID: businessID,
		SnapshotID: snapshotID,
	}

	workflowOptions := client.StartWorkflowOptions{
		ID:                                       workflowID,
		TaskQueue:                                taskQueue,
		WorkflowExecutionTimeout:                 time.Hour * 120,
		WorkflowIDReusePolicy:                    enums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE,
		WorkflowExecutionErrorWhenAlreadyStarted: false,
	}
	mockTemporalClient := temporal.NewMockTemporalWorkflowExecutor(ctrl)
	t.Run("Successful Workflow Execution", func(t *testing.T) {
		mockTemporalClient.EXPECT().ExecuteWorkflow(ctx, workflowOptions, SnapshotSEOWorkflow, workflowParams).Return(nil, nil)

		err := service.StartSnapshotSEOWorkflow(ctx, businessID, snapshotID)

		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// Assert that the mock was called as expected
		assert.NoError(t, err)
	})

	t.Run("Error in Workflow Execution", func(t *testing.T) {
		mockError := fmt.Errorf("mock execution error")
		mockTemporalClient.EXPECT().ExecuteWorkflow(ctx, workflowOptions, SnapshotSEOWorkflow, workflowParams).Return(nil, mockError)

		err := service.StartSnapshotSEOWorkflow(ctx, businessID, snapshotID)

		if err == nil || err.Error() != mockError.Error() {
			t.Fatalf("expected error %v, got %v", mockError, err)
		}

		// Assert that the mock was called as expected
		assert.NoError(t, err)
	})
}
func TestStartCategoryWorkflow(t *testing.T) {
	ctx := context.Background()

	service, mockTemporalClient, _, _, _, _, _ := setupMocks(t)

	const dateFMT = "2006-01-02"
	testDate := time.Date(2024, 11, 30, 0, 0, 0, 0, time.UTC)
	dateString := testDate.Format(dateFMT)

	businesses := []string{"business-1", "business-2", "business-3"}
	var expectedErrors []string
	for i, businessID := range businesses {
		workflowOptions := client.StartWorkflowOptions{
			ID:                                       fmt.Sprintf("%s-CategoryWorkflow-%s-%s", businessID, dateString, uuid.New()),
			TaskQueue:                                constants.SEOTaskList,
			WorkflowExecutionTimeout:                 time.Hour * 120,
			WorkflowIDReusePolicy:                    enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING,
			WorkflowExecutionErrorWhenAlreadyStarted: false,
			SearchAttributes: map[string]interface{}{
				"businessID": businessID,
			},
		}

		workflowParams := &CategoryWorkflowParams{
			BusinessID:           businessID,
			Date:                 dateString,
			InitialSleepDuration: DurationOfSleepBetweenWorkflows * time.Duration(i),
		}

		// Mock success or error for each business
		if businessID == "business-2" { // Simulate an error for business-2
			err := fmt.Errorf("mock error for %s", businessID)
			expectedErrors = append(expectedErrors, fmt.Sprintf("%s %s", businessID, err.Error()))
			mockTemporalClient.EXPECT().ExecuteWorkflow(gomock.Any(), workflowOptions, CategoryWorkflow, workflowParams).Return(nil, err).Times(1)
		} else {
			mockTemporalClient.EXPECT().ExecuteWorkflow(gomock.Any(), workflowOptions, CategoryWorkflow, workflowParams).Return(nil, nil).Times(len(businesses))
		}
	}

	t.Run("Handle multiple workflows with some errors", func(t *testing.T) {
		errors, err := service.StartCategoryWorkflow(ctx, testDate, businesses)

		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// Verify the errors list matches the expected errors
		if !reflect.DeepEqual(errors, expectedErrors) {
			t.Errorf("expected errors %v, got %v", expectedErrors, errors)
		}

	})
}
