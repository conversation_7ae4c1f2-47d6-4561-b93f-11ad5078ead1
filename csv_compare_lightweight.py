#!/usr/bin/env python3
"""
Lightweight CSV Comparison Tool
Compares all 4 columns (business_id, latitude, longitude, seo_keywords) between two CSV files 
and creates a result CSV with match indicators in the 5th column.
"""

import csv
import argparse
import sys
from pathlib import Path


def compare_csv_files(csv_file1: str, csv_file2: str, output_file: str = None) -> str:
    """
    Compare all 4 columns between two CSV files and create a result CSV with match status.
    
    Args:
        csv_file1: Path to the first CSV file
        csv_file2: Path to the second CSV file  
        output_file: Path to the output CSV file (optional)
        
    Returns:
        Path to the created comparison CSV file
    """
    
    # Generate output filename if not provided
    if output_file is None:
        file1_name = Path(csv_file1).stem
        file2_name = Path(csv_file2).stem
        output_file = f"csv_comparison_{file1_name}_vs_{file2_name}.csv"
    
    try:
        # Expected columns (first 4 columns)
        expected_columns = ['business_id', 'latitude', 'longitude', 'seo_keywords']
        
        # Read both CSV files into sets of tuples
        print(f"Reading {csv_file1}...")
        file1_rows = set()
        with open(csv_file1, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            # Check if expected columns exist
            missing_cols = [col for col in expected_columns if col not in reader.fieldnames]
            if missing_cols:
                raise ValueError(f"Missing columns in {csv_file1}: {missing_cols}")
            
            for row in reader:
                # Create tuple of the 4 columns for comparison
                row_tuple = (
                    row['business_id'],
                    row['latitude'], 
                    row['longitude'],
                    row['seo_keywords']
                )
                file1_rows.add(row_tuple)
        
        print(f"Reading {csv_file2}...")
        file2_rows = set()
        with open(csv_file2, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            # Check if expected columns exist
            missing_cols = [col for col in expected_columns if col not in reader.fieldnames]
            if missing_cols:
                raise ValueError(f"Missing columns in {csv_file2}: {missing_cols}")
            
            for row in reader:
                # Create tuple of the 4 columns for comparison
                row_tuple = (
                    row['business_id'],
                    row['latitude'],
                    row['longitude'], 
                    row['seo_keywords']
                )
                file2_rows.add(row_tuple)
        
        print(f"Found {len(file1_rows):,} unique rows in file 1")
        print(f"Found {len(file2_rows):,} unique rows in file 2")
        
        # Get all unique rows from both files
        all_rows = file1_rows.union(file2_rows)
        print(f"Total unique rows: {len(all_rows):,}")
        
        # Create comparison results
        print("Analyzing matches...")
        comparison_results = []
        
        for row_tuple in sorted(all_rows):
            business_id, latitude, longitude, seo_keywords = row_tuple
            
            # Check if this exact row exists in both files
            in_file1 = row_tuple in file1_rows
            in_file2 = row_tuple in file2_rows
            
            if in_file1 and in_file2:
                match_status = "match"
            else:
                match_status = "not match"
            
            comparison_results.append({
                'business_id': business_id,
                'latitude': latitude,
                'longitude': longitude,
                'seo_keywords': seo_keywords,
                'match_status': match_status
            })
        
        # Write results to CSV
        print(f"Writing results to {output_file}...")
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['business_id', 'latitude', 'longitude', 'seo_keywords', 'match_status']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(comparison_results)
        
        # Print summary statistics
        matches = sum(1 for result in comparison_results if result['match_status'] == 'match')
        no_matches = len(comparison_results) - matches
        
        print(f"\n=== COMPARISON RESULTS ===")
        print(f"Total unique rows: {len(comparison_results):,}")
        print(f"Exact matches (all 4 columns identical): {matches:,}")
        print(f"No matches (row exists in only one file): {no_matches:,}")
        print(f"Match percentage: {(matches / len(comparison_results) * 100):.2f}%")
        
        return output_file
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description='Compare all 4 columns between two CSV files')
    parser.add_argument('file1', help='Path to the first CSV file')
    parser.add_argument('file2', help='Path to the second CSV file')
    parser.add_argument('-o', '--output', help='Path to the output CSV file (optional)')
    parser.add_argument('--preview', action='store_true', help='Show a preview of the first few comparison results')
    
    args = parser.parse_args()
    
    try:
        output_path = compare_csv_files(args.file1, args.file2, args.output)
        print(f"\nComparison complete! Results saved to: {output_path}")
        
        # Show preview if requested
        if args.preview:
            print(f"\n=== PREVIEW (first 10 rows) ===")
            with open(output_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for i, row in enumerate(reader):
                    if i >= 10:
                        break
                    print(f"{row['business_id']:<15} {row['latitude']:<12} {row['longitude']:<15} {row['seo_keywords']:<30} {row['match_status']}")
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main() 